use std::error::Error;
use serde::{Serialize, Deserialize};

/// Trait for errors that can have associated status codes (e.g., for HTTP responses)
pub trait StatusCode {
    /// Get the numerical status code
    fn status_code(&self) -> u16;
}

/// Trait for errors that can have user-friendly messages
pub trait UserFriendlyError {
    /// Get a message suitable for display to end users
    fn user_message(&self) -> String;
}

/// Trait for errors that can be logged with different severity levels
pub trait LoggableError: Error {
    /// The severity level of this error
    fn severity(&self) -> LogSeverity;
    
    /// Whether this error should be reported to monitoring systems
    fn should_alert(&self) -> bool {
        matches!(self.severity(), LogSeverity::Error | LogSeverity::Critical)
    }
}

/// Possible severity levels for errors
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum LogSeverity {
    /// Informational messages that don't indicate an error
    Info,
    /// Minor issues that don't affect functionality
    Warning,
    /// Significant issues that affect functionality
    Error,
    /// Severe issues that require immediate attention
    Critical,
}

/// Trait for errors that can be retried
pub trait RetryableError: Error {
    /// Whether the operation can be retried
    fn is_retryable(&self) -> bool;
    
    /// Suggested minimum delay before retry (in milliseconds)
    fn suggested_retry_delay_ms(&self) -> u64 {
        1000 // Default 1 second
    }
    
    /// Maximum number of retry attempts recommended
    fn max_retry_attempts(&self) -> u8 {
        3 // Default 3 attempts
    }
}
