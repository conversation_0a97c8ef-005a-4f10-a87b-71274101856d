use std::error::<PERSON>rro<PERSON>;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON><PERSON>, Debug};
use std::sync::Arc;

/// A generic result type for operations that can fail
pub type PrismaResult<T> = Result<T, PrismaError>;

/// A generic error container that can wrap any error type
#[derive(Debug)]
pub struct PrismaError {
    /// The source error that caused this error
    source: Arc<dyn Error + Send + Sync>,
    /// Optional context message to provide additional information
    context: Option<String>,
}

impl PrismaError {
    /// Create a new error from any error type
    pub fn new<E>(error: E) -> Self 
    where 
        E: Error + Send + Sync + 'static,
    {
        Self {
            source: Arc::new(error),
            context: None,
        }
    }

    /// Create a new error with additional context
    pub fn with_context<E, S>(error: E, context: S) -> Self
    where
        E: Error + Send + Sync + 'static,
        S: Into<String>,
    {
        Self {
            source: Arc::new(error),
            context: Some(context.into()),
        }
    }

    /// Add context to an existing error
    pub fn add_context<S: Into<String>>(&mut self, context: S) {
        self.context = Some(context.into());
    }

    /// Get a reference to the source error
    pub fn source_ref(&self) -> &(dyn Error + Send + Sync) {
        self.source.as_ref()
    }
}

impl Clone for PrismaError {
    fn clone(&self) -> Self {
        Self {
            source: self.source.clone(),
            context: self.context.clone(),
        }
    }
}

impl Display for PrismaError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        if let Some(context) = &self.context {
            write!(f, "{}: {}", context, self.source)
        } else {
            write!(f, "{}", self.source)
        }
    }
}

impl Error for PrismaError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        Some(self.source.as_ref())
    }
}

// Implement From for common error types but not for PrismaError itself
// to avoid conflicts with the blanket implementation
impl From<std::io::Error> for PrismaError {
    fn from(error: std::io::Error) -> Self {
        Self::new(error)
    }
}

impl From<String> for PrismaError {
    fn from(error: String) -> Self {
        Self::new(std::io::Error::new(std::io::ErrorKind::Other, error))
    }
}

impl From<&str> for PrismaError {
    fn from(error: &str) -> Self {
        Self::new(std::io::Error::new(std::io::ErrorKind::Other, error.to_string()))
    }
}

// Implement conversion from domain-specific errors
impl From<crate::err::types::errors_mod::PrismaError> for PrismaError {
    fn from(error: crate::err::types::errors_mod::PrismaError) -> Self {
        Self::new(std::io::Error::new(std::io::ErrorKind::Other, error.to_string()))
    }
}

// Define a simple error type for engine operations (moved from prisma_engine.rs)
#[derive(Debug)]
pub struct EngineOperationError(String); // Made public

impl EngineOperationError {
    pub fn new(msg: String) -> Self { // Made public
        EngineOperationError(msg)
    }
}

impl Display for EngineOperationError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "Engine Operation Error: {}", self.0)
    }
}

impl Error for EngineOperationError {} // Implements std::error::Error
