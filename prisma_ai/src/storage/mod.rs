pub mod traits;
pub mod types;
pub mod surrealdb;

// Re-export the traits and types
pub use traits::*;
pub use types::*;
pub use surrealdb::*;

use ::surrealdb::Surreal;
use ::surrealdb::engine::remote::http::{Client, Http};
use ::surrealdb::opt::auth::Root;

// Import the correct PrismaError type
use crate::err::DomainError as PrismaError;
use crate::err::PrismaResult;

// Re-export key types from surrealdb module
pub use self::surrealdb::{Database, Document, Embedding, ChatMessage};
// Use ShortTermMemory instead of Memory to avoid conflict with LTM Memory
pub use self::surrealdb::ShortTermMemory;

pub struct Storage {
    client: Surreal<Client>,
}

impl Storage {
    pub async fn new() -> PrismaResult<Self> {
        let client = Surreal::new::<Http>("127.0.0.1:8000").await
            .map_err(|e| PrismaError::ConnectionError(format!("Failed to connect to SurrealDB: {}", e)))?;

        client.signin(Root {
            username: "root",
            password: "root",
        }).await
            .map_err(|e| PrismaError::AuthError(format!("Failed to authenticate with SurrealDB: {}", e)))?;

        client.use_ns("test").use_db("test").await
            .map_err(|e| PrismaError::DatabaseError(format!("Failed to select namespace and database: {}", e)))?;

        Ok(Self { client })
    }

    pub async fn init(&self) -> PrismaResult<()> {
        // Initialize database, create schemas, etc.
        Ok(())
    }

    pub fn database(&self) -> &Surreal<Client> {
        &self.client
    }
}

// Ensure all error usage refers to the correct PrismaError type
pub fn handle_connection_error(error_msg: &str) -> PrismaError {
    PrismaError::ConnectionError(error_msg.to_string())
}

// TODO: Implement CRUD operations
// TODO: Set up schemas for agents and tasks
// TODO: Implement query builders