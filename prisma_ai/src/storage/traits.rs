use async_trait::async_trait;
use serde::{de::DeserializeOwned, Serialize};
use surrealdb::opt::auth;
use chrono::{DateTime, Utc};

// Update import to use the renamed error type
use crate::err::DomainError as PrismaError;
use crate::err::PrismaResult;

use crate::storage::types::*;
use crate::llm::interface::Memory;

/// Database connection trait for storage operations
#[async_trait]
pub trait DatabaseConnection: Send + Sync {
    async fn connect(connection_string: &str) -> PrismaResult<Self> where Self: Sized;
    async fn disconnect(&self) -> PrismaResult<()>;
    async fn ping(&self) -> PrismaResult<()>;

    /// Sign in to the database with credentials
    async fn signin<C, R>(&self, credentials: C) -> PrismaResult<R>
    where
        C: auth::Credentials<auth::Signin, R> + Send + Sync,
        R: DeserializeOwned + Send + Sync;

    /// Use a specific namespace
    async fn use_ns(&self, namespace: &str) -> PrismaResult<()>;

    /// Use a specific database
    async fn use_db(&self, database: &str) -> PrismaResult<()>;
}

/// CRUD operations trait for data storage
#[async_trait]
pub trait DataStore: Send + Sync {
    async fn create<T: Serialize + Send + Sync>(&self, table: &str, data: &T) -> PrismaResult<Record>;
    async fn get<T: DeserializeOwned + Send + Sync>(&self, table: &str, id: &str) -> PrismaResult<Option<T>>;
    async fn update<T: Serialize + Send + Sync>(&self, table: &str, id: &str, data: &T) -> PrismaResult<Record>;
    async fn delete(&self, table: &str, id: &str) -> PrismaResult<bool>;
    async fn query<T: DeserializeOwned + Send + Sync>(&self, query: &str, params: &[(&str, &str)]) -> PrismaResult<Vec<T>>;

    // Method to retrieve conversation history for a specific agent
    // TODO: Define the return type more specifically, e.g., Vec<ConversationTurn>
    async fn get_conversation_history<T: DeserializeOwned + Send + Sync>(
        &self,
        agent_id: &str,
        limit: usize,
    ) -> PrismaResult<Vec<T>>;
}

/// Long-term memory operations trait
#[async_trait]
pub trait LongTermMemoryStore: Send + Sync {
    /// Store a new memory with embeddings
    async fn store_memory(
        &self,
        agent_id: &str,
        content: &str,
        category: &str,
        importance: f32,
        embedding: &[f32]
    ) -> PrismaResult<String>;

    /// Get a specific memory by ID
    async fn get_memory(&self, memory_id: &str) -> PrismaResult<Memory>;

    /// Update an existing memory
    async fn update_memory(
        &self,
        memory_id: &str,
        content: Option<&str>,
        category: Option<&str>,
        importance: Option<f32>,
        embedding: Option<Vec<f32>>
    ) -> PrismaResult<()>;

    /// Delete a memory
    async fn delete_memory(&self, memory_id: &str) -> PrismaResult<()>;

    /// Get all memories for an agent
    async fn get_all_memories(&self, agent_id: &str) -> PrismaResult<Vec<Memory>>;

    /// Get memories by category
    async fn get_memories_by_category(&self, agent_id: &str, category: &str) -> PrismaResult<Vec<Memory>>;

    /// Get memories by importance threshold
    async fn get_memories_by_importance(&self, agent_id: &str, min_importance: f32) -> PrismaResult<Vec<Memory>>;

    /// Get the most recent memories
    async fn get_recent_memories(&self, agent_id: &str, limit: usize) -> PrismaResult<Vec<Memory>>;

    /// Find memories relevant to a query using embedding similarity
    async fn get_relevant_memories(&self, agent_id: &str, query_embedding: &[f32], limit: usize) -> PrismaResult<Vec<Memory>>;

    /// Format memories for inclusion in a prompt
    fn format_memories_for_prompt(&self, memories: &[Memory]) -> String;

    /// Clean up old or low-importance memories
    async fn cleanup_old_memories(&self, agent_id: &str, max_age_days: u32, min_importance: f32) -> PrismaResult<usize>;

    /// Remove all memories for a specific agent
    async fn remove_agent_memories(&self, agent_id: &str) -> PrismaResult<usize>;
}
