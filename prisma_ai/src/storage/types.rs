use serde::{Serialize, Deserialize};
use crate::err::DomainError as PrismaError;
use crate::err::PrismaResult;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use surrealdb::sql::Thing;
// Remove duplicate import
// use crate::err::PrismaError;

/// Represents a database record with an identifier
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Record {
    #[serde(with = "thing_id_string")]
    pub id: String,
    // Other common record fields
}

/// Custom serialization/deserialization for SurrealDB Thing IDs
pub mod thing_id_string {
    use serde::{self, Deserialize, Deserializer, Serializer};
    use surrealdb::sql::Thing;

    pub fn serialize<S>(id: &String, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(id)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<String, D::Error>
    where
        D: Deserializer<'de>,
    {
        let value = serde_json::Value::deserialize(deserializer)?;

        // Try to deserialize as Thing first
        if let Ok(thing) = serde_json::from_value::<Thing>(value.clone()) {
            // Return the full string representation of the Thing
            Ok(thing.to_string())
        } else if let Some(s) = value.as_str() {
            // Handle cases where the ID might already be a plain string
            Ok(s.to_string())
        } else {
            // Fallback: try converting the value to string (might indicate unexpected format)
            // Consider logging a warning here if this path is hit often
            eprintln!("Warning: thing_id_string deserializer falling back to value.to_string() for value: {:?}", value);
            Ok(value.to_string())
        }
    }
}

/// Connection configuration for the database
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub user: Option<String>,
    pub password: Option<String>,
    pub database: String,
}

// Database-specific error conversion
impl From<surrealdb::Error> for PrismaError {
    fn from(err: surrealdb::Error) -> Self {
        PrismaError::DatabaseError(format!("SurrealDB error: {}", err))
    }
}

// Add any other type definitions that might have been in surrealdb.rs
