# PROJECT SETTINGS

# PROJECT SETUP

# NEW PROJECT

[new_project]
# Form field configurations
fields = [
    { name = "user_name", type = "string", required = true, label = "Your Name" },
    { name = "project_name", type = "string", required = true, label = "Project Name" },
    { name = "project_description", type = "string", required = false, label = "Project Description", multiline = true },
    { name = "project_specifications", type = "string", required = true, label = "Project Specifications", multiline = true }
]

# Embedding model configuration
embedding_model = "required=true,default_model=,model_list_source=llama_models.embedding_only"

# Document upload settings
document_upload = "allowed_extensions=txt,pdf,doc,docx,md;allow_multiple=true;max_file_size=100MB;storage_path=./documents"

# MODEL SELECTION SCREEN

# LLM
[model_selection.llm]
# Basic fields configuration
fields = [
    { name = "name", type = "string", required = true, label = "Agent Name" },
    { name = "role", type = "string", required = true, label = "Role" },
    { name = "background", type = "string", required = true, label = "Background Story", multiline = true },
    { name = "tasks", type = "string", required = true, label = "Tasks" }
]

# Model configuration
model_config = "allow_model_selection=true,required=true,model_list_source=llama_models.all"

# Tool configuration
tools = "enable_tools=true,allow_multiple=true,tool_list_source=llama_tools"

# Sampling parameters
sampling = "temperature=0.7,top_k=40,top_p=0.9,min_p=0.05,repeat_penalty=1.1,presence_penalty=0.0,frequency_penalty=0.0"

# General Parameters
general_parameters = "context_size=2048,batch_size=512,threads=4"

# Rules and Skills
rules_and_skills = "required=true,multiline=true,label=Rules and Skills"

# RAG document configuration
rag_documents = "allowed_extensions=txt,pdf,doc,docx,md;allow_multiple=true;max_file_size=100MB"

# Add Another Participant
add_participant = "enabled=true,button_label=Add Another Participant"

# HUMAN
[model_selection.human]
# Basic fields configuration
fields = [
    { name = "name", type = "string", required = true, label = "Participant Name" },
    { name = "role", type = "string", required = true, label = "Role" },
    { name = "background", type = "string", required = true, label = "Background Story", multiline = true }
]

# NOTIFICATIONS

[NOTIFICATIONS.rabbitmq]
enabled = true
host = "localhost"
port = 5672
username = "guest"
password = "guest"
vhost = "/"
connection_name = "prisma_ui_notifications"
auto_reconnect = true
reconnect_interval_seconds = 5
connection_timeout_seconds = 30

# Exchange configuration
exchange = "name=prisma.notifications,type=topic,durable=true,auto_delete=false"

# Queue configuration
queue = "name=ui.notifications,durable=true,exclusive=false,auto_delete=false"

# Message configuration
message = "routing_key=ui.notification.#,content_type=application/json,delivery_mode=2"


# COLLABORATION

[COLLABORATION]
enabled = true
description = "Topic management for collaboration between participants"

[COLLABORATION.topics]
allow_creation = true
allow_editing = false
default_list_empty = true

[COLLABORATION.topic_form]
name_field_label = "Topic Name *"
name_field_required = true
description_field_label = "Description"
description_field_required = false

[COLLABORATION.dialog]
title = "Create New Topic"

[COLLABORATION.navigation]
continue_button_text = "Continue to Chat"
back_button_enabled = true

[COLLABORATION.participants]
required = true
allow_assignment_to_topics = true

# CHAT

[CHAT]
enabled = true
description = "Configuration for chat interface and functionality"

[CHAT.interface]
show_sidebar = true
show_participants_button = true
message_input_placeholder = "Type a message..."
send_button_icon = "send"
scroll_behavior = "auto_scroll_on_new_message"

[CHAT.streaming]
enabled = true
buffer_size = 100
token_delay_ms = 0
show_typing_indicator = true
show_partial_messages = true
use_markdown_rendering = true

[CHAT.topics]
allow_topic_selection = true
show_all_topics_toggle = true
sidebar_width = "25%"
show_topic_filter = true

[CHAT.messages]
show_sender_name = true
show_timestamp = true
allow_message_deletion = false
show_typing_indicator = true
show_processing_state = true
ai_response_delay_ms = 50

[CHAT.participant_management]
allow_participant_editing = true
allow_adding_participants = true
allow_removing_participants = true
show_management_dialog = true

[CHAT.ai_behavior]
simulate_typing = true
auto_scroll_on_response = true
streaming_enabled = true
streaming_buffer_size = 100

# DEFAULT SETTINGS

# PRISMA ENGINE

# PRISMA ENGINE INITIALIZATION

[prisma_engine_initialization]
enable_prisma_engine = true


# METADATA CONFIGURATION

[project_metadata]
version = "1.0.0"
export_format = "json"

[session_tracking]
enable_participant_tracking = true
enable_message_tracking = true
enable_time_tracking = true
tracking_interval_seconds = 60

