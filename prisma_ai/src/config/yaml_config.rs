use std::path::{Path, PathBuf};
use serde::{Serialize, Deserialize};
use tracing::{info, error, warn, debug};
use tokio::fs;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::time::SystemTime;

// Import custom error types
use crate::err::{PrismaResult, GenericError};
use std::error::Error;
use std::fmt::{Display, Formatter, Result as FmtResult};

/// Custom error type for YAML configuration errors
#[derive(Debug)]
pub struct YamlConfigError {
    message: String,
    source: Option<Box<dyn Error + Send + Sync>>,
}

impl YamlConfigError {
    /// Create a new YAML configuration error with a message
    pub fn new<S: Into<String>>(message: S) -> Self {
        Self {
            message: message.into(),
            source: None,
        }
    }

    /// Create a new YAML configuration error with a message and source error
    pub fn with_source<S, E>(message: S, source: E) -> Self
    where
        S: Into<String>,
        E: Error + Send + Sync + 'static,
    {
        Self {
            message: message.into(),
            source: Some(Box::new(source)),
        }
    }
}

impl Display for YamlConfigError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "YAML Configuration Error: {}", self.message)
    }
}

impl Error for YamlConfigError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        self.source.as_ref().map(|e| e.as_ref() as &(dyn Error + 'static))
    }
}

/// Service credentials configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceCredentials {
    /// RabbitMQ configuration
    #[serde(default)]
    pub rabbitmq: RabbitMQConfig,

    /// SurrealDB configuration
    #[serde(default)]
    pub surrealdb: SurrealDBConfig,

    /// Loki configuration
    #[serde(default)]
    pub loki: LokiConfig,
}

/// RabbitMQ configuration
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct RabbitMQConfig {
    /// RabbitMQ host
    #[serde(default = "default_rabbitmq_host")]
    pub host: String,

    /// RabbitMQ port
    #[serde(default = "default_rabbitmq_port")]
    pub port: u16,

    /// RabbitMQ username
    #[serde(default = "default_rabbitmq_username")]
    pub username: String,

    /// RabbitMQ password
    #[serde(default = "default_rabbitmq_password")]
    pub password: String,

    /// RabbitMQ virtual host
    #[serde(default = "default_rabbitmq_vhost")]
    pub vhost: String,

    /// RabbitMQ management port
    #[serde(default = "default_rabbitmq_management_port")]
    pub management_port: u16,

    /// RabbitMQ version
    #[serde(default)]
    pub version: String,
}

/// SurrealDB configuration
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SurrealDBConfig {
    /// SurrealDB host
    #[serde(default = "default_surrealdb_host")]
    pub host: String,

    /// SurrealDB port
    #[serde(default = "default_surrealdb_port")]
    pub port: u16,

    /// SurrealDB username
    #[serde(default = "default_surrealdb_username")]
    pub username: String,

    /// SurrealDB password
    #[serde(default = "default_surrealdb_password")]
    pub password: String,

    /// SurrealDB namespace
    #[serde(default = "default_surrealdb_namespace")]
    pub namespace: String,

    /// SurrealDB database
    #[serde(default = "default_surrealdb_database")]
    pub database: String,

    /// Use HTTP protocol instead of WebSocket
    #[serde(default = "default_surrealdb_use_http")]
    pub use_http: bool,

    /// SurrealDB version
    #[serde(default)]
    pub version: String,
}

/// Loki configuration
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct LokiConfig {
    /// Loki host
    #[serde(default = "default_loki_host")]
    pub host: String,

    /// Loki port
    #[serde(default = "default_loki_port")]
    pub port: u16,

    /// Loki version
    #[serde(default)]
    pub version: String,
}

/// Main configuration structure loaded from the YAML file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct YamlConfig {
    /// Service credentials
    #[serde(default)]
    pub services: ServiceCredentials,

    // Add other configuration sections as needed
}

impl Default for YamlConfig {
    fn default() -> Self {
        Self {
            services: ServiceCredentials {
                rabbitmq: RabbitMQConfig::default(),
                surrealdb: SurrealDBConfig::default(),
                loki: LokiConfig::default(),
            },
        }
    }
}

impl Default for ServiceCredentials {
    fn default() -> Self {
        Self {
            rabbitmq: RabbitMQConfig::default(),
            surrealdb: SurrealDBConfig::default(),
            loki: LokiConfig::default(),
        }
    }
}

// Default values for RabbitMQ configuration
fn default_rabbitmq_host() -> String { "localhost".to_string() }
fn default_rabbitmq_port() -> u16 { 5672 }
fn default_rabbitmq_username() -> String { "guest".to_string() }
fn default_rabbitmq_password() -> String { "guest".to_string() }
fn default_rabbitmq_vhost() -> String { "/".to_string() }
fn default_rabbitmq_management_port() -> u16 { 15672 }

// Default values for SurrealDB configuration
fn default_surrealdb_host() -> String { "localhost".to_string() }
fn default_surrealdb_port() -> u16 { 8000 }
fn default_surrealdb_username() -> String { "root".to_string() }
fn default_surrealdb_password() -> String { "root".to_string() }
fn default_surrealdb_namespace() -> String { "prisma".to_string() }
fn default_surrealdb_database() -> String { "prisma".to_string() }
fn default_surrealdb_use_http() -> bool { true }

// Default values for Loki configuration
fn default_loki_host() -> String { "localhost".to_string() }
fn default_loki_port() -> u16 { 3100 }

/// Configuration manager for YAML configuration files
#[derive(Debug)]
pub struct YamlConfigManager {
    /// Path to the configuration file
    config_path: PathBuf,
    /// Loaded configuration
    config: Arc<RwLock<YamlConfig>>,
    /// Last modified time of the configuration file
    last_modified: SystemTime,
}

impl YamlConfigManager {
    /// Create a new configuration manager using the default config path
    pub async fn new() -> PrismaResult<Self> {
        let default_path = PathBuf::from("configs/config.yaml");
        Self::from_path(&default_path).await
    }

    /// Create a new configuration manager from a specific file path
    pub async fn from_path<P: AsRef<Path>>(path: P) -> PrismaResult<Self> {
        let config_path = path.as_ref().to_path_buf();

        // Load the configuration file
        let content = fs::read_to_string(&config_path)
            .await
            .map_err(|e| {
                let err_msg = format!("Failed to read config file: {:?}", config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        let config = serde_yaml::from_str::<YamlConfig>(&content)
            .map_err(|e| {
                let err_msg = format!("Failed to parse YAML configuration");
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        let metadata = fs::metadata(&config_path)
            .await
            .map_err(|e| {
                let err_msg = format!("Failed to get metadata for config file: {:?}", config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        let last_modified = metadata.modified()
            .map_err(|e| {
                let err_msg = format!("Failed to get last modified time for config file: {:?}", config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        info!("Loaded YAML configuration from {:?}", config_path);

        Ok(Self {
            config_path,
            config: Arc::new(RwLock::new(config)),
            last_modified,
        })
    }

    /// Get a reference to the configuration
    pub fn get_config(&self) -> Arc<RwLock<YamlConfig>> {
        self.config.clone()
    }

    /// Check if the configuration file has been modified and reload if necessary
    pub async fn check_and_reload(&mut self) -> PrismaResult<bool> {
        let metadata = fs::metadata(&self.config_path)
            .await
            .map_err(|e| {
                let err_msg = format!("Failed to get metadata for config file: {:?}", self.config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        let modified = metadata.modified()
            .map_err(|e| {
                let err_msg = format!("Failed to get last modified time for config file: {:?}", self.config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        if modified > self.last_modified {
            info!("Configuration file has been modified, reloading...");

            let content = fs::read_to_string(&self.config_path)
                .await
                .map_err(|e| {
                    let err_msg = format!("Failed to read config file: {:?}", self.config_path);
                    GenericError::new(YamlConfigError::with_source(err_msg, e))
                })?;

            let new_config = serde_yaml::from_str::<YamlConfig>(&content)
                .map_err(|e| {
                    let err_msg = format!("Failed to parse YAML configuration");
                    GenericError::new(YamlConfigError::with_source(err_msg, e))
                })?;

            let mut config = self.config.write().await;
            *config = new_config;
            self.last_modified = modified;

            info!("Configuration reloaded successfully");

            return Ok(true);
        }

        Ok(false)
    }

    /// Force reload the configuration
    pub async fn force_reload(&mut self) -> PrismaResult<()> {
        info!("Forcing configuration reload...");

        let content = fs::read_to_string(&self.config_path)
            .await
            .map_err(|e| {
                let err_msg = format!("Failed to read config file: {:?}", self.config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        let new_config = serde_yaml::from_str::<YamlConfig>(&content)
            .map_err(|e| {
                let err_msg = format!("Failed to parse YAML configuration");
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        let mut config = self.config.write().await;
        *config = new_config;

        let metadata = fs::metadata(&self.config_path)
            .await
            .map_err(|e| {
                let err_msg = format!("Failed to get metadata for config file: {:?}", self.config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        self.last_modified = metadata.modified()
            .map_err(|e| {
                let err_msg = format!("Failed to get last modified time for config file: {:?}", self.config_path);
                GenericError::new(YamlConfigError::with_source(err_msg, e))
            })?;

        info!("Configuration reloaded successfully");

        Ok(())
    }

    /// Get the RabbitMQ connection string
    pub async fn get_rabbitmq_connection_string(&self) -> String {
        let config = self.config.read().await;
        let rabbitmq = &config.services.rabbitmq;

        format!(
            "amqp://{}:{}@{}:{}/{}",
            rabbitmq.username,
            rabbitmq.password,
            rabbitmq.host,
            rabbitmq.port,
            rabbitmq.vhost
        )
    }

    /// Get the SurrealDB connection string
    pub async fn get_surrealdb_connection_string(&self) -> String {
        let config = self.config.read().await;
        let surrealdb = &config.services.surrealdb;

        if surrealdb.use_http {
            format!("http://{}:{}", surrealdb.host, surrealdb.port)
        } else {
            format!("ws://{}:{}", surrealdb.host, surrealdb.port)
        }
    }

    /// Get the Loki connection string
    pub async fn get_loki_connection_string(&self) -> String {
        let config = self.config.read().await;
        let loki = &config.services.loki;

        format!("http://{}:{}", loki.host, loki.port)
    }

    /// Get the RabbitMQ configuration
    pub async fn get_rabbitmq_config(&self) -> RabbitMQConfig {
        let config = self.config.read().await;
        config.services.rabbitmq.clone()
    }

    /// Get the SurrealDB configuration
    pub async fn get_surrealdb_config(&self) -> SurrealDBConfig {
        let config = self.config.read().await;
        config.services.surrealdb.clone()
    }

    /// Get the Loki configuration
    pub async fn get_loki_config(&self) -> LokiConfig {
        let config = self.config.read().await;
        config.services.loki.clone()
    }
}
