# Prompt Templates for Prisma AI
# These templates use the Tera templating engine syntax

[templates]

# Common sections that can be reused across templates
# These are not meant to be used directly as templates

# Common conversation footer - appears in all templates
# Variables: history, user_msg
conversation_footer = """
Conversation history:
{{ history }}

User: {{ user_msg }}
Assistant: """

# Common goal section - appears in most templates
# Variables: goal
goal_section = "Your goal is: {{ goal }}"

# Basic role introduction - appears in simple templates
# Variables: role
role_intro = "You are an AI assistant with the following role: {{ role }}"

# Named role introduction - appears in templates with agent names
# Variables: name, role
named_role_intro = "You are {{ name }}, an AI assistant with the following role: {{ role }}"

# Document settings section - appears in templates with document access
# Variables: document_settings
document_settings_section = """
Document settings for this project:
{{ document_settings }}"""

# RAG configuration section - appears in templates with RAG capabilities
# Variables: rag_config
rag_config_section = """
You have access to the following document retrieval configuration:
{{ rag_config }}

When referencing information from documents, cite the source."""

# Long-term memory section - appears in templates with LTM capabilities
# Variables: long_term_memory
long_term_memory_section = """
You have access to the following relevant memories from your long-term memory:
{{ long_term_memory }}

Use these memories to inform your responses when relevant."""

# Base template with common elements - to be extended by other templates
# Variables: role, goal, history, user_msg
base_template = """
{% if name is defined %}{{ named_role_intro }}{% else %}{{ role_intro }}{% endif %}

{{ goal_section }}

{% if background is defined %}Background: {{ background }}
{% endif %}
{% if tasks is defined %}Your tasks include: {{ tasks }}
{% endif %}
{% if rules_and_skills is defined %}You must follow these rules and use these skills:
{{ rules_and_skills }}
{% endif %}
{% if available_tools is defined and available_tools != "None" %}You have access to the following tools:
{{ available_tools }}

When you need to use a tool, use the following format:
```json
{"tool": "tool_name", "parameters": {"param1": "value1", "param2": "value2"}}
```
{% endif %}
{% if rag_config is defined %}{{ rag_config_section }}{% endif %}
{% if document_settings is defined %}{{ document_settings_section }}{% endif %}
{% if long_term_memory is defined %}{{ long_term_memory_section }}{% endif %}
{% if participants is defined %}You are participating in a collaborative discussion with:
{{ participants }}
{% endif %}
{% if topic is defined %}The current topic is: {{ topic }}
{% endif %}

{{ conversation_footer }}"""

# Actual templates that use the base template

# Default template - basic assistant template
# Variables: role, goal, history, user_msg
default = "{{ base_template }}"

# Comprehensive agent template - includes more detailed agent information
# Variables: name, role, background, tasks, goal, history, user_msg
comprehensive = "{{ base_template }}"

# Project-focused template - for project-related discussions
# Variables: user_name, project_name, project_description, project_specifications, document_settings, role, goal, history, user_msg
project = """
You are an AI assistant helping {{ user_name }} with their project: {{ project_name }}

Project Description: {{ project_description }}

Project Specifications: {{ project_specifications }}

Your role is: {{ role }}

{{ goal_section }}

{% if document_settings is defined %}{{ document_settings_section }}{% endif %}

{{ conversation_footer }}"""

# Rules and skills template - includes specific rules and skills for the agent
# Variables: role, goal, rules_and_skills, history, user_msg
rules_based = "{{ base_template }}"

# Tool-enabled template - for agents with tool access
# Variables: role, goal, available_tools, history, user_msg
tool_enabled = "{{ base_template }}"

# RAG-enabled template - for agents with document retrieval capabilities
# Variables: role, goal, rag_config, history, user_msg
rag_enabled = "{{ base_template }}"

# LTM-enabled template - for agents with long-term memory capabilities
# Variables: role, goal, long_term_memory, history, user_msg
ltm_enabled = "{{ base_template }}"

# Streaming-enabled template - for agents with streaming output capabilities
# Variables: role, goal, history, user_msg
streaming_enabled = "{{ base_template }}"

# Collaborative template - for multi-agent collaboration scenarios
# Variables: name, role, goal, participants, topic, history, user_msg
collaborative = "{{ base_template }}"
