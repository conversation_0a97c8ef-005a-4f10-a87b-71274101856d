use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::err::PrismaResult;

// Update custom datetime serialization for Memory struct to handle Option<DateTime<Utc>>
mod datetime_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Format as RFC3339 for compatibility with SurrealDB
        serializer.serialize_str(&dt.to_rfc3339())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        DateTime::parse_from_rfc3339(&s)
            .map(|dt| dt.with_timezone(&Utc))
            .map_err(serde::de::Error::custom)
    }
}

// Add separate module for Optional datetime fields
mod datetime_option_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match dt {
            Some(dt) => serializer.serialize_str(&dt.to_rfc3339()),
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = Option::<String>::deserialize(deserializer)?;
        match s {
            Some(s) => {
                DateTime::parse_from_rfc3339(&s)
                    .map(|dt| Some(dt.with_timezone(&Utc)))
                    .map_err(serde::de::Error::custom)
            }
            None => Ok(None),
        }
    }
}

/// Represents a memory entry in the long-term memory system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Memory {
    /// Unique identifier for the memory
    pub id: String,

    /// ID of the agent this memory belongs to
    pub agent_id: String,

    /// The actual content of the memory
    pub content: String,

    /// Vector embedding of the memory content
    pub embedding: Vec<f32>,

    /// Category or type of memory (e.g., "fact", "conversation", "insight")
    pub category: String,

    /// Importance score (0.0 to 1.0) - higher values indicate more important memories
    pub importance: f32,

    /// When the memory was created
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,

    /// When the memory was last accessed (None if never accessed)
    #[serde(with = "datetime_option_format", skip_serializing_if = "Option::is_none")]
    pub last_accessed: Option<DateTime<Utc>>,

    /// Number of times this memory has been accessed
    pub access_count: u32,
}

/// Interface for long-term memory operations
#[async_trait]
pub trait LongTermMemory: Send + Sync {
    /// Store a new memory
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent this memory belongs to
    /// * `content` - The content of the memory
    /// * `category` - Category of the memory
    /// * `importance` - Importance score (0.0 to 1.0)
    ///
    /// # Returns
    /// The ID of the stored memory or an error
    async fn store_memory(
        &self,
        agent_id: &str,
        content: &str,
        category: &str,
        importance: f32,
    ) -> PrismaResult<String>;

    /// Retrieve a specific memory by ID
    ///
    /// # Arguments
    /// * `memory_id` - ID of the memory to retrieve
    ///
    /// # Returns
    /// The memory if found, or an error
    async fn get_memory(&self, memory_id: &str) -> PrismaResult<Memory>;

    /// Update an existing memory
    ///
    /// # Arguments
    /// * `memory_id` - ID of the memory to update
    /// * `content` - Optional new content
    /// * `category` - Optional new category
    /// * `importance` - Optional new importance score
    ///
    /// # Returns
    /// Success or an error
    async fn update_memory(
        &self,
        memory_id: &str,
        content: Option<&str>,
        category: Option<&str>,
        importance: Option<f32>,
    ) -> PrismaResult<()>;

    /// Delete a memory
    ///
    /// # Arguments
    /// * `memory_id` - ID of the memory to delete
    ///
    /// # Returns
    /// Success or an error
    async fn delete_memory(&self, memory_id: &str) -> PrismaResult<()>;

    /// Get all memories for an agent
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent
    ///
    /// # Returns
    /// A vector of memories or an error
    async fn get_all_memories(&self, agent_id: &str) -> PrismaResult<Vec<Memory>>;

    /// Get memories by category
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent
    /// * `category` - Category to filter by
    ///
    /// # Returns
    /// A vector of memories or an error
    async fn get_memories_by_category(&self, agent_id: &str, category: &str) -> PrismaResult<Vec<Memory>>;

    /// Get memories by minimum importance
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent
    /// * `min_importance` - Minimum importance threshold
    ///
    /// # Returns
    /// A vector of memories or an error
    async fn get_memories_by_importance(&self, agent_id: &str, min_importance: f32) -> PrismaResult<Vec<Memory>>;

    /// Get the most recent memories
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent
    /// * `limit` - Maximum number of memories to return
    ///
    /// # Returns
    /// A vector of memories or an error
    async fn get_recent_memories(&self, agent_id: &str, limit: usize) -> PrismaResult<Vec<Memory>>;

    /// Find memories relevant to a query using embedding similarity
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent
    /// * `query` - The query text
    /// * `limit` - Maximum number of memories to return
    ///
    /// # Returns
    /// A vector of memories or an error
    async fn get_relevant_memories(&self, agent_id: &str, query: &str, limit: usize) -> PrismaResult<Vec<Memory>>;

    /// Format memories for inclusion in a prompt
    ///
    /// # Arguments
    /// * `memories` - The memories to format
    ///
    /// # Returns
    /// A formatted string for inclusion in a prompt
    fn format_memories_for_prompt(&self, memories: &[Memory]) -> String;

    /// Clean up old or low-importance memories
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent
    /// * `max_age_days` - Maximum age in days
    /// * `min_importance` - Minimum importance threshold
    ///
    /// # Returns
    /// Number of memories removed or an error
    ///
    /// # Note
    /// This method should only be used for manual cleanup. For automatic cleanup
    /// when an agent is deleted, use `remove_agent_memories` instead.
    async fn cleanup_old_memories(&self, agent_id: &str, max_age_days: u32, min_importance: f32) -> PrismaResult<usize>;

    /// Remove all memories associated with an agent
    ///
    /// # Arguments
    /// * `agent_id` - ID of the agent whose memories should be removed
    ///
    /// # Returns
    /// Number of memories removed or an error
    ///
    /// # Note
    /// This method should be called when an agent is deleted to ensure all
    /// associated memories are properly cleaned up.
    async fn remove_agent_memories(&self, agent_id: &str) -> PrismaResult<usize>;
}
