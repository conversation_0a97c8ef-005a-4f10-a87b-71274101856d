// =================================================================================================
// File: prisma_ai/src/llm/interface/performance_metrics.rs
// =================================================================================================
// Purpose: Defines the PerformanceMetrics trait for tracking LLM model performance metrics.
// This trait provides methods to access performance-related information such as tokens processed,
// requests processed, and processing speed.
//
// Integration:
// - Internal Dependencies:
//   - interface/mod.rs: Re-exports this trait
//   - interface/performance.rs: Uses Performance trait for low-level metrics
//
// - External Dependencies:
//   - None
//
// Platform Considerations:
// - This module is platform-independent
// =================================================================================================

use async_trait::async_trait;
use crate::err::PrismaResult;

/// Trait for accessing performance metrics of an LLM model
#[async_trait]
pub trait PerformanceMetrics: Send + Sync {
    /// Get the total number of tokens processed by the model
    async fn total_tokens_processed(&self) -> u64;

    /// Get the total number of requests processed by the model
    async fn total_requests_processed(&self) -> u64;

    /// Get the average tokens per second processing speed
    async fn tokens_per_second(&self) -> f64;

    /// Get the average time per token in milliseconds
    async fn time_per_token_ms(&self) -> f64;

    /// Get the total processing time in seconds
    async fn total_processing_time_seconds(&self) -> f64;

    /// Get the average request latency in milliseconds
    async fn average_request_latency_ms(&self) -> f64;

    /// Get the peak tokens per second observed
    async fn peak_tokens_per_second(&self) -> f64;

    /// Reset performance counters
    async fn reset_counters(&mut self) -> PrismaResult<()>;

    /// Record a new inference operation
    async fn record_inference(&mut self, tokens: u64, duration_ms: f64) -> PrismaResult<()>;
}
