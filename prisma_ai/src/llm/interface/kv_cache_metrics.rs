// =================================================================================================
// File: prisma_ai/src/llm/interface/kv_cache_metrics.rs
// =================================================================================================
// Purpose: Defines the KvCacheMetrics trait for tracking LLM model KV cache metrics.
// This trait provides methods to access KV cache-related information such as usage percentage,
// token count, and cell utilization.
//
// Integration:
// - Internal Dependencies:
//   - interface/mod.rs: Re-exports this trait
//   - interface/cache.rs: Provides low-level KV cache operations
//
// - External Dependencies:
//   - None
//
// Platform Considerations:
// - This module is platform-independent
// =================================================================================================

use async_trait::async_trait;
use crate::err::PrismaResult;

/// Trait for accessing KV cache metrics of an LLM model
#[async_trait]
pub trait KvCacheMetrics: Send + Sync {
    /// Get the KV cache usage as a percentage of total capacity
    async fn usage_percentage(&self) -> f64;

    /// Get the number of tokens in the KV cache
    async fn token_count(&self) -> i32;

    /// Get the number of used cells in the KV cache
    async fn used_cells(&self) -> i32;

    /// Get the total capacity of the KV cache in cells
    async fn total_cells(&self) -> i32;

    /// Get the memory usage of the KV cache in bytes
    async fn memory_bytes(&self) -> u64;

    /// Get the cell utilization ratio (used cells / total cells)
    async fn cell_utilization(&self) -> f64;

    /// Get the average tokens per sequence in the KV cache
    async fn avg_tokens_per_sequence(&self) -> f64;

    /// Get the number of active sequences in the KV cache
    async fn active_sequences(&self) -> i32;

    /// Get the fragmentation level of the KV cache (0.0 to 1.0)
    async fn fragmentation_level(&self) -> f64;

    /// Check if the KV cache needs defragmentation
    async fn needs_defragmentation(&self) -> bool;
}
