// =================================================================================================
// File: prisma_ai/src/llm/interface/memory_usage.rs
// =================================================================================================
// Purpose: Defines the MemoryUsage trait for tracking LLM model memory consumption.
// This trait provides methods to access memory-related information such as total memory usage,
// tensor memory, and KV cache memory.
//
// Integration:
// - Internal Dependencies:
//   - interface/mod.rs: Re-exports this trait
//   - interface/memory.rs: Related to memory management but focuses on long-term memory
//
// - External Dependencies:
//   - None
//
// Platform Considerations:
// - This module is platform-independent
// =================================================================================================

use async_trait::async_trait;
use crate::err::PrismaResult;

/// Trait for accessing memory usage information of an LLM model
#[async_trait]
pub trait MemoryUsage: Send + Sync {
    /// Get the total memory usage in bytes
    async fn total_bytes(&self) -> u64;

    /// Get the memory used by model weights in bytes
    async fn weights_bytes(&self) -> u64;

    /// Get the memory used by tensors in bytes
    async fn tensor_bytes(&self) -> u64;

    /// Get the memory used by the KV cache in bytes
    async fn kv_cache_bytes(&self) -> u64;

    /// Get the memory used by scratch buffers in bytes
    async fn scratch_bytes(&self) -> u64;

    /// Get the memory used by other components in bytes
    async fn other_bytes(&self) -> u64;

    /// Get the peak memory usage in bytes
    async fn peak_bytes(&self) -> u64;

    /// Get the memory usage as a percentage of available system memory
    async fn usage_percentage(&self) -> f64;

    /// Check if the model is using GPU memory
    async fn is_using_gpu(&self) -> bool;

    /// Get the GPU memory usage in bytes (if applicable)
    async fn gpu_bytes(&self) -> u64;
}
