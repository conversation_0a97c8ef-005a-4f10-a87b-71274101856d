use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tokio::time::sleep;
use tracing::{debug, error, instrument};

use crate::err::{PrismaResult, GenericError};
use crate::err::types::generics::EngineOperationError;
use crate::llm::interface::Token;
use crate::llm::interface::streaming::{TokenReceiver, TokenSender};
use crate::telemetry::metrics::llm::{
    record_token_throughput,
    record_token_latency,
    record_buffer_usage
};

/// Configuration for streaming performance optimization
#[derive(Debug, Clone)]
pub struct StreamingPerfConfig {
    /// Maximum tokens per second to emit
    pub max_tokens_per_second: Option<u32>,
    /// Batch size for token emission (0 = no batching)
    pub batch_size: usize,
    /// Artificial delay between tokens in milliseconds (0 = no delay)
    pub token_delay_ms: u64,
    /// Buffer size for the token channel
    pub buffer_size: usize,
    /// Whether to collect metrics
    pub collect_metrics: bool,
}

impl Default for StreamingPerfConfig {
    fn default() -> Self {
        Self {
            max_tokens_per_second: None,
            batch_size: 0,
            token_delay_ms: 0,
            buffer_size: 100,
            collect_metrics: true,
        }
    }
}

/// Streaming performance optimizer
pub struct StreamingPerformanceOptimizer {
    config: StreamingPerfConfig,
    start_time: Instant,
    token_count: usize,
    last_metrics_time: Instant,
    model_id: String,
}

impl StreamingPerformanceOptimizer {
    /// Create a new streaming performance optimizer
    pub fn new(config: StreamingPerfConfig, model_id: String) -> Self {
        Self {
            config,
            start_time: Instant::now(),
            token_count: 0,
            last_metrics_time: Instant::now(),
            model_id,
        }
    }

    /// Create a new streaming performance optimizer with default configuration
    pub fn default() -> Self {
        Self::new(StreamingPerfConfig::default(), "default".to_string())
    }

    /// Create an optimized token channel
    pub fn create_optimized_channel(&self) -> (TokenSender, TokenReceiver) {
        let (sender, receiver) = mpsc::channel(self.config.buffer_size);

        // Record buffer size metric
        if self.config.collect_metrics {
            record_buffer_usage(&self.model_id, "allocated", self.config.buffer_size as f64);
        }

        (sender, receiver)
    }

    /// Process a token through the optimizer
    #[instrument(skip(self, token, text, sender), fields(token_id = token))]
    pub async fn process_token(
        &mut self,
        token: Token,
        text: &str,
        sender: &mpsc::Sender<(Token, String)>
    ) -> PrismaResult<()> {
        // Apply token delay if configured
        if self.config.token_delay_ms > 0 {
            sleep(Duration::from_millis(self.config.token_delay_ms)).await;
        }

        // Apply rate limiting if configured
        if let Some(max_tps) = self.config.max_tokens_per_second {
            let elapsed = self.start_time.elapsed();
            let expected_time = Duration::from_secs_f64(self.token_count as f64 / max_tps as f64);

            if elapsed < expected_time {
                let sleep_time = expected_time - elapsed;
                debug!("Rate limiting: sleeping for {:?}", sleep_time);
                sleep(sleep_time).await;
            }
        }

        // Send the token
        let start = Instant::now();
        if let Err(e) = sender.send((token, text.to_string())).await {
            error!("Failed to send token through channel: {}", e);
            return Err(GenericError::new(EngineOperationError::new(
                format!("Failed to send token through channel: {}", e)
            )));
        }

        // Update metrics
        self.token_count += 1;

        if self.config.collect_metrics {
            // Record latency
            let latency = start.elapsed().as_secs_f64();
            record_token_latency(&self.model_id, latency);

            // Update throughput metrics every second
            if self.last_metrics_time.elapsed() >= Duration::from_secs(1) {
                let elapsed = self.start_time.elapsed().as_secs_f64();
                if elapsed > 0.0 {
                    let throughput = self.token_count as f64 / elapsed;
                    record_token_throughput(&self.model_id, throughput);

                    // Update buffer usage metric
                    let capacity = self.config.buffer_size;
                    let usage = capacity.saturating_sub(sender.capacity());
                    record_buffer_usage(&self.model_id, "used", usage as f64);
                }
                self.last_metrics_time = Instant::now();
            }
        }

        Ok(())
    }

    /// Process a batch of tokens
    pub async fn process_batch(
        &mut self,
        tokens: Vec<(Token, String)>,
        sender: &mpsc::Sender<(Token, String)>
    ) -> PrismaResult<()> {
        if self.config.batch_size == 0 || tokens.len() <= 1 {
            // No batching, process tokens individually
            for (token, text) in tokens {
                self.process_token(token, &text, sender).await?;
            }
            return Ok(());
        }

        // Process in batches
        for chunk in tokens.chunks(self.config.batch_size) {
            let start = Instant::now();

            // Send all tokens in the batch
            for (token, text) in chunk {
                if let Err(e) = sender.send((*token, text.clone())).await {
                    error!("Failed to send token in batch: {}", e);
                    return Err(GenericError::new(EngineOperationError::new(
                        format!("Failed to send token in batch: {}", e)
                    )));
                }
                self.token_count += 1;
            }

            // Apply batch delay if configured
            if self.config.token_delay_ms > 0 {
                sleep(Duration::from_millis(self.config.token_delay_ms)).await;
            }

            // Update metrics
            if self.config.collect_metrics {
                let latency = start.elapsed().as_secs_f64() / chunk.len() as f64;
                record_token_latency(&self.model_id, latency);

                if self.last_metrics_time.elapsed() >= Duration::from_secs(1) {
                    let elapsed = self.start_time.elapsed().as_secs_f64();
                    if elapsed > 0.0 {
                        let throughput = self.token_count as f64 / elapsed;
                        record_token_throughput(&self.model_id, throughput);
                    }
                    self.last_metrics_time = Instant::now();
                }
            }
        }

        Ok(())
    }

    /// Reset the optimizer state
    pub fn reset(&mut self) {
        self.start_time = Instant::now();
        self.token_count = 0;
        self.last_metrics_time = Instant::now();
    }
}
