// =================================================================================================
// File: /prisma_ai/src/llm/implementation/embedding.rs
// =================================================================================================
// Purpose: Implements the EmbeddingServiceImpl wrapper for generating embeddings from text.
// This file provides a concrete implementation of the EmbeddingGenerator trait.
// =================================================================================================
// Internal Dependencies:
// - service.rs: Uses LlmServiceImpl for embedding generation
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - std::sync::Arc: For shared ownership
// - crate::err: For error handling
// - crate::llm::interface::embedding: For EmbeddingGenerator trait
// =================================================================================================
// Module Interactions:
// - Used by PrismaEngine to create embedding services
// - Implements EmbeddingGenerator trait for embedding generation
// - Used by TaskFactory to create embedding tasks
// =================================================================================================

use std::sync::Arc;
use async_trait::async_trait;
use crate::err::PrismaResult;
use crate::llm::interface::embedding::EmbeddingGenerator;
use crate::prisma::prisma_engine::tcl::embedding_task::LlmEmbeddingService;
use crate::prisma::prisma_engine::tcl::llm_task::LlmService;

/// A wrapper around LlmService that implements the EmbeddingGenerator trait.
/// This allows us to use the same LLM service for both text generation and embeddings.
pub struct EmbeddingServiceImpl {
    llm_service: Arc<dyn LlmService + Send + Sync>,
}

// Manual Debug implementation for EmbeddingServiceImpl
impl std::fmt::Debug for EmbeddingServiceImpl {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("EmbeddingServiceImpl")
            .field("llm_service", &"Arc<dyn LlmService + Send + Sync>")
            .finish()
    }
}

impl EmbeddingServiceImpl {
    /// Creates a new EmbeddingServiceImpl with the given LlmService.
    pub fn new(llm_service: Arc<dyn LlmService + Send + Sync>) -> Self {
        Self { llm_service }
    }
}

#[async_trait]
impl EmbeddingGenerator for EmbeddingServiceImpl {
    /// Generates embeddings for a batch of input texts by delegating to the underlying LlmService.
    async fn generate_embeddings(&self, texts: &[String]) -> PrismaResult<Vec<Vec<f32>>> {
        // Since LlmService now includes EmbeddingGenerator, we can directly call its methods
        self.llm_service.generate_embeddings(texts).await
    }

    /// Returns the embedding dimension size for the model used by this generator.
    fn embedding_size(&self) -> usize {
        self.llm_service.embedding_size()
    }
}

// Implement the LlmEmbeddingService trait for EmbeddingServiceImpl
#[async_trait]
impl LlmEmbeddingService for EmbeddingServiceImpl {}
