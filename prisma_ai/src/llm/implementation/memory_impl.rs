use std::sync::Arc;
use async_trait::async_trait;
use tracing::info;

use crate::err::{PrismaResult, GenericError};
use crate::err::DomainError;
use crate::llm::interface::{Memory, LongTermMemory, EmbeddingGenerator};
use crate::storage::traits::LongTermMemoryStore;

/// Implementation of the LongTermMemory trait
pub struct LtmServiceImpl {
    /// Database connection for storing and retrieving memories
    db: Arc<dyn LongTermMemoryStore>,
    /// Embedding generator for creating embeddings from text
    embedding_generator: Arc<dyn EmbeddingGenerator>,
}

impl LtmServiceImpl {
    /// Create a new LTM service
    pub fn new(
        db_connection: Arc<dyn LongTermMemoryStore>,
        embedding_generator: Arc<dyn EmbeddingGenerator>,
    ) -> Self {
        Self {
            db: db_connection,
            embedding_generator,
        }
    }
}

#[async_trait]
impl LongTermMemory for LtmServiceImpl {
    async fn store_memory(
        &self,
        agent_id: &str,
        content: &str,
        category: &str,
        importance: f32,
    ) -> PrismaResult<String> {
        info!("Storing memory for agent {}: {} ({})", agent_id, content, category);

        // Generate embedding for the content
        let embeddings = self.embedding_generator.generate_embeddings(&[content.to_string()]).await
            .map_err(|e| GenericError::new(DomainError::LLM(format!("Failed to generate embedding: {}", e))))?;

        if embeddings.is_empty() {
            return Err(GenericError::new(DomainError::LLM("Failed to generate embedding: empty result".to_string())));
        }

        // Store the memory with the embedding
        let memory_id = self.db.store_memory(agent_id, content, category, importance, &embeddings[0]).await?;

        info!("Memory stored with ID: {}", memory_id);
        Ok(memory_id)
    }

    async fn get_memory(&self, memory_id: &str) -> PrismaResult<Memory> {
        info!("Retrieving memory with ID: {}", memory_id);
        self.db.get_memory(memory_id).await
    }

    async fn update_memory(
        &self,
        memory_id: &str,
        content: Option<&str>,
        category: Option<&str>,
        importance: Option<f32>,
    ) -> PrismaResult<()> {
        info!("Updating memory with ID: {}", memory_id);

        // If content is updated, we need to regenerate the embedding
        let mut embedding = None;

        if let Some(content_str) = content {
            info!("Regenerating embedding for updated content");
            let embeddings = self.embedding_generator.generate_embeddings(&[content_str.to_string()]).await
                .map_err(|e| GenericError::new(DomainError::LLM(format!("Failed to generate embedding: {}", e))))?;

            if embeddings.is_empty() {
                return Err(GenericError::new(DomainError::LLM("Failed to generate embedding: empty result".to_string())));
            }

            // Take ownership of the embedding vector
            embedding = Some(embeddings[0].clone());
        }

        // Update the memory
        self.db.update_memory(memory_id, content, category, importance, embedding).await
    }

    async fn delete_memory(&self, memory_id: &str) -> PrismaResult<()> {
        info!("Deleting memory with ID: {}", memory_id);
        self.db.delete_memory(memory_id).await
    }

    async fn get_all_memories(&self, agent_id: &str) -> PrismaResult<Vec<Memory>> {
        info!("Retrieving all memories for agent: {}", agent_id);
        self.db.get_all_memories(agent_id).await
    }

    async fn get_memories_by_category(&self, agent_id: &str, category: &str) -> PrismaResult<Vec<Memory>> {
        info!("Retrieving memories for agent {} with category: {}", agent_id, category);
        self.db.get_memories_by_category(agent_id, category).await
    }

    async fn get_memories_by_importance(&self, agent_id: &str, min_importance: f32) -> PrismaResult<Vec<Memory>> {
        info!("Retrieving memories for agent {} with importance >= {}", agent_id, min_importance);
        self.db.get_memories_by_importance(agent_id, min_importance).await
    }

    async fn get_recent_memories(&self, agent_id: &str, limit: usize) -> PrismaResult<Vec<Memory>> {
        info!("Retrieving {} most recent memories for agent: {}", limit, agent_id);
        self.db.get_recent_memories(agent_id, limit).await
    }

    async fn get_relevant_memories(&self, agent_id: &str, query: &str, limit: usize) -> PrismaResult<Vec<Memory>> {
        info!("Finding memories relevant to query for agent {}: {}", agent_id, query);

        // Generate embedding for the query
        let embeddings = self.embedding_generator.generate_embeddings(&[query.to_string()]).await
            .map_err(|e| GenericError::new(DomainError::LLM(format!("Failed to generate embedding: {}", e))))?;

        if embeddings.is_empty() {
            return Err(GenericError::new(DomainError::LLM("Failed to generate embedding: empty result".to_string())));
        }

        // Find relevant memories using the embedding
        self.db.get_relevant_memories(agent_id, &embeddings[0], limit).await
    }

    fn format_memories_for_prompt(&self, memories: &[Memory]) -> String {
        self.db.format_memories_for_prompt(memories)
    }

    async fn cleanup_old_memories(&self, agent_id: &str, max_age_days: u32, min_importance: f32) -> PrismaResult<usize> {
        info!("Cleaning up old memories for agent {}: max age {} days, min importance {}",
              agent_id, max_age_days, min_importance);
        self.db.cleanup_old_memories(agent_id, max_age_days, min_importance).await
    }

    async fn remove_agent_memories(&self, agent_id: &str) -> PrismaResult<usize> {
        info!("Removing all memories for agent {}", agent_id);

        // Get all memories for the agent
        let memories = self.get_all_memories(agent_id).await?;

        // Delete each memory
        let mut removed_count = 0;
        for memory in memories {
            if let Ok(_) = self.delete_memory(&memory.id).await {
                removed_count += 1;
            }
        }

        info!("Removed {} memories for agent {}", removed_count, agent_id);
        Ok(removed_count)
    }
}
