use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{info, warn, error};
use crate::llm::implementation::properties_impl::Model;
use crate::llm::implementation::context_impl::Context;
use crate::{<PERSON><PERSON><PERSON><PERSON>, Token, <PERSON>text<PERSON><PERSON><PERSON>, Token<PERSON>tt<PERSON>, Batch as CrateBatch, SeqId, Pos};
use crate::err::{PrismaResult, GenericError};
use crate::err::types::generics::EngineOperationError;
use crate::ffi::{self, llama_sampler_accept}; // Import ffi module and specific function
use crate::llm::interface::properties::ModelProperties;
use crate::llm::interface::{
    VocabManager, BatchOperations, Inference, Sampling, EmbeddingGenerator, StreamingOutput
};
use crate::llm::interface::vocab::VocabType;
use crate::llm::interface::batch::Batch as InterfaceBatch;
use crate::llm::interface::sampling::SamplingParams;
use crate::llm::interface::sampling::SamplerChainParams;
use crate::llm::implementation::sampling_impl::{
    sampler_init_top_k, sampler_init_top_p, sampler_init_min_p, sampler_init_typical,
    sampler_init_temp, sampler_init_penalties, sampler_init_mirostat, sampler_init_mirostat_v2,
    sampler_init_greedy, sampler_init_dist, SamplerChain, SamplerImpl
};
use crate::llm::interface::properties::Properties; // Needed for n_vocab
use crate::llm::interface::ContextManager; // Needed for as_ptr on Context
use crate::llm::interface::cache::KvCache; // Needed for EmbeddingGenerator
use crate::llm::interface::embedding::EmbeddingGenerator as EmbeddingGeneratorTrait; // Alias trait
use crate::llm::implementation::streaming_impl::StreamingState;

// Concrete implementation of the LLM service traits
#[derive(Debug)]
pub struct LlmServiceImpl {
    model: Arc<Model>,
    context: Arc<Mutex<Context>>,
    streaming_state: Arc<Mutex<StreamingState>>,
}

impl LlmServiceImpl {
    pub fn new(model_path: &str, model_params: Option<ModelParams>, context_params: Option<ContextParams>) -> PrismaResult<Self> {
        info!("Loading LLM model from path: {}", model_path);
        let mparams = model_params.unwrap_or_default();
        let model = Model::load(model_path, mparams)
             .map_err(|e| GenericError::new(EngineOperationError::new(format!("Failed to load model: {}", e))))?;
        info!("Model loaded successfully. Creating context...");
        let cparams = context_params.unwrap_or_default();
        let context_instance = Context::new_with_model(&model, cparams)
             .map_err(|e| GenericError::new(EngineOperationError::new(format!("Failed to create context: {}", e))))?;
        info!("Context created successfully.");
        Ok(Self {
            model: Arc::new(model),
            context: Arc::new(Mutex::new(context_instance)),
            streaming_state: Arc::new(Mutex::new(StreamingState::new())),
        })
    }

    // Helper for vocab pointer
    fn get_vocab_ptr(&self) -> *mut ffi::llama_vocab {
        unsafe { ffi::llama_model_get_vocab(self.model.as_ptr()) as *mut ffi::llama_vocab }
    }
}

// Implement VocabManager for LlmServiceImpl using self.model
impl VocabManager for LlmServiceImpl {
    fn as_ptr(&self) -> *mut ffi::llama_model { self.model.as_ptr() }
    fn tokenize(&self, text: &str, tokens: &mut [Token], add_special: bool, parse_special: bool) -> Result<usize, i32> {
        let text_bytes = text.as_bytes();
        let result = unsafe { ffi::llama_tokenize(self.get_vocab_ptr(), text_bytes.as_ptr() as *const i8, text_bytes.len() as i32, tokens.as_mut_ptr(), tokens.len() as i32, add_special, parse_special) };
        if result >= 0 { Ok(result as usize) } else { Err(result) }
    }
    fn token_to_piece(&self, token: Token, buf: &mut [u8], lstrip: i32, special: bool) -> i32 { unsafe { ffi::llama_token_to_piece(self.get_vocab_ptr(), token, buf.as_mut_ptr() as *mut i8, buf.len() as i32, lstrip, special) } }
    fn detokenize(&self, tokens: &[Token], text: &mut [u8], remove_special: bool, unparse_special: bool) -> Result<usize, i32> {
        let result = unsafe { ffi::llama_detokenize(self.get_vocab_ptr(), tokens.as_ptr(), tokens.len() as i32, text.as_mut_ptr() as *mut i8, text.len() as i32, remove_special, unparse_special) };
        if result >= 0 { Ok(result as usize) } else { Err(result) }
    }
    fn bos(&self) -> Token { unsafe { ffi::llama_vocab_bos(self.get_vocab_ptr()) } }
    fn eos(&self) -> Token { unsafe { ffi::llama_vocab_eos(self.get_vocab_ptr()) } }
    fn eot(&self) -> Token { unsafe { ffi::llama_vocab_eot(self.get_vocab_ptr()) } }
    fn sep(&self) -> Token { unsafe { ffi::llama_vocab_sep(self.get_vocab_ptr()) } }
    fn nl(&self) -> Token { unsafe { ffi::llama_vocab_nl(self.get_vocab_ptr()) } }
    fn pad(&self) -> Token { unsafe { ffi::llama_vocab_pad(self.get_vocab_ptr()) } }
    fn n_vocab(&self) -> i32 { unsafe { ffi::llama_vocab_n_tokens(self.get_vocab_ptr()) } }
    fn vocab_type(&self) -> VocabType { unsafe { ffi::llama_vocab_type(self.get_vocab_ptr()).into() } }
    fn n_tokens(&self) -> i32 { self.n_vocab() }
    fn get_text(&self, token: Token) -> Option<String> { unsafe { let ptr = ffi::llama_vocab_get_text(self.get_vocab_ptr(), token); if ptr.is_null() { None } else { Some(std::ffi::CStr::from_ptr(ptr).to_string_lossy().into_owned()) } } }
    fn get_score(&self, token: Token) -> f32 { unsafe { ffi::llama_vocab_get_score(self.get_vocab_ptr(), token) } }
    fn get_attr(&self, token: Token) -> TokenAttr { unsafe { ffi::llama_vocab_get_attr(self.get_vocab_ptr(), token).into() } }
    fn is_eog(&self, token: Token) -> bool { unsafe { ffi::llama_vocab_is_eog(self.get_vocab_ptr(), token) } }
    fn is_control(&self, token: Token) -> bool { unsafe { ffi::llama_vocab_is_control(self.get_vocab_ptr(), token) } }
    fn get_add_bos(&self) -> bool { unsafe { ffi::llama_vocab_get_add_bos(self.get_vocab_ptr()) } }
    fn get_add_eos(&self) -> bool { unsafe { ffi::llama_vocab_get_add_eos(self.get_vocab_ptr()) } }
    fn fim_pre(&self) -> Token { unsafe { ffi::llama_vocab_fim_pre(self.get_vocab_ptr()) } }
    fn fim_suf(&self) -> Token { unsafe { ffi::llama_vocab_fim_suf(self.get_vocab_ptr()) } }
    fn fim_mid(&self) -> Token { unsafe { ffi::llama_vocab_fim_mid(self.get_vocab_ptr()) } }
    fn fim_pad(&self) -> Token { unsafe { ffi::llama_vocab_fim_pad(self.get_vocab_ptr()) } }
    fn fim_rep(&self) -> Token { unsafe { ffi::llama_vocab_fim_rep(self.get_vocab_ptr()) } }
    fn fim_sep(&self) -> Token { unsafe { ffi::llama_vocab_fim_sep(self.get_vocab_ptr()) } }
}

#[async_trait::async_trait]
impl BatchOperations for LlmServiceImpl {
    fn batch_get_one(&self, tokens: &mut [Token]) -> InterfaceBatch {
        let context_guard = self.context.blocking_lock();
        // Assuming Context implements BatchOperations (via batch_impl.rs)
        context_guard.batch_get_one(tokens)
    }
    async fn encode(&self, batch: InterfaceBatch) -> PrismaResult<()> {
        let mut context_guard = self.context.lock().await;
        // Assuming Context implements BatchOperations
        context_guard.encode(batch).await
    }
    async fn decode(&self, batch: InterfaceBatch) -> PrismaResult<i32> {
        let mut context_guard = self.context.lock().await;
        // Assuming Context implements BatchOperations
        context_guard.decode(batch).await
    }
}

impl Inference for LlmServiceImpl {
    fn get_logits(&self) -> Option<Vec<f32>> {
        let context_guard = self.context.blocking_lock();
        // Assuming Context implements Inference (via inference_impl.rs)
        context_guard.get_logits()
    }
    fn get_logits_ith(&self, i: i32) -> Option<Vec<f32>> {
        let context_guard = self.context.blocking_lock();
        // Assuming Context implements Inference
        context_guard.get_logits_ith(i)
    }
}

#[async_trait::async_trait]
impl Sampling for LlmServiceImpl {
     async fn sample_next_token(&self, logits: &[f32], params: &SamplingParams) -> PrismaResult<Token> {
         let context_guard = self.context.lock().await;
         let n_vocab = Properties::n_vocab(&*context_guard); // Qualified call
         if logits.len() != n_vocab as usize {
             return Err(GenericError::new(EngineOperationError::new(format!("Logit size mismatch: expected {}, got {}", n_vocab, logits.len()))));
         }
         let mut sampler_chain = SamplerChain::init(SamplerChainParams::default())
             .ok_or_else(|| GenericError::new(EngineOperationError::new("Failed to initialize sampler chain".to_string())))?;
         if let Some(pen) = &params.penalties { if let Some(sampler) = sampler_init_penalties(pen.repeat_last_n.unwrap_or(64), pen.repeat_penalty.unwrap_or(1.1), pen.frequency_penalty.unwrap_or(0.0), pen.presence_penalty.unwrap_or(0.0)) { sampler_chain.add(sampler); } else { warn!("Failed to initialize penalties sampler"); } }
         if let Some(k) = params.top_k { if k > 0 { if let Some(sampler) = sampler_init_top_k(k) { sampler_chain.add(sampler); } else { warn!("Failed to initialize top_k sampler"); } } }
         if let Some(p) = params.top_p { if p < 1.0 { if let Some(sampler) = sampler_init_top_p(p, params.min_keep.unwrap_or(1)) { sampler_chain.add(sampler); } else { warn!("Failed to initialize top_p sampler"); } } }
         if let Some(p) = params.min_p { if p > 0.0 { if let Some(sampler) = sampler_init_min_p(p, params.min_keep.unwrap_or(1)) { sampler_chain.add(sampler); } else { warn!("Failed to initialize min_p sampler"); } } }
         if let Some(p) = params.typical_p { if p < 1.0 { if let Some(sampler) = sampler_init_typical(p, params.min_keep.unwrap_or(1)) { sampler_chain.add(sampler); } else { warn!("Failed to initialize typical_p sampler"); } } }
         if let Some(temp) = params.temp { if temp >= 0.0 { if let Some(sampler) = sampler_init_temp(temp) { sampler_chain.add(sampler); } else { warn!("Failed to initialize temperature sampler"); } } }
         match params.mirostat {
             Some(1) => { if let Some(sampler) = sampler_init_mirostat(n_vocab, params.mirostat_seed.unwrap_or(0), params.mirostat_tau.unwrap_or(5.0), params.mirostat_eta.unwrap_or(0.1), params.mirostat_m.unwrap_or(100)) { sampler_chain.add(sampler); } else { warn!("Failed to initialize Mirostat v1 sampler"); } }
             Some(2) => { if let Some(sampler) = sampler_init_mirostat_v2(params.mirostat_seed.unwrap_or(0), params.mirostat_tau.unwrap_or(5.0), params.mirostat_eta.unwrap_or(0.1)) { sampler_chain.add(sampler); } else { warn!("Failed to initialize Mirostat v2 sampler"); } }
             _ => {}
         }
         if params.mirostat.is_none() || params.mirostat == Some(0) {
             if params.temp.unwrap_or(1.0) == 0.0 { if let Some(sampler) = sampler_init_greedy() { sampler_chain.add(sampler); } else { return Err(GenericError::new(EngineOperationError::new("Failed to initialize greedy sampler".to_string()))); } }
             else { if let Some(sampler) = sampler_init_dist(params.seed.unwrap_or(0)) { sampler_chain.add(sampler); } else { return Err(GenericError::new(EngineOperationError::new("Failed to initialize distribution sampler".to_string()))); } }
         }
         let mut candidates_vec: Vec<_> = logits.iter().enumerate().map(|(i, &logit)| ffi::llama_token_data { id: i as Token, logit, p: 0.0 }).collect();
         let mut candidates_array = ffi::llama_token_data_array { data: candidates_vec.as_mut_ptr(), size: candidates_vec.len(), sorted: false, selected: -1 };
         let token = unsafe {
             // Corrected FFI call signature: sampler_ptr, ctx_ptr, index
             ffi::llama_sampler_sample(sampler_chain.as_ptr(), ContextManager::as_ptr(&*context_guard), -1) // Qualified call
         };
         unsafe {
             ffi::llama_sampler_accept(sampler_chain.as_ptr(), token); // Use ffi:: prefix
         }
         Ok(token)
     }
 }

#[async_trait::async_trait]
impl EmbeddingGeneratorTrait for LlmServiceImpl { // Use aliased trait name
    // Delegate to Context implementation
    async fn generate_embeddings(&self, texts: &[String]) -> PrismaResult<Vec<Vec<f32>>> { // Changed to &self to match trait
        // Access to context for this operation
        let context_guard = self.context.lock().await;
        // Assuming Context implements EmbeddingGenerator (which it should via context_impl.rs)
        context_guard.generate_embeddings(texts).await
    }

    fn embedding_size(&self) -> usize {
        // Can use blocking lock as it's a simple read
        let context_guard = self.context.blocking_lock();
        // Assuming Context implements EmbeddingGenerator
        context_guard.embedding_size()
    }
}

// Marker trait implementations
#[async_trait::async_trait]
impl crate::prisma::prisma_engine::tcl::llm_task::LlmService for LlmServiceImpl {}

#[async_trait::async_trait]
impl crate::prisma::prisma_engine::tcl::embedding_task::LlmEmbeddingService for LlmServiceImpl {}

/// Implementation of StreamingStateAccess for LlmServiceImpl
impl crate::llm::implementation::streaming_impl::StreamingStateAccess for LlmServiceImpl {
    fn get_streaming_state(&self) -> Arc<Mutex<StreamingState>> {
        // Now we properly use the Arc<Mutex<StreamingState>> field
        self.streaming_state.clone()
    }
}
