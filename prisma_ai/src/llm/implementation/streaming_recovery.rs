use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tracing::{error, info};

use crate::err::{PrismaResult, GenericError};
use crate::err::types::generics::EngineOperationError;
use crate::err::error_handler::{CircuitBreakerConfig, RetryConfig, with_retry, with_circuit_breaker};
use crate::llm::interface::Token;
use crate::telemetry::metrics::llm::{
    record_streaming_error,
    record_streaming_recovery
};

/// Configuration for streaming error recovery
#[derive(Debug, Clone)]
pub struct StreamingRecoveryConfig {
    /// Maximum number of retry attempts
    pub max_retries: usize,
    /// Base delay for exponential backoff (in milliseconds)
    pub base_delay_ms: u64,
    /// Maximum delay for exponential backoff (in milliseconds)
    pub max_delay_ms: u64,
    /// Circuit breaker failure threshold
    pub circuit_breaker_threshold: usize,
    /// Circuit breaker reset timeout (in milliseconds)
    pub circuit_breaker_reset_ms: u64,
    /// Whether to collect metrics
    pub collect_metrics: bool,
}

impl Default for StreamingRecoveryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            base_delay_ms: 100,
            max_delay_ms: 5000,
            circuit_breaker_threshold: 5,
            circuit_breaker_reset_ms: 30000,
            collect_metrics: true,
        }
    }
}

/// Streaming error recovery manager
pub struct StreamingRecoveryManager {
    config: StreamingRecoveryConfig,
    retry_config: RetryConfig,
    circuit_breaker_config: CircuitBreakerConfig,
    error_count: u32,
    recovery_count: u32,
    last_error_time: Option<Instant>,
    buffer: Vec<(Token, String)>,
    model_id: String,
}

impl StreamingRecoveryManager {
    /// Create a new streaming recovery manager
    pub fn new(config: StreamingRecoveryConfig, model_id: String) -> Self {
        let retry_config = RetryConfig {
            max_attempts: config.max_retries as u32,
            initial_delay: Duration::from_millis(config.base_delay_ms),
            max_delay: Duration::from_millis(config.max_delay_ms),
            multiplier: 2.0,
        };

        let circuit_breaker_config = CircuitBreakerConfig {
            failure_threshold: config.circuit_breaker_threshold as usize,
            reset_timeout: Duration::from_millis(config.circuit_breaker_reset_ms),
            half_open_timeout: Duration::from_millis(config.base_delay_ms),
        };

        Self {
            config,
            retry_config,
            circuit_breaker_config,
            error_count: 0,
            recovery_count: 0,
            last_error_time: None,
            buffer: Vec::new(),
            model_id,
        }
    }

    /// Create a new streaming recovery manager with default configuration
    pub fn default() -> Self {
        Self::new(StreamingRecoveryConfig::default(), "default".to_string())
    }

    /// Add a token to the recovery buffer
    pub fn buffer_token(&mut self, token: Token, text: String) {
        self.buffer.push((token, text));
    }

    /// Clear the recovery buffer
    pub fn clear_buffer(&mut self) {
        self.buffer.clear();
    }

    /// Get the buffered tokens
    pub fn get_buffer(&self) -> &[(Token, String)] {
        &self.buffer
    }

    /// Record an error and check if recovery is needed
    pub fn record_error(&mut self) -> bool {
        self.error_count += 1;
        self.last_error_time = Some(Instant::now());

        if self.config.collect_metrics {
            record_streaming_error(&self.model_id);
        }

        // Check if we've exceeded the circuit breaker threshold
        self.error_count >= self.circuit_breaker_config.failure_threshold as u32
    }

    /// Record a successful recovery
    pub fn record_recovery(&mut self) {
        self.recovery_count += 1;

        if self.config.collect_metrics {
            record_streaming_recovery(&self.model_id, 0.0);
        }
    }

    /// Reset the error count
    pub fn reset_errors(&mut self) {
        self.error_count = 0;
    }

    /// Execute a streaming operation with retry logic
    pub async fn with_retry<F, T>(&self, operation: F, operation_name: &str) -> PrismaResult<T>
    where
        F: Fn() -> PrismaResult<T> + Clone,
    {
        let start = Instant::now();
        let result = with_retry(operation, operation_name, Some(self.retry_config.clone())).await;

        if self.config.collect_metrics {
            record_streaming_recovery(&self.model_id, start.elapsed().as_secs_f64());
        }

        result
    }

    /// Execute a streaming operation with circuit breaker pattern
    pub async fn with_circuit_breaker<F, T>(&self, operation: F, service_name: &str) -> PrismaResult<T>
    where
        F: FnOnce() -> PrismaResult<T>,
    {
        let start = Instant::now();
        let result = with_circuit_breaker(operation, service_name, Some(self.circuit_breaker_config.clone())).await;

        if self.config.collect_metrics {
            record_streaming_recovery(&self.model_id, start.elapsed().as_secs_f64());
        }

        result
    }

    /// Attempt to recover a streaming session by replaying buffered tokens
    pub async fn recover_session(
        &mut self,
        sender: &mpsc::Sender<(Token, String)>
    ) -> PrismaResult<()> {
        info!("Attempting to recover streaming session with {} buffered tokens", self.buffer.len());

        let start = Instant::now();

        // Replay buffered tokens
        for (token, text) in self.buffer.iter() {
            if let Err(e) = sender.send((*token, text.clone())).await {
                error!("Failed to replay token during recovery: {}", e);
                return Err(GenericError::new(EngineOperationError::new(
                    format!("Failed to replay token during recovery: {}", e)
                )));
            }
        }

        // Record recovery metrics
        if self.config.collect_metrics {
            record_streaming_recovery(&self.model_id, start.elapsed().as_secs_f64());
        }

        self.record_recovery();
        info!("Successfully recovered streaming session");

        Ok(())
    }
}
