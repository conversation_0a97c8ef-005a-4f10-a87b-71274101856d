use std::sync::Arc;
use tokio::sync::mpsc;
use uuid::Uuid;
use tracing::{debug, error, info, warn};

use crate::llm::interface::Token;
use crate::llm::interface::streaming::{Token<PERSON>allback, TokenR<PERSON>eiver};
use crate::err::{PrismaResult, GenericError};
use crate::err::types::generics::EngineOperationError;

use super::types::{StreamingPublisher, PublisherResult};

/// Adapter for connecting LLM streaming output to a publisher
pub struct StreamingAdapter {
    /// The publisher to send streaming tokens to
    publisher: Arc<dyn StreamingPublisher>,
    /// The message ID for the streaming session
    message_id: String,
    /// The agent ID for the streaming session
    agent_id: String,
    /// The user ID for the streaming session
    user_id: Option<String>,
    /// Whether the streaming session has started
    started: bool,
    /// The accumulated content
    content: String,
}

impl StreamingAdapter {
    /// Create a new streaming adapter
    pub fn new(
        publisher: Arc<dyn StreamingPublisher>,
        message_id: String,
        agent_id: String,
        user_id: Option<String>,
    ) -> Self {
        Self {
            publisher,
            message_id,
            agent_id,
            user_id,
            started: false,
            content: String::new(),
        }
    }

    /// Start the streaming session
    pub async fn start(&mut self) -> PublisherResult<()> {
        if !self.started {
            self.started = true;
            self.publisher.start_streaming(
                &self.message_id,
                &self.agent_id,
                self.user_id.as_deref(),
            ).await?;
        }
        Ok(())
    }

    /// Process a token
    pub async fn process_token(&mut self, token: Token, text: &str) -> PublisherResult<()> {
        // Make sure the session has started
        if !self.started {
            self.start().await?;
        }

        // Add the token text to the accumulated content
        self.content.push_str(text);

        // Generate a unique ID for this token
        let token_id = Uuid::new_v4().to_string();

        // Publish the token
        self.publisher.publish_token(
            &token_id,
            &self.message_id,
            &self.agent_id,
            self.user_id.as_deref(),
            text,
        ).await
    }

    /// End the streaming session
    pub async fn end(&mut self) -> PublisherResult<()> {
        if self.started {
            self.publisher.end_streaming(
                &self.message_id,
                &self.agent_id,
                self.user_id.as_deref(),
                &self.content,
            ).await?;
            self.started = false;
        }
        Ok(())
    }

    /// Create a token callback function that sends tokens to the publisher
    pub fn create_token_callback(
        publisher: Arc<dyn StreamingPublisher>,
        message_id: String,
        agent_id: String,
        user_id: Option<String>,
    ) -> Box<dyn Fn(Token, &str) + Send + Sync> {
        // Create a channel for the adapter to communicate with the callback
        let (sender, mut receiver) = mpsc::channel::<(Token, String)>(100);

        // Clone the values for the spawned task
        let publisher_clone = publisher.clone();
        let message_id_clone = message_id.clone();
        let agent_id_clone = agent_id.clone();
        let user_id_clone = user_id.clone();

        // Spawn a task to process tokens
        tokio::spawn(async move {
            let mut adapter = StreamingAdapter::new(
                publisher_clone,
                message_id_clone,
                agent_id_clone,
                user_id_clone,
            );

            // Start the streaming session
            if let Err(e) = adapter.start().await {
                error!("Failed to start streaming session: {}", e);
                return;
            }

            // Process tokens as they arrive
            while let Some((token, text)) = receiver.recv().await {
                if let Err(e) = adapter.process_token(token, &text).await {
                    error!("Failed to process streaming token: {}", e);
                    break;
                }
            }

            // End the streaming session
            if let Err(e) = adapter.end().await {
                error!("Failed to end streaming session: {}", e);
            }
        });

        // Create the callback function
        Box::new(move |token, text| {
            let sender = sender.clone();
            let text_owned = text.to_owned();

            // Send the token to the adapter
            tokio::spawn(async move {
                if let Err(e) = sender.send((token, text_owned)).await {
                    error!("Failed to send token to streaming adapter: {}", e);
                }
            });
        })
    }

    /// Process a token stream from a TokenReceiver
    pub async fn process_token_stream(
        publisher: Arc<dyn StreamingPublisher>,
        message_id: String,
        agent_id: String,
        user_id: Option<String>,
        mut receiver: TokenReceiver,
    ) -> PrismaResult<String> {
        let mut adapter = StreamingAdapter::new(
            publisher,
            message_id,
            agent_id,
            user_id,
        );

        // Start the streaming session
        adapter.start().await.map_err(|e| {
            error!("Failed to start streaming session: {}", e);
            GenericError::new(EngineOperationError::new(format!("Failed to start streaming session: {}", e)))
        })?;

        // Process tokens as they arrive
        while let Some((token, text)) = receiver.recv().await {
            adapter.process_token(token, &text).await.map_err(|e| {
                error!("Failed to process streaming token: {}", e);
                GenericError::new(EngineOperationError::new(format!("Failed to process streaming token: {}", e)))
            })?;
        }

        // End the streaming session
        adapter.end().await.map_err(|e| {
            error!("Failed to end streaming session: {}", e);
            GenericError::new(EngineOperationError::new(format!("Failed to end streaming session: {}", e)))
        })?;

        // Return the accumulated content
        Ok(adapter.content)
    }
}
