use async_trait::async_trait;
use lapin::{
    options::*, types::FieldTable, BasicProperties, Connection, ConnectionProperties, ExchangeKind,
};
use std::sync::Arc;
use tokio::sync::{<PERSON><PERSON><PERSON>, RwLock};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use crate::telemetry::logging;
use super::types::{Publisher, PublisherError, PublisherResult, PublishEvent};

/// Configuration for RabbitMQ publisher
#[derive(Debug, Clone)]
pub struct RabbitMQConfig {
    /// RabbitMQ host
    pub host: String,
    /// RabbitMQ port
    pub port: u16,
    /// RabbitMQ username
    pub username: String,
    /// RabbitMQ password
    pub password: String,
    /// RabbitMQ virtual host
    pub vhost: String,
    /// RabbitMQ connection name
    pub connection_name: String,
    /// RabbitMQ exchange name
    pub exchange_name: String,
    /// RabbitMQ exchange type
    pub exchange_type: ExchangeKind,
    /// Whether the exchange is durable
    pub durable: bool,
    /// Whether the exchange is auto-deleted
    pub auto_delete: bool,
}

impl Default for RabbitMQConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 5672,
            username: "guest".to_string(),
            password: "guest".to_string(),
            vhost: "/".to_string(),
            connection_name: "prisma_publisher".to_string(),
            exchange_name: "prisma.events".to_string(),
            exchange_type: ExchangeKind::Topic,
            durable: true,
            auto_delete: false,
        }
    }
}

/// RabbitMQ publisher for sending data to the UI
pub struct RabbitMQPublisher {
    /// RabbitMQ configuration
    config: RabbitMQConfig,
    /// RabbitMQ connection
    connection: Arc<Mutex<Option<Connection>>>,
}

impl RabbitMQPublisher {
    /// Create a new RabbitMQ publisher
    pub fn new(config: RabbitMQConfig) -> Self {
        Self {
            config,
            connection: Arc::new(Mutex::new(None)),
        }
    }

    /// Create a new RabbitMQ publisher with default configuration
    pub fn default() -> Self {
        Self::new(RabbitMQConfig::default())
    }

    /// Get the RabbitMQ connection string
    fn get_connection_string(&self) -> String {
        format!(
            "amqp://{}:{}@{}:{}/{}",
            self.config.username,
            self.config.password,
            self.config.host,
            self.config.port,
            self.config.vhost.trim_start_matches('/')
        )
    }

    /// Create a routing key for an event
    fn create_routing_key(&self, event: &PublishEvent) -> String {
        let event_type = match event.event_type {
            super::types::PublishEventType::ChatMessage => "chat",
            super::types::PublishEventType::StreamingChatMessage => "chat.streaming",
            super::types::PublishEventType::TaskStatus => "task",
            super::types::PublishEventType::AgentStatus => "agent",
            super::types::PublishEventType::Notification => "notification",
            super::types::PublishEventType::Error => "error",
        };

        // Create routing key in the format: event_type.source[.target]
        if let Some(target) = &event.target {
            format!("{}.{}.{}", event_type, event.source, target)
        } else {
            format!("{}.{}", event_type, event.source)
        }
    }
}

#[async_trait]
impl Publisher for RabbitMQPublisher {
    async fn connect(&self) -> PublisherResult<()> {
        let conn_string = self.get_connection_string();
        info!("Connecting to RabbitMQ at {}", self.config.host);

        // Use tokio-executor-trait for lapin
        let conn_properties = ConnectionProperties::default()
            .with_connection_name(self.config.connection_name.clone().into());

        match Connection::connect(&conn_string, conn_properties).await {
            Ok(conn) => {
                info!("Connected to RabbitMQ");

                // Set up the exchange
                let channel = conn.create_channel().await.map_err(|e| {
                    error!("Failed to create channel: {}", e);
                    PublisherError::Connection(format!("Failed to create channel: {}", e))
                })?;

                channel
                    .exchange_declare(
                        &self.config.exchange_name,
                        self.config.exchange_type.clone(),
                        ExchangeDeclareOptions {
                            durable: self.config.durable,
                            auto_delete: self.config.auto_delete,
                            ..Default::default()
                        },
                        FieldTable::default(),
                    )
                    .await
                    .map_err(|e| {
                        error!("Failed to declare exchange: {}", e);
                        PublisherError::Connection(format!("Failed to declare exchange: {}", e))
                    })?;

                info!("Declared exchange: {}", self.config.exchange_name);

                // Store the connection
                let mut conn_guard = self.connection.lock().await;
                *conn_guard = Some(conn);

                Ok(())
            }
            Err(e) => {
                error!("Failed to connect to RabbitMQ: {}", e);
                Err(PublisherError::Connection(format!(
                    "Failed to connect to RabbitMQ: {}",
                    e
                )))
            }
        }
    }

    async fn disconnect(&self) -> PublisherResult<()> {
        let mut conn_guard = self.connection.lock().await;
        if let Some(conn) = conn_guard.take() {
            info!("Disconnecting from RabbitMQ");
            match conn.close(0, "Disconnecting").await {
                Ok(_) => {
                    info!("Disconnected from RabbitMQ");
                    Ok(())
                }
                Err(e) => {
                    error!("Failed to disconnect from RabbitMQ: {}", e);
                    Err(PublisherError::Connection(format!(
                        "Failed to disconnect from RabbitMQ: {}",
                        e
                    )))
                }
            }
        } else {
            debug!("Already disconnected from RabbitMQ");
            Ok(())
        }
    }

    async fn is_connected(&self) -> bool {
        let conn_guard = self.connection.lock().await;
        conn_guard.is_some()
    }

    async fn publish(&self, event: PublishEvent) -> PublisherResult<()> {
        // Check if connected
        if !self.is_connected().await {
            warn!("Not connected to RabbitMQ, attempting to connect");
            self.connect().await?;
        }

        // Get the connection
        let conn_guard = self.connection.lock().await;
        let conn = conn_guard.as_ref().ok_or_else(|| {
            PublisherError::Connection("Not connected to RabbitMQ".to_string())
        })?;

        // Create a channel
        let channel = conn.create_channel().await.map_err(|e| {
            error!("Failed to create channel: {}", e);
            PublisherError::Publishing(format!("Failed to create channel: {}", e))
        })?;

        // Serialize the event
        let payload = serde_json::to_vec(&event).map_err(|e| {
            error!("Failed to serialize event: {}", e);
            PublisherError::Serialization(format!("Failed to serialize event: {}", e))
        })?;

        // Create routing key
        let routing_key = self.create_routing_key(&event);

        // Publish the event
        channel
            .basic_publish(
                &self.config.exchange_name,
                &routing_key,
                BasicPublishOptions::default(),
                &payload,
                BasicProperties::default()
                    .with_content_type("application/json".into())
                    .with_delivery_mode(2) // persistent
                    .with_message_id(Uuid::new_v4().to_string().into()),
            )
            .await
            .map_err(|e| {
                error!("Failed to publish event: {}", e);
                PublisherError::Publishing(format!("Failed to publish event: {}", e))
            })?;

        debug!("Published event to {}: {:?}", routing_key, event);
        Ok(())
    }
}
