use std::sync::Arc;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

use crate::err::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};
use crate::storage::surrealdb::ChatMessage;
use crate::telemetry::logging;

use super::types::{Publisher, PublisherError, PublisherResult, PublishEvent, PublishEventType, StreamingPublisher};
use super::rabbitmq::{RabbitMQPublisher, RabbitMQConfig};

/// Chat message format for the UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UIChatMessage {
    /// Message ID
    pub id: String,
    /// Agent ID
    pub agent_id: String,
    /// User ID
    pub user_id: Option<String>,
    /// Message content
    pub content: String,
    /// Timestamp
    pub timestamp: i64,
    /// Whether the message is from the user
    pub is_user: bool,
    /// Whether the message is being processed
    pub is_processing: bool,
    /// Error message if any
    pub error: Option<String>,
    /// Whether this is a streaming message (partial content)
    #[serde(default)]
    pub is_streaming: bool,
    /// Message ID of the parent message (for streaming updates)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub parent_id: Option<String>,
}

impl UIChatMessage {
    /// Create a new UI chat message from a storage chat message
    pub fn from_storage(message: &ChatMessage, agent_id: &str) -> Self {
        // Parse the role to determine if it's a user message
        let is_user = message.role.starts_with("user");

        // Extract user_id from role if available (format: "user:user_id")
        let user_id = if is_user && message.role.contains(':') {
            let parts: Vec<&str> = message.role.splitn(2, ':').collect();
            if parts.len() > 1 {
                Some(parts[1].to_string())
            } else {
                None
            }
        } else {
            None
        };

        Self {
            id: message.id.clone(),
            agent_id: agent_id.to_string(),
            user_id,
            content: message.content.clone(),
            timestamp: message.created_at.timestamp(),
            is_user,
            is_processing: false,
            error: None,
            is_streaming: false,
            parent_id: None,
        }
    }

    /// Create a new UI chat message
    pub fn new(
        id: String,
        agent_id: String,
        user_id: Option<String>,
        content: String,
        is_user: bool,
        is_processing: bool,
    ) -> Self {
        Self {
            id,
            agent_id,
            user_id,
            content,
            timestamp: chrono::Utc::now().timestamp(),
            is_user,
            is_processing,
            error: None,
            is_streaming: false,
            parent_id: None,
        }
    }

    /// Create a new streaming token message
    pub fn streaming_token(
        id: String,
        parent_id: String,
        agent_id: String,
        user_id: Option<String>,
        token: String,
    ) -> Self {
        let mut message = Self::new(id, agent_id, user_id, token, false, false);
        message.is_streaming = true;
        message.parent_id = Some(parent_id);
        message
    }

    /// Create a new user message
    pub fn user_message(
        id: String,
        agent_id: String,
        user_id: Option<String>,
        content: String,
    ) -> Self {
        Self::new(id, agent_id, user_id, content, true, false)
    }

    /// Create a new agent message
    pub fn agent_message(
        id: String,
        agent_id: String,
        user_id: Option<String>,
        content: String,
    ) -> Self {
        Self::new(id, agent_id, user_id, content, false, false)
    }

    /// Create a new processing message
    pub fn processing_message(
        id: String,
        agent_id: String,
        user_id: Option<String>,
    ) -> Self {
        Self::new(id, agent_id, user_id, String::new(), false, true)
    }

    /// Create a new error message
    pub fn error_message(
        id: String,
        agent_id: String,
        user_id: Option<String>,
        error: String,
    ) -> Self {
        let mut message = Self::new(id, agent_id, user_id, String::new(), false, false);
        message.error = Some(error);
        message
    }
}

/// Chat publisher for sending chat messages to the UI
pub struct ChatPublisher {
    /// RabbitMQ publisher
    publisher: Arc<RabbitMQPublisher>,
}

impl ChatPublisher {
    /// Create a new chat publisher
    pub fn new(publisher: Arc<RabbitMQPublisher>) -> Self {
        Self { publisher }
    }

    /// Create a new chat publisher with default RabbitMQ configuration
    pub async fn default() -> PublisherResult<Self> {
        let publisher = Arc::new(RabbitMQPublisher::default());
        publisher.connect().await?;
        Ok(Self::new(publisher))
    }

    /// Create a new chat publisher with custom RabbitMQ configuration
    pub async fn with_config(config: RabbitMQConfig) -> PublisherResult<Self> {
        let publisher = Arc::new(RabbitMQPublisher::new(config));
        publisher.connect().await?;
        Ok(Self::new(publisher))
    }

    /// Publish a chat message to the UI
    pub async fn publish_message(&self, message: UIChatMessage) -> PublisherResult<()> {
        // Create the event
        let event = PublishEvent::new(
            PublishEventType::ChatMessage,
            message.agent_id.clone(),
            message.user_id.clone(),
            serde_json::to_value(message).map_err(|e| {
                error!("Failed to serialize chat message: {}", e);
                PublisherError::Serialization(format!("Failed to serialize chat message: {}", e))
            })?,
        );

        // Publish the event
        self.publisher.publish(event).await
    }

    /// Publish a user message to the UI
    pub async fn publish_user_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        content: &str,
    ) -> PublisherResult<()> {
        let message = UIChatMessage::user_message(
            id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
            content.to_string(),
        );
        self.publish_message(message).await
    }

    /// Publish an agent message to the UI
    pub async fn publish_agent_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        content: &str,
    ) -> PublisherResult<()> {
        let message = UIChatMessage::agent_message(
            id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
            content.to_string(),
        );
        self.publish_message(message).await
    }

    /// Publish a processing message to the UI
    pub async fn publish_processing_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
    ) -> PublisherResult<()> {
        let message = UIChatMessage::processing_message(
            id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
        );
        self.publish_message(message).await
    }

    /// Publish an error message to the UI
    pub async fn publish_error_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        error: &str,
    ) -> PublisherResult<()> {
        let message = UIChatMessage::error_message(
            id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
            error.to_string(),
        );
        self.publish_message(message).await
    }

    /// Update a processing message with the final content
    pub async fn update_processing_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        content: &str,
    ) -> PublisherResult<()> {
        let message = UIChatMessage::agent_message(
            id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
            content.to_string(),
        );
        self.publish_message(message).await
    }

    /// Update a processing message with an error
    pub async fn update_processing_message_with_error(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        error: &str,
    ) -> PublisherResult<()> {
        let message = UIChatMessage::error_message(
            id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
            error.to_string(),
        );
        self.publish_message(message).await
    }

    /// Start a streaming message session
    pub async fn start_streaming_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
    ) -> PublisherResult<()> {
        // First, publish a processing message to indicate that streaming is starting
        self.publish_processing_message(id, agent_id, user_id).await
    }

    /// Publish a streaming token
    pub async fn publish_streaming_token(
        &self,
        token_id: &str,
        parent_id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        token_text: &str,
    ) -> PublisherResult<()> {
        // Create a streaming token message
        let message = UIChatMessage::streaming_token(
            token_id.to_string(),
            parent_id.to_string(),
            agent_id.to_string(),
            user_id.map(|u| u.to_string()),
            token_text.to_string(),
        );

        // Create the event with StreamingChatMessage type
        let event = PublishEvent::new(
            PublishEventType::StreamingChatMessage,
            message.agent_id.clone(),
            message.user_id.clone(),
            serde_json::to_value(message).map_err(|e| {
                error!("Failed to serialize streaming token: {}", e);
                PublisherError::Serialization(format!("Failed to serialize streaming token: {}", e))
            })?,
        );

        // Publish the event
        self.publisher.publish(event).await
    }

    /// End a streaming message session by publishing the complete message
    pub async fn end_streaming_message(
        &self,
        id: &str,
        agent_id: &str,
        user_id: Option<&str>,
        complete_content: &str,
    ) -> PublisherResult<()> {
        // Publish the final complete message
        self.update_processing_message(id, agent_id, user_id, complete_content).await
    }
}

// Implement Publisher trait for ChatPublisher to satisfy the StreamingPublisher trait bound
#[async_trait::async_trait]
impl Publisher for ChatPublisher {
    async fn connect(&self) -> PublisherResult<()> {
        self.publisher.connect().await
    }

    async fn disconnect(&self) -> PublisherResult<()> {
        self.publisher.disconnect().await
    }

    async fn is_connected(&self) -> bool {
        self.publisher.is_connected().await
    }

    async fn publish(&self, event: PublishEvent) -> PublisherResult<()> {
        self.publisher.publish(event).await
    }
}

#[async_trait::async_trait]
impl StreamingPublisher for ChatPublisher {
    async fn start_streaming(&self, id: &str, source: &str, target: Option<&str>) -> PublisherResult<()> {
        self.start_streaming_message(id, source, target).await
    }

    async fn publish_token(&self, token_id: &str, parent_id: &str, source: &str, target: Option<&str>, token: &str) -> PublisherResult<()> {
        self.publish_streaming_token(token_id, parent_id, source, target, token).await
    }

    async fn end_streaming(&self, id: &str, source: &str, target: Option<&str>, complete_content: &str) -> PublisherResult<()> {
        self.end_streaming_message(id, source, target, complete_content).await
    }
}