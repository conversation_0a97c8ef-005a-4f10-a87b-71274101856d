use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use std::fmt;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

use crate::err::{GenericError, PrismaResult};
use crate::telemetry::logging;

/// Result type for publisher operations
pub type PublisherResult<T> = Result<T, PublisherError>;

/// Error type for publisher operations
#[derive(Debug)]
pub enum PublisherError {
    /// Connection error
    Connection(String),
    /// Publishing error
    Publishing(String),
    /// Serialization error
    Serialization(String),
    /// Other error
    Other(String),
}

impl fmt::Display for PublisherError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PublisherError::Connection(msg) => write!(f, "Connection error: {}", msg),
            PublisherError::Publishing(msg) => write!(f, "Publishing error: {}", msg),
            PublisherError::Serialization(msg) => write!(f, "Serialization error: {}", msg),
            PublisherError::Other(msg) => write!(f, "Other error: {}", msg),
        }
    }
}

impl std::error::Error for PublisherError {}

impl From<PublisherError> for GenericError {
    fn from(err: PublisherError) -> Self {
        GenericError::new(err)
    }
}

/// Event type for publishing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PublishEventType {
    /// Chat message
    ChatMessage,
    /// Streaming chat message token
    StreamingChatMessage,
    /// Task status update
    TaskStatus,
    /// Agent status update
    AgentStatus,
    /// System notification
    Notification,
    /// Error message
    Error,
}

/// Event data for publishing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PublishEvent {
    /// Unique ID for the event
    pub id: String,
    /// Type of the event
    pub event_type: PublishEventType,
    /// Timestamp of the event
    pub timestamp: i64,
    /// Source of the event (e.g., agent ID, system, etc.)
    pub source: String,
    /// Target of the event (e.g., user ID, agent ID, etc.)
    pub target: Option<String>,
    /// Payload of the event
    pub payload: serde_json::Value,
}

impl PublishEvent {
    /// Create a new publish event
    pub fn new(
        event_type: PublishEventType,
        source: String,
        target: Option<String>,
        payload: serde_json::Value,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            event_type,
            timestamp: chrono::Utc::now().timestamp(),
            source,
            target,
            payload,
        }
    }

    /// Create a new chat message event
    pub fn chat_message(
        source: String,
        target: Option<String>,
        message: &str,
    ) -> Self {
        Self::new(
            PublishEventType::ChatMessage,
            source,
            target,
            serde_json::json!({
                "content": message,
            }),
        )
    }

    /// Create a new task status event
    pub fn task_status(
        source: String,
        target: Option<String>,
        task_id: &str,
        status: &str,
        result: Option<serde_json::Value>,
    ) -> Self {
        Self::new(
            PublishEventType::TaskStatus,
            source,
            target,
            serde_json::json!({
                "task_id": task_id,
                "status": status,
                "result": result,
            }),
        )
    }

    /// Create a new error event
    pub fn error(
        source: String,
        target: Option<String>,
        error_message: &str,
    ) -> Self {
        Self::new(
            PublishEventType::Error,
            source,
            target,
            serde_json::json!({
                "error": error_message,
            }),
        )
    }
}

/// Publisher trait for sending data to the UI
#[async_trait]
pub trait Publisher: Send + Sync {
    /// Connect to the publisher
    async fn connect(&self) -> PublisherResult<()>;

    /// Disconnect from the publisher
    async fn disconnect(&self) -> PublisherResult<()>;

    /// Check if the publisher is connected
    async fn is_connected(&self) -> bool;

    /// Publish an event
    async fn publish(&self, event: PublishEvent) -> PublisherResult<()>;

    /// Publish a chat message
    async fn publish_chat_message(
        &self,
        source: &str,
        target: Option<&str>,
        message: &str,
    ) -> PublisherResult<()> {
        let event = PublishEvent::chat_message(
            source.to_string(),
            target.map(|t| t.to_string()),
            message,
        );
        self.publish(event).await
    }

    /// Publish a task status
    async fn publish_task_status(
        &self,
        source: &str,
        target: Option<&str>,
        task_id: &str,
        status: &str,
        result: Option<serde_json::Value>,
    ) -> PublisherResult<()> {
        let event = PublishEvent::task_status(
            source.to_string(),
            target.map(|t| t.to_string()),
            task_id,
            status,
            result,
        );
        self.publish(event).await
    }

    /// Publish an error
    async fn publish_error(
        &self,
        source: &str,
        target: Option<&str>,
        error_message: &str,
    ) -> PublisherResult<()> {
        let event = PublishEvent::error(
            source.to_string(),
            target.map(|t| t.to_string()),
            error_message,
        );
        self.publish(event).await
    }
}

/// Streaming publisher trait for sending streaming data to the UI
#[async_trait]
pub trait StreamingPublisher: Publisher {
    /// Start a streaming session
    async fn start_streaming(&self, id: &str, source: &str, target: Option<&str>) -> PublisherResult<()>;

    /// Publish a token in a streaming session
    async fn publish_token(&self, token_id: &str, parent_id: &str, source: &str, target: Option<&str>, token: &str) -> PublisherResult<()>;

    /// End a streaming session
    async fn end_streaming(&self, id: &str, source: &str, target: Option<&str>, complete_content: &str) -> PublisherResult<()>;
}
