// Declare modules within the services directory
pub mod basic_agent_service;
pub mod chat_service; // Add the new chat service module
pub mod generics;
pub mod new_project; // Assuming this directory contains a mod.rs or file
pub mod participants; // Assuming this directory contains a mod.rs or file
pub mod prisma_ui;
pub mod services;
pub mod traits;
pub mod types;

// Optional: Re-export key items if needed
// pub use basic_agent_service::BasicAgentService;
