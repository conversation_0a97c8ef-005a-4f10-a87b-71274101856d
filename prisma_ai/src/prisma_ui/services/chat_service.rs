use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, error, debug};
use uuid::Uuid;

use crate::prisma::prisma_engine::prisma_engine::PrismaEngine;
// Use the re-exported error types from the err module
use crate::err::{PrismaResult, GenericError as PrismaError, types::generics::EngineOperationError};
use crate::storage::DataStore;
use crate::storage::surrealdb::{ChatMessage, Database};
// Import task-related traits
use crate::prisma::prisma_engine::traits::{Task, EngineController};
// Import publisher
use crate::prisma_ui::publishers::chat_publisher::{ChatPublisher, UIChatMessage};
use crate::prisma_ui::publishers::types::PublisherError;
// Import telemetry
use crate::telemetry::logging;

pub struct ChatService {
    engine: Arc<RwLock<PrismaEngine>>,
    publisher: Arc<ChatPublisher>,
}

impl ChatService {
    /// Create a new ChatService with the given engine and publisher
    pub fn new(engine: Arc<RwLock<PrismaEngine>>, publisher: Arc<ChatPublisher>) -> Self {
        Self { engine, publisher }
    }

    /// Create a new ChatService with the given engine and a default publisher
    pub async fn with_engine(engine: Arc<RwLock<PrismaEngine>>) -> PrismaResult<Self> {
        let publisher = match ChatPublisher::default().await {
            Ok(publisher) => Arc::new(publisher),
            Err(e) => {
                error!("Failed to create default publisher: {}", e);
                return Err(PrismaError::new(e));
            }
        };
        Ok(Self::new(engine, publisher))
    }

    /// Publish a user message to the UI and store it in the database
    ///
    /// # Arguments
    /// * `agent_id` - The ID of the agent to handle the message
    /// * `user_id` - The ID of the user sending the message (optional)
    /// * `message` - The message from the user
    ///
    /// # Returns
    /// A `PrismaResult` containing the message ID if successful
    pub async fn publish_user_message(&self, agent_id: &str, user_id: Option<&str>, message: &str) -> PrismaResult<String> {
        // Generate a unique ID for the message
        let message_id = Uuid::new_v4().to_string();

        // Publish the message to the UI
        if let Err(e) = self.publisher.publish_user_message(&message_id, agent_id, user_id, message).await {
            error!("Failed to publish user message: {}", e);
            return Err(PrismaError::new(EngineOperationError::new(
                format!("Failed to publish user message: {}", e)
            )));
        }

        // Store the message in the database
        let engine = self.engine.read().await;
        let _storage_service = engine.get_storage_service().ok_or_else(|| {
            error!("StorageService not initialized in PrismaEngine");
            PrismaError::new(EngineOperationError::new("StorageService not available".to_string()))
        })?;

        // Create a chat message
        // Note: The ChatMessage struct doesn't have agent_id or user_id fields yet
        // We'll need to add those fields to the struct in a future update
        // For now, we'll use the role field to store the agent/user information
        let role = if let Some(user_id) = user_id {
            format!("user:{}", user_id)
        } else {
            "user".to_string()
        };

        // Get the database from the storage service
        // This is a temporary solution until we add the create_chat_message method to the DataStore trait
        // We know that the storage service is a SurrealDbConnection, which contains a Database
        // So we'll just create a new Database instance for now
        let db = Database::new().await?;

        // Store the message in the database
        db.create_chat_message(
            Some(message_id.clone()),
            role,
            message.to_string(),
        ).await?;

        Ok(message_id)
    }

    /// Handle a chat message from a user to an agent
    ///
    /// This method implements the complete flow from receiving a user message to submitting an LLM task:
    /// 1. Get component handles from the engine
    /// 2. Get the agent's sequence ID
    /// 3. Retrieve conversation history
    /// 4. Format the history for prompt construction
    /// 5. Load agent configuration and template
    /// 6. Construct the prompt using the template
    /// 7. Create an LLM task with the prompt
    /// 8. Submit the task to the engine
    /// 9. Publish the response to the UI
    ///
    /// # Arguments
    /// * `agent_id` - The ID of the agent to handle the message
    /// * `user_message` - The message from the user
    ///
    /// # Returns
    /// A `PrismaResult` containing the task ID if successful
    pub async fn handle_chat_message(&self, agent_id: &str, user_message: &str) -> PrismaResult<String> {
        info!("Handling chat message for agent: {}", agent_id);
        let engine = self.engine.read().await; // Acquire read lock

        // --- 1. Get Component Handles ---
        let agent_manager_handle = engine.get_agent_manager().ok_or_else(|| {
            error!("AgentManager not initialized in PrismaEngine");
            PrismaError::new(EngineOperationError::new("AgentManager not available".to_string()))
        })?;

        let storage_service = engine.get_storage_service().ok_or_else(|| {
            error!("StorageService not initialized in PrismaEngine");
            PrismaError::new(EngineOperationError::new("StorageService not available".to_string()))
        })?;

        // Get LLM Service handle
        // Use a default model ID for now, this should come from agent_config in the future
        let model_id = "default_model";
        let llm_service = engine.get_llm_service(model_id).ok_or_else(|| {
            error!("LlmService not initialized in PrismaEngine for model: {}", model_id);
            PrismaError::new(EngineOperationError::new(format!("LlmService not available for model: {}", model_id)))
        })?;

        info!("Successfully obtained component handles from PrismaEngine");

        // --- 2. Get SeqId using agent_manager ---
        let agent_manager = agent_manager_handle.read().await; // Acquire read lock on AgentManager
        let seq_id = agent_manager.register_or_get_agent(agent_id.to_string()).await?;
        debug!("Using SeqId {} for agent '{}'", seq_id, agent_id);
        drop(agent_manager); // Release AgentManager read lock

        // --- 3. Get history using storage_service ---
        let history: Vec<ChatMessage> = storage_service
            .get_conversation_history(agent_id, 15) // Fetch last 15 turns
            .await?;
        debug!("Retrieved {} history messages for agent '{}'", history.len(), agent_id);

        // --- 4. Format history for prompt construction ---
        let formatted_history = history.iter().map(|msg| {
            format!("{}\n", msg.content)
        }).collect::<String>();
        debug!("Formatted history for prompt construction");

        // --- 5. Load agent configuration and template ---
        // Use the prompt_template module to get the agent's configuration and template
        let agent_config = match crate::prisma_ui::services::participants::prompt_template::get_agent_config(agent_id) {
            Ok(Some(config)) => config,
            Ok(None) => {
                error!("Agent configuration not found for agent_id: {}", agent_id);
                return Err(PrismaError::new(EngineOperationError::new(
                    format!("Agent configuration not found for agent_id: {}", agent_id)
                )));
            },
            Err(e) => {
                error!("Failed to get agent configuration: {}", e);
                return Err(PrismaError::new(EngineOperationError::new(
                    format!("Failed to get agent configuration: {}", e)
                )));
            }
        };
        debug!("Loaded agent configuration for agent '{}'", agent_id);

        // --- 6. Construct prompt using template ---
        let prompt = match crate::prisma_ui::services::participants::prompt_template::construct_prompt_with_config(
            agent_id,
            &agent_config,
            &formatted_history,
            user_message,
            None, // Use the agent's template_key from its config
            None, // No additional context variables
        ) {
            Ok(p) => p,
            Err(e) => {
                error!("Failed to construct prompt: {}", e);
                return Err(PrismaError::new(EngineOperationError::new(
                    format!("Failed to construct prompt: {}", e)
                )));
            }
        };
        debug!("Constructed prompt for agent '{}'", agent_id);

        // --- 7. Create LlmTask ---
        // Create LlmInferenceParams
        let params = crate::prisma::prisma_engine::tcl::types::LlmInferenceParams {
            model_id: agent_config.template_key.clone(), // Use template_key as model_id for now
            prompt,
            max_tokens: Some(1024), // Example value, could be from agent_config
            temperature: Some(0.7), // Example value, could be from agent_config
            top_k: Some(40),       // Example value, could be from agent_config
            top_p: Some(0.9),      // Example value, could be from agent_config
            streaming: Some(false), // Disable streaming by default
            streaming_buffer_size: Some(100), // Default buffer size
        };

        // Create LlmTask
        let llm_task = crate::prisma::prisma_engine::tcl::llm_task::LlmTask::new(
            params,
            seq_id,
            llm_service.clone(),
            None, // Default priority
        );

        // Get the task ID for return value using the Task trait
        let task_id = Task::id(&llm_task).to_string();
        debug!("Created LlmTask with ID: {}", task_id);

        // Drop the engine read lock before acquiring write lock
        drop(engine);

        // --- 8. Submit task (requires write lock on engine) ---
        let mut engine = self.engine.write().await; // Acquire write lock
        // Use the EngineController trait to submit the task
        let (task_id, result_receiver) = EngineController::submit_task(&mut *engine, Box::new(llm_task)).await?;
        info!("Submitted LlmTask {} for agent '{}'", task_id, agent_id);

        // Create a unique message ID for the response
        let message_id = Uuid::new_v4().to_string();
        let agent_id_owned = agent_id.to_string(); // Clone the agent_id to own it

        // Publish a processing message to indicate that the agent is thinking
        if let Err(e) = self.publisher.publish_processing_message(&message_id, &agent_id_owned, None).await {
            error!("Failed to publish processing message: {}", e);
            // Continue anyway, as this is not critical
        }

        // --- 9. Wait for the task result ---
        // Handle the result asynchronously
        let publisher = self.publisher.clone();
        tokio::spawn(async move {
            match result_receiver.await {
                Ok(result) => {
                    match result {
                        Ok(any_box) => {
                            info!("Task {} completed successfully", task_id);

                            // Try to downcast the result to a string
                            if let Some(result_str) = any_box.downcast_ref::<String>() {
                                // Update the processing message with the result
                                if let Err(e) = publisher.update_processing_message(&message_id, &agent_id_owned, None, result_str).await {
                                    error!("Failed to update processing message with result: {}", e);
                                }
                            } else {
                                // If we can't downcast to a string, use a generic message
                                if let Err(e) = publisher.update_processing_message(&message_id, &agent_id_owned, None, "I've completed the task, but I can't provide a detailed response.").await {
                                    error!("Failed to update processing message with generic result: {}", e);
                                }
                            }
                        },
                        Err(e) => {
                            error!("Task {} failed: {}", task_id, e);

                            // Update the processing message with the error
                            if let Err(pub_err) = publisher.update_processing_message_with_error(&message_id, &agent_id_owned, None, &format!("Task failed: {}", e)).await {
                                error!("Failed to update processing message with error: {}", pub_err);
                            }
                        }
                    }
                },
                Err(e) => {
                    error!("Failed to receive result for task {}: {}", task_id, e);

                    // Update the processing message with the error
                    if let Err(pub_err) = publisher.update_processing_message_with_error(&message_id, &agent_id_owned, None, &format!("Failed to receive result: {}", e)).await {
                        error!("Failed to update processing message with error: {}", pub_err);
                    }
                }
            }
        });

        // Return the task ID
        Ok(task_id.to_string())
    }
}
