use std::sync::Arc;
use std::fmt;
use tokio::sync::{RwLock, oneshot}; // Added for async RwLock and oneshot
use tracing::{info, error, debug};
use uuid::Uuid;

// Corrected and necessary imports
use crate::err::{PrismaResult, DomainError}; // Removed unused GenericError
use crate::err::types::PrismaError; // Corrected PrismaError import path
use crate::prisma::prisma_engine::agent_manager::types::AgentId; // Use the public path for AgentId
use crate::prisma::prisma_engine::prisma_engine::PrismaEngine;
use crate::prisma::prisma_engine::tcl::llm_task::LlmTask;
use crate::prisma::prisma_engine::tcl::types::LlmInferenceParams;
// use crate::storage::DataStore; // Removed unused import
use crate::prisma::prisma_engine::EngineController; // Keep trait import
use crate::prisma::prisma_engine::traits::Task; // Import Task trait for id()
use crate::prisma::prisma_engine::types::{TaskId, TaskPriority}; // Keep only used types
// use crate::llm::types::SeqId; // Removed unused import
use crate::err::types::generics::EngineOperationError; // Corrected EngineOperationError import path
use crate::telemetry::logging::log_agent_event; // Import the logging function
use crate::prisma_ui::publishers::ChatPublisher;

// A basic service demonstrating agent task creation using the PrismaEngine handle
pub struct BasicAgentService {
    engine_handle: Arc<RwLock<PrismaEngine>>, // Use engine handle
    publisher: Option<Arc<ChatPublisher>>, // Publisher for streaming
}

// Manual Debug implementation
impl fmt::Debug for BasicAgentService {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("BasicAgentService")
         .field("engine_handle", &format_args!("Arc<RwLock<PrismaEngine>>"))
         .finish()
    }
}

impl BasicAgentService {
    pub fn new(engine_handle: Arc<RwLock<PrismaEngine>>) -> Self {
        Self {
            engine_handle,
            publisher: None,
        }
    }

    /// Set the publisher for streaming
    pub fn with_publisher(mut self, publisher: Arc<ChatPublisher>) -> Self {
        self.publisher = Some(publisher);
        self
    }

    /// Example function demonstrating how to access engine components.
    pub async fn access_engine_components(&self) -> PrismaResult<()> {
        info!("Attempting to access engine components...");

        // Acquire a read lock on the PrismaEngine
        let engine_read = self.engine_handle.read().await;
        info!("Acquired read lock on PrismaEngine.");

        // --- Access AgentManager ---
        match engine_read.get_agent_manager() {
            Some(agent_manager_handle) => {
                // We have the Arc<RwLock<AgentManager>> handle
                // We can now acquire a lock on it if needed, e.g.:
                // let agent_manager_read = agent_manager_handle.read().await;
                // info!("Successfully accessed AgentManager: {:?}", agent_manager_read);
                info!("Successfully obtained AgentManager handle.");
                // Example: Register or get an agent
                let agent_id = "test_agent_123".to_string();
                // Acquire write lock to call register_or_get_agent
                match agent_manager_handle.write().await.register_or_get_agent(agent_id.clone()).await {
                    Ok(seq_id) => { // Result is SeqId
                        info!("Successfully registered/retrieved agent '{}' with SeqId: {}", agent_id, seq_id);
                        // You can now use the seq_id
                    }
                    Err(e) => {
                        error!("Failed to register or get agent {}: {:?}", agent_id, e);
                        // Handle error appropriately
                    }
                }
            }
            None => {
                error!("AgentManager is not initialized in PrismaEngine.");
                // Use PrismaError::new directly
                return Err(PrismaError::new(EngineOperationError::new(
                    "AgentManager not available".to_string(),
                )));
            }
        }

        // --- Access StorageService ---
        match engine_read.get_storage_service() {
            Some(_storage_service_handle) => { // Mark as unused if not used
                // We have the Arc<dyn DataStore> handle
                info!("Successfully obtained StorageService handle.");
                // Example: Try to get a document (replace "doc_id" with a real ID)
                // match storage_service_handle.get_document("doc_id").await {
                //     Ok(Some(doc)) => info!("Retrieved document: {:?}", doc),
                //     Ok(None) => info!("Document 'doc_id' not found."),
                //     Err(e) => error!("Error getting document: {:?}", e),
                // }
            }
            None => {
                error!("StorageService is not initialized in PrismaEngine.");
                // Use PrismaError::new directly
                return Err(PrismaError::new(EngineOperationError::new(
                    "StorageService not available".to_string(),
                )));
            }
        }

        // --- Access LlmService ---
        // Use a default model ID for now
        let model_id = "default_model";
        match engine_read.get_llm_service(model_id) {
            Some(_llm_service_handle) => { // Mark as unused if not used
                // We have the Arc<dyn LlmService + Send + Sync> handle
                info!("Successfully obtained LlmService handle.");
                // Example: Get vocab size
                // Need to import LlmService trait if using its methods
                // use crate::prisma::prisma_engine::tcl::llm_task::LlmService;
                // let vocab_size = llm_service_handle.n_vocab();
                // info!("LLM Service Vocab Size: {}", vocab_size);
                info!("LLM Service access example - method call commented out as LlmService trait is not imported by default.");
            }
            None => {
                error!("LlmService is not initialized in PrismaEngine.");
                // Use PrismaError::new directly
                return Err(PrismaError::new(EngineOperationError::new(
                    "LlmService not available".to_string(),
                )));
            }
        }

        info!("Successfully accessed all required engine components.");
        Ok(())
    }

    /// Simulates handling a request for a specific agent to process a prompt.
    pub async fn handle_agent_prompt(
        &self,
        agent_id: AgentId,
        prompt: String,
        model_id: String, // Model needed for LlmInferenceParams
    ) -> PrismaResult<()> {
        info!("[TEST] Starting handle_agent_prompt for agent '{}'", agent_id);
        let start_time = std::time::Instant::now();

        // 1. Get Engine Read Lock
        info!("[TEST] Getting engine read lock...");
        let engine_lock_start = std::time::Instant::now();
        let engine = self.engine_handle.read().await;
        info!("[TEST] Got engine read lock in {:?}", engine_lock_start.elapsed());

        // 2. Get AgentManager handle and SeqId
        info!("[TEST] Getting AgentManager handle...");
        let agent_manager_start = std::time::Instant::now();
        let agent_manager = engine.get_agent_manager().ok_or_else(|| {
            let err = DomainError::SystemError("AgentManager not available in engine".to_string());
            PrismaError::new(err) // Convert to PrismaError using new()
        })?;
        info!("[TEST] Got AgentManager handle in {:?}", agent_manager_start.elapsed());

        // Need write lock for AgentManager to register/get agent
        // Need to import SeqId
        use crate::llm::types::SeqId;
        info!("[TEST] Registering agent...");
        let register_start = std::time::Instant::now();
        let seq_id: SeqId = agent_manager.write().await.register_or_get_agent(agent_id.clone()).await?;
        info!("[TEST] Registered agent with SeqId {} in {:?}", seq_id, register_start.elapsed());

        // 3. Get StorageService handle
        info!("[TEST] Getting StorageService handle...");
        let storage_start = std::time::Instant::now();
        let _storage_service = engine.get_storage_service().ok_or_else(|| { // Mark unused
             let err = DomainError::SystemError("StorageService not available in engine".to_string());
             PrismaError::new(err) // Convert to PrismaError using new()
        })?;
        info!("[TEST] Got StorageService handle in {:?}", storage_start.elapsed());

        // Skip history retrieval for testing
        let formatted_history = "PLACEHOLDER_HISTORY"; // Placeholder
        info!("[TEST] Using placeholder history");

        // 4. Construct Prompt
        info!("[TEST] Constructing prompt...");
        let prompt_start = std::time::Instant::now();
        let final_prompt = format!("History:\n{}\n\nUser Prompt: {}", formatted_history, prompt); // Basic placeholder construction
        info!("[TEST] Constructed prompt in {:?}", prompt_start.elapsed());

        // 5. Create LlmInferenceParams
        info!("[TEST] Creating LlmInferenceParams...");
        let params_start = std::time::Instant::now();
        #[allow(unused_variables)] // Allow unused task_params for now as task creation is commented out
        let task_params = LlmInferenceParams {
            model_id: model_id.clone(), // Use the provided model_id
            prompt: final_prompt, // Use the constructed prompt
            max_tokens: Some(128), // Example value
            temperature: Some(0.7), // Example value
            top_k: Some(40), // Example value
            top_p: Some(0.9), // Example value
            streaming: Some(false), // Disable streaming by default
            streaming_buffer_size: Some(100), // Default buffer size
            // Add other params as needed
        };
        info!("[TEST] Created LlmInferenceParams in {:?}", params_start.elapsed());

        // Drop the read lock
        info!("[TEST] Dropping engine read lock...");
        drop(engine);

        // Return immediately for testing
        info!("[TEST] Completed handle_agent_prompt in {:?} - skipping task submission for testing", start_time.elapsed());
        Ok(())
    }

    pub async fn create_and_submit_llm_task(
        &self,
        agent_id: String,
        prompt: String, // Keep prompt separate for clarity, it's part of params
        params: LlmInferenceParams,
    ) -> PrismaResult<TaskId> {
        info!("Attempting to create and submit LLM task for agent: {}", agent_id);
        debug!("Prompt: '{}', Params: {:?}", prompt, params); // Log original prompt too

        // 1. Get AgentManager handle (Read Lock on Engine)
        let agent_manager_handle = {
            let engine_read = self.engine_handle.read().await;
            engine_read.get_agent_manager().ok_or_else(|| {
                error!("AgentManager not available during task creation");
                PrismaError::new(EngineOperationError::new(
                    "AgentManager not available".to_string(),
                )) // Convert to PrismaError using new()
            })?.clone() // Clone the Arc<RwLock<AgentManager>>
        };
        debug!("Got AgentManager handle");

        // 2. Get or create Agent and obtain SeqId (Write Lock on AgentManager)
        // Need to import SeqId
        use crate::llm::types::SeqId;
        let agent_manager_write = agent_manager_handle.write().await; // Remove mut
        debug!("Acquired write lock on AgentManager");
        // Check if agent is new before calling register_or_get_agent
        let is_new_agent = !agent_manager_write.is_agent_registered(&agent_id).await;
        // register_or_get_agent returns the SeqId directly
        let result = agent_manager_write.register_or_get_agent(agent_id.clone()).await;
        // Log the event based on whether it was registration or retrieval
        if result.is_ok() {
            let event_type = if is_new_agent { "agent_registered" } else { "agent_retrieved" };
            log_agent_event(event_type, &agent_id, None);
        }
        let seq_id: SeqId = result?;
        debug!("Obtained SeqId: {} for agent {}", seq_id, agent_id);


        // 3. Get LlmService handle (Read Lock on Engine)
        // Need to import LlmService trait for the handle type
        use crate::prisma::prisma_engine::tcl::llm_task::LlmService;
        let llm_service_handle: Arc<dyn LlmService + Send + Sync> = {
            let engine_read = self.engine_handle.read().await;
            // Assuming get_llm_service might need model_id from params or agent config
            let model_id_to_use = params.model_id.clone(); // Example: Get model_id from params
            // Use a default model ID for now
            let model_id = "default_model";
            engine_read.get_llm_service(model_id).ok_or_else(|| {
                error!("LlmService not available for model '{}' during task creation", model_id_to_use);
                PrismaError::new(EngineOperationError::new(
                    format!("LlmService not available for model '{}'", model_id_to_use),
                )) // Convert to PrismaError using new()
            })?.clone() // Clone the Arc<dyn LlmService>
        };
        debug!("Got LlmService handle");

        // Check if streaming is enabled before creating the task
        let is_streaming = params.streaming.unwrap_or(false);

        // 4. Create the LlmTask
        // Corrected arguments: params, seq_id, llm_service_handle, Option<TaskPriority>
        let llm_task = LlmTask::new(
            params.clone(), // Arg 1: LlmInferenceParams (contains prompt) - Clone to avoid ownership issues
            seq_id, // Arg 2: SeqId
            llm_service_handle, // Arg 3: Arc<dyn LlmService>
            Some(TaskPriority::Normal), // Arg 4: Option<TaskPriority> - Example priority
        );
        // Use the id() method from the Task trait
        info!("Created LlmTask with TaskId: {}", llm_task.id());

        // 5. Submit the task (Read Lock on Engine - submit_task takes &self)
        let (task_id, result_receiver) = {
            let engine_read = self.engine_handle.read().await;
            // Use submit_task from EngineController trait
            engine_read.submit_task(Box::new(llm_task)).await?
        };

        info!("Submitted LlmTask, received TaskId: {:?}", task_id);

        // Check if streaming is enabled
        if is_streaming {
            // Get the publisher for streaming
            let publisher = match self.publisher.as_ref() {
                Some(p) => p.clone(),
                None => {
                    error!("Publisher not available for streaming task {}", task_id);
                    // Continue without streaming
                    self.handle_non_streaming_result(task_id, result_receiver);
                    return Ok(task_id);
                }
            };

            // Create a unique message ID for the streaming session
            let message_id = Uuid::new_v4().to_string();
            let agent_id_clone = agent_id.clone();

            // Handle streaming result
            self.handle_streaming_result(task_id, message_id, agent_id_clone, result_receiver, publisher);
        } else {
            // Handle non-streaming result
            self.handle_non_streaming_result(task_id, result_receiver);
        }

        Ok(task_id)
    }

    /// Example function demonstrating agent registration and task submission.
    // Added model_id parameter
    pub async fn register_and_submit_task(
        &self,
        agent_id: String,
        prompt: String,
        _model_id: String, // Unused but kept for API compatibility
        streaming: Option<bool>,
    ) -> PrismaResult<TaskId> {
        info!("Attempting to register agent '{}' and submit task...", agent_id);

        // Acquire a read lock on the PrismaEngine
        let engine_read = self.engine_handle.read().await;
        info!("Acquired read lock on PrismaEngine.");

        // --- Get AgentManager Handle ---
        let agent_manager_handle = match engine_read.get_agent_manager() {
            Some(handle) => handle.clone(), // Clone Arc
            None => {
                error!("AgentManager is not initialized in PrismaEngine.");
                return Err(PrismaError::new(EngineOperationError::new(
                    "AgentManager not available".to_string(),
                ))); // Convert to PrismaError using new()
            }
        };

        // --- Get LlmService Handle ---
        // Need to import LlmService trait for the handle type
        use crate::prisma::prisma_engine::tcl::llm_task::LlmService;
        // Use a default model ID for now
        let model_id = "default_model";
        let llm_service_handle: Arc<dyn LlmService + Send + Sync> = match engine_read.get_llm_service(model_id) {
             Some(handle) => handle.clone(), // Clone Arc
             None => {
                 error!("LlmService not available for model '{}'", model_id);
                 return Err(PrismaError::new(EngineOperationError::new(
                     format!("LlmService not available for model '{}'", model_id),
                 ))); // Convert to PrismaError using new()
             }
        };

        // Drop read lock before acquiring write lock for agent registration
        drop(engine_read);

        // --- Register or Get Agent (SeqId) ---
        // Need to import SeqId
        use crate::llm::types::SeqId;
        let seq_id: SeqId = match agent_manager_handle.write().await.register_or_get_agent(agent_id.clone()).await {
            Ok(id) => {
                info!("Successfully registered/retrieved agent '{}' with SeqId: {}", agent_id, id);
                id
            }
            Err(e) => {
                error!("Failed to register or get agent {}: {:?}", agent_id, e);
                return Err(e);
            }
        };

        // --- Create LLM Task ---
        // Create LlmInferenceParams
        let params = LlmInferenceParams {
            model_id: model_id.to_string(), // Use the provided model_id
            prompt, // Use the provided prompt
            max_tokens: Some(1024), // Example value
            temperature: Some(0.7), // Example value
            top_k: Some(40), // Example value
            top_p: Some(0.9), // Example value
            streaming, // Use the provided streaming flag
            streaming_buffer_size: Some(100), // Default buffer size
        };
        let priority = TaskPriority::High; // Example value

        // Call LlmTask::new with correct signature
        let llm_task = LlmTask::new(
            params,
            seq_id,
            llm_service_handle,
            Some(priority), // Wrap priority in Some
        );
        let task_id_str = llm_task.id().to_string(); // Get task ID for logging
        info!("Created LLM task with ID: {}", task_id_str);


        // --- Submit Task to Engine ---
        // Re-acquire read lock to call submit_task (&self)
        let engine_read = self.engine_handle.read().await;
        match engine_read.submit_task(Box::new(llm_task)).await {
            Ok((task_id, result_receiver)) => {
                info!("Successfully submitted task {:?} for agent {}", task_id, agent_id);

                // Handle the result asynchronously
                tokio::spawn(async move {
                    match result_receiver.await {
                        Ok(result) => {
                            match result {
                                Ok(_any_box) => {
                                    info!("Task {} completed successfully", task_id);
                                },
                                Err(e) => {
                                    error!("Task {} failed: {}", task_id, e);
                                }
                            }
                        },
                        Err(e) => {
                            error!("Failed to receive result for task {}: {}", task_id, e);
                        }
                    }
                });

                Ok(task_id)
            }
            Err(e) => {
                error!("Failed to submit task for agent {}: {:?}", agent_id, e);
                Err(e)
            }
        }
    }

    /// Handle non-streaming result
    fn handle_non_streaming_result(
        &self,
        task_id: TaskId,
        result_receiver: oneshot::Receiver<PrismaResult<Box<dyn std::any::Any + Send>>>,
    ) {
        // Handle the result asynchronously
        tokio::spawn(async move {
            match result_receiver.await {
                Ok(result) => {
                    match result {
                        Ok(any_box) => {
                            // Try to downcast to String
                            if let Ok(text) = any_box.downcast::<String>() {
                                info!("Task {} completed successfully with result: {}", task_id, *text);
                            } else {
                                info!("Task {} completed successfully", task_id);
                            }
                        },
                        Err(e) => {
                            error!("Task {} failed: {}", task_id, e);
                        }
                    }
                },
                Err(e) => {
                    error!("Failed to receive result for task {}: {}", task_id, e);
                }
            }
        });
    }

    /// Handle streaming result
    fn handle_streaming_result(
        &self,
        task_id: TaskId,
        message_id: String,
        agent_id: String,
        result_receiver: oneshot::Receiver<PrismaResult<Box<dyn std::any::Any + Send>>>,
        publisher: Arc<ChatPublisher>,
    ) {
        // Spawn a task to handle the streaming result
        tokio::spawn(async move {
            // Start the streaming session
            if let Err(e) = publisher.start_streaming_message(&message_id, &agent_id, None).await {
                error!("Failed to start streaming session for task {}: {}", task_id, e);
                return;
            }

            // Wait for the result
            match result_receiver.await {
                Ok(result) => {
                    match result {
                        Ok(any_box) => {
                            // Try to downcast to String
                            if let Ok(text) = any_box.downcast::<String>() {
                                // End the streaming session with the complete text
                                if let Err(e) = publisher.end_streaming_message(&message_id, &agent_id, None, &text).await {
                                    error!("Failed to end streaming session for task {}: {}", task_id, e);
                                }
                                info!("Task {} completed successfully with streaming result", task_id);
                            } else {
                                // End the streaming session with a generic message
                                if let Err(e) = publisher.end_streaming_message(&message_id, &agent_id, None, "Task completed").await {
                                    error!("Failed to end streaming session for task {}: {}", task_id, e);
                                }
                                info!("Task {} completed successfully", task_id);
                            }
                        },
                        Err(e) => {
                            // End the streaming session with an error message
                            if let Err(publish_err) = publisher.update_processing_message_with_error(&message_id, &agent_id, None, &e.to_string()).await {
                                error!("Failed to update streaming session with error for task {}: {}", task_id, publish_err);
                            }
                            error!("Task {} failed: {}", task_id, e);
                        }
                    }
                },
                Err(e) => {
                    // End the streaming session with an error message
                    if let Err(publish_err) = publisher.update_processing_message_with_error(&message_id, &agent_id, None, &e.to_string()).await {
                        error!("Failed to update streaming session with error for task {}: {}", task_id, publish_err);
                    }
                    error!("Failed to receive result for task {}: {}", task_id, e);
                }
            }
        });
    }
}
