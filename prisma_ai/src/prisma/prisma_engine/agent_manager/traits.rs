// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/traits.rs
// =================================================================================================
// Purpose: Defines the core traits that define the interfaces for the agent_manager module.
// This file contains the trait definitions that establish the contracts for the various
// components of the agent_manager module. These traits define the capabilities and
// responsibilities of each component, allowing for modular and testable implementations.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses AgentId, AgentState, AgentRole, AgentCapability, AgentMessage, etc.
// - agents.rs: Uses the Agent struct
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - std::sync::Arc: For shared ownership
// - crate::err::PrismaResult: For error handling
// - crate::SeqId: For sequence ID management
// =================================================================================================
// Module Interactions:
// - Implemented by agent_registry.rs for agent registration and lookup
// - Implemented by agent_state.rs for agent state management
// - Implemented by agent_capabilities.rs for capability management
// - Implemented by agent_communication.rs for agent communication
// - Provides the contract for all agent_manager components
// - Enables modular and testable implementations
// =================================================================================================

use async_trait::async_trait;
use std::sync::Arc;

use crate::err::PrismaResult;
use crate::SeqId;

use super::types::{AgentId, AgentState, AgentRole, AgentCapability, AgentMessage, MessagePriority, HumanId};
use super::agents::Agent;

/// Trait for managing agent registration and lifecycle
#[async_trait]
pub trait AgentRegistry: Send + Sync {
    /// Register a new agent or get an existing one
    async fn register_or_get_agent(&self, agent_id: AgentId) -> PrismaResult<SeqId>;

    /// Check if an agent is registered
    async fn is_agent_registered(&self, agent_id: &AgentId) -> PrismaResult<bool>;

    /// Get the SeqId for an agent
    async fn get_seq_id(&self, agent_id: &AgentId) -> PrismaResult<Option<SeqId>>;

    /// Unregister an agent
    async fn unregister_agent(&self, agent_id: &AgentId) -> PrismaResult<bool>;

    /// Get a list of all registered agent IDs
    async fn get_all_agent_ids(&self) -> PrismaResult<Vec<AgentId>>;

    /// Get a complete agent by ID
    async fn get_agent(&self, agent_id: &AgentId) -> PrismaResult<Option<Agent>>;

    /// Update an agent's information
    async fn update_agent(&self, agent: Agent) -> PrismaResult<()>;
}

/// Trait for managing agent state
#[async_trait]
pub trait AgentStateManager: Send + Sync + std::fmt::Debug {
    /// Get the current state of an agent
    async fn get_state(&self, agent_id: &AgentId) -> PrismaResult<Option<AgentState>>;

    /// Set the state of an agent
    async fn set_state(&self, agent_id: &AgentId, state: AgentState) -> PrismaResult<()>;

    /// Get all agents in a specific state
    async fn get_agents_by_state(&self, state: AgentState) -> PrismaResult<Vec<AgentId>>;

    /// Update the last active timestamp for an agent
    async fn update_last_active(&self, agent_id: &AgentId) -> PrismaResult<()>;
}

/// Trait for managing agent capabilities
#[async_trait]
pub trait CapabilityManager: Send + Sync {
    /// Add a capability to an agent
    async fn add_capability(&self, agent_id: &AgentId, capability: AgentCapability) -> PrismaResult<()>;

    /// Remove a capability from an agent
    async fn remove_capability(&self, agent_id: &AgentId, capability: &AgentCapability) -> PrismaResult<()>;

    /// Check if an agent has a specific capability
    async fn has_capability(&self, agent_id: &AgentId, capability: &AgentCapability) -> PrismaResult<bool>;

    /// Get all capabilities for an agent
    async fn get_capabilities(&self, agent_id: &AgentId) -> PrismaResult<Vec<AgentCapability>>;

    /// Get all agents with a specific capability
    async fn get_agents_with_capability(&self, capability: &AgentCapability) -> PrismaResult<Vec<AgentId>>;
}

/// Trait for agent communication
#[async_trait]
pub trait AgentCommunicator: Send + Sync {
    /// Send a message from one agent to another agent, human, or broadcast to multiple recipients
    async fn send_message(&self, message: AgentMessage) -> PrismaResult<()>;

    /// Send a message from one agent to another
    async fn send_agent_to_agent(&self, from: &AgentId, to: &AgentId, content: String, priority: MessagePriority) -> PrismaResult<()>;

    /// Send a message from an agent to a human
    async fn send_agent_to_human(&self, from: &AgentId, to: &HumanId, content: String, priority: MessagePriority) -> PrismaResult<()>;

    /// Broadcast a message to multiple agents
    async fn broadcast_to_agents(&self, from: &AgentId, to: &[AgentId], content: String, priority: MessagePriority) -> PrismaResult<()>;

    /// Broadcast a message to multiple humans
    async fn broadcast_to_humans(&self, from: &AgentId, to: &[HumanId], content: String, priority: MessagePriority) -> PrismaResult<()>;

    /// Get all messages sent to an agent
    async fn get_messages(&self, agent_id: &AgentId) -> PrismaResult<Vec<AgentMessage>>;

    /// Get all messages sent to a human
    async fn get_human_messages(&self, human_id: &HumanId) -> PrismaResult<Vec<AgentMessage>>;

    /// Get all messages between two agents
    async fn get_agent_conversation(&self, agent1: &AgentId, agent2: &AgentId) -> PrismaResult<Vec<AgentMessage>>;

    /// Get all messages between an agent and a human
    async fn get_human_conversation(&self, agent_id: &AgentId, human_id: &HumanId) -> PrismaResult<Vec<AgentMessage>>;

    /// Configure whether an agent can initiate communication with humans
    async fn set_human_communication_enabled(&self, agent_id: &AgentId, enabled: bool) -> PrismaResult<()>;

    /// Check if an agent can initiate communication with humans
    async fn can_communicate_with_humans(&self, agent_id: &AgentId) -> PrismaResult<bool>;
}
