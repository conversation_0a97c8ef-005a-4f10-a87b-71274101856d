// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/agent_manager/model_metadata.rs
// =================================================================================================
// Purpose: Implements the ModelMetadataManagerTrait for managing LLM model metadata.
// This file contains the implementation of the ModelMetadataManager struct that extracts,
// stores, and retrieves metadata from LLM models.
//
// Integration:
// - Internal Dependencies:
//   - agent_manager/mod.rs: Re-exports this module
//   - agent_manager/model_metadata_trait.rs: Defines the trait implemented here
//
// - External Dependencies:
//   - llm/interface/ModelMetadata: Used for extracting metadata from models
//
// Platform Considerations:
// - This module is platform-independent
// =================================================================================================

use std::collections::HashMap;
use tracing::{debug, info, warn};
use serde_json::{Value, json};
use async_trait::async_trait;

use crate::err::PrismaResult;
use crate::err::GenericError;
use crate::llm::interface::ModelMetadata;
use super::model_metadata_trait::ModelMetadataManagerTrait;

/// Metadata manager for LLM models
#[derive(Debug)]
pub struct ModelMetadataManager {
    /// Cache of model metadata
    metadata_cache: HashMap<String, Value>,
}

impl ModelMetadataManager {
    /// Creates a new ModelMetadataManager
    pub fn new() -> Self {
        info!("Initializing ModelMetadataManager");

        Self {
            metadata_cache: HashMap::new(),
        }
    }

    /// Extracts metadata from a model
    pub fn extract_metadata<T: ModelMetadata>(&mut self, model: &T, model_id: &str) -> PrismaResult<Value> {
        debug!("Extracting metadata for model: {}", model_id);

        // Check if metadata is already cached
        if let Some(metadata) = self.metadata_cache.get(model_id) {
            debug!("Using cached metadata for model: {}", model_id);
            return Ok(metadata.clone());
        }

        // Get model description
        let model_desc = match model.model_desc(1024) {
            Ok(desc) => desc,
            Err(e) => {
                warn!("Failed to get model description: {}", e);
                "Unknown model".to_string()
            }
        };

        // Get metadata count
        let meta_count = model.model_meta_count();
        debug!("Model has {} metadata entries", meta_count);

        // Extract all metadata
        let mut metadata = HashMap::new();
        metadata.insert("model_desc".to_string(), json!(model_desc));

        for i in 0..meta_count {
            // Get key
            let key = match model.model_meta_key_by_index(i, 1024) {
                Ok(key) => key,
                Err(e) => {
                    warn!("Failed to get metadata key at index {}: {}", i, e);
                    continue;
                }
            };

            // Get value
            let value = match model.model_meta_val_str_by_index(i, 1024) {
                Ok(value) => value,
                Err(e) => {
                    warn!("Failed to get metadata value at index {}: {}", i, e);
                    continue;
                }
            };

            metadata.insert(key, json!(value));
        }

        // Convert to JSON
        let metadata_json = json!(metadata);

        // Cache the metadata
        self.metadata_cache.insert(model_id.to_string(), metadata_json.clone());

        Ok(metadata_json)
    }

    /// Gets metadata for a model by ID
    pub fn get_metadata(&self, model_id: &str) -> Option<Value> {
        self.metadata_cache.get(model_id).cloned()
    }

    /// Gets a specific metadata field for a model
    pub fn get_metadata_field(&self, model_id: &str, field: &str) -> Option<Value> {
        if let Some(metadata) = self.metadata_cache.get(model_id) {
            if let Some(obj) = metadata.as_object() {
                if let Some(field_value) = obj.get(field) {
                    return Some(field_value.clone());
                }
            }
        }
        None
    }

    /// Clears the metadata cache
    pub fn clear_cache(&mut self) {
        debug!("Clearing metadata cache");
        self.metadata_cache.clear();
    }

    /// Removes a model from the metadata cache
    pub fn remove_from_cache(&mut self, model_id: &str) {
        debug!("Removing model {} from metadata cache", model_id);
        self.metadata_cache.remove(model_id);
    }
}

#[async_trait]
impl ModelMetadataManagerTrait for ModelMetadataManager {
    async fn extract_metadata<T: ModelMetadata + Send + Sync + 'static>(
        &self,
        model: &T,
        model_id: &str
    ) -> PrismaResult<Value> {
        // Since we can't easily move the model reference into a closure for tokio::spawn_blocking,
        // we'll extract the metadata here directly.

        // Get model description
        let model_desc = match model.model_desc(1024) {
            Ok(desc) => desc,
            Err(e) => {
                tracing::warn!("Failed to get model description: {}", e);
                "Unknown model".to_string()
            }
        };

        // Get metadata count
        let meta_count = model.model_meta_count();
        tracing::debug!("Model has {} metadata entries", meta_count);

        // Extract all metadata
        let mut metadata = std::collections::HashMap::new();
        metadata.insert("model_desc".to_string(), serde_json::json!(model_desc));

        for i in 0..meta_count {
            // Get key
            let key = match model.model_meta_key_by_index(i, 1024) {
                Ok(key) => key,
                Err(e) => {
                    tracing::warn!("Failed to get metadata key at index {}: {}", i, e);
                    continue;
                }
            };

            // Get value
            let value = match model.model_meta_val_str_by_index(i, 1024) {
                Ok(value) => value,
                Err(e) => {
                    tracing::warn!("Failed to get metadata value at index {}: {}", i, e);
                    continue;
                }
            };

            metadata.insert(key, serde_json::json!(value));
        }

        // Convert to JSON
        let metadata_json = serde_json::json!(metadata);

        // Cache the metadata in a mutable copy of self
        let mut manager = self.clone();
        manager.metadata_cache.insert(model_id.to_string(), metadata_json.clone());

        Ok(metadata_json)
    }

    async fn get_model_metadata(&self, model_id: &str) -> PrismaResult<Option<Value>> {
        Ok(self.get_metadata(model_id))
    }

    async fn get_metadata_field(&self, model_id: &str, field: &str) -> PrismaResult<Option<Value>> {
        Ok(self.get_metadata_field(model_id, field))
    }

    async fn clear_cache(&mut self) -> PrismaResult<()> {
        self.clear_cache();
        Ok(())
    }

    async fn remove_from_cache(&self, model_id: &str) -> PrismaResult<()> {
        // Create a mutable copy of self to use the existing implementation
        let mut manager = self.clone();
        manager.remove_from_cache(model_id);
        Ok(())
    }

    async fn get_all_model_ids(&self) -> PrismaResult<Vec<String>> {
        Ok(self.metadata_cache.keys().cloned().collect())
    }

    async fn extract_capabilities(&self, model_id: &str) -> PrismaResult<Vec<String>> {
        let metadata = self.get_metadata(model_id).ok_or_else(|| {
            GenericError::from(format!("Metadata for model {} not found", model_id))
        })?;

        Ok(extract_model_capabilities(&metadata))
    }

    async fn extract_recommended_roles(&self, model_id: &str) -> PrismaResult<Vec<String>> {
        let metadata = self.get_metadata(model_id).ok_or_else(|| {
            GenericError::from(format!("Metadata for model {} not found", model_id))
        })?;

        Ok(extract_recommended_roles(&metadata))
    }

    async fn extract_parameters(&self, model_id: &str) -> PrismaResult<HashMap<String, Value>> {
        let metadata = self.get_metadata(model_id).ok_or_else(|| {
            GenericError::from(format!("Metadata for model {} not found", model_id))
        })?;

        Ok(extract_model_parameters(&metadata))
    }
}

// Make ModelMetadataManager cloneable
impl Clone for ModelMetadataManager {
    fn clone(&self) -> Self {
        Self {
            metadata_cache: self.metadata_cache.clone(),
        }
    }
}

/// Helper function to extract model capabilities from metadata
pub fn extract_model_capabilities(metadata: &Value) -> Vec<String> {
    let mut capabilities = Vec::new();

    // Check if metadata contains capability information
    if let Some(obj) = metadata.as_object() {
        // Check for general capabilities
        if let Some(general_caps) = obj.get("general_capabilities") {
            if let Some(caps_array) = general_caps.as_array() {
                for cap in caps_array {
                    if let Some(cap_str) = cap.as_str() {
                        capabilities.push(cap_str.to_string());
                    }
                }
            } else if let Some(caps_str) = general_caps.as_str() {
                // Handle comma-separated string
                for cap in caps_str.split(',') {
                    capabilities.push(cap.trim().to_string());
                }
            }
        }

        // Check for specific capabilities
        for (key, value) in obj {
            if key.ends_with("_capability") && value.as_bool() == Some(true) {
                let cap_name = key.replace("_capability", "");
                capabilities.push(cap_name);
            }
        }

        // Check for context size
        if let Some(ctx_size) = obj.get("context_length") {
            if let Some(size) = ctx_size.as_u64() {
                if size >= 8192 {
                    capabilities.push("LongContext".to_string());
                }
            }
        }
    }

    capabilities
}

/// Helper function to extract recommended roles from metadata
pub fn extract_recommended_roles(metadata: &Value) -> Vec<String> {
    let mut roles = Vec::new();

    // Check if metadata contains role information
    if let Some(obj) = metadata.as_object() {
        // Check for recommended roles
        if let Some(recommended_roles) = obj.get("recommended_roles") {
            if let Some(roles_array) = recommended_roles.as_array() {
                for role in roles_array {
                    if let Some(role_str) = role.as_str() {
                        roles.push(role_str.to_string());
                    }
                }
            } else if let Some(roles_str) = recommended_roles.as_str() {
                // Handle comma-separated string
                for role in roles_str.split(',') {
                    roles.push(role.trim().to_string());
                }
            }
        }
    }

    roles
}

/// Helper function to extract model parameters from metadata
pub fn extract_model_parameters(metadata: &Value) -> HashMap<String, Value> {
    let mut params = HashMap::new();

    // Check if metadata contains parameter information
    if let Some(obj) = metadata.as_object() {
        // Extract common parameters
        let param_keys = [
            "context_length", "embedding_length", "vocab_size",
            "n_layers", "n_heads", "n_gpu_layers", "rope_freq_base",
            "rope_freq_scale", "tensor_split"
        ];

        for key in param_keys.iter() {
            if let Some(value) = obj.get(*key) {
                params.insert(key.to_string(), value.clone());
            }
        }
    }

    params
}
