// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/types.rs
// =================================================================================================
// Purpose: Defines the core types used throughout the agent_manager module.
// This file contains the fundamental data structures that represent the domain model of the
// agent_manager module, including agent states, roles, capabilities, and message types.
// These types form the foundation of the agent management system.
// =================================================================================================
// Internal Dependencies:
// - None (this file is a dependency for other files)
// =================================================================================================
// External Dependencies:
// - serde: For serialization and deserialization
// - std::fmt: For formatting and display
// =================================================================================================
// Module Interactions:
// - Used by all other files in the agent_manager module
// - Provides the core data structures for agent representation
// - Defines the domain model for agent management
// - Establishes the type system for agent-related concepts
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::fmt;

/// Type alias for agent identifiers
pub type AgentId = String;

/// Represents the current state of an agent
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AgentState {
    /// Agent is being initialized
    Initializing,

    /// Agent is active and ready to process tasks
    Active,

    /// Agent is paused and won't process new tasks
    Paused,

    /// Agent is terminated and can't be reactivated
    Terminated,
}

impl fmt::Display for AgentState {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AgentState::Initializing => write!(f, "Initializing"),
            AgentState::Active => write!(f, "Active"),
            AgentState::Paused => write!(f, "Paused"),
            AgentState::Terminated => write!(f, "Terminated"),
        }
    }
}

/// Represents a role that an agent can have
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct AgentRole {
    /// Name of the role
    pub name: String,

    /// Description of the role
    pub description: Option<String>,

    /// Capabilities associated with this role
    pub capabilities: Vec<AgentCapability>,
}

impl AgentRole {
    /// Creates a new role with the given name
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            description: None,
            capabilities: Vec::new(),
        }
    }

    /// Creates a new role with the given name and description
    pub fn with_description(name: impl Into<String>, description: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            description: Some(description.into()),
            capabilities: Vec::new(),
        }
    }

    /// Creates a new role with the given name and capabilities
    pub fn with_capabilities(name: impl Into<String>, capabilities: Vec<AgentCapability>) -> Self {
        Self {
            name: name.into(),
            description: None,
            capabilities,
        }
    }

    /// Creates a new role with the given name, description, and capabilities
    pub fn with_all(
        name: impl Into<String>,
        description: impl Into<String>,
        capabilities: Vec<AgentCapability>,
    ) -> Self {
        Self {
            name: name.into(),
            description: Some(description.into()),
            capabilities,
        }
    }

    /// Creates a role with Assistant capabilities (for internal use only)
    ///
    /// Note: This is provided for backward compatibility and testing.
    /// In production, roles should be created from the UI.
    pub fn assistant() -> Self {
        Self::with_capabilities(
            "Assistant",
            vec![AgentCapability::ToolUse, AgentCapability::AgentCommunication],
        )
    }

    /// Creates a role with Coder capabilities (for internal use only)
    ///
    /// Note: This is provided for backward compatibility and testing.
    /// In production, roles should be created from the UI.
    pub fn coder() -> Self {
        Self::with_capabilities(
            "Coder",
            vec![AgentCapability::FileSystem, AgentCapability::ToolUse],
        )
    }

    /// Creates a role with Analyst capabilities (for internal use only)
    ///
    /// Note: This is provided for backward compatibility and testing.
    /// In production, roles should be created from the UI.
    pub fn analyst() -> Self {
        Self::with_capabilities(
            "Analyst",
            vec![AgentCapability::Database, AgentCapability::ToolUse],
        )
    }

    /// Creates a role with Writer capabilities (for internal use only)
    ///
    /// Note: This is provided for backward compatibility and testing.
    /// In production, roles should be created from the UI.
    pub fn writer() -> Self {
        Self::with_capabilities(
            "Writer",
            vec![AgentCapability::ToolUse],
        )
    }

    /// Creates a role with Researcher capabilities (for internal use only)
    ///
    /// Note: This is provided for backward compatibility and testing.
    /// In production, roles should be created from the UI.
    pub fn researcher() -> Self {
        Self::with_capabilities(
            "Researcher",
            vec![AgentCapability::Network, AgentCapability::ToolUse],
        )
    }

    /// Converts a string to an AgentCapability
    pub fn capability_from_string(capability_str: &str) -> AgentCapability {
        AgentCapability::new(capability_str)
    }
}

impl fmt::Display for AgentRole {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name)
    }
}

/// Represents a capability that an agent can have
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AgentCapability {
    /// Access to file system operations
    FileSystem,

    /// Access to network operations
    Network,

    /// Access to database operations
    Database,

    /// Ability to execute code
    CodeExecution,

    /// Ability to use external tools
    ToolUse,

    /// Ability to communicate with other agents
    AgentCommunication,

    /// Custom capability with specific description
    Custom(String),
}

impl AgentCapability {
    /// Creates a new AgentCapability from a string
    pub fn new(capability: &str) -> Self {
        match capability {
            "FileSystem" => AgentCapability::FileSystem,
            "Network" => AgentCapability::Network,
            "Database" => AgentCapability::Database,
            "CodeExecution" => AgentCapability::CodeExecution,
            "ToolUse" => AgentCapability::ToolUse,
            "AgentCommunication" => AgentCapability::AgentCommunication,
            _ => AgentCapability::Custom(capability.to_string()),
        }
    }

    /// Gets the name of the capability
    pub fn name(&self) -> String {
        match self {
            AgentCapability::FileSystem => "FileSystem".to_string(),
            AgentCapability::Network => "Network".to_string(),
            AgentCapability::Database => "Database".to_string(),
            AgentCapability::CodeExecution => "CodeExecution".to_string(),
            AgentCapability::ToolUse => "ToolUse".to_string(),
            AgentCapability::AgentCommunication => "AgentCommunication".to_string(),
            AgentCapability::Custom(cap) => cap.clone(),
        }
    }
}

impl fmt::Display for AgentCapability {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AgentCapability::FileSystem => write!(f, "FileSystem"),
            AgentCapability::Network => write!(f, "Network"),
            AgentCapability::Database => write!(f, "Database"),
            AgentCapability::CodeExecution => write!(f, "CodeExecution"),
            AgentCapability::ToolUse => write!(f, "ToolUse"),
            AgentCapability::AgentCommunication => write!(f, "AgentCommunication"),
            AgentCapability::Custom(cap) => write!(f, "Custom({})", cap),
        }
    }
}

/// Type alias for human user identifiers
pub type HumanId = String;

/// Represents the priority level of a message
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MessagePriority {
    /// Low priority - informational only
    Low,

    /// Normal priority - standard messages
    Normal,

    /// High priority - important messages
    High,

    /// Urgent priority - critical messages requiring immediate attention
    Urgent,
}

impl Default for MessagePriority {
    fn default() -> Self {
        Self::Normal
    }
}

/// Represents the type of message recipient
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum RecipientType {
    /// Message to an agent
    Agent(AgentId),

    /// Message to a human user
    Human(HumanId),

    /// Message to multiple recipients
    Broadcast(Vec<String>),
}

/// Represents a message that can be sent between agents or to humans
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentMessage {
    /// Unique identifier for the message
    pub id: String,

    /// Agent that sent the message
    pub from: AgentId,

    /// Recipient of the message (agent, human, or broadcast)
    pub to: RecipientType,

    /// Content of the message
    pub content: String,

    /// Priority of the message
    #[serde(default)]
    pub priority: MessagePriority,

    /// When the message was created
    pub timestamp: chrono::DateTime<chrono::Utc>,

    /// Optional metadata for the message
    pub metadata: Option<serde_json::Value>,
}
