// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/generics.rs
// =================================================================================================
// Purpose: Contains generic implementations and utilities for the agent_manager module.
// This file provides reusable components like thread-safe caches and rate limiters that are
// used throughout the agent_manager module. These utilities help with common tasks like
// caching, concurrency control, and resource management.
// =================================================================================================
// Internal Dependencies:
// - None (this file is a dependency for other files)
// =================================================================================================
// External Dependencies:
// - std::collections::HashMap: For storing cache data
// - std::hash::Hash: For hashable keys in caches
// - std::sync::Arc: For shared ownership
// - tokio::sync::RwLock: For thread-safe access to shared data
// - crate::err::PrismaResult: For error handling
// =================================================================================================
// Module Interactions:
// - Used by agent_capabilities.rs for caching agent capabilities
// - Used by agent_registry.rs for caching agent information
// - Used by agent_state.rs for caching agent states
// - Provides thread-safe data structures for concurrent access
// - Implements rate limiting for resource-intensive operations
// =================================================================================================

use std::collections::HashMap;
use std::hash::Hash;
use std::sync::Arc;

use tokio::sync::RwLock;

use crate::err::PrismaResult;

/// A thread-safe cache for storing and retrieving values by key
#[derive(Debug)]
pub struct Cache<K, V>
where
    K: Eq + Hash + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static + std::fmt::Debug,
{
    data: Arc<RwLock<HashMap<K, V>>>,
}

impl<K, V> Cache<K, V>
where
    K: Eq + Hash + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static + std::fmt::Debug,
{
    /// Creates a new empty cache
    pub fn new() -> Self {
        Self {
            data: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Gets a value from the cache by key
    pub async fn get(&self, key: &K) -> Option<V> {
        let data = self.data.read().await;
        data.get(key).cloned()
    }

    /// Inserts a value into the cache
    pub async fn insert(&self, key: K, value: V) -> PrismaResult<()> {
        let mut data = self.data.write().await;
        data.insert(key, value);
        Ok(())
    }

    /// Removes a value from the cache
    pub async fn remove(&self, key: &K) -> PrismaResult<bool> {
        let mut data = self.data.write().await;
        Ok(data.remove(key).is_some())
    }

    /// Clears the entire cache
    pub async fn clear(&self) -> PrismaResult<()> {
        let mut data = self.data.write().await;
        data.clear();
        Ok(())
    }

    /// Gets all keys in the cache
    pub async fn keys(&self) -> PrismaResult<Vec<K>> {
        let data = self.data.read().await;
        Ok(data.keys().cloned().collect())
    }

    /// Gets all values in the cache
    pub async fn values(&self) -> PrismaResult<Vec<V>> {
        let data = self.data.read().await;
        Ok(data.values().cloned().collect())
    }

    /// Gets the number of items in the cache
    pub async fn len(&self) -> PrismaResult<usize> {
        let data = self.data.read().await;
        Ok(data.len())
    }

    /// Checks if the cache is empty
    pub async fn is_empty(&self) -> PrismaResult<bool> {
        let data = self.data.read().await;
        Ok(data.is_empty())
    }
}

impl<K, V> Default for Cache<K, V>
where
    K: Eq + Hash + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static + std::fmt::Debug,
{
    fn default() -> Self {
        Self::new()
    }
}

/// A utility for rate limiting operations
pub struct RateLimiter {
    max_requests: usize,
    time_window_ms: u64,
    requests: Arc<RwLock<Vec<std::time::Instant>>>,
}

impl RateLimiter {
    /// Creates a new rate limiter
    pub fn new(max_requests: usize, time_window_ms: u64) -> Self {
        Self {
            max_requests,
            time_window_ms,
            requests: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Checks if an operation should be allowed based on rate limits
    pub async fn allow_request(&self) -> PrismaResult<bool> {
        let now = std::time::Instant::now();
        let window_start = now - std::time::Duration::from_millis(self.time_window_ms);

        let mut requests = self.requests.write().await;

        // Remove old requests outside the time window
        requests.retain(|time| *time >= window_start);

        // Check if we're under the limit
        if requests.len() < self.max_requests {
            requests.push(now);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Resets the rate limiter
    pub async fn reset(&self) -> PrismaResult<()> {
        let mut requests = self.requests.write().await;
        requests.clear();
        Ok(())
    }
}
