// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/agents.rs
// =================================================================================================
// Purpose: Defines the comprehensive Agent struct that represents an agent in the system.
// This file contains the core Agent data structure and its methods for managing an agent's
// lifecycle, state, roles, capabilities, and configuration. It serves as the central
// representation of agents within the PrismaAI system.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses AgentId, AgentState, AgentRole, and AgentCapability types
// =================================================================================================
// External Dependencies:
// - crate::SeqId: For sequence ID management
// - chrono: For timestamp handling
// - serde: For serialization and deserialization
// - std::collections::HashSet: For storing capabilities
// - tracing: For logging
// - crate::llm::interface::ModelMetadata: For LLM model metadata
// =================================================================================================
// Module Interactions:
// - Used by agent_registry.rs to create and manage agents
// - Used by agent_state.rs to track agent state
// - Used by agent_capabilities.rs to manage agent capabilities
// - Used by agent_communication.rs to handle agent communication
// - Used by config_integration.rs to load and save agent configurations
// - Used by ui_integration.rs to present agent information to the UI
// =================================================================================================

use crate::SeqId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::sync::Arc;
use tracing::{debug, info};

// Import necessary modules
use super::types::{AgentId, AgentState, AgentRole, AgentCapability};

// Import LLM metadata interface
use crate::llm::interface::ModelMetadata;

/// Configuration for an agent loaded from pm.toml
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AgentConfig {
    /// Background information for the agent
    pub background: Option<String>,

    /// Tasks assigned to the agent
    pub tasks: Option<Vec<String>>,

    /// Rules the agent should follow
    pub rules: Option<Vec<String>>,

    /// Skills the agent possesses
    pub skills: Option<Vec<String>>,

    /// Tool configuration for the agent
    pub tools: Option<serde_json::Value>,

    /// RAG document configuration for the agent
    pub rag_config: Option<serde_json::Value>,
}

/// Default value for whether an agent can communicate with humans
fn default_human_communication() -> bool {
    false // Default to false for safety
}

/// Represents a complete agent in the PrismaAI system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Agent {
    /// Unique identifier for the agent
    pub id: AgentId,

    /// Sequence ID for LLM context management
    pub seq_id: SeqId,

    /// Human-readable name of the agent
    pub name: String,

    /// Optional description of the agent's purpose
    pub description: Option<String>,

    /// Current state of the agent (active, paused, etc.)
    pub state: AgentState,

    /// The agent's roles in the system
    pub roles: Vec<AgentRole>,

    /// Set of capabilities this agent has access to
    pub capabilities: HashSet<AgentCapability>,

    /// When the agent was created
    pub created_at: DateTime<Utc>,

    /// When the agent was last active
    pub last_active: DateTime<Utc>,

    /// Preferred LLM model for this agent
    pub model_preference: Option<String>,

    /// Template key for prompt construction
    pub template_key: Option<String>,

    /// Model metadata for this agent
    #[serde(skip)]
    pub model_metadata: Option<serde_json::Value>,

    /// Whether this agent can initiate communication with humans
    #[serde(default = "default_human_communication")]
    pub can_communicate_with_humans: bool,

    /// Configuration for this agent (from pm.toml)
    #[serde(default)]
    pub config: AgentConfig,
}

impl Agent {
    /// Creates a new agent with minimal required information and a single role
    pub fn new(id: AgentId, seq_id: SeqId, name: String, role: AgentRole) -> Self {
        let now = Utc::now();
        info!("Creating new agent: {} ({})", name, id);

        // Add capabilities from the role
        let mut capabilities = HashSet::new();
        for capability in &role.capabilities {
            capabilities.insert(capability.clone());
        }

        Self {
            id,
            seq_id,
            name,
            description: None,
            state: AgentState::Initializing,
            roles: vec![role],
            capabilities,
            created_at: now,
            last_active: now,
            model_preference: None,
            template_key: None,
            model_metadata: None,
            can_communicate_with_humans: default_human_communication(),
            config: AgentConfig::default(),
        }
    }

    /// Creates a new agent with multiple roles
    pub fn with_roles(id: AgentId, seq_id: SeqId, name: String, roles: Vec<AgentRole>) -> Self {
        let now = Utc::now();
        info!("Creating new agent with multiple roles: {} ({})", name, id);

        // Add capabilities from all roles
        let mut capabilities = HashSet::new();
        for role in &roles {
            for capability in &role.capabilities {
                capabilities.insert(capability.clone());
            }
        }

        Self {
            id,
            seq_id,
            name,
            description: None,
            state: AgentState::Initializing,
            roles,
            capabilities,
            created_at: now,
            last_active: now,
            model_preference: None,
            template_key: None,
            model_metadata: None,
            can_communicate_with_humans: default_human_communication(),
            config: AgentConfig::default(),
        }
    }

    /// Adds a role to the agent
    pub fn add_role(&mut self, role: AgentRole) {
        debug!("Adding role {} to agent {}", role.name, self.id);

        // Add capabilities from the role
        for capability in &role.capabilities {
            self.capabilities.insert(capability.clone());
        }

        self.roles.push(role);
        self.update_last_active();
    }

    /// Removes a role from the agent by name
    pub fn remove_role(&mut self, role_name: &str) -> bool {
        let initial_count = self.roles.len();

        // Remove the role
        self.roles.retain(|r| r.name != role_name);

        // If a role was removed, recalculate capabilities
        if self.roles.len() < initial_count {
            debug!("Removed role {} from agent {}", role_name, self.id);

            // Recalculate capabilities from remaining roles
            self.capabilities.clear();
            for role in &self.roles {
                for capability in &role.capabilities {
                    self.capabilities.insert(capability.clone());
                }
            }

            self.update_last_active();
            return true;
        }

        false
    }

    /// Checks if the agent has a role with the given name
    pub fn has_role(&self, role_name: &str) -> bool {
        self.roles.iter().any(|r| r.name == role_name)
    }

    /// Gets all role names
    pub fn get_role_names(&self) -> Vec<String> {
        self.roles.iter().map(|r| r.name.clone()).collect()
    }

    /// Activates the agent, setting its state to Active
    pub fn activate(&mut self) {
        debug!("Activating agent: {}", self.id);
        self.state = AgentState::Active;
        self.update_last_active();
    }

    /// Pauses the agent, setting its state to Paused
    pub fn pause(&mut self) {
        debug!("Pausing agent: {}", self.id);
        self.state = AgentState::Paused;
    }

    /// Terminates the agent, setting its state to Terminated
    pub fn terminate(&mut self) {
        debug!("Terminating agent: {}", self.id);
        self.state = AgentState::Terminated;
    }

    /// Adds a capability to the agent
    pub fn add_capability(&mut self, capability: AgentCapability) {
        debug!("Adding capability {:?} to agent {}", capability, self.id);
        self.capabilities.insert(capability);
    }

    /// Checks if the agent has a specific capability
    pub fn has_capability(&self, capability: &AgentCapability) -> bool {
        self.capabilities.contains(capability)
    }

    /// Updates the last active timestamp to now
    pub fn update_last_active(&mut self) {
        self.last_active = Utc::now();
    }

    /// Sets the agent's preferred model
    pub fn set_model_preference(&mut self, model: String) {
        debug!("Setting model preference for agent {}: {}", self.id, model);
        self.model_preference = Some(model);
    }

    /// Sets the agent's template key for prompt construction
    pub fn set_template_key(&mut self, key: String) {
        debug!("Setting template key for agent {}: {}", self.id, key);
        self.template_key = Some(key);
    }

    /// Sets the model metadata for this agent
    pub fn set_model_metadata(&mut self, metadata: serde_json::Value) {
        debug!("Setting model metadata for agent {}", self.id);
        self.model_metadata = Some(metadata);
    }

    /// Gets the model metadata for this agent
    pub fn get_model_metadata(&self) -> Option<&serde_json::Value> {
        self.model_metadata.as_ref()
    }

    /// Sets whether this agent can communicate with humans
    pub fn set_human_communication(&mut self, enabled: bool) {
        debug!("Setting human communication for agent {} to {}", self.id, enabled);
        self.can_communicate_with_humans = enabled;
    }

    /// Checks if this agent can communicate with humans
    pub fn can_communicate_with_humans(&self) -> bool {
        self.can_communicate_with_humans
    }

    /// Updates the agent's configuration from pm.toml
    pub fn update_config(&mut self, config: AgentConfig) {
        debug!("Updating configuration for agent {}", self.id);
        self.config = config;
    }

    /// Gets the agent's background information
    pub fn get_background(&self) -> Option<&String> {
        self.config.background.as_ref()
    }

    /// Sets the agent's background information
    pub fn set_background(&mut self, background: String) {
        debug!("Setting background for agent {}", self.id);
        self.config.background = Some(background);
    }

    /// Gets the agent's tasks
    pub fn get_tasks(&self) -> Option<&Vec<String>> {
        self.config.tasks.as_ref()
    }

    /// Sets the agent's tasks
    pub fn set_tasks(&mut self, tasks: Vec<String>) {
        debug!("Setting tasks for agent {}", self.id);
        self.config.tasks = Some(tasks);
    }

    /// Gets the agent's rules
    pub fn get_rules(&self) -> Option<&Vec<String>> {
        self.config.rules.as_ref()
    }

    /// Sets the agent's rules
    pub fn set_rules(&mut self, rules: Vec<String>) {
        debug!("Setting rules for agent {}", self.id);
        self.config.rules = Some(rules);
    }

    /// Gets the agent's skills
    pub fn get_skills(&self) -> Option<&Vec<String>> {
        self.config.skills.as_ref()
    }

    /// Sets the agent's skills
    pub fn set_skills(&mut self, skills: Vec<String>) {
        debug!("Setting skills for agent {}", self.id);
        self.config.skills = Some(skills);
    }

    /// Gets the agent's tool configuration
    pub fn get_tools_config(&self) -> Option<&serde_json::Value> {
        self.config.tools.as_ref()
    }

    /// Sets the agent's tool configuration
    pub fn set_tools_config(&mut self, tools: serde_json::Value) {
        debug!("Setting tool configuration for agent {}", self.id);
        self.config.tools = Some(tools);
    }

    /// Gets the agent's RAG configuration
    pub fn get_rag_config(&self) -> Option<&serde_json::Value> {
        self.config.rag_config.as_ref()
    }

    /// Sets the agent's RAG configuration
    pub fn set_rag_config(&mut self, rag_config: serde_json::Value) {
        debug!("Setting RAG configuration for agent {}", self.id);
        self.config.rag_config = Some(rag_config);
    }

    /// Updates the agent's capabilities based on its roles
    pub fn update_capabilities_from_roles(&mut self) {
        debug!("Updating capabilities from roles for agent {}", self.id);

        // Clear existing capabilities
        self.capabilities.clear();

        // Add capabilities from all roles
        for role in &self.roles {
            for capability in &role.capabilities {
                self.capabilities.insert(capability.clone());
            }
        }

        debug!("Agent {} now has {} capabilities", self.id, self.capabilities.len());
    }
}