// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/config_integration.rs
// =================================================================================================
// Purpose: Implements the configuration integration for the agent_manager module.
// This file handles loading and saving agent definitions from/to pm.toml and prompt_template.toml.
// It provides functionality for managing agent configurations, prompt templates, and project
// settings through configuration files.
// =================================================================================================
// Internal Dependencies:
// - agents.rs: Uses Agent and AgentConfig types
// - types.rs: Uses AgentId, AgentRole, and AgentCapability types
// =================================================================================================
// External Dependencies:
// - std::collections::HashMap: For storing configuration mappings
// - std::fs: For file I/O operations
// - std::path: For file path handling
// - tokio::sync::RwLock: For thread-safe access to shared data
// - tracing: For logging
// - serde: For serialization and deserialization
// - toml: For parsing and generating TOML files
// - crate::err: For error handling
// =================================================================================================
// Module Interactions:
// - Used by AgentManager to load and save agent configurations
// - Provides prompt templates for agent interactions
// - Manages project-level settings in pm.toml
// - Supports persistence of agent definitions across system restarts
// - Enables dynamic configuration of agents at runtime
// =================================================================================================

use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use serde::{Deserialize, Serialize};
use toml::{Table, Value};

use crate::err::PrismaResult;
use crate::err::GenericError;

use super::agents::{Agent, AgentConfig};
use super::types::{AgentId, AgentRole, AgentCapability};

/// Configuration for agents loaded from pm.toml
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PmConfig {
    /// Project name
    pub project_name: Option<String>,

    /// Project description
    pub project_description: Option<String>,

    /// Project specifications
    pub project_specifications: Option<Vec<String>>,

    /// User name
    pub user_name: Option<String>,

    /// Agents configuration
    pub agents: Option<HashMap<String, AgentConfig>>,

    /// Other configuration fields
    #[serde(flatten)]
    pub other: HashMap<String, Value>,
}

impl Default for PmConfig {
    fn default() -> Self {
        Self {
            project_name: None,
            project_description: None,
            project_specifications: None,
            user_name: None,
            agents: Some(HashMap::new()),
            other: HashMap::new(),
        }
    }
}

/// Configuration for prompt templates loaded from prompt_template.toml
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptTemplateConfig {
    /// Templates for different agent types
    pub templates: HashMap<String, PromptTemplate>,

    /// Other configuration fields
    #[serde(flatten)]
    pub other: HashMap<String, Value>,
}

impl Default for PromptTemplateConfig {
    fn default() -> Self {
        Self {
            templates: HashMap::new(),
            other: HashMap::new(),
        }
    }
}

/// Prompt template for an agent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptTemplate {
    /// Template content
    pub content: String,

    /// Template variables
    pub variables: Option<HashMap<String, String>>,

    /// Other template fields
    #[serde(flatten)]
    pub other: HashMap<String, Value>,
}

/// Manager for configuration integration
#[derive(Debug)]
pub struct ConfigIntegrationManager {
    /// Path to pm.toml
    pm_path: PathBuf,

    /// Path to prompt_template.toml
    prompt_template_path: PathBuf,

    /// Cached pm.toml configuration
    pm_config: Arc<RwLock<PmConfig>>,

    /// Cached prompt_template.toml configuration
    prompt_template_config: Arc<RwLock<PromptTemplateConfig>>,
}

impl ConfigIntegrationManager {
    /// Creates a new ConfigIntegrationManager
    pub fn new(pm_path: impl AsRef<Path>, prompt_template_path: impl AsRef<Path>) -> Self {
        info!("Initializing ConfigIntegrationManager");

        let pm_path = pm_path.as_ref().to_path_buf();
        let prompt_template_path = prompt_template_path.as_ref().to_path_buf();

        Self {
            pm_path,
            prompt_template_path,
            pm_config: Arc::new(RwLock::new(PmConfig::default())),
            prompt_template_config: Arc::new(RwLock::new(PromptTemplateConfig::default())),
        }
    }

    /// Loads the pm.toml configuration
    pub async fn load_pm_config(&self) -> PrismaResult<()> {
        debug!("Loading pm.toml from {:?}", self.pm_path);

        let content = match fs::read_to_string(&self.pm_path) {
            Ok(content) => content,
            Err(e) => {
                warn!("Failed to read pm.toml: {}", e);
                return Err(GenericError::from(format!("Failed to read pm.toml: {}", e)));
            }
        };

        let config: PmConfig = match toml::from_str(&content) {
            Ok(config) => config,
            Err(e) => {
                error!("Failed to parse pm.toml: {}", e);
                return Err(GenericError::from(format!("Failed to parse pm.toml: {}", e)));
            }
        };

        let mut pm_config = self.pm_config.write().await;
        *pm_config = config;

        debug!("Successfully loaded pm.toml");
        Ok(())
    }

    /// Loads the prompt_template.toml configuration
    pub async fn load_prompt_template_config(&self) -> PrismaResult<()> {
        debug!("Loading prompt_template.toml from {:?}", self.prompt_template_path);

        let content = match fs::read_to_string(&self.prompt_template_path) {
            Ok(content) => content,
            Err(e) => {
                warn!("Failed to read prompt_template.toml: {}", e);
                return Err(GenericError::from(format!("Failed to read prompt_template.toml: {}", e)));
            }
        };

        let config: PromptTemplateConfig = match toml::from_str(&content) {
            Ok(config) => config,
            Err(e) => {
                error!("Failed to parse prompt_template.toml: {}", e);
                return Err(GenericError::from(format!("Failed to parse prompt_template.toml: {}", e)));
            }
        };

        let mut prompt_template_config = self.prompt_template_config.write().await;
        *prompt_template_config = config;

        debug!("Successfully loaded prompt_template.toml");
        Ok(())
    }

    /// Saves the pm.toml configuration
    pub async fn save_pm_config(&self) -> PrismaResult<()> {
        debug!("Saving pm.toml to {:?}", self.pm_path);

        let pm_config = self.pm_config.read().await;

        let content = match toml::to_string_pretty(&*pm_config) {
            Ok(content) => content,
            Err(e) => {
                error!("Failed to serialize pm.toml: {}", e);
                return Err(GenericError::from(format!("Failed to serialize pm.toml: {}", e)));
            }
        };

        match fs::write(&self.pm_path, content) {
            Ok(_) => {
                debug!("Successfully saved pm.toml");
                Ok(())
            },
            Err(e) => {
                error!("Failed to write pm.toml: {}", e);
                Err(GenericError::from(format!("Failed to write pm.toml: {}", e)))
            }
        }
    }

    /// Saves the prompt_template.toml configuration
    pub async fn save_prompt_template_config(&self) -> PrismaResult<()> {
        debug!("Saving prompt_template.toml to {:?}", self.prompt_template_path);

        let prompt_template_config = self.prompt_template_config.read().await;

        let content = match toml::to_string_pretty(&*prompt_template_config) {
            Ok(content) => content,
            Err(e) => {
                error!("Failed to serialize prompt_template.toml: {}", e);
                return Err(GenericError::from(format!("Failed to serialize prompt_template.toml: {}", e)));
            }
        };

        match fs::write(&self.prompt_template_path, content) {
            Ok(_) => {
                debug!("Successfully saved prompt_template.toml");
                Ok(())
            },
            Err(e) => {
                error!("Failed to write prompt_template.toml: {}", e);
                Err(GenericError::from(format!("Failed to write prompt_template.toml: {}", e)))
            }
        }
    }

    /// Gets the agent configuration from pm.toml
    pub async fn get_agent_config(&self, agent_id: &AgentId) -> PrismaResult<Option<AgentConfig>> {
        let pm_config = self.pm_config.read().await;

        if let Some(agents) = &pm_config.agents {
            if let Some(agent_config) = agents.get(agent_id) {
                return Ok(Some(agent_config.clone()));
            }
        }

        Ok(None)
    }

    /// Updates the agent configuration in pm.toml
    pub async fn update_agent_config(&self, agent_id: &AgentId, config: AgentConfig) -> PrismaResult<()> {
        let mut pm_config = self.pm_config.write().await;

        if let Some(agents) = &mut pm_config.agents {
            agents.insert(agent_id.clone(), config);
        } else {
            let mut agents = HashMap::new();
            agents.insert(agent_id.clone(), config);
            pm_config.agents = Some(agents);
        }

        drop(pm_config); // Release the write lock

        self.save_pm_config().await
    }

    /// Removes the agent configuration from pm.toml
    pub async fn remove_agent_config(&self, agent_id: &AgentId) -> PrismaResult<bool> {
        let mut pm_config = self.pm_config.write().await;

        let removed = if let Some(agents) = &mut pm_config.agents {
            agents.remove(agent_id).is_some()
        } else {
            false
        };

        drop(pm_config); // Release the write lock

        if removed {
            self.save_pm_config().await?;
        }

        Ok(removed)
    }

    /// Gets the prompt template for an agent
    pub async fn get_prompt_template(&self, template_key: &str) -> PrismaResult<Option<PromptTemplate>> {
        let prompt_template_config = self.prompt_template_config.read().await;

        if let Some(template) = prompt_template_config.templates.get(template_key) {
            return Ok(Some(template.clone()));
        }

        Ok(None)
    }

    /// Updates the prompt template for an agent
    pub async fn update_prompt_template(&self, template_key: &str, template: PromptTemplate) -> PrismaResult<()> {
        let mut prompt_template_config = self.prompt_template_config.write().await;

        prompt_template_config.templates.insert(template_key.to_string(), template);

        drop(prompt_template_config); // Release the write lock

        self.save_prompt_template_config().await
    }

    /// Removes the prompt template for an agent
    pub async fn remove_prompt_template(&self, template_key: &str) -> PrismaResult<bool> {
        let mut prompt_template_config = self.prompt_template_config.write().await;

        let removed = prompt_template_config.templates.remove(template_key).is_some();

        drop(prompt_template_config); // Release the write lock

        if removed {
            self.save_prompt_template_config().await?;
        }

        Ok(removed)
    }

    /// Loads agent configurations from pm.toml
    pub async fn load_agents(&self) -> PrismaResult<HashMap<AgentId, AgentConfig>> {
        let pm_config = self.pm_config.read().await;

        if let Some(agents) = &pm_config.agents {
            Ok(agents.clone())
        } else {
            Ok(HashMap::new())
        }
    }

    /// Updates an agent's configuration from the Agent struct
    pub async fn update_agent_from_struct(&self, agent: &Agent) -> PrismaResult<()> {
        // Extract the agent's configuration
        let config = agent.config.clone();

        // Update the agent's configuration in pm.toml
        self.update_agent_config(&agent.id, config).await
    }

    /// Gets the project name from pm.toml
    pub async fn get_project_name(&self) -> PrismaResult<Option<String>> {
        let pm_config = self.pm_config.read().await;
        Ok(pm_config.project_name.clone())
    }

    /// Gets the project description from pm.toml
    pub async fn get_project_description(&self) -> PrismaResult<Option<String>> {
        let pm_config = self.pm_config.read().await;
        Ok(pm_config.project_description.clone())
    }

    /// Gets the project specifications from pm.toml
    pub async fn get_project_specifications(&self) -> PrismaResult<Option<Vec<String>>> {
        let pm_config = self.pm_config.read().await;
        Ok(pm_config.project_specifications.clone())
    }

    /// Gets the user name from pm.toml
    pub async fn get_user_name(&self) -> PrismaResult<Option<String>> {
        let pm_config = self.pm_config.read().await;
        Ok(pm_config.user_name.clone())
    }

    /// Updates the project name in pm.toml
    pub async fn update_project_name(&self, name: String) -> PrismaResult<()> {
        let mut pm_config = self.pm_config.write().await;
        pm_config.project_name = Some(name);
        drop(pm_config); // Release the write lock
        self.save_pm_config().await
    }

    /// Updates the project description in pm.toml
    pub async fn update_project_description(&self, description: String) -> PrismaResult<()> {
        let mut pm_config = self.pm_config.write().await;
        pm_config.project_description = Some(description);
        drop(pm_config); // Release the write lock
        self.save_pm_config().await
    }

    /// Updates the project specifications in pm.toml
    pub async fn update_project_specifications(&self, specifications: Vec<String>) -> PrismaResult<()> {
        let mut pm_config = self.pm_config.write().await;
        pm_config.project_specifications = Some(specifications);
        drop(pm_config); // Release the write lock
        self.save_pm_config().await
    }

    /// Updates the user name in pm.toml
    pub async fn update_user_name(&self, name: String) -> PrismaResult<()> {
        let mut pm_config = self.pm_config.write().await;
        pm_config.user_name = Some(name);
        drop(pm_config); // Release the write lock
        self.save_pm_config().await
    }
}
