// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/agent_manager/model_metadata_trait.rs
// =================================================================================================
// Purpose: Defines the ModelMetadataManagerTrait for managing LLM model metadata.
// This trait provides methods to extract, store, and retrieve metadata from LLM models.
//
// Integration:
// - Internal Dependencies:
//   - agent_manager/mod.rs: Re-exports this trait
//   - agent_manager/model_metadata.rs: Implements this trait
//
// - External Dependencies:
//   - llm/interface/ModelMetadata: Used for extracting metadata from models
//
// Platform Considerations:
// - This module is platform-independent
// =================================================================================================

use async_trait::async_trait;
use serde_json::Value;
use std::collections::HashMap;

use crate::err::PrismaResult;
use crate::llm::interface::ModelMetadata;

/// Trait for managing LLM model metadata
#[async_trait]
pub trait ModelMetadataManagerTrait: Send + Sync {
    /// Extract metadata from a model
    async fn extract_metadata<T: ModelMetadata + Send + Sync + 'static>(
        &self,
        model: &T,
        model_id: &str
    ) -> PrismaResult<Value>;

    /// Get metadata for a model by ID
    async fn get_model_metadata(&self, model_id: &str) -> PrismaResult<Option<Value>>;

    /// Get a specific metadata field for a model
    async fn get_metadata_field(&self, model_id: &str, field: &str) -> PrismaResult<Option<Value>>;

    /// Clear the metadata cache
    async fn clear_cache(&mut self) -> PrismaResult<()>;

    /// Remove a model from the metadata cache
    async fn remove_from_cache(&self, model_id: &str) -> PrismaResult<()>;

    /// Get all model IDs with metadata
    async fn get_all_model_ids(&self) -> PrismaResult<Vec<String>>;

    /// Extract capabilities from model metadata
    async fn extract_capabilities(&self, model_id: &str) -> PrismaResult<Vec<String>>;

    /// Extract recommended roles from model metadata
    async fn extract_recommended_roles(&self, model_id: &str) -> PrismaResult<Vec<String>>;

    /// Extract model parameters from metadata
    async fn extract_parameters(&self, model_id: &str) -> PrismaResult<HashMap<String, Value>>;
}
