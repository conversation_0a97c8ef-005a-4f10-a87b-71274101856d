// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/agent_state.rs
// =================================================================================================
// Purpose: Implements the AgentStateManager trait for managing agent runtime state.
// This file handles tracking and updating agent state, conversation context, and execution status.
// It provides functionality for managing agent conversation history, busy/idle status, and
// context values.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses AgentId and AgentState types
// - traits.rs: Implements the AgentStateManager trait
// - generics.rs: Uses the Cache generic for storing agent states
// =================================================================================================
// External Dependencies:
// - std::collections::HashMap: For storing context values
// - tracing: For logging
// - chrono: For timestamp handling
// - crate::err::PrismaResult: For error handling
// - crate::storage::SurrealDbConnection: For database persistence
// =================================================================================================
// Module Interactions:
// - Used by AgentManager to track and update agent states
// - Interacts with the database for persistent storage of agent states
// - Provides conversation history tracking for agents
// - Manages agent busy/idle status for task execution
// - Stores and retrieves context values for agents
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, info, warn};
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use serde_json::json;

use crate::err::{PrismaResult, GenericError};
use crate::storage::{SurrealDbConnection, DataStore};

use super::types::{AgentId, AgentState};
use super::traits::AgentStateManager;
use super::generics::Cache;

/// Maximum number of conversation turns to keep in memory
const MAX_CONVERSATION_TURNS: usize = 10;

/// Represents a conversation turn between user and agent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationTurn {
    /// User's message
    pub user_message: String,

    /// Agent's response
    pub agent_response: String,

    /// When this turn occurred
    #[serde(with = "chrono::serde::ts_seconds")]
    pub timestamp: DateTime<Utc>,
}

/// Database representation of agent runtime state
#[derive(Debug, Serialize, Deserialize)]
struct AgentStateRecord {
    /// Agent ID
    agent_id: String,

    /// Current state of the agent (as string)
    state: String,

    /// When the agent was last active
    #[serde(with = "chrono::serde::ts_seconds")]
    last_active: DateTime<Utc>,

    /// Recent conversation history
    conversation_history: Vec<ConversationTurn>,

    /// Whether the agent is currently processing a task
    is_busy: bool,

    /// Current task ID being processed, if any
    current_task_id: Option<String>,

    /// Additional context or metadata
    context: HashMap<String, String>,
}

impl AgentStateRecord {
    /// Create a new AgentStateRecord from an AgentRuntimeState
    fn new(agent_id: &AgentId, state: &AgentRuntimeState) -> Self {
        // Convert agent state to string
        let state_str = match state.state {
            AgentState::Initializing => "initializing",
            AgentState::Active => "active",
            AgentState::Paused => "paused",
            AgentState::Terminated => "terminated",
        };

        Self {
            agent_id: agent_id.clone(),
            state: state_str.to_string(),
            last_active: state.last_active,
            conversation_history: state.conversation_history.clone(),
            is_busy: state.is_busy,
            current_task_id: state.current_task_id.clone(),
            context: state.context.clone(),
        }
    }

    /// Convert to AgentRuntimeState
    fn to_runtime_state(&self) -> PrismaResult<AgentRuntimeState> {
        // Convert state string to AgentState
        let state = match self.state.as_str() {
            "initializing" => AgentState::Initializing,
            "active" => AgentState::Active,
            "paused" => AgentState::Paused,
            "terminated" => AgentState::Terminated,
            _ => {
                warn!("Unknown agent state: {}, defaulting to Initializing", self.state);
                AgentState::Initializing
            }
        };

        Ok(AgentRuntimeState {
            state,
            last_active: self.last_active,
            conversation_history: self.conversation_history.clone(),
            is_busy: self.is_busy,
            current_task_id: self.current_task_id.clone(),
            context: self.context.clone(),
        })
    }
}

/// Represents the runtime state of an agent
#[derive(Debug, Clone)]
pub struct AgentRuntimeState {
    /// Current state of the agent
    pub state: AgentState,

    /// When the agent was last active
    pub last_active: DateTime<Utc>,

    /// Recent conversation history
    pub conversation_history: Vec<ConversationTurn>,

    /// Whether the agent is currently processing a task
    pub is_busy: bool,

    /// Current task ID being processed, if any
    pub current_task_id: Option<String>,

    /// Additional context or metadata
    pub context: HashMap<String, String>,
}

impl AgentRuntimeState {
    /// Creates a new agent runtime state
    pub fn new(state: AgentState) -> Self {
        Self {
            state,
            last_active: Utc::now(),
            conversation_history: Vec::new(),
            is_busy: false,
            current_task_id: None,
            context: HashMap::new(),
        }
    }

    /// Adds a conversation turn to the history
    pub fn add_conversation_turn(&mut self, user_message: String, agent_response: String) {
        let turn = ConversationTurn {
            user_message,
            agent_response,
            timestamp: Utc::now(),
        };

        self.conversation_history.push(turn);

        // Limit the history size
        if self.conversation_history.len() > MAX_CONVERSATION_TURNS {
            self.conversation_history.remove(0);
        }

        self.update_last_active();
    }

    /// Updates the last active timestamp
    pub fn update_last_active(&mut self) {
        self.last_active = Utc::now();
    }

    /// Sets the agent as busy with a specific task
    pub fn set_busy(&mut self, task_id: String) {
        self.is_busy = true;
        self.current_task_id = Some(task_id);
        self.update_last_active();
    }

    /// Sets the agent as idle
    pub fn set_idle(&mut self) {
        self.is_busy = false;
        self.current_task_id = None;
        self.update_last_active();
    }

    /// Adds or updates a context value
    pub fn set_context(&mut self, key: String, value: String) {
        self.context.insert(key, value);
        self.update_last_active();
    }

    /// Gets a context value
    pub fn get_context(&self, key: &str) -> Option<&String> {
        self.context.get(key)
    }
}

/// Manager for agent runtime state
#[derive(Debug)]
pub struct AgentStateManagerImpl {
    /// Cache of agent runtime states
    states: Cache<AgentId, AgentRuntimeState>,

    /// Optional database connection for persistence
    db_connection: Option<Arc<SurrealDbConnection>>,
}

impl AgentStateManagerImpl {
    /// Creates a new AgentStateManagerImpl
    pub fn new(db_connection: Option<Arc<SurrealDbConnection>>) -> Self {
        info!("Initializing AgentStateManagerImpl");
        Self {
            states: Cache::new(),
            db_connection,
        }
    }

    /// Gets or creates a runtime state for an agent
    async fn get_or_create_state(&self, agent_id: &AgentId) -> PrismaResult<AgentRuntimeState> {
        if let Some(state) = self.states.get(agent_id).await {
            return Ok(state);
        }

        // Try to load from database if available
        if let Some(db) = &self.db_connection {
            debug!("Loading agent state for {} from database", agent_id);

            // Query the agent state from the database
            let query = "SELECT * FROM agent_states WHERE agent_id = $1";
            let records: Vec<AgentStateRecord> = db.query(query, &[("1", agent_id)]).await?;

            if let Some(record) = records.first() {
                match record.to_runtime_state() {
                    Ok(state) => {
                        // Cache the state for future use
                        self.states.insert(agent_id.clone(), state.clone()).await?;
                        debug!("Loaded agent state for {} from database", agent_id);
                        return Ok(state);
                    },
                    Err(e) => {
                        warn!("Failed to convert agent state record to runtime state: {}", e);
                    }
                }
            } else {
                debug!("No state found for agent {} in database", agent_id);
            }
        }

        // Create a new state if not found
        let new_state = AgentRuntimeState::new(AgentState::Initializing);
        self.states.insert(agent_id.clone(), new_state.clone()).await?;
        Ok(new_state)
    }

    /// Saves an agent state to the database if available
    async fn save_state_to_db(&self, agent_id: &AgentId, state: &AgentRuntimeState) -> PrismaResult<()> {
        if let Some(db) = &self.db_connection {
            debug!("Saving agent state for {} to database", agent_id);

            // Convert runtime state to database record
            let record = AgentStateRecord::new(agent_id, state);

            // Check if the state already exists
            let exists_query = "SELECT COUNT(*) as count FROM agent_states WHERE agent_id = $1";
            let result: Vec<serde_json::Value> = db.query(exists_query, &[("1", agent_id)]).await?;

            let exists = if let Some(value) = result.first() {
                if let Some(count) = value.get("count") {
                    count.as_i64().unwrap_or(0) > 0
                } else {
                    false
                }
            } else {
                false
            };

            if exists {
                // Update existing state
                debug!("Updating existing state for agent {} in database", agent_id);
                let update_query = "UPDATE agent_states SET * = $1 WHERE agent_id = $2";
                let record_json = serde_json::to_string(&record).map_err(|e| GenericError::from(format!("Failed to serialize record: {}", e)))?;
                db.query::<serde_json::Value>(update_query, &[("1", &record_json), ("2", agent_id)]).await?;
            } else {
                // Create new state
                debug!("Creating new state for agent {} in database", agent_id);
                let create_query = "CREATE agent_states CONTENT $1";
                let record_json = serde_json::to_string(&record).map_err(|e| GenericError::from(format!("Failed to serialize record: {}", e)))?;
                db.query::<serde_json::Value>(create_query, &[("1", &record_json)]).await?;
            }

            debug!("Agent state for {} saved to database", agent_id);
        }
        Ok(())
    }

    /// Adds a conversation turn for an agent
    pub async fn add_conversation_turn(&self, agent_id: &AgentId, user_message: String, agent_response: String) -> PrismaResult<()> {
        let mut state = self.get_or_create_state(agent_id).await?;
        state.add_conversation_turn(user_message, agent_response);
        self.states.insert(agent_id.clone(), state.clone()).await?;
        self.save_state_to_db(agent_id, &state).await?;
        Ok(())
    }

    /// Gets the conversation history for an agent
    pub async fn get_conversation_history(&self, agent_id: &AgentId) -> PrismaResult<Vec<ConversationTurn>> {
        let state = self.get_or_create_state(agent_id).await?;
        Ok(state.conversation_history.clone())
    }

    /// Sets an agent as busy with a specific task
    pub async fn set_agent_busy(&self, agent_id: &AgentId, task_id: String) -> PrismaResult<()> {
        let mut state = self.get_or_create_state(agent_id).await?;
        state.set_busy(task_id);
        self.states.insert(agent_id.clone(), state.clone()).await?;
        self.save_state_to_db(agent_id, &state).await?;
        Ok(())
    }

    /// Sets an agent as idle
    pub async fn set_agent_idle(&self, agent_id: &AgentId) -> PrismaResult<()> {
        let mut state = self.get_or_create_state(agent_id).await?;
        state.set_idle();
        self.states.insert(agent_id.clone(), state.clone()).await?;
        self.save_state_to_db(agent_id, &state).await?;
        Ok(())
    }

    /// Checks if an agent is busy
    pub async fn is_agent_busy(&self, agent_id: &AgentId) -> PrismaResult<bool> {
        let state = self.get_or_create_state(agent_id).await?;
        Ok(state.is_busy)
    }

    /// Sets a context value for an agent
    pub async fn set_agent_context(&self, agent_id: &AgentId, key: String, value: String) -> PrismaResult<()> {
        let mut state = self.get_or_create_state(agent_id).await?;
        state.set_context(key, value);
        self.states.insert(agent_id.clone(), state.clone()).await?;
        self.save_state_to_db(agent_id, &state).await?;
        Ok(())
    }

    /// Gets a context value for an agent
    pub async fn get_agent_context(&self, agent_id: &AgentId, key: &str) -> PrismaResult<Option<String>> {
        let state = self.get_or_create_state(agent_id).await?;
        Ok(state.get_context(key).cloned())
    }
}

#[async_trait::async_trait]
impl AgentStateManager for AgentStateManagerImpl {
    async fn get_state(&self, agent_id: &AgentId) -> PrismaResult<Option<AgentState>> {
        let state = self.get_or_create_state(agent_id).await?;
        Ok(Some(state.state))
    }

    async fn set_state(&self, agent_id: &AgentId, state: AgentState) -> PrismaResult<()> {
        let mut runtime_state = self.get_or_create_state(agent_id).await?;
        runtime_state.state = state;
        runtime_state.update_last_active();
        self.states.insert(agent_id.clone(), runtime_state.clone()).await?;
        self.save_state_to_db(agent_id, &runtime_state).await?;
        Ok(())
    }

    async fn get_agents_by_state(&self, state: AgentState) -> PrismaResult<Vec<AgentId>> {
        // Convert state to string for database query
        let state_str = match state {
            AgentState::Initializing => "initializing",
            AgentState::Active => "active",
            AgentState::Paused => "paused",
            AgentState::Terminated => "terminated",
        };

        // If we have a database connection, use it for efficient querying
        if let Some(db) = &self.db_connection {
            debug!("Querying database for agents with state: {}", state_str);

            // Query the database for agents with the specified state
            let query = "SELECT agent_id FROM agent_states WHERE state = $1";
            let results: Vec<serde_json::Value> = db.query(query, &[("1", state_str)]).await?;

            // Extract agent IDs from the results
            let mut agent_ids = Vec::new();
            for result in results {
                if let Some(agent_id) = result.get("agent_id").and_then(|id| id.as_str()) {
                    agent_ids.push(agent_id.to_string());
                }
            }

            debug!("Found {} agents with state {} in database", agent_ids.len(), state_str);
            return Ok(agent_ids);
        }

        // Fallback to in-memory search if no database connection
        debug!("No database connection, using in-memory search for agents with state: {}", state_str);
        let mut result = Vec::new();
        let all_agent_ids = self.states.keys().await?;

        for agent_id in all_agent_ids {
            if let Some(runtime_state) = self.states.get(&agent_id).await {
                if runtime_state.state == state {
                    result.push(agent_id);
                }
            }
        }

        Ok(result)
    }

    async fn update_last_active(&self, agent_id: &AgentId) -> PrismaResult<()> {
        let mut state = self.get_or_create_state(agent_id).await?;
        state.update_last_active();
        self.states.insert(agent_id.clone(), state.clone()).await?;
        self.save_state_to_db(agent_id, &state).await?;
        Ok(())
    }
}
