// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/agent_capabilities.rs
// =================================================================================================
// Purpose: Implements the CapabilityManager trait for managing agent capabilities.
// This file handles defining, enforcing, and checking agent capabilities and permissions.
// It provides functionality to add, remove, and check capabilities for agents.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses AgentId and AgentCapability types
// - traits.rs: Implements the CapabilityManager trait
// - generics.rs: Uses the Cache generic for storing capabilities
// =================================================================================================
// External Dependencies:
// - std::collections: For HashMap and HashSet
// - tokio::sync::RwLock: For thread-safe access to shared data
// - tracing: For logging
// - crate::err::PrismaResult: For error handling
// - crate::storage::SurrealDbConnection: For database persistence
// =================================================================================================
// Module Interactions:
// - Used by AgentManager to manage agent capabilities
// - Interacts with the database for persistent storage of capabilities
// - Provides capability checking for other modules to enforce permissions
// - Maintains a capability index for efficient lookup of agents with specific capabilities
// =================================================================================================

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use serde::{Serialize, Deserialize};
use serde_json::json;

use crate::err::{PrismaResult, GenericError};
use crate::storage::{SurrealDbConnection, DataStore};

use super::types::{AgentId, AgentCapability};
use super::traits::CapabilityManager;
use super::generics::Cache;

/// Database representation of agent capabilities
#[derive(Debug, Serialize, Deserialize)]
struct AgentCapabilityRecord {
    /// Agent ID
    agent_id: String,

    /// Capability name
    capability: String,

    /// When the capability was added
    #[serde(with = "chrono::serde::ts_seconds")]
    created_at: chrono::DateTime<chrono::Utc>,
}

/// Manager for agent capabilities
#[derive(Debug)]
pub struct AgentCapabilityManager {
    /// Cache of agent capabilities
    capabilities: Cache<AgentId, HashSet<AgentCapability>>,

    /// Reverse index for quick lookup of agents with a specific capability
    capability_index: Arc<RwLock<HashMap<AgentCapability, HashSet<AgentId>>>>,

    /// Optional database connection for persistence
    db_connection: Option<Arc<SurrealDbConnection>>,
}

impl AgentCapabilityManager {
    /// Creates a new AgentCapabilityManager
    pub fn new(db_connection: Option<Arc<SurrealDbConnection>>) -> Self {
        info!("Initializing AgentCapabilityManager");
        Self {
            capabilities: Cache::new(),
            capability_index: Arc::new(RwLock::new(HashMap::new())),
            db_connection,
        }
    }

    /// Loads capabilities from the database if available
    pub async fn load_from_db(&self) -> PrismaResult<()> {
        if let Some(db) = &self.db_connection {
            info!("Loading agent capabilities from database");

            // Query all agent capabilities from the database
            let query = "SELECT * FROM agent_capabilities";
            let records: Vec<AgentCapabilityRecord> = db.query(query, &[]).await?;

            if records.is_empty() {
                debug!("No agent capabilities found in database");
                return Ok(());
            }

            info!("Found {} agent capability records in database", records.len());

            // Group capabilities by agent ID
            let mut agent_capabilities: HashMap<AgentId, HashSet<AgentCapability>> = HashMap::new();

            for record in records {
                let agent_id = record.agent_id;
                let capability = AgentCapability::new(&record.capability);

                // Add to the agent's capability set
                agent_capabilities
                    .entry(agent_id.clone())
                    .or_insert_with(HashSet::new)
                    .insert(capability.clone());

                // Update the capability index
                self.update_capability_index(&agent_id, &capability, true).await?;
            }

            // Get the count before moving the HashMap
            let agent_count = agent_capabilities.len();

            // Update the cache with all loaded capabilities
            for (agent_id, capabilities) in agent_capabilities {
                self.capabilities.insert(agent_id, capabilities).await?;
            }

            info!("Successfully loaded agent capabilities for {} agents", agent_count);
        } else {
            debug!("No database connection available, skipping capability loading");
        }
        Ok(())
    }

    /// Saves agent capabilities to the database if available
    async fn save_to_db(&self, agent_id: &AgentId, capabilities: &HashSet<AgentCapability>) -> PrismaResult<()> {
        if let Some(db) = &self.db_connection {
            debug!("Saving capabilities for agent {} to database", agent_id);

            // First, delete existing capabilities for this agent to avoid duplicates
            let delete_query = "DELETE FROM agent_capabilities WHERE agent_id = $1";
            db.query::<serde_json::Value>(delete_query, &[("1", agent_id)]).await?;

            // If there are no capabilities, we're done
            if capabilities.is_empty() {
                debug!("No capabilities to save for agent {}", agent_id);
                return Ok(());
            }

            // Insert each capability as a separate record
            for capability in capabilities {
                let record = AgentCapabilityRecord {
                    agent_id: agent_id.clone(),
                    capability: capability.name(),
                    created_at: chrono::Utc::now(),
                };

                // Create the record in the database
                let create_query = "CREATE agent_capabilities CONTENT $1";
                let record_json = serde_json::to_string(&record).map_err(|e| GenericError::from(format!("Failed to serialize record: {}", e)))?;
                db.query::<serde_json::Value>(create_query, &[("1", &record_json)]).await?;
            }

            debug!("Saved {} capabilities for agent {}", capabilities.len(), agent_id);
        }
        Ok(())
    }

    /// Gets or creates a capability set for an agent
    async fn get_or_create_capabilities(&self, agent_id: &AgentId) -> PrismaResult<HashSet<AgentCapability>> {
        if let Some(capabilities) = self.capabilities.get(agent_id).await {
            return Ok(capabilities);
        }

        // Try to load from database if available
        if let Some(db) = &self.db_connection {
            debug!("Loading capabilities for agent {} from database", agent_id);

            // Query capabilities for this agent
            let query = "SELECT * FROM agent_capabilities WHERE agent_id = $1";
            let records: Vec<AgentCapabilityRecord> = db.query(query, &[("1", agent_id)]).await?;

            if !records.is_empty() {
                debug!("Found {} capabilities for agent {} in database", records.len(), agent_id);

                // Create a set of capabilities from the records
                let mut capabilities = HashSet::new();

                for record in records {
                    let capability = AgentCapability::new(&record.capability);
                    capabilities.insert(capability.clone());

                    // Update the capability index
                    self.update_capability_index(agent_id, &capability, true).await?;
                }

                // Cache the capabilities
                self.capabilities.insert(agent_id.clone(), capabilities.clone()).await?;
                return Ok(capabilities);
            } else {
                debug!("No capabilities found for agent {} in database", agent_id);
            }
        }

        // Create a new empty set if not found
        let capabilities = HashSet::new();
        self.capabilities.insert(agent_id.clone(), capabilities.clone()).await?;
        Ok(capabilities)
    }

    /// Updates the capability index
    async fn update_capability_index(&self, agent_id: &AgentId, capability: &AgentCapability, add: bool) -> PrismaResult<()> {
        let mut index = self.capability_index.write().await;

        if add {
            // Add agent to the capability's set
            index.entry(capability.clone())
                .or_insert_with(HashSet::new)
                .insert(agent_id.clone());
        } else {
            // Remove agent from the capability's set
            if let Some(agents) = index.get_mut(capability) {
                agents.remove(agent_id);

                // Remove the capability entry if no agents have it
                if agents.is_empty() {
                    index.remove(capability);
                }
            }
        }

        Ok(())
    }

    /// Checks if a capability is allowed for the current system configuration
    pub fn is_capability_allowed(&self, capability: &AgentCapability) -> bool {
        // This is where you would implement system-wide capability restrictions
        // For example, you might want to disable certain capabilities in production
        match capability {
            // Allow most capabilities
            AgentCapability::FileSystem => true,
            AgentCapability::Network => true,
            AgentCapability::Database => true,
            AgentCapability::ToolUse => true,
            AgentCapability::AgentCommunication => true,

            // Restrict potentially dangerous capabilities
            AgentCapability::CodeExecution => false, // Disabled by default for security

            // Custom capabilities are allowed by default
            AgentCapability::Custom(_) => true,
        }
    }
}

#[async_trait::async_trait]
impl CapabilityManager for AgentCapabilityManager {
    async fn add_capability(&self, agent_id: &AgentId, capability: AgentCapability) -> PrismaResult<()> {
        // Check if the capability is allowed
        if !self.is_capability_allowed(&capability) {
            warn!("Attempted to add restricted capability {:?} to agent {}", capability, agent_id);
            return Ok(()); // Silently ignore restricted capabilities
        }

        let mut capabilities = self.get_or_create_capabilities(agent_id).await?;

        // Add the capability if it doesn't already exist
        if capabilities.insert(capability.clone()) {
            debug!("Added capability {:?} to agent {}", capability, agent_id);

            // Update the cache
            self.capabilities.insert(agent_id.clone(), capabilities.clone()).await?;

            // Update the capability index
            self.update_capability_index(agent_id, &capability, true).await?;

            // Save to database
            self.save_to_db(agent_id, &capabilities).await?;
        }

        Ok(())
    }

    async fn remove_capability(&self, agent_id: &AgentId, capability: &AgentCapability) -> PrismaResult<()> {
        let mut capabilities = self.get_or_create_capabilities(agent_id).await?;

        // Remove the capability if it exists
        if capabilities.remove(capability) {
            debug!("Removed capability {:?} from agent {}", capability, agent_id);

            // Update the cache
            self.capabilities.insert(agent_id.clone(), capabilities.clone()).await?;

            // Update the capability index
            self.update_capability_index(agent_id, capability, false).await?;

            // Save to database
            self.save_to_db(agent_id, &capabilities).await?;
        }

        Ok(())
    }

    async fn has_capability(&self, agent_id: &AgentId, capability: &AgentCapability) -> PrismaResult<bool> {
        let capabilities = self.get_or_create_capabilities(agent_id).await?;
        Ok(capabilities.contains(capability))
    }

    async fn get_capabilities(&self, agent_id: &AgentId) -> PrismaResult<Vec<AgentCapability>> {
        let capabilities = self.get_or_create_capabilities(agent_id).await?;
        Ok(capabilities.into_iter().collect())
    }

    async fn get_agents_with_capability(&self, capability: &AgentCapability) -> PrismaResult<Vec<AgentId>> {
        let index = self.capability_index.read().await;

        if let Some(agents) = index.get(capability) {
            Ok(agents.iter().cloned().collect())
        } else {
            Ok(Vec::new())
        }
    }
}
