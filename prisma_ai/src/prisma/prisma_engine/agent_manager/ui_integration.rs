// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/ui_integration.rs
// =================================================================================================
// Purpose: Implements the UI integration for the agent_manager module.
// This file handles mapping UI-defined roles and attributes to the agent manager, providing
// a bridge between the user interface and the agent management system. It enables users to
// create and configure agents through the UI with appropriate data transformations.
// =================================================================================================
// Internal Dependencies:
// - agents.rs: Uses Agent and AgentConfig types
// - types.rs: Uses AgentId, AgentRole, and AgentCapability types
// - config_integration.rs: Uses ConfigIntegrationManager for saving configurations
// =================================================================================================
// External Dependencies:
// - std::collections::HashMap: For storing mappings
// - std::sync::Arc: For shared ownership
// - tokio::sync::RwLock: For thread-safe access to shared data
// - tracing: For logging
// - serde: For serialization and deserialization
// - serde_json: For JSON handling
// - crate::err: For error handling
// =================================================================================================
// Module Interactions:
// - Used by AgentManager to create and update agents from UI data
// - Interacts with config_integration.rs to save agent configurations
// - Provides data transformation between UI and internal representations
// - Manages UI-defined roles and their mapping to agent roles
// - Enables dynamic agent configuration through the user interface
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};

use crate::err::PrismaResult;
use crate::err::GenericError;

use super::agents::{Agent, AgentConfig};
use super::types::{AgentId, AgentRole, AgentCapability};
use super::config_integration::ConfigIntegrationManager;

/// UI-defined agent data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiAgentData {
    /// Agent ID
    pub id: Option<String>,

    /// Agent name
    pub name: String,

    /// Agent role name
    pub role_name: String,

    /// Agent role description
    pub role_description: Option<String>,

    /// Agent capabilities
    pub capabilities: Option<Vec<String>>,

    /// Agent background
    pub background: Option<String>,

    /// Agent tasks
    pub tasks: Option<Vec<String>>,

    /// Agent rules
    pub rules: Option<Vec<String>>,

    /// Agent skills
    pub skills: Option<Vec<String>>,

    /// Agent model preference
    pub model_preference: Option<String>,

    /// Agent template key
    pub template_key: Option<String>,

    /// Agent can communicate with humans
    pub can_communicate_with_humans: Option<bool>,

    /// Agent tool configuration
    pub tools: Option<Value>,

    /// Agent RAG configuration
    pub rag_config: Option<Value>,

    /// Additional attributes
    #[serde(flatten)]
    pub additional: HashMap<String, Value>,
}

/// Manager for UI integration
#[derive(Debug)]
pub struct UiIntegrationManager {
    /// Configuration manager for saving changes
    config_manager: Option<Arc<ConfigIntegrationManager>>,

    /// Cache of UI-defined roles
    ui_roles: Arc<RwLock<HashMap<String, AgentRole>>>,
}

impl UiIntegrationManager {
    /// Creates a new UiIntegrationManager
    pub fn new(config_manager: Option<Arc<ConfigIntegrationManager>>) -> Self {
        info!("Initializing UiIntegrationManager");

        Self {
            config_manager,
            ui_roles: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Converts UI agent data to an Agent struct
    pub fn ui_data_to_agent(&self, ui_data: UiAgentData) -> PrismaResult<Agent> {
        debug!("Converting UI data to Agent: {}", ui_data.name);

        // Generate an ID if not provided
        let id = ui_data.id.unwrap_or_else(|| {
            let id = format!("agent_{}", uuid::Uuid::new_v4());
            debug!("Generated new agent ID: {}", id);
            id
        });

        // Create the agent role
        let role = AgentRole::new(&ui_data.role_name);

        // Generate a sequence ID (this would normally come from the registry)
        let seq_id = rand::random::<i32>().abs();

        // Create the agent
        let mut agent = Agent::new(id.clone(), seq_id, ui_data.name, role);

        // Set agent properties
        if let Some(background) = ui_data.background {
            agent.set_background(background);
        }

        if let Some(tasks) = ui_data.tasks {
            agent.set_tasks(tasks);
        }

        if let Some(rules) = ui_data.rules {
            agent.set_rules(rules);
        }

        if let Some(skills) = ui_data.skills {
            agent.set_skills(skills);
        }

        if let Some(model) = ui_data.model_preference {
            agent.set_model_preference(model);
        }

        if let Some(template) = ui_data.template_key {
            agent.set_template_key(template);
        }

        if let Some(can_communicate) = ui_data.can_communicate_with_humans {
            agent.set_human_communication(can_communicate);
        }

        if let Some(tools) = ui_data.tools {
            agent.set_tools_config(tools);
        }

        if let Some(rag_config) = ui_data.rag_config {
            agent.set_rag_config(rag_config);
        }

        // Add capabilities if provided
        if let Some(capabilities) = ui_data.capabilities {
            for capability in capabilities {
                agent.add_capability(AgentCapability::new(&capability));
            }
        }

        Ok(agent)
    }

    /// Converts an Agent struct to UI agent data
    pub fn agent_to_ui_data(&self, agent: &Agent) -> UiAgentData {
        debug!("Converting Agent to UI data: {}", agent.name);

        // Get the primary role (first one)
        let role = if !agent.roles.is_empty() {
            agent.roles[0].clone()
        } else {
            AgentRole::new("Default")
        };

        UiAgentData {
            id: Some(agent.id.clone()),
            name: agent.name.clone(),
            role_name: role.name,
            role_description: role.description,
            capabilities: Some(agent.capabilities.iter().map(|c| c.name()).collect()),
            background: agent.get_background().cloned(),
            tasks: agent.get_tasks().cloned(),
            rules: agent.get_rules().cloned(),
            skills: agent.get_skills().cloned(),
            model_preference: agent.model_preference.clone(),
            template_key: agent.template_key.clone(),
            can_communicate_with_humans: Some(agent.can_communicate_with_humans()),
            tools: agent.get_tools_config().cloned(),
            rag_config: agent.get_rag_config().cloned(),
            additional: HashMap::new(),
        }
    }

    /// Registers a UI-defined role
    pub async fn register_ui_role(&self, name: &str, description: Option<String>, _capabilities: Option<Vec<String>>) -> PrismaResult<AgentRole> {
        let mut ui_roles = self.ui_roles.write().await;

        let role = if let Some(desc) = description {
            let mut role = AgentRole::new(name);
            role.description = Some(desc);
            role
        } else {
            AgentRole::new(name)
        };

        ui_roles.insert(name.to_string(), role.clone());

        debug!("Registered UI role: {}", name);
        Ok(role)
    }

    /// Gets a UI-defined role
    pub async fn get_ui_role(&self, name: &str) -> Option<AgentRole> {
        let ui_roles = self.ui_roles.read().await;
        ui_roles.get(name).cloned()
    }

    /// Gets all UI-defined roles
    pub async fn get_all_ui_roles(&self) -> HashMap<String, AgentRole> {
        let ui_roles = self.ui_roles.read().await;
        ui_roles.clone()
    }

    /// Saves UI agent data to configuration
    pub async fn save_ui_agent_data(&self, ui_data: UiAgentData) -> PrismaResult<Agent> {
        // Convert UI data to Agent
        let agent = self.ui_data_to_agent(ui_data)?;

        // Save to configuration if available
        if let Some(config_manager) = &self.config_manager {
            config_manager.update_agent_from_struct(&agent).await?;
            debug!("Saved agent {} to configuration", agent.id);
        } else {
            warn!("No configuration manager available, agent not saved to configuration");
        }

        Ok(agent)
    }

    /// Updates an agent from UI data
    pub async fn update_agent_from_ui(&self, agent: &mut Agent, ui_data: UiAgentData) -> PrismaResult<()> {
        debug!("Updating agent {} from UI data", agent.id);

        // Update agent properties
        agent.name = ui_data.name;

        // Update role if different from the first role
        let primary_role_name = if !agent.roles.is_empty() {
            agent.roles[0].name.clone()
        } else {
            String::new()
        };

        if primary_role_name != ui_data.role_name {
            let role = if let Some(role) = self.get_ui_role(&ui_data.role_name).await {
                role
            } else {
                AgentRole::new(&ui_data.role_name)
            };

            // Remove the old primary role if it exists
            if !primary_role_name.is_empty() {
                agent.remove_role(&primary_role_name);
            }

            // Add the new role
            agent.add_role(role);
        }

        // Update other properties if provided
        if let Some(background) = ui_data.background {
            agent.set_background(background);
        }

        if let Some(tasks) = ui_data.tasks {
            agent.set_tasks(tasks);
        }

        if let Some(rules) = ui_data.rules {
            agent.set_rules(rules);
        }

        if let Some(skills) = ui_data.skills {
            agent.set_skills(skills);
        }

        if let Some(model) = ui_data.model_preference {
            agent.set_model_preference(model);
        }

        if let Some(template) = ui_data.template_key {
            agent.set_template_key(template);
        }

        if let Some(can_communicate) = ui_data.can_communicate_with_humans {
            agent.set_human_communication(can_communicate);
        }

        if let Some(tools) = ui_data.tools {
            agent.set_tools_config(tools);
        }

        if let Some(rag_config) = ui_data.rag_config {
            agent.set_rag_config(rag_config);
        }

        // Update capabilities if provided
        if let Some(capabilities) = ui_data.capabilities {
            // Clear existing capabilities
            agent.capabilities.clear();

            // Add new capabilities
            for capability in capabilities {
                agent.add_capability(AgentCapability::new(&capability));
            }
        }

        // Save to configuration if available
        if let Some(config_manager) = &self.config_manager {
            config_manager.update_agent_from_struct(agent).await?;
            debug!("Saved updated agent {} to configuration", agent.id);
        } else {
            warn!("No configuration manager available, agent not saved to configuration");
        }

        Ok(())
    }

    /// Synchronizes UI-defined roles with the agent manager
    pub async fn sync_ui_roles(&self, roles: Vec<(String, Option<String>, Option<Vec<String>>)>) -> PrismaResult<()> {
        let mut ui_roles = self.ui_roles.write().await;
        ui_roles.clear();

        for (name, description, _capabilities) in roles {
            let mut role = AgentRole::new(&name);

            if let Some(desc) = description {
                role.description = Some(desc);
            }

            ui_roles.insert(name, role);
        }

        debug!("Synchronized {} UI roles", ui_roles.len());
        Ok(())
    }
}
