use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use num_cpus; // Added import
use crate::err::GenericError; // Use re-exported GenericError

// Unique identifier for tasks within the engine
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TaskId(Uuid);

impl TaskId {
    pub fn new() -> Self {
        TaskId(Uuid::new_v4())
    }
}

impl Default for TaskId {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Display for TaskId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

// Priority levels for tasks, influencing scheduling
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, <PERSON>ial<PERSON>rd, Or<PERSON>, <PERSON>h, Serialize, Deserialize)]
pub enum TaskPriority {
    Low,
    Normal,
    High,
    Realtime, // For the "Express Lane" concept
}

impl std::fmt::Display for TaskPriority {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskPriority::Low => write!(f, "Low"),
            TaskPriority::Normal => write!(f, "Normal"),
            TaskPriority::High => write!(f, "High"),
            TaskPriority::Realtime => write!(f, "Realtime"),
        }
    }
}

// Categories of tasks the engine might handle
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TaskCategory {
    LLMInference,
    EmbeddingGeneration,
    DatabaseQuery, // Keep this generic, maps to StorageTask
    FileProcessing,
    NetworkRequest,
    UICallback,
    Internal,
    Custom(String),
}

// Types of system resources the engine monitors and tasks require
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceType {
    CPU,
    Memory,
    GPU,
    NetworkBandwidth,
    DiskIO,
}

// Represents the usage or requirement of a specific resource
#[derive(Debug, Clone, Copy, PartialEq, PartialOrd, Serialize, Deserialize)]
pub struct ResourceUsage(pub f64);

// Represents the overall resource requirements of a task (PrismaScore)
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PrismaScore {
    pub resources: HashMap<ResourceType, ResourceUsage>,
}

impl Default for PrismaScore {
    fn default() -> Self {
        let mut resources = HashMap::new();
        resources.insert(ResourceType::CPU, ResourceUsage(0.5));
        resources.insert(ResourceType::Memory, ResourceUsage(0.5));
        resources.insert(ResourceType::DiskIO, ResourceUsage(0.3));
        resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.2));
        resources.insert(ResourceType::GPU, ResourceUsage(0.0));

        PrismaScore { resources }
    }
}

// Represents the current state or availability of system resources (SystemScore)
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SystemScore {
    pub availability: HashMap<ResourceType, ResourceUsage>,
}

impl Default for SystemScore {
    fn default() -> Self {
        let mut availability = HashMap::new();
        availability.insert(ResourceType::CPU, ResourceUsage(0.7));
        availability.insert(ResourceType::Memory, ResourceUsage(0.6));
        availability.insert(ResourceType::DiskIO, ResourceUsage(0.8));
        availability.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.9));
        availability.insert(ResourceType::GPU, ResourceUsage(0.0));

        SystemScore { availability }
    }
}

// Different strategies the engine can use to execute tasks
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ExecutionStrategyType {
    Rayon,
    Tokio,
    Direct,
}

// Represents the configuration for the Prisma Engine
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    // General engine configuration
    pub max_concurrent_tasks: Option<usize>,
    pub default_priority: TaskPriority,
    pub monitor_interval_ms: u64,

    // Component-specific configurations
    pub monitor_config: Option<MonitorConfig>,
    pub decision_maker_config: Option<DecisionMakerConfig>,
    pub executor_config: Option<ExecutorConfig>,
    pub execution_strategy_config: Option<ExecutionStrategyConfig>,

    // Service configurations
    pub storage_config: Option<StorageConfig>,
    pub llm_config: Option<LlmConfig>,
    pub embedding_config: Option<EmbeddingConfig>,
    pub agent_manager_config: Option<AgentManagerConfig>,
}

impl Default for EngineConfig {
    fn default() -> Self {
        EngineConfig {
            max_concurrent_tasks: Some(num_cpus::get() * 2),
            default_priority: TaskPriority::Normal,
            monitor_interval_ms: 5000,
            monitor_config: None,
            decision_maker_config: None,
            executor_config: None,
            execution_strategy_config: None,
            storage_config: None,
            llm_config: None,
            embedding_config: None,
            agent_manager_config: None,
        }
    }
}

// Represents the overall status of the Prisma Engine
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EngineStatus {
    Initializing,
    Running,
    Paused,
    Degraded,
    Stopping,
    Stopped,
    Error(EngineErrorType),
}

// Specific error types originating from the engine itself
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EngineErrorType {
    InitializationFailed,
    ConfigurationError,
    ResourceMonitorFailed,
    ExecutorFailed,
    DecisionMakerError,
    TaskQueueOverflow,
}

// Generic result type used within the engine
pub type EngineResult<T> = Result<T, GenericError>;

// Configuration for the Monitor component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    // Monitoring intervals in milliseconds
    pub system_monitor_interval_ms: u64,
    pub prisma_monitor_interval_ms: u64,
    // Resource thresholds (0.0 to 1.0)
    pub cpu_threshold: f64,
    pub memory_threshold: f64,
    pub disk_threshold: f64,
    pub network_threshold: f64,
    // Whether to enable specific monitors
    pub enable_system_monitor: bool,
    pub enable_prisma_monitor: bool,
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            system_monitor_interval_ms: 5000,
            prisma_monitor_interval_ms: 2000,
            cpu_threshold: 0.8,
            memory_threshold: 0.85,
            disk_threshold: 0.9,
            network_threshold: 0.7,
            enable_system_monitor: true,
            enable_prisma_monitor: true,
        }
    }
}

// Configuration for the DecisionMaker component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionMakerConfig {
    // Thresholds for resource usage
    pub high_cpu_threshold: f64,
    pub high_memory_threshold: f64,
    // Default execution strategy
    pub default_strategy: ExecutionStrategyType,
    // Rule evaluation timeout in milliseconds
    pub rule_evaluation_timeout_ms: u64,
    // Feature flags
    pub enable_priority_adjustment: bool,
    pub enable_rule_based_decisions: bool,
    pub enable_state_tracking: bool,
}

impl Default for DecisionMakerConfig {
    fn default() -> Self {
        Self {
            high_cpu_threshold: 80.0,
            high_memory_threshold: 85.0,
            default_strategy: ExecutionStrategyType::Tokio,
            rule_evaluation_timeout_ms: 100,
            enable_priority_adjustment: true,
            enable_rule_based_decisions: true,
            enable_state_tracking: true,
        }
    }
}

// Configuration for the Executor component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutorConfig {
    // Worker thread configurations
    pub tokio_worker_threads: Option<usize>,
    pub rayon_worker_threads: Option<usize>,
    // Queue capacities
    pub realtime_queue_capacity: usize,
    pub high_priority_queue_capacity: usize,
    pub normal_priority_queue_capacity: usize,
    pub low_priority_queue_capacity: usize,
    // Feature flags
    pub enable_task_timeout: bool,
    pub default_task_timeout_ms: u64,
}

impl Default for ExecutorConfig {
    fn default() -> Self {
        Self {
            tokio_worker_threads: None,
            rayon_worker_threads: None,
            realtime_queue_capacity: 100,
            high_priority_queue_capacity: 1000,
            normal_priority_queue_capacity: 5000,
            low_priority_queue_capacity: 10000,
            enable_task_timeout: true,
            default_task_timeout_ms: 30000,
        }
    }
}

// Configuration for the ExecutionStrategies component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStrategyConfig {
    // Default strategy
    pub default_strategy: ExecutionStrategyType,
    // Maximum concurrent tasks per strategy
    pub max_concurrent_direct_tasks: usize,
    pub max_concurrent_rayon_tasks: usize,
    pub max_concurrent_tokio_tasks: usize,
    // Task timeout in milliseconds
    pub task_timeout_ms: u64,
    // Feature flags
    pub enable_detailed_logging: bool,
}

impl Default for ExecutionStrategyConfig {
    fn default() -> Self {
        Self {
            default_strategy: ExecutionStrategyType::Tokio,
            max_concurrent_direct_tasks: 10,
            max_concurrent_rayon_tasks: num_cpus::get(),
            max_concurrent_tokio_tasks: num_cpus::get() * 2,
            task_timeout_ms: 30000,
            enable_detailed_logging: false,
        }
    }
}

// Configuration for the Storage service
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    // Connection details
    pub connection_string: String,
    pub username: String,
    pub password: String,
    pub namespace: String,
    pub database: String,
    pub use_http: bool,
    // Feature flags
    pub enable_query_cache: bool,
    pub cache_ttl_seconds: u64,
}

impl Default for StorageConfig {
    fn default() -> Self {
        Self {
            connection_string: "http://localhost:8000".to_string(),
            username: "root".to_string(),
            password: "root".to_string(),
            namespace: "prisma".to_string(),
            database: "prisma".to_string(),
            use_http: true,
            enable_query_cache: true,
            cache_ttl_seconds: 300,
        }
    }
}

// Configuration for the LLM service
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmConfig {
    // Model details
    pub model_path: String,
    pub context_size: usize,
    pub batch_size: usize,
    pub threads: usize,
    // Sampling parameters
    pub temperature: f64,
    pub top_k: usize,
    pub top_p: f64,
    pub min_p: f64,
    pub repeat_penalty: f64,
    pub presence_penalty: f64,
    pub frequency_penalty: f64,
    // Feature flags
    pub enable_streaming: bool,
}

impl Default for LlmConfig {
    fn default() -> Self {
        Self {
            model_path: "models/Meta-Llama-3-8B-Instruct.Q8_0.gguf".to_string(),
            context_size: 2048,
            batch_size: 512,
            threads: num_cpus::get(),
            temperature: 0.7,
            top_k: 40,
            top_p: 0.9,
            min_p: 0.05,
            repeat_penalty: 1.1,
            presence_penalty: 0.0,
            frequency_penalty: 0.0,
            enable_streaming: true,
        }
    }
}

// Configuration for the Embedding service
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingConfig {
    // Model details
    pub model_path: String,
    pub embedding_size: usize,
    pub batch_size: usize,
    pub threads: usize,
    // Feature flags
    pub enable_caching: bool,
    pub cache_ttl_seconds: u64,
}

impl Default for EmbeddingConfig {
    fn default() -> Self {
        Self {
            model_path: "models/all-MiniLM-L6-v2.Q4_0.gguf".to_string(),
            embedding_size: 384,
            batch_size: 32,
            threads: num_cpus::get(),
            enable_caching: true,
            cache_ttl_seconds: 3600,
        }
    }
}

// Configuration for the AgentManager component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentManagerConfig {
    // Agent limits
    pub max_agents: usize,
    pub max_capabilities_per_agent: usize,
    // Memory management
    pub short_term_memory_capacity: usize,
    pub long_term_memory_enabled: bool,
    // Feature flags
    pub enable_agent_communication: bool,
    pub enable_human_communication: bool,
    pub enable_capability_inheritance: bool,
}

impl Default for AgentManagerConfig {
    fn default() -> Self {
        Self {
            max_agents: 10,
            max_capabilities_per_agent: 20,
            short_term_memory_capacity: 100,
            long_term_memory_enabled: true,
            enable_agent_communication: true,
            enable_human_communication: true,
            enable_capability_inheritance: true,
        }
    }
}
