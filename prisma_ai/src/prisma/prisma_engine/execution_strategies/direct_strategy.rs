// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/direct_strategy.rs
// =================================================================================================
// Purpose: Legacy implementation of the direct execution strategy. This file provides functions
// for executing tasks directly in the current async context. It is maintained for backward
// compatibility and delegates to the new direct/ submodule implementation.
//
// Integration:
// - Internal Dependencies:
//   - `direct/`: New implementation of the direct execution strategy
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::any::Any`: For type-erased return values
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `executor`: May use these functions directly for backward compatibility
//   - `direct/`: New implementation that this file should eventually delegate to
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::any::Any;
use tracing::{instrument, debug};

// Import the new implementation
use super::direct::DirectStrategy;
use super::traits::ExecutionStrategy;

// We'll create a new DirectStrategy instance for each call

// Executes a task directly in the current async context.
// Suitable for very short, non-blocking tasks.
#[instrument(level = "debug", skip(task), fields(task_id = %task.id()))]
pub async fn execute_direct_task(task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
    // Record task_id explicitly as it's no longer in the macro args directly
    tracing::Span::current().record("task_id", &tracing::field::display(task.id()));
    debug!("Legacy direct_strategy.rs delegating to new DirectStrategy implementation");

    // Create a new DirectStrategy instance for this call
    let mut strategy = DirectStrategy::default();
    strategy.execute_task(task).await
}

// Overload for Arc<Mutex<Box<dyn Task>>>
#[instrument(level = "debug", skip(task_arc), fields(task_id))]
pub async fn execute_direct_task_arc(task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
    let task_id = { // Get ID for tracing field
        let task = task_arc.lock().await;
        task.id()
    };
    tracing::Span::current().record("task_id", &tracing::field::display(task_id));
    debug!("Legacy direct_strategy.rs delegating to new DirectStrategy implementation (Arc)");

    // Create a new DirectStrategy instance for this call
    let mut strategy = DirectStrategy::default();
    strategy.execute_task_arc(task_arc).await
}
