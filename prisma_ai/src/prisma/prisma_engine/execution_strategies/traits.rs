// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/traits.rs
// =================================================================================================
// Purpose: Defines common traits for all execution strategies. These traits establish a unified
// interface for task execution across different strategies, allowing the system to select the
// most appropriate strategy based on task characteristics and system state.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses ExecutionStrategyStats for metrics
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `crate::prisma::prisma_engine::types::ExecutionStrategyType`: Enum for strategy types
//
// - Module Interactions:
//   - `direct/`: Implements the ExecutionStrategy trait for direct execution
//   - `rayon/`: Implements the ExecutionStrategy trait for Rayon-based parallel execution
//   - `tokio/`: Implements the ExecutionStrategy trait for Tokio-based asynchronous execution
//   - `execution_strategies.rs`: Uses the ExecutionStrategy trait for strategy management
//   - `executor`: Uses the ExecutionStrategy trait for task execution
// =================================================================================================

use async_trait::async_trait;
use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::any::Any;

/// ExecutionStrategy defines the interface for all execution strategies.
/// It provides methods for executing tasks and managing strategy state.
#[async_trait]
pub trait ExecutionStrategy: Send + Sync {
    /// Executes a task using this strategy
    async fn execute_task(&mut self, task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>>;

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> using this strategy
    async fn execute_task_arc(&mut self, task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>>;

    /// Gets the current statistics for this strategy
    fn get_stats(&self) -> Box<dyn Any + Send>;

    /// Resets the statistics for this strategy
    fn reset_stats(&mut self);
}