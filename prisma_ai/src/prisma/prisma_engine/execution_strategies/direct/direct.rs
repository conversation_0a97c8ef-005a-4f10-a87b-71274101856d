// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/direct/direct.rs
// =================================================================================================
// Purpose: Implements the DirectStrategy for executing tasks directly in the current async context.
// This strategy is suitable for lightweight, non-blocking tasks that don't require separate
// concurrency mechanisms.
//
// Integration:
// - Internal Dependencies:
//   - `traits.rs`: Uses DirectStrategyTrait for the strategy interface
//   - `types.rs`: Uses DirectStrategyConfig and DirectStrategyStats for configuration and metrics
//   - `generics.rs`: Uses utility functions for task execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `executor`: Uses DirectStrategy for executing tasks directly
//   - `decision_maker`: Selects DirectStrategy based on task characteristics and system state
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::any::Any;
use tracing::{instrument, error, info, debug};
use async_trait::async_trait;

use super::traits::DirectStrategyTrait;
use super::types::{DirectStrategyConfig, DirectStrategyStats};
use crate::prisma::prisma_engine::execution_strategies::traits::ExecutionStrategy;

/// DirectStrategy implements the ExecutionStrategy trait for direct task execution.
/// It executes tasks directly in the current async context without additional concurrency mechanisms.
#[derive(Debug)]
pub struct DirectStrategy {
    /// Configuration for the direct execution strategy
    config: DirectStrategyConfig,
    /// Statistics for monitoring and telemetry
    stats: DirectStrategyStats,
}

impl DirectStrategy {
    /// Creates a new DirectStrategy with the given configuration
    pub fn new(config: DirectStrategyConfig) -> Self {
        DirectStrategy {
            config,
            stats: DirectStrategyStats::default(),
        }
    }

    /// Creates a new DirectStrategy with default configuration
    pub fn default() -> Self {
        DirectStrategy {
            config: DirectStrategyConfig::default(),
            stats: DirectStrategyStats::default(),
        }
    }

    /// Executes a task directly in the current async context.
    /// This is the core implementation used by both the trait methods and the legacy functions.
    #[instrument(level = "debug", skip(task), fields(task_id = %task.id()))]
    async fn execute_task_internal(&mut self, task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
        // Record task_id explicitly
        tracing::Span::current().record("task_id", &tracing::field::display(task.id()));
        info!("Executing task via DirectStrategy");

        // Update stats before execution
        self.stats.tasks_started += 1;
        let start_time = std::time::Instant::now();

        // Directly await the task's execute method
        let result = match task.execute().await {
            Ok(result) => {
                // Update stats after successful execution
                self.stats.tasks_completed += 1;
                self.stats.total_execution_time += start_time.elapsed();
                debug!("Task executed successfully via DirectStrategy");
                Ok(result)
            },
            Err(e) => {
                // Update stats after failed execution
                self.stats.tasks_failed += 1;
                self.stats.total_execution_time += start_time.elapsed();
                error!("Direct task execution failed: {:?}", e);
                Err(e) // Propagate the error
            }
        };

        // Log execution time
        debug!(
            "Task execution took {:?} via DirectStrategy",
            start_time.elapsed()
        );

        result
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> directly in the current async context.
    #[instrument(level = "debug", skip(task_arc), fields(task_id))]
    async fn execute_task_arc_internal(&mut self, task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
        let task_id = { // Get ID for tracing field
            let task = task_arc.lock().await;
            task.id()
        };
        tracing::Span::current().record("task_id", &tracing::field::display(task_id));

        info!("Executing task via DirectStrategy (Arc)");

        // Update stats before execution
        self.stats.tasks_started += 1;
        let start_time = std::time::Instant::now();

        // Lock the mutex and execute the task
        let mut task_guard = task_arc.lock().await;
        let result = match task_guard.execute().await {
            Ok(result) => {
                // Update stats after successful execution
                self.stats.tasks_completed += 1;
                self.stats.total_execution_time += start_time.elapsed();
                debug!("Task executed successfully via DirectStrategy (Arc)");
                Ok(result)
            },
            Err(e) => {
                // Update stats after failed execution
                self.stats.tasks_failed += 1;
                self.stats.total_execution_time += start_time.elapsed();
                error!("Direct task execution failed: {:?}", e);
                Err(e) // Propagate the error
            }
        };

        // Log execution time
        debug!(
            "Task execution took {:?} via DirectStrategy (Arc)",
            start_time.elapsed()
        );

        result
    }
}

#[async_trait]
impl ExecutionStrategy for DirectStrategy {
    /// Executes a task directly in the current async context
    async fn execute_task(&mut self, task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
        self.execute_task_internal(task).await
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> directly in the current async context
    async fn execute_task_arc(&mut self, task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
        self.execute_task_arc_internal(task_arc).await
    }

    /// Gets the current statistics for this strategy
    fn get_stats(&self) -> Box<dyn Any + Send> {
        Box::new(self.stats.clone())
    }

    /// Resets the statistics for this strategy
    fn reset_stats(&mut self) {
        self.stats = DirectStrategyStats::default();
    }
}

#[async_trait]
impl DirectStrategyTrait for DirectStrategy {
    /// Gets the configuration for this strategy
    fn get_config(&self) -> DirectStrategyConfig {
        self.config.clone()
    }

    /// Sets the configuration for this strategy
    fn set_config(&mut self, config: DirectStrategyConfig) {
        self.config = config;
    }

    /// Gets the statistics for this strategy
    fn get_direct_stats(&self) -> DirectStrategyStats {
        self.stats.clone()
    }
}