// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/direct/mod.rs
// =================================================================================================
// Purpose: Re-exports the direct execution strategy components and provides a unified interface
// for direct task execution. This module handles tasks that should be executed directly in the
// current async context without additional concurrency mechanisms.
//
// Integration:
// - Internal Dependencies:
//   - `direct.rs`: Contains the main DirectStrategy implementation
//   - `traits.rs`: Defines traits specific to direct execution
//   - `types.rs`: Defines types used by the direct execution strategy
//   - `generics.rs`: Provides utility functions for direct execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//
// - Module Interactions:
//   - `executor`: Uses DirectStrategy for executing tasks directly
//   - `decision_maker`: Selects DirectStrategy for lightweight, non-blocking tasks
//   - `direct_strategy.rs`: Legacy file that now delegates to this module
// =================================================================================================

// Declare the submodules
pub mod direct;
pub mod traits;
pub mod types;
pub mod generics;

// Re-export the main components for easier access
pub use direct::DirectStrategy;
pub use traits::DirectStrategyTrait;
pub use types::{DirectStrategyConfig, DirectStrategyStats};

// Re-export utility functions
pub use generics::{update_stats, check_timeout, log_task_execution};

// Provide a convenience function to create a new DirectStrategy with default configuration
pub fn create_default_direct_strategy() -> DirectStrategy {
    DirectStrategy::default()
}

// Provide a convenience function to create a new DirectStrategy with custom configuration
pub fn create_direct_strategy(config: DirectStrategyConfig) -> DirectStrategy {
    DirectStrategy::new(config)
}