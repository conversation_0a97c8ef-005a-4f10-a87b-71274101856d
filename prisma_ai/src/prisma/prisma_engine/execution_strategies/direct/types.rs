// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/direct/types.rs
// =================================================================================================
// Purpose: Defines types used by the direct execution strategy, including configuration options
// and statistics for monitoring and telemetry. These types provide a structured way to configure
// and monitor the direct execution strategy.
//
// Integration:
// - Internal Dependencies:
//   - None
//
// - External Dependencies:
//   - `serde`: For serialization and deserialization
//   - `std::time::Duration`: For timing metrics
//
// - Module Interactions:
//   - `direct.rs`: Uses DirectStrategyConfig and DirectStrategyStats
//   - `traits.rs`: References DirectStrategyStats in trait methods
//   - `executor`: Uses DirectStrategyConfig for initialization
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Configuration options for the direct execution strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectStrategyConfig {
    /// Maximum number of tasks that can be executed concurrently
    pub max_concurrent_tasks: usize,
    /// Maximum time a task can execute before it's considered timed out
    pub task_timeout: Option<Duration>,
    /// Whether to log detailed execution information
    pub detailed_logging: bool,
}

impl Default for DirectStrategyConfig {
    fn default() -> Self {
        DirectStrategyConfig {
            max_concurrent_tasks: 1, // Direct execution is single-threaded
            task_timeout: None,      // No timeout by default
            detailed_logging: false, // Minimal logging by default
        }
    }
}

/// Statistics for monitoring and telemetry of the direct execution strategy
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct DirectStrategyStats {
    /// Number of tasks that have been started
    pub tasks_started: usize,
    /// Number of tasks that have been completed successfully
    pub tasks_completed: usize,
    /// Number of tasks that have failed
    pub tasks_failed: usize,
    /// Number of tasks that have timed out
    pub tasks_timed_out: usize,
    /// Total time spent executing tasks
    pub total_execution_time: Duration,
    /// Average time spent executing tasks
    pub average_execution_time: Option<Duration>,
    /// Maximum time spent executing a task
    pub max_execution_time: Duration,
    /// Minimum time spent executing a task
    pub min_execution_time: Option<Duration>,
}