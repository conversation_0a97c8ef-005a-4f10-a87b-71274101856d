// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/direct/traits.rs
// =================================================================================================
// Purpose: Defines traits specific to the direct execution strategy. These traits establish the
// interface for direct task execution and provide a contract that the DirectStrategy implementation
// must fulfill.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses DirectStrategyStats for metrics
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//
// - Module Interactions:
//   - `direct.rs`: Implements the DirectStrategyTrait
//   - `executor`: Uses the DirectStrategyTrait for task execution
// =================================================================================================

use async_trait::async_trait;
use super::types::{DirectStrategyConfig, DirectStrategyStats};

/// DirectStrategyTrait defines the interface for direct execution strategies.
/// It provides methods for configuring and monitoring the strategy.
#[async_trait]
pub trait DirectStrategyTrait: Send + Sync {
    /// Gets the configuration for this strategy
    fn get_config(&self) -> DirectStrategyConfig;

    /// Sets the configuration for this strategy
    fn set_config(&mut self, config: DirectStrategyConfig);

    /// Gets the statistics for this strategy
    fn get_direct_stats(&self) -> DirectStrategyStats;
}