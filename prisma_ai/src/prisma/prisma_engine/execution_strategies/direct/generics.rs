// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/direct/generics.rs
// =================================================================================================
// Purpose: Provides utility functions for the direct execution strategy. These functions handle
// common operations like task execution timing, error handling, and statistics updates. They
// serve as building blocks for the DirectStrategy implementation.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses DirectStrategyStats for metrics updates
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::time::{Duration, Instant}`: For timing metrics
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `direct.rs`: Uses utility functions for task execution
//   - `direct_strategy.rs`: Legacy file that may use these functions
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::time::{Duration, Instant};
use std::any::Any;
use tracing::{debug, error, info};
use super::types::DirectStrategyStats;

/// Updates the statistics with the result of a task execution
pub fn update_stats(
    stats: &mut DirectStrategyStats,
    start_time: Instant,
    result: &PrismaResult<Box<dyn Any + Send>>,
) {
    let elapsed = start_time.elapsed();

    // Update total execution time
    stats.total_execution_time += elapsed;

    // Update max execution time if this task took longer
    if elapsed > stats.max_execution_time {
        stats.max_execution_time = elapsed;
    }

    // Update min execution time if this is the first task or if this task took less time
    if stats.min_execution_time.is_none() || elapsed < stats.min_execution_time.unwrap() {
        stats.min_execution_time = Some(elapsed);
    }

    // Update average execution time
    let total_tasks = stats.tasks_completed + stats.tasks_failed + stats.tasks_timed_out;
    if total_tasks > 0 {
        stats.average_execution_time = Some(
            stats.total_execution_time / total_tasks as u32
        );
    }

    // Update success/failure counts
    match result {
        Ok(_) => {
            stats.tasks_completed += 1;
            debug!("Task completed successfully in {:?}", elapsed);
        },
        Err(e) => {
            stats.tasks_failed += 1;
            error!("Task failed after {:?}: {:?}", elapsed, e);
        }
    }
}

/// Checks if a task has timed out based on the configured timeout
pub fn check_timeout(
    start_time: Instant,
    timeout: Option<Duration>,
) -> bool {
    if let Some(timeout_duration) = timeout {
        if start_time.elapsed() > timeout_duration {
            return true;
        }
    }
    false
}

/// Logs task execution information
pub fn log_task_execution(
    task: &dyn Task,
    start_time: Instant,
    detailed_logging: bool,
) {
    let task_id = task.id();
    let elapsed = start_time.elapsed();

    if detailed_logging {
        info!(
            "Task {} executed in {:?} (category: {:?}, priority: {:?})",
            task_id,
            elapsed,
            task.category(),
            task.priority()
        );
    } else {
        debug!("Task {} executed in {:?}", task_id, elapsed);
    }
}