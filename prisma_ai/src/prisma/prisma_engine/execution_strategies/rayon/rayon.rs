// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/rayon/rayon.rs
// =================================================================================================
// Purpose: Implements the RayonStrategy for executing tasks in parallel using Rayon's thread pool.
// This strategy is suitable for CPU-intensive tasks that benefit from parallel processing across
// multiple threads, such as data processing, batch operations, and computationally expensive
// operations.
//
// Integration:
// - Internal Dependencies:
//   - `traits.rs`: Uses RayonStrategyTrait for the strategy interface
//   - `types.rs`: Uses RayonStrategyConfig and RayonStrategyStats for configuration and metrics
//   - `generics.rs`: Uses utility functions for task execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `rayon`: For parallel processing
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `executor`: Uses RayonStrategy for executing CPU-intensive tasks
//   - `decision_maker`: Selects RayonStrategy based on task characteristics and system state
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::{PrismaResult, GenericError};
use std::sync::Arc;
use tokio::sync::Mutex;
use std::any::Any;
use tracing::{instrument, error, info, debug};
use async_trait::async_trait;
use rayon::ThreadPool;
use std::time::Instant;
use std::sync::atomic::{AtomicUsize, Ordering};

use super::traits::RayonStrategyTrait;
use super::types::{RayonStrategyConfig, RayonStrategyStats};
use super::generics::{update_stats, check_timeout, log_task_execution, create_thread_pool};
use crate::prisma::prisma_engine::execution_strategies::traits::ExecutionStrategy;

/// RayonStrategy implements the ExecutionStrategy trait for parallel task execution using Rayon.
/// It executes tasks in parallel using Rayon's work-stealing thread pool, which is optimized for
/// CPU-bound tasks that can be parallelized.
#[derive(Debug)]
pub struct RayonStrategy {
    /// Configuration for the Rayon execution strategy
    config: RayonStrategyConfig,
    /// Statistics for monitoring and telemetry
    stats: RayonStrategyStats,
    /// Rayon thread pool for parallel execution
    thread_pool: ThreadPool,
    /// Number of tasks currently in progress
    tasks_in_progress: Arc<AtomicUsize>,
    /// Number of tasks waiting to be executed
    tasks_waiting: Arc<AtomicUsize>,
}

impl RayonStrategy {
    /// Creates a new RayonStrategy with the given configuration
    pub fn new(config: RayonStrategyConfig) -> Self {
        // Create a thread pool with the configured number of threads
        let thread_pool = match create_thread_pool(config.num_threads) {
            Ok(pool) => pool,
            Err(e) => {
                error!("Failed to create Rayon thread pool: {:?}", e);
                // Fall back to the global Rayon thread pool
                rayon::ThreadPoolBuilder::new()
                    .build()
                    .expect("Failed to create fallback Rayon thread pool")
            }
        };

        RayonStrategy {
            config,
            stats: RayonStrategyStats {
                thread_count: thread_pool.current_num_threads(),
                ..RayonStrategyStats::default()
            },
            thread_pool,
            tasks_in_progress: Arc::new(AtomicUsize::new(0)),
            tasks_waiting: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// Creates a new RayonStrategy with default configuration
    pub fn default() -> Self {
        Self::new(RayonStrategyConfig::default())
    }

    /// Executes a task in the Rayon thread pool.
    /// This is the core implementation used by both the trait methods and the legacy functions.
    #[instrument(level = "debug", skip(self, task), fields(task_id = %task.id()))]
    async fn execute_task_internal(&mut self, task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
        // Record task_id explicitly
        tracing::Span::current().record("task_id", &tracing::field::display(task.id()));
        info!("Executing task via RayonStrategy");

        // Check if we've reached the maximum number of concurrent tasks
        let current_tasks = self.tasks_in_progress.load(Ordering::SeqCst);
        if current_tasks >= self.config.max_concurrent_tasks {
            self.tasks_waiting.fetch_add(1, Ordering::SeqCst);
            debug!("Waiting for available slot in Rayon thread pool (current: {}, max: {})",
                current_tasks, self.config.max_concurrent_tasks);

            // Wait until a slot becomes available
            while self.tasks_in_progress.load(Ordering::SeqCst) >= self.config.max_concurrent_tasks {
                tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
            }

            self.tasks_waiting.fetch_sub(1, Ordering::SeqCst);
        }

        // Update stats before execution
        self.stats.tasks_started += 1;
        self.tasks_in_progress.fetch_add(1, Ordering::SeqCst);
        let start_time = Instant::now();

        // Get configuration values
        let timeout = self.config.task_timeout;
        let detailed_logging = self.config.detailed_logging;

        // We need to execute the task directly since we can't move the borrowed reference into the closure
        // First, check for timeout
        if check_timeout(start_time, timeout) {
            return Err(GenericError::new(std::io::Error::new(
                std::io::ErrorKind::TimedOut,
                format!("Task execution timed out after {:?}", start_time.elapsed()),
            )));
        }

        // Log task execution
        log_task_execution(task, start_time, detailed_logging);

        // Execute the task directly
        let result = task.execute().await;

        // Update stats after execution
        self.tasks_in_progress.fetch_sub(1, Ordering::SeqCst);
        self.stats.tasks_in_progress = self.tasks_in_progress.load(Ordering::SeqCst);
        self.stats.tasks_waiting = self.tasks_waiting.load(Ordering::SeqCst);

        // Update timeout count if needed
        if let Err(e) = &result {
            if e.to_string().contains("timed out") {
                self.stats.tasks_timed_out += 1;
            }
        }

        // Update other stats
        update_stats(&mut self.stats, start_time, &result);

        result
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> in the Rayon thread pool.
    #[instrument(level = "debug", skip(self, task_arc), fields(task_id))]
    async fn execute_task_arc_internal(&mut self, task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
        let task_id = { // Get ID for tracing field
            let task = task_arc.lock().await;
            task.id()
        };
        tracing::Span::current().record("task_id", &tracing::field::display(task_id));

        info!("Executing task via RayonStrategy (Arc)");

        // Check if we've reached the maximum number of concurrent tasks
        let current_tasks = self.tasks_in_progress.load(Ordering::SeqCst);
        if current_tasks >= self.config.max_concurrent_tasks {
            self.tasks_waiting.fetch_add(1, Ordering::SeqCst);
            debug!("Waiting for available slot in Rayon thread pool (current: {}, max: {})",
                current_tasks, self.config.max_concurrent_tasks);

            // Wait until a slot becomes available
            while self.tasks_in_progress.load(Ordering::SeqCst) >= self.config.max_concurrent_tasks {
                tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
            }

            self.tasks_waiting.fetch_sub(1, Ordering::SeqCst);
        }

        // Update stats before execution
        self.stats.tasks_started += 1;
        self.tasks_in_progress.fetch_add(1, Ordering::SeqCst);
        let start_time = Instant::now();

        // Execute the task directly
        let mut task_guard = task_arc.lock().await;
        let result = task_guard.execute().await;

        // Update stats after execution
        self.tasks_in_progress.fetch_sub(1, Ordering::SeqCst);
        self.stats.tasks_in_progress = self.tasks_in_progress.load(Ordering::SeqCst);
        self.stats.tasks_waiting = self.tasks_waiting.load(Ordering::SeqCst);

        // Update timeout count if needed
        if let Err(e) = &result {
            if e.to_string().contains("timed out") {
                self.stats.tasks_timed_out += 1;
            }
        }

        // Update other stats
        update_stats(&mut self.stats, start_time, &result);

        result
    }
}

#[async_trait]
impl ExecutionStrategy for RayonStrategy {
    /// Executes a task in the Rayon thread pool
    async fn execute_task(&mut self, task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
        self.execute_task_internal(task).await
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> in the Rayon thread pool
    async fn execute_task_arc(&mut self, task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
        self.execute_task_arc_internal(task_arc).await
    }

    /// Gets the current statistics for this strategy
    fn get_stats(&self) -> Box<dyn Any + Send> {
        Box::new(self.stats.clone())
    }

    /// Resets the statistics for this strategy
    fn reset_stats(&mut self) {
        self.stats = RayonStrategyStats {
            thread_count: self.thread_pool.current_num_threads(),
            ..RayonStrategyStats::default()
        };
    }
}

#[async_trait]
impl RayonStrategyTrait for RayonStrategy {
    /// Gets the configuration for this strategy
    fn get_config(&self) -> RayonStrategyConfig {
        self.config.clone()
    }

    /// Sets the configuration for this strategy
    fn set_config(&mut self, config: RayonStrategyConfig) -> PrismaResult<()> {
        // Check if we need to resize the thread pool
        if config.num_threads != self.config.num_threads {
            self.resize_thread_pool(config.num_threads)?;
        }

        // Update the configuration
        self.config = config;

        Ok(())
    }

    /// Gets the statistics for this strategy
    fn get_rayon_stats(&self) -> RayonStrategyStats {
        self.stats.clone()
    }

    /// Gets a reference to the Rayon thread pool
    fn get_thread_pool(&self) -> &ThreadPool {
        &self.thread_pool
    }

    /// Resizes the thread pool to the specified number of threads
    fn resize_thread_pool(&mut self, num_threads: usize) -> PrismaResult<()> {
        // Create a new thread pool with the specified number of threads
        let new_thread_pool = create_thread_pool(num_threads)?;

        // Replace the old thread pool with the new one
        self.thread_pool = new_thread_pool;

        // Update the statistics
        self.stats.thread_count = self.thread_pool.current_num_threads();
        self.stats.thread_pool_resizes += 1;

        Ok(())
    }

    /// Checks if the thread pool is active
    fn is_thread_pool_active(&self) -> bool {
        // Rayon thread pools are always active once created
        true
    }
}