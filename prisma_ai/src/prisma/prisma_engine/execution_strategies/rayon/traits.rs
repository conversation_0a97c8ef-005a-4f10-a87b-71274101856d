// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/rayon/traits.rs
// =================================================================================================
// Purpose: Defines traits specific to the Rayon execution strategy. These traits establish the
// interface for parallel task execution using Rayon and provide a contract that the RayonStrategy
// implementation must fulfill.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses RayonStrategyStats for metrics
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `rayon`: For parallel processing
//
// - Module Interactions:
//   - `rayon.rs`: Implements the RayonStrategyTrait
//   - `executor`: Uses the RayonStrategyTrait for task execution
// =================================================================================================

use async_trait::async_trait;
use super::types::{RayonStrategyConfig, RayonStrategyStats};
use rayon::ThreadPool;
use crate::err::PrismaResult;

/// RayonStrategyTrait defines the interface for Rayon-based parallel execution strategies.
/// It provides methods for configuring and monitoring the strategy, as well as accessing
/// the underlying Rayon thread pool.
#[async_trait]
pub trait RayonStrategyTrait: Send + Sync {
    /// Gets the configuration for this strategy
    fn get_config(&self) -> RayonStrategyConfig;

    /// Sets the configuration for this strategy
    fn set_config(&mut self, config: RayonStrategyConfig) -> PrismaResult<()>;

    /// Gets the statistics for this strategy
    fn get_rayon_stats(&self) -> RayonStrategyStats;

    /// Gets a reference to the Rayon thread pool
    fn get_thread_pool(&self) -> &ThreadPool;

    /// Resizes the thread pool to the specified number of threads
    fn resize_thread_pool(&mut self, num_threads: usize) -> PrismaResult<()>;

    /// Checks if the thread pool is active
    fn is_thread_pool_active(&self) -> bool;
}