// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/rayon/types.rs
// =================================================================================================
// Purpose: Defines types used by the Rayon execution strategy, including configuration options
// and statistics for monitoring and telemetry. These types provide a structured way to configure
// and monitor the Rayon execution strategy.
//
// Integration:
// - Internal Dependencies:
//   - None
//
// - External Dependencies:
//   - `serde`: For serialization and deserialization
//   - `std::time::Duration`: For timing metrics
//   - `rayon`: For parallel processing configuration
//
// - Module Interactions:
//   - `rayon.rs`: Uses RayonStrategyConfig and RayonStrategyStats
//   - `traits.rs`: References RayonStrategyStats in trait methods
//   - `executor`: Uses RayonStrategyConfig for initialization
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Configuration options for the Rayon execution strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RayonStrategyConfig {
    /// Number of threads in the Rayon thread pool
    pub num_threads: usize,
    /// Maximum number of tasks that can be executed concurrently
    pub max_concurrent_tasks: usize,
    /// Maximum time a task can execute before it's considered timed out
    pub task_timeout: Option<Duration>,
    /// Whether to log detailed execution information
    pub detailed_logging: bool,
    /// Whether to use work stealing for load balancing
    pub use_work_stealing: bool,
    /// Stack size for Rayon worker threads (in bytes)
    pub thread_stack_size: Option<usize>,
    /// Thread name prefix for Rayon worker threads
    pub thread_name: Option<String>,
}

impl Default for RayonStrategyConfig {
    fn default() -> Self {
        RayonStrategyConfig {
            num_threads: num_cpus::get(),
            max_concurrent_tasks: num_cpus::get() * 2,
            task_timeout: None,
            detailed_logging: false,
            use_work_stealing: true,
            thread_stack_size: None,
            thread_name: Some("prisma-rayon-worker".to_string()),
        }
    }
}

/// Statistics for monitoring and telemetry of the Rayon execution strategy
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct RayonStrategyStats {
    /// Number of tasks that have been started
    pub tasks_started: usize,
    /// Number of tasks that have been completed successfully
    pub tasks_completed: usize,
    /// Number of tasks that have failed
    pub tasks_failed: usize,
    /// Number of tasks that have timed out
    pub tasks_timed_out: usize,
    /// Total time spent executing tasks
    pub total_execution_time: Duration,
    /// Average time spent executing tasks
    pub average_execution_time: Option<Duration>,
    /// Maximum time spent executing a task
    pub max_execution_time: Duration,
    /// Minimum time spent executing a task
    pub min_execution_time: Option<Duration>,
    /// Number of tasks currently executing
    pub tasks_in_progress: usize,
    /// Number of tasks waiting to be executed
    pub tasks_waiting: usize,
    /// Number of threads in the thread pool
    pub thread_count: usize,
    /// Number of times the thread pool has been resized
    pub thread_pool_resizes: usize,
}