// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/rayon/mod.rs
// =================================================================================================
// Purpose: Re-exports the Rayon execution strategy components and provides a unified interface
// for parallel task execution using Rayon. This module handles CPU-intensive tasks that benefit
// from parallel processing across multiple threads.
//
// Integration:
// - Internal Dependencies:
//   - `rayon.rs`: Contains the main RayonStrategy implementation
//   - `traits.rs`: Defines traits specific to Rayon execution
//   - `types.rs`: Defines types used by the Rayon execution strategy
//   - `generics.rs`: Provides utility functions for Rayon execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `rayon`: For parallel processing
//
// - Module Interactions:
//   - `executor`: Uses RayonStrategy for executing CPU-intensive tasks
//   - `decision_maker`: Selects RayonStrategy for tasks that benefit from parallel processing
//   - `rayon_strategy.rs`: Legacy file that now delegates to this module
// =================================================================================================

// Declare the submodules
pub mod rayon;
pub mod traits;
pub mod types;
pub mod generics;

// Re-export the main components for easier access
pub use rayon::RayonStrategy;
pub use traits::RayonStrategyTrait;
pub use types::{RayonStrategyConfig, RayonStrategyStats};

// Re-export utility functions
pub use generics::{update_stats, check_timeout, log_task_execution, create_thread_pool};

// Provide a convenience function to create a new RayonStrategy with default configuration
pub fn create_default_rayon_strategy() -> RayonStrategy {
    RayonStrategy::default()
}

// Provide a convenience function to create a new RayonStrategy with custom configuration
pub fn create_rayon_strategy(config: RayonStrategyConfig) -> RayonStrategy {
    RayonStrategy::new(config)
}