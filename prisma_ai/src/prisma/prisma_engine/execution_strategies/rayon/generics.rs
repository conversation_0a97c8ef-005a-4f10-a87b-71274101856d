// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/rayon/generics.rs
// =================================================================================================
// Purpose: Provides utility functions for the Rayon execution strategy. These functions handle
// common operations like task execution timing, error handling, and statistics updates. They
// also provide utilities for integrating async tasks with Rayon's synchronous thread pool.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses RayonStrategyStats for metrics updates
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::time::{Duration, Instant}`: For timing metrics
//   - `rayon`: For parallel processing
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `rayon.rs`: Uses utility functions for task execution
//   - `rayon_strategy.rs`: Legacy file that may use these functions
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::{PrismaResult, GenericError};
use std::time::{Duration, Instant};
use std::any::Any;
use tracing::{debug, error, info};
use super::types::RayonStrategyStats;
use tokio::runtime::Handle;
use tokio::sync::Mutex;
use std::sync::Arc;
use rayon::ThreadPool;

/// Updates the statistics with the result of a task execution
pub fn update_stats(
    stats: &mut RayonStrategyStats,
    start_time: Instant,
    result: &PrismaResult<Box<dyn Any + Send>>,
) {
    let elapsed = start_time.elapsed();

    // Update total execution time
    stats.total_execution_time += elapsed;

    // Update max execution time if this task took longer
    if elapsed > stats.max_execution_time {
        stats.max_execution_time = elapsed;
    }

    // Update min execution time if this is the first task or if this task took less time
    if stats.min_execution_time.is_none() || elapsed < stats.min_execution_time.unwrap() {
        stats.min_execution_time = Some(elapsed);
    }

    // Update average execution time
    let total_tasks = stats.tasks_completed + stats.tasks_failed + stats.tasks_timed_out;
    if total_tasks > 0 {
        stats.average_execution_time = Some(
            stats.total_execution_time / total_tasks as u32
        );
    }

    // Update success/failure counts
    match result {
        Ok(_) => {
            stats.tasks_completed += 1;
            debug!("Task completed successfully in {:?} via Rayon", elapsed);
        },
        Err(e) => {
            stats.tasks_failed += 1;
            error!("Task execution failed in Rayon: {:?}", e);
        }
    }
}

/// Checks if a task has timed out based on the configured timeout
pub fn check_timeout(
    start_time: Instant,
    timeout: Option<Duration>,
) -> bool {
    if let Some(timeout_duration) = timeout {
        if start_time.elapsed() > timeout_duration {
            return true;
        }
    }
    false
}

/// Logs task execution information
pub fn log_task_execution(
    task: &dyn Task,
    start_time: Instant,
    detailed_logging: bool,
) {
    let task_id = task.id();
    let elapsed = start_time.elapsed();

    if detailed_logging {
        info!(
            "Task {} executed in {:?} via Rayon strategy (category: {:?}, priority: {:?})",
            task_id,
            elapsed,
            task.category(),
            task.priority()
        );
    } else {
        debug!("Task {} executed in {:?} via Rayon strategy", task_id, elapsed);
    }
}

/// Executes an async task in the Rayon thread pool by blocking the thread
/// and running the task on the current Tokio runtime
pub async fn execute_async_task_in_rayon<F, T>(
    thread_pool: &ThreadPool,
    future: F,
) -> PrismaResult<T>
where
    F: std::future::Future<Output = PrismaResult<T>> + Send + 'static,
    T: Send + 'static,
{
    // Get a handle to the current Tokio runtime
    let handle = Handle::try_current().map_err(|e| {
        GenericError::new(std::io::Error::new(
            std::io::ErrorKind::Other,
            format!("Failed to get Tokio runtime handle: {}", e),
        ))
    })?;

    // Use a oneshot channel for async communication
    let (tx, rx) = tokio::sync::oneshot::channel();

    // Execute the task in the Rayon thread pool
    thread_pool.spawn(move || {
        // Block the Rayon thread and run the future on the Tokio runtime
        let result = handle.block_on(future);

        // Send the result back through the channel
        let _ = tx.send(result);
    });

    // Wait for the result from the Rayon thread
    rx.await.map_err(|e| {
        GenericError::new(std::io::Error::new(
            std::io::ErrorKind::Other,
            format!("Failed to receive result from Rayon thread: {}", e),
        ))
    })?
}

/// Creates a new thread pool with the specified number of threads
pub fn create_thread_pool(num_threads: usize) -> PrismaResult<ThreadPool> {
    rayon::ThreadPoolBuilder::new()
        .num_threads(num_threads)
        .build()
        .map_err(|e| {
            GenericError::new(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Failed to create Rayon thread pool: {}", e),
            ))
        })
}

/// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> in the Rayon thread pool
pub async fn execute_task_arc_in_rayon(
    thread_pool: &ThreadPool,
    task_arc: Arc<Mutex<Box<dyn Task>>>,
) -> PrismaResult<Box<dyn Any + Send>> {
    // Create a clone of the task_arc for the Rayon thread
    let task_arc_clone = task_arc.clone();

    // Execute the task in the Rayon thread pool
    execute_async_task_in_rayon(thread_pool, async move {
        // Lock the mutex and execute the task
        let mut task_guard = task_arc_clone.lock().await;
        task_guard.execute().await
    }).await
}