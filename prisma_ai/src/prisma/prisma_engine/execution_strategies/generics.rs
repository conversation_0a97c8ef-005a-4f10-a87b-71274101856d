// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/generics.rs
// =================================================================================================
// Purpose: Provides common utility functions for all execution strategies. These functions handle
// operations that are shared across different strategies, such as task execution timing, error
// handling, and statistics updates.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses ExecutionStrategyStats for metrics updates
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::time::{Duration, Instant}`: For timing metrics
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `direct/`: Uses utility functions for direct execution
//   - `rayon/`: Uses utility functions for Rayon-based execution
//   - `tokio/`: Uses utility functions for Tokio-based execution
//   - `execution_strategies.rs`: Uses utility functions for strategy management
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::time::{Duration, Instant};
use std::any::Any;
use tracing::{debug, error, info};
use super::types::ExecutionStrategyStats;
use crate::prisma::prisma_engine::types::ExecutionStrategyType;

/// Updates the statistics with the result of a task execution
pub fn update_stats(
    stats: &mut ExecutionStrategyStats,
    start_time: Instant,
    result: &PrismaResult<Box<dyn Any + Send>>,
) {
    let elapsed = start_time.elapsed();

    // Update total execution time
    stats.total_execution_time += elapsed;

    // Update max execution time if this task took longer
    if elapsed > stats.max_execution_time {
        stats.max_execution_time = elapsed;
    }

    // Update min execution time if this is the first task or if this task took less time
    if stats.min_execution_time.is_none() || elapsed < stats.min_execution_time.unwrap() {
        stats.min_execution_time = Some(elapsed);
    }

    // Update average execution time
    let total_tasks = stats.tasks_completed + stats.tasks_failed + stats.tasks_timed_out;
    if total_tasks > 0 {
        stats.average_execution_time = Some(
            stats.total_execution_time / total_tasks as u32
        );
    }

    // Update success/failure counts
    match result {
        Ok(_) => {
            stats.tasks_completed += 1;
            debug!("Task completed successfully in {:?}", elapsed);
        },
        Err(e) => {
            stats.tasks_failed += 1;
            error!("Task execution failed: {:?}", e);
        }
    }
}

/// Checks if a task has timed out based on the configured timeout
pub fn check_timeout(
    start_time: Instant,
    timeout: Option<Duration>,
) -> bool {
    if let Some(timeout_duration) = timeout {
        if start_time.elapsed() > timeout_duration {
            return true;
        }
    }
    false
}

/// Logs task execution information
pub fn log_task_execution(
    task: &dyn Task,
    start_time: Instant,
    strategy_type: ExecutionStrategyType,
    detailed_logging: bool,
) {
    let task_id = task.id();
    let elapsed = start_time.elapsed();

    if detailed_logging {
        info!(
            "Task {} executed in {:?} via {:?} strategy (category: {:?}, priority: {:?})",
            task_id,
            elapsed,
            strategy_type,
            task.category(),
            task.priority()
        );
    } else {
        debug!(
            "Task {} executed in {:?} via {:?} strategy",
            task_id,
            elapsed,
            strategy_type
        );
    }
}

/// Creates a new ExecutionStrategyStats instance for the given strategy type
pub fn create_stats(strategy_type: ExecutionStrategyType) -> ExecutionStrategyStats {
    ExecutionStrategyStats {
        strategy_type,
        ..ExecutionStrategyStats::default()
    }
}