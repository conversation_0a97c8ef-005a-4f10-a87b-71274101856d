// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/tokio/traits.rs
// =================================================================================================
// Purpose: Defines traits specific to the Tokio execution strategy. These traits establish the
// interface for asynchronous task execution using Tokio and provide a contract that the
// TokioStrategy implementation must fulfill.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses TokioStrategyStats for metrics
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `tokio`: For asynchronous processing
//
// - Module Interactions:
//   - `tokio.rs`: Implements the TokioStrategyTrait
//   - `executor`: Uses the TokioStrategyTrait for task execution
// =================================================================================================

use async_trait::async_trait;
use super::types::{TokioStrategyConfig, TokioStrategyStats};
use tokio::runtime::Handle;
use crate::err::PrismaResult;

/// TokioStrategyTrait defines the interface for Tokio execution strategies.
/// It provides methods for configuring and monitoring the strategy.
#[async_trait]
pub trait TokioStrategyTrait: Send + Sync {
    /// Gets the configuration for this strategy
    fn get_config(&self) -> TokioStrategyConfig;

    /// Sets the configuration for this strategy
    fn set_config(&mut self, config: TokioStrategyConfig);

    /// Gets the statistics for this strategy
    fn get_tokio_stats(&self) -> TokioStrategyStats;

    /// Gets the Tokio runtime handle used by this strategy
    fn get_runtime_handle(&self) -> PrismaResult<Handle>;

    /// Sets the maximum number of concurrent tasks
    fn set_max_concurrent_tasks(&mut self, max_tasks: usize);

    /// Gets the maximum number of concurrent tasks
    fn get_max_concurrent_tasks(&self) -> usize;
}