// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/tokio/tokio.rs
// =================================================================================================
// Purpose: Implements the TokioStrategy for executing tasks asynchronously using Tokio's runtime.
// This strategy is suitable for I/O-bound tasks that benefit from asynchronous processing, such
// as network requests, database operations, and file I/O.
//
// Integration:
// - Internal Dependencies:
//   - `traits.rs`: Uses TokioStrategyTrait for the strategy interface
//   - `types.rs`: Uses TokioStrategyConfig and TokioStrategyStats for configuration and metrics
//   - `generics.rs`: Uses utility functions for task execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `tokio`: For asynchronous processing
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `executor`: Uses TokioStrategy for executing I/O-bound tasks
//   - `decision_maker`: Selects TokioStrategy based on task characteristics and system state
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::Instant;
use tokio::runtime::{Handle, Runtime};
use tokio::sync::{Mutex, Semaphore};
use tracing::{error, info, instrument, trace};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::execution_strategies::traits::ExecutionStrategy;

use super::generics::{execute_task_with_timeout, get_or_create_runtime, log_tokio_execution, update_stats};
use super::traits::TokioStrategyTrait;
use super::types::{TokioStrategyConfig, TokioStrategyStats};

/// TokioStrategy implements the ExecutionStrategy trait for asynchronous task execution.
/// It executes tasks using Tokio's async runtime, which is optimized for I/O-bound tasks.
#[derive(Debug)]
pub struct TokioStrategy {
    /// Configuration for the Tokio execution strategy
    config: TokioStrategyConfig,
    /// Statistics for monitoring and telemetry
    stats: Arc<RwLock<TokioStrategyStats>>,
    /// Semaphore for limiting concurrent tasks
    semaphore: Arc<Semaphore>,
    /// Optional dedicated runtime for this strategy
    runtime: Option<Runtime>,
}

impl Default for TokioStrategy {
    fn default() -> Self {
        Self::new(TokioStrategyConfig::default())
    }
}

impl TokioStrategy {
    /// Creates a new TokioStrategy with the given configuration
    pub fn new(config: TokioStrategyConfig) -> Self {
        let runtime = if config.use_dedicated_runtime {
            let mut builder = tokio::runtime::Builder::new_multi_thread();

            if let Some(threads) = config.worker_threads {
                builder.worker_threads(threads);
            }

            if let Some(name) = &config.thread_name {
                builder.thread_name(name);
            }

            match builder.enable_all().build() {
                Ok(rt) => {
                    info!("Created dedicated Tokio runtime for TokioStrategy");
                    Some(rt)
                }
                Err(e) => {
                    error!("Failed to create dedicated Tokio runtime: {}", e);
                    None
                }
            }
        } else {
            None
        };

        let stats = Arc::new(RwLock::new(TokioStrategyStats {
            max_execution_time: std::time::Duration::from_secs(0),
            ..Default::default()
        }));

        TokioStrategy {
            semaphore: Arc::new(Semaphore::new(config.max_concurrent_tasks)),
            config,
            stats,
            runtime,
        }
    }
}

#[async_trait]
impl ExecutionStrategy for TokioStrategy {
    /// Executes a task using the Tokio strategy
    #[instrument(level = "debug", skip(self, task), fields(task_id = %task.id()))]
    async fn execute_task(&mut self, task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
        let task_id = task.id();
        trace!("Executing task {} with Tokio strategy", task_id);

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            stats.tasks_started += 1;
            stats.tasks_in_progress += 1;
        }

        // Acquire a permit from the semaphore to limit concurrent tasks
        let _permit = match self.semaphore.acquire().await {
            Ok(permit) => permit,
            Err(e) => {
                error!("Failed to acquire semaphore permit: {}", e);
                return Err(GenericError::from(format!(
                    "Failed to acquire semaphore permit: {}",
                    e
                )));
            }
        };

        let start_time = Instant::now();
        let result = if let Some(timeout) = self.config.task_timeout {
            execute_task_with_timeout(task, timeout).await
        } else {
            task.execute().await
        };

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            stats.tasks_in_progress -= 1;
            update_stats(&mut *stats, start_time, &result);
        }

        // Log execution details
        log_tokio_execution(task, start_time.elapsed(), self.config.detailed_logging);

        result
    }

    /// Gets the statistics for this strategy
    fn get_stats(&self) -> Box<dyn Any + Send> {
        let stats = self.stats.read().unwrap();
        let stats_clone = stats.clone();
        Box::new(stats_clone)
    }

    /// Resets the statistics for this strategy
    fn reset_stats(&mut self) {
        let mut stats = self.stats.write().unwrap();
        *stats = TokioStrategyStats {
            max_execution_time: std::time::Duration::from_secs(0),
            ..Default::default()
        };
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> using the Tokio strategy
    async fn execute_task_arc(&mut self, task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
        let task_id = {
            let task = task_arc.lock().await;
            task.id()
        };
        trace!("Executing task {} with Tokio strategy (Arc)", task_id);

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            stats.tasks_started += 1;
            stats.tasks_in_progress += 1;
        }

        // Acquire a permit from the semaphore to limit concurrent tasks
        let _permit = match self.semaphore.acquire().await {
            Ok(permit) => permit,
            Err(e) => {
                error!("Failed to acquire semaphore permit: {}", e);
                return Err(GenericError::from(format!(
                    "Failed to acquire semaphore permit: {}",
                    e
                )));
            }
        };

        let start_time = Instant::now();
        let result = super::generics::execute_tokio_task_arc(task_arc.clone()).await;

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            stats.tasks_in_progress -= 1;
            update_stats(&mut *stats, start_time, &result);
        }

        result
    }
}

#[async_trait]
impl TokioStrategyTrait for TokioStrategy {
    /// Gets the configuration for this strategy
    fn get_config(&self) -> TokioStrategyConfig {
        self.config.clone()
    }

    /// Sets the configuration for this strategy
    fn set_config(&mut self, config: TokioStrategyConfig) {
        // Update semaphore if max_concurrent_tasks changed
        if config.max_concurrent_tasks != self.config.max_concurrent_tasks {
            self.semaphore = Arc::new(Semaphore::new(config.max_concurrent_tasks));
        }

        // Update runtime if use_dedicated_runtime changed
        if config.use_dedicated_runtime != self.config.use_dedicated_runtime {
            if config.use_dedicated_runtime {
                let mut builder = tokio::runtime::Builder::new_multi_thread();

                if let Some(threads) = config.worker_threads {
                    builder.worker_threads(threads);
                }

                if let Some(name) = &config.thread_name {
                    builder.thread_name(name);
                }

                match builder.enable_all().build() {
                    Ok(rt) => {
                        info!("Created dedicated Tokio runtime for TokioStrategy");
                        self.runtime = Some(rt);
                    }
                    Err(e) => {
                        error!("Failed to create dedicated Tokio runtime: {}", e);
                        self.runtime = None;
                    }
                }
            } else {
                self.runtime = None;
            }
        }

        self.config = config;
    }

    /// Gets the statistics for this strategy
    fn get_tokio_stats(&self) -> TokioStrategyStats {
        self.stats.read().unwrap().clone()
    }

    /// Gets the Tokio runtime handle used by this strategy
    fn get_runtime_handle(&self) -> PrismaResult<Handle> {
        if let Some(runtime) = &self.runtime {
            Ok(runtime.handle().clone())
        } else {
            get_or_create_runtime()
        }
    }

    /// Sets the maximum number of concurrent tasks
    fn set_max_concurrent_tasks(&mut self, max_tasks: usize) {
        self.config.max_concurrent_tasks = max_tasks;
        self.semaphore = Arc::new(Semaphore::new(max_tasks));
    }

    /// Gets the maximum number of concurrent tasks
    fn get_max_concurrent_tasks(&self) -> usize {
        self.config.max_concurrent_tasks
    }
}