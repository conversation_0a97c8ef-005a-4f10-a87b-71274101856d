// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/tokio/mod.rs
// =================================================================================================
// Purpose: Re-exports the Tokio execution strategy components and provides a unified interface
// for asynchronous task execution using Tokio. This module handles I/O-bound tasks that benefit
// from asynchronous processing, such as network requests, database operations, and file I/O.
//
// Integration:
// - Internal Dependencies:
//   - `tokio.rs`: Contains the main TokioStrategy implementation
//   - `traits.rs`: Defines traits specific to Tokio execution
//   - `types.rs`: Defines types used by the Tokio execution strategy
//   - `generics.rs`: Provides utility functions for Tokio execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `tokio`: For asynchronous processing
//
// - Module Interactions:
//   - `executor`: Uses TokioStrategy for executing I/O-bound tasks
//   - `decision_maker`: Selects TokioStrategy for tasks that benefit from asynchronous processing
//   - `tokio_strategy.rs`: Legacy file that now delegates to this module
// =================================================================================================

// Declare the submodules
pub mod tokio;
pub mod traits;
pub mod types;
pub mod generics;

// Re-export the main components for easier access
pub use tokio::TokioStrategy;
pub use traits::TokioStrategyTrait;
pub use types::{TokioStrategyConfig, TokioStrategyStats};

// Re-export utility functions
pub use generics::{
    update_stats,
    check_timeout,
    execute_task_with_timeout,
    execute_tokio_task_arc,
    spawn_tokio_task,
    get_or_create_runtime,
    log_tokio_execution
};

// Provide a convenience function to create a new TokioStrategy with default configuration
pub fn create_default_tokio_strategy() -> TokioStrategy {
    TokioStrategy::new(TokioStrategyConfig::default())
}