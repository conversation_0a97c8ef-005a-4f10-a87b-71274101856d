// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/tokio/types.rs
// =================================================================================================
// Purpose: Defines types used by the Tokio execution strategy, including configuration options
// and statistics for monitoring and telemetry. These types provide a structured way to configure
// and monitor the Tokio execution strategy.
//
// Integration:
// - Internal Dependencies:
//   - None
//
// - External Dependencies:
//   - `serde`: For serialization and deserialization
//   - `std::time::Duration`: For timing metrics
//   - `tokio`: For asynchronous processing configuration
//
// - Module Interactions:
//   - `tokio.rs`: Uses TokioStrategyConfig and TokioStrategyStats
//   - `traits.rs`: References TokioStrategyStats in trait methods
//   - `executor`: Uses TokioStrategyConfig for initialization
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Configuration options for the Tokio execution strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokioStrategyConfig {
    /// Maximum number of tasks that can be executed concurrently
    pub max_concurrent_tasks: usize,
    /// Maximum time a task can execute before it's considered timed out
    pub task_timeout: Option<Duration>,
    /// Whether to log detailed execution information
    pub detailed_logging: bool,
    /// Whether to use a dedicated runtime for this strategy
    pub use_dedicated_runtime: bool,
    /// Number of worker threads in the Tokio runtime (if dedicated)
    pub worker_threads: Option<usize>,
    /// Thread name prefix for Tokio worker threads
    pub thread_name: Option<String>,
}

impl Default for TokioStrategyConfig {
    fn default() -> Self {
        TokioStrategyConfig {
            max_concurrent_tasks: num_cpus::get() * 4, // Tokio can handle more concurrent tasks
            task_timeout: None,
            detailed_logging: false,
            use_dedicated_runtime: false,
            worker_threads: None,
            thread_name: Some("prisma-tokio-worker".to_string()),
        }
    }
}

/// Statistics for monitoring and telemetry of the Tokio execution strategy
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TokioStrategyStats {
    /// Number of tasks that have been started
    pub tasks_started: usize,
    /// Number of tasks that have been completed successfully
    pub tasks_completed: usize,
    /// Number of tasks that have failed
    pub tasks_failed: usize,
    /// Number of tasks that have timed out
    pub tasks_timed_out: usize,
    /// Total time spent executing tasks
    pub total_execution_time: Duration,
    /// Average time spent executing tasks
    pub average_execution_time: Option<Duration>,
    /// Maximum time spent executing a task
    pub max_execution_time: Duration,
    /// Minimum time spent executing a task
    pub min_execution_time: Option<Duration>,
    /// Number of tasks currently executing
    pub tasks_in_progress: usize,
    /// Number of tasks waiting to be executed
    pub tasks_waiting: usize,
    /// Number of worker threads in the runtime
    pub worker_thread_count: Option<usize>,
}