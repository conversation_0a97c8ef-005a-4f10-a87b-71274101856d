// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/tokio/generics.rs
// =================================================================================================
// Purpose: Provides utility functions for the Tokio execution strategy. These functions handle
// common operations like task execution timing, error handling, and statistics updates. They
// also provide utilities for managing asynchronous task execution with Tokio.
//
// Integration:
// - Internal Dependencies:
//   - `types.rs`: Uses TokioStrategyStats for metrics updates
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::time::{Duration, Instant}`: For timing metrics
//   - `tokio`: For asynchronous processing
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `tokio.rs`: Uses utility functions for task execution
//   - `tokio_strategy.rs`: Legacy file that may use these functions
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::{PrismaResult, GenericError};
use std::time::{Duration, Instant};
use std::any::Any;
use tracing::{debug, error, info, instrument, trace};
use super::types::TokioStrategyStats;
use tokio::sync::Mutex;
use std::sync::Arc;
use tokio::runtime::Handle;
use tokio::time::timeout;

/// Updates the statistics with the result of a task execution
pub fn update_stats(
    stats: &mut TokioStrategyStats,
    start_time: Instant,
    result: &PrismaResult<Box<dyn Any + Send>>,
) {
    let elapsed = start_time.elapsed();

    // Update total execution time
    stats.total_execution_time += elapsed;

    // Update max execution time if this task took longer
    if elapsed > stats.max_execution_time {
        stats.max_execution_time = elapsed;
    }

    // Update min execution time if this is the first task or if this task took less time
    if stats.min_execution_time.is_none() || elapsed < stats.min_execution_time.unwrap() {
        stats.min_execution_time = Some(elapsed);
    }

    // Update average execution time
    let total_tasks = stats.tasks_completed + stats.tasks_failed + stats.tasks_timed_out;
    if total_tasks > 0 {
        stats.average_execution_time = Some(
            stats.total_execution_time / total_tasks as u32
        );
    }

    // Update success/failure counts
    match result {
        Ok(_) => {
            stats.tasks_completed += 1;
            debug!("Task completed successfully in {:?} via Tokio", elapsed);
        },
        Err(e) => {
            stats.tasks_failed += 1;
            error!("Task execution failed in Tokio: {:?}", e);
        }
    }
}

/// Checks if a task has timed out based on the configured timeout
pub fn check_timeout(
    start_time: Instant,
    timeout: Option<Duration>,
) -> bool {
    if let Some(timeout_duration) = timeout {
        if start_time.elapsed() > timeout_duration {
            return true;
        }
    }
    false
}

/// Executes a task with timeout using Tokio's async runtime
pub async fn execute_task_with_timeout(
    task: &mut (dyn Task + Send + Sync),
    timeout_duration: Duration,
) -> PrismaResult<Box<dyn Any + Send>> {
    let task_id = task.id();
    trace!("Executing task {} with timeout {:?}", task_id, timeout_duration);

    match timeout(timeout_duration, task.execute()).await {
        Ok(result) => result,
        Err(_) => {
            let err_msg = format!(
                "Task {} execution timed out after {:?}",
                task_id, timeout_duration
            );
            error!("{}", err_msg);
            Err(GenericError::from(err_msg))
        }
    }
}

/// Executes a task wrapped in an Arc<Mutex<Box<dyn Task>>> with Tokio
#[instrument(level = "debug", skip(task_arc), fields(task_id))]
pub async fn execute_tokio_task_arc(
    task_arc: Arc<Mutex<Box<dyn Task>>>,
) -> PrismaResult<Box<dyn Any + Send>> {
    let task_id = {
        let task = task_arc.lock().await;
        task.id()
    };
    // Add task_id to the current span
    tracing::Span::current().record("task_id", &tracing::field::display(task_id));

    trace!("Executing task via Tokio strategy (Arc)");
    let mut task_guard = task_arc.lock().await;
    match task_guard.execute().await {
        Ok(result) => Ok(result),
        Err(e) => {
            error!("Tokio task execution failed: {:?}", e);
            Err(e)
        }
    }
}

/// Spawns a task on the Tokio runtime and returns a JoinHandle
pub fn spawn_tokio_task<F, T>(future: F) -> tokio::task::JoinHandle<T>
where
    F: std::future::Future<Output = T> + Send + 'static,
    T: Send + 'static,
{
    tokio::spawn(future)
}

/// Gets the current Tokio runtime handle or creates a new one if not in a Tokio context
pub fn get_or_create_runtime() -> PrismaResult<Handle> {
    Handle::try_current().or_else(|_| {
        debug!("No Tokio runtime found in current context, creating a new one");
        let runtime = tokio::runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .map_err(|e| {
                GenericError::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Failed to create Tokio runtime: {}", e),
                ))
            })?;
        Ok(runtime.handle().clone())
    })
}

/// Logs task execution information specific to Tokio strategy
pub fn log_tokio_execution(
    task: &dyn Task,
    elapsed: Duration,
    detailed_logging: bool,
) {
    let task_id = task.id();

    if detailed_logging {
        info!(
            "Task {} executed in {:?} via Tokio strategy (category: {:?}, priority: {:?})",
            task_id,
            elapsed,
            task.category(),
            task.priority()
        );
    } else {
        debug!(
            "Task {} executed in {:?} via Tokio strategy",
            task_id,
            elapsed
        );
    }
}