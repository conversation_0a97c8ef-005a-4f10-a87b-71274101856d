// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/rayon_strategy.rs
// =================================================================================================
// Purpose: Legacy implementation of the Rayon execution strategy. This file provides functions
// for executing tasks using Rayon's thread pool. It is maintained for backward compatibility
// and delegates to the new rayon/ submodule implementation.
//
// Integration:
// - Internal Dependencies:
//   - `rayon/`: New implementation of the Rayon execution strategy
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::any::Any`: For type-erased return values
//   - `rayon`: For parallel processing
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `executor`: May use these functions directly for backward compatibility
//   - `rayon/`: New implementation that this file should eventually delegate to
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::any::Any;
use tracing::{instrument, debug};

// Import the new implementation
use super::rayon::RayonStrategy;
use super::traits::ExecutionStrategy;

// Executes a task using Rayon's thread pool.
// Suitable for CPU-bound tasks that can be parallelized.
#[instrument(level = "debug", skip(task), fields(task_id = %task.id()))]
pub async fn execute_rayon_task(task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
    // Record task_id explicitly
    let task_id = task.id();
    let priority = task.priority();
    let category = task.category();

    tracing::Span::current().record("task_id", &tracing::field::display(task_id));
    println!("execute_rayon_task called for task {} with priority {:?}, category {:?}",
             task_id, priority, category);
    debug!("Legacy rayon_strategy.rs delegating to new RayonStrategy implementation");

    // Get the current thread name to help with debugging
    let thread_name = std::thread::current().name().unwrap_or("unnamed").to_string();
    println!("Current thread for task {}: {}", task_id, thread_name);

    // Create a new RayonStrategy instance for this call
    let mut strategy = RayonStrategy::default();
    println!("Created RayonStrategy instance for task {}", task_id);

    // Execute the task
    println!("Calling RayonStrategy.execute_task for task {}", task_id);
    let result = strategy.execute_task(task).await;
    println!("RayonStrategy.execute_task completed for task {} with result: {:?}", task_id, result.is_ok());

    // If the result is an error, log the error
    if let Err(ref e) = result {
        println!("RayonStrategy.execute_task failed for task {}: {}", task_id, e);
    }

    result
}

// Overload for Arc<Mutex<Box<dyn Task>>>
#[instrument(level = "debug", skip(task_arc), fields(task_id))]
pub async fn execute_rayon_task_arc(task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
    let task_id = { // Get ID for tracing field
        let task = task_arc.lock().await;
        task.id()
    };
    tracing::Span::current().record("task_id", &tracing::field::display(task_id));
    debug!("Legacy rayon_strategy.rs delegating to new RayonStrategy implementation (Arc)");

    // Create a new RayonStrategy instance for this call
    let mut strategy = RayonStrategy::default();
    strategy.execute_task_arc(task_arc).await
}
