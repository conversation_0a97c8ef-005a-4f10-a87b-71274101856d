// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/types.rs
// =================================================================================================
// Purpose: Defines common types for all execution strategies, including configuration options
// and statistics for monitoring and telemetry. These types provide a structured way to configure
// and monitor execution strategies across the system.
//
// Integration:
// - Internal Dependencies:
//   - None
//
// - External Dependencies:
//   - `serde`: For serialization and deserialization
//   - `std::time::Duration`: For timing metrics
//   - `crate::prisma::prisma_engine::types::ExecutionStrategyType`: Enum for strategy types
//
// - Module Interactions:
//   - `traits.rs`: Uses ExecutionStrategyConfig and ExecutionStrategyStats in trait definitions
//   - `direct/`: Uses ExecutionStrategyConfig and ExecutionStrategyStats for direct execution
//   - `rayon/`: Uses ExecutionStrategyConfig and ExecutionStrategyStats for Rayon-based execution
//   - `tokio/`: Uses ExecutionStrategyConfig and ExecutionStrategyStats for Tokio-based execution
//   - `execution_strategies.rs`: Uses ExecutionStrategyConfig for strategy initialization
//   - `executor`: Uses ExecutionStrategyConfig for strategy configuration
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::time::Duration;
use crate::prisma::prisma_engine::types::ExecutionStrategyType;

/// Common configuration options for all execution strategies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStrategyConfig {
    /// The type of execution strategy
    pub strategy_type: ExecutionStrategyType,
    /// Maximum number of tasks that can be executed concurrently
    pub max_concurrent_tasks: usize,
    /// Maximum time a task can execute before it's considered timed out
    pub task_timeout: Option<Duration>,
    /// Whether to log detailed execution information
    pub detailed_logging: bool,
    /// Strategy-specific configuration options as JSON string
    pub strategy_specific_json: Option<String>,
}

impl Default for ExecutionStrategyConfig {
    fn default() -> Self {
        ExecutionStrategyConfig {
            strategy_type: ExecutionStrategyType::Direct,
            max_concurrent_tasks: 1,
            task_timeout: None,
            detailed_logging: false,
            strategy_specific_json: None,
        }
    }
}

/// Common statistics for monitoring and telemetry of execution strategies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStrategyStats {
    /// The type of execution strategy
    pub strategy_type: ExecutionStrategyType,
    /// Number of tasks that have been started
    pub tasks_started: usize,
    /// Number of tasks that have been completed successfully
    pub tasks_completed: usize,
    /// Number of tasks that have failed
    pub tasks_failed: usize,
    /// Number of tasks that have timed out
    pub tasks_timed_out: usize,
    /// Total time spent executing tasks
    pub total_execution_time: Duration,
    /// Average time spent executing tasks
    pub average_execution_time: Option<Duration>,
    /// Maximum time spent executing a task
    pub max_execution_time: Duration,
    /// Minimum time spent executing a task
    pub min_execution_time: Option<Duration>,
    /// Strategy-specific statistics as JSON string
    pub strategy_specific_json: Option<String>,
}

impl Default for ExecutionStrategyStats {
    fn default() -> Self {
        ExecutionStrategyStats {
            strategy_type: ExecutionStrategyType::Direct,
            tasks_started: 0,
            tasks_completed: 0,
            tasks_failed: 0,
            tasks_timed_out: 0,
            total_execution_time: Duration::from_secs(0),
            average_execution_time: None,
            max_execution_time: Duration::from_secs(0),
            min_execution_time: None,
            strategy_specific_json: None,
        }
    }
}