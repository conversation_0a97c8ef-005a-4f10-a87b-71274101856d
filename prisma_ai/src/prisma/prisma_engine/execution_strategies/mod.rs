// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/mod.rs
// =================================================================================================
// Purpose: Main entry point for the execution_strategies module. This module provides different
// strategies for executing tasks based on their characteristics and system state. It includes
// direct execution for lightweight tasks, Rayon-based parallel execution for CPU-intensive tasks,
// and Tokio-based asynchronous execution for I/O-bound tasks.
//
// Integration:
// - Internal Dependencies:
//   - `direct/`: Submodule for direct execution strategy
//   - `rayon/`: Submodule for Rayon-based parallel execution strategy
//   - `tokio/`: Submodule for Tokio-based asynchronous execution strategy
//   - `traits.rs`: Defines common traits for all execution strategies
//   - `types.rs`: Defines common types for all execution strategies
//   - `generics.rs`: Provides common utility functions for all execution strategies
//   - `execution_strategies.rs`: Implements the main execution strategies manager
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//
// - Module Interactions:
//   - `executor`: Uses execution strategies for task execution
//   - `decision_maker`: Selects appropriate execution strategy based on task characteristics and system state
// =================================================================================================

// Declare the submodules
pub mod direct;
pub mod rayon;
pub mod tokio;
pub mod traits;
pub mod types;
pub mod generics;
pub mod execution_strategies;

// Legacy files for backward compatibility
pub mod tokio_strategy;
pub mod rayon_strategy;
pub mod direct_strategy;

// Re-export the main execution functions for use by the executor
// Legacy exports for backward compatibility
pub use tokio_strategy::{execute_tokio_task, execute_tokio_task_arc};
pub use rayon_strategy::{execute_rayon_task, execute_rayon_task_arc};
pub use direct_strategy::{execute_direct_task, execute_direct_task_arc};

// Re-export the main execution strategy components
pub use execution_strategies::ExecutionStrategies;
pub use traits::ExecutionStrategy;
pub use types::{ExecutionStrategyConfig, ExecutionStrategyStats};

// Re-export the direct execution strategy components
pub use direct::direct::DirectStrategy;
pub use direct::traits::DirectStrategyTrait;
pub use direct::types::{DirectStrategyConfig, DirectStrategyStats};

// Re-export the rayon execution strategy components
pub use rayon::rayon::RayonStrategy;
pub use rayon::traits::RayonStrategyTrait;
pub use rayon::types::{RayonStrategyConfig, RayonStrategyStats};

// Re-export the tokio execution strategy components
pub use tokio::tokio::TokioStrategy;
pub use tokio::traits::TokioStrategyTrait;
pub use tokio::types::{TokioStrategyConfig, TokioStrategyStats};
