// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/tokio_strategy.rs
// =================================================================================================
// Purpose: Legacy implementation of the Tokio execution strategy. This file provides functions
// for executing tasks using Tokio's async runtime. It is maintained for backward compatibility
// and delegates to the new tokio/ submodule implementation.
//
// Integration:
// - Internal Dependencies:
//   - `tokio/`: New implementation of the Tokio execution strategy
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `std::any::Any`: For type-erased return values
//   - `tokio`: For asynchronous processing
//   - `tracing`: For logging and instrumentation
//
// - Module Interactions:
//   - `executor`: May use these functions directly for backward compatibility
//   - `tokio/`: New implementation that this file should eventually delegate to
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::err::PrismaResult;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::any::Any;
use tracing::{instrument, debug};

// Re-export from the new implementation
pub use crate::prisma::prisma_engine::execution_strategies::tokio::create_default_tokio_strategy;

// Executes a task using Tokio's async runtime.
// Suitable for I/O-bound tasks or tasks that benefit from async concurrency.
#[instrument(level = "debug", skip(task), fields(task_id = %task.id()))]
pub async fn execute_tokio_task(task: &mut (dyn Task + Send + Sync)) -> PrismaResult<Box<dyn Any + Send>> {
    debug!("Delegating to new tokio implementation");
    // Delegate to the new implementation
    crate::prisma::prisma_engine::execution_strategies::tokio::generics::execute_task_with_timeout(
        task,
        std::time::Duration::from_secs(3600), // Default 1 hour timeout
    )
    .await
}

// Overload for Arc<Mutex<Box<dyn Task>>>
// This allows spawning the execution without needing mutable access at the call site.
#[instrument(level = "debug", skip(task_arc), fields(task_id))]
pub async fn execute_tokio_task_arc(task_arc: Arc<Mutex<Box<dyn Task>>>) -> PrismaResult<Box<dyn Any + Send>> {
    debug!("Delegating to new tokio implementation (Arc)");
    // Delegate to the new implementation
    crate::prisma::prisma_engine::execution_strategies::tokio::generics::execute_tokio_task_arc(task_arc).await
}
