// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/mod.rs
// =================================================================================================
// Purpose: Declares and exports the Task Creation Layer (TCL) module components, which provide
// task creation and management services for the PrismaAI system.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Defines parameter types for different task categories
// - traits.rs: Defines traits specific to the TCL module
// - generics.rs: Contains generic types and functions for the TCL module
// - llm_task.rs: Implements LLM inference tasks
// - embedding_task.rs: Implements embedding generation tasks
// - storage_task.rs: Implements database storage tasks
// - tcl.rs: Contains the main TaskFactory implementation
// =================================================================================================
// External Dependencies:
// - prisma_engine::traits::Task: The main Task trait implemented by concrete task types
// - prisma_engine::types: TaskId, TaskPriority, TaskCategory, etc.
// - llm::interface: LLM service interfaces
// - storage: Database connection and query interfaces
// =================================================================================================
// Module Interactions:
// - Provides task creation services to the PrismaEngine
// - Integrates with LLM services for inference and embedding tasks
// - Integrates with storage services for database operations
// - Tasks created here are executed by the executor module
// =================================================================================================

// Declare submodules or files containing task definitions
pub mod types;
pub mod traits;
pub mod generics;
pub mod llm_task;
pub mod embedding_task;
pub mod storage_task;
// pub mod file_task; // Example for future task types
// pub mod network_task; // Example

// Optionally declare a factory or builder module
// pub mod factory;
pub mod tcl; // Central TCL struct and factory

// Re-export the concrete task types for easier use by the engine
pub use llm_task::LlmTask;
pub use embedding_task::EmbeddingTask;
pub use storage_task::StorageTask;
// pub use file_task::FileTask; // Example
// pub use network_task::NetworkTask; // Example

// Re-export parameter types if they are intended to be used directly
// when creating tasks from outside the engine (e.g., from prisma_ui services)
pub use types::{
    LlmInferenceParams, EmbeddingGenerationParams, DatabaseQueryParams,
    // FileProcessingParams, NetworkRequestParams, UiCallbackParams, InternalTaskParams,
};

// Re-export the TaskFactory for easier access
pub use tcl::TaskFactory;
