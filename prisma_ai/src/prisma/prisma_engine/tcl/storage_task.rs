// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/storage_task.rs
// =================================================================================================
// Purpose: Implements the StorageTask struct for database operations. This task type handles
// queries, updates, and other operations on the SurrealDB database.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses DatabaseQueryParams for task configuration
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - tracing: For logging
// - serde_json: For flexible parameter and result values
// - crate::err: For error handling
// - crate::prisma::prisma_engine::traits::Task: The main Task trait
// - crate::prisma::prisma_engine::types: For task-related types
// - crate::storage: For database connection and operations
// =================================================================================================
// Module Interactions:
// - Implements the Task trait from prisma_engine::traits
// - Uses storage services for database operations
// - Created by TaskFactory in tcl.rs
// - Executed by the executor module
// - Provides data persistence for the PrismaAI system
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::any::Any;
use tracing::{info, debug, error, warn};
use std::sync::Arc;
use serde_json::Value; // For parameter type and potential result type

use crate::err::{PrismaResult, GenericError};
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{
    TaskId, TaskPriority, TaskCategory, PrismaScore, ResourceType, ResourceUsage
};
use super::types::DatabaseQueryParams; // Use the specific param struct
// Import the concrete SurrealDbConnection type instead of the trait
use crate::storage::SurrealDbConnection;
use crate::storage::traits::DataStore; // Import the trait to use its methods
use crate::err::types::{PrismaError, generics::EngineOperationError}; // Import error types
use std::fmt; // Import fmt for manual Debug impl

// Concrete task implementation for Storage operations
// #[derive(Debug)] // Remove derive Debug
pub struct StorageTask {
    id: TaskId,
    params: DatabaseQueryParams,
    priority: TaskPriority,
    storage_service: Arc<SurrealDbConnection>, // Use concrete type
}

// Manual Debug implementation for StorageTask
impl fmt::Debug for StorageTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("StorageTask")
         .field("id", &self.id)
         .field("params", &self.params) // Assuming DatabaseQueryParams derives Debug
         .field("priority", &self.priority)
         .field("storage_service", &format_args!("Arc<SurrealDbConnection>")) // Indicate presence
         .finish()
    }
}


impl StorageTask {
    // Updated constructor to use concrete type
    pub fn new(
        params: DatabaseQueryParams,
        priority: Option<TaskPriority>,
        storage_service: Arc<SurrealDbConnection>, // Use concrete type
    ) -> Self {
        StorageTask {
            id: TaskId::new(),
            params,
            priority: priority.unwrap_or(TaskPriority::Normal),
            storage_service,
        }
    }
}

#[async_trait]
impl Task for StorageTask {
    fn id(&self) -> TaskId {
        self.id
    }

    fn category(&self) -> TaskCategory {
        TaskCategory::DatabaseQuery
    }

    fn priority(&self) -> TaskPriority {
        self.priority
    }

    /// Estimate resource requirements for a Storage task.
    fn get_prisma_score(&self) -> PrismaScore {
        let mut resources = HashMap::new();
        // Explicitly type as f64
        let mut cpu_usage: f64 = 0.15;
        let mut mem_usage: f64 = 0.1;
        let mut disk_io: f64 = 0.3;
        let mut network: f64 = 0.2; // Assume remote DB

        // Default float literals are f64, addition is fine
        if self.params.query_string.len() > 200 { cpu_usage += 0.1; disk_io += 0.1; }
        else if self.params.query_string.len() > 50 { cpu_usage += 0.05; disk_io += 0.05; }
        if self.params.parameters.is_some() { cpu_usage += 0.05; }

        // Use f64 for .min()
        resources.insert(ResourceType::CPU, ResourceUsage(cpu_usage.min(1.0)));
        resources.insert(ResourceType::Memory, ResourceUsage(mem_usage.min(1.0)));
        resources.insert(ResourceType::DiskIO, ResourceUsage(disk_io.min(1.0)));
        resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(network.min(1.0)));
        resources.insert(ResourceType::GPU, ResourceUsage(0.0));

        debug!("Calculated PrismaScore for StorageTask {}: {:?}", self.id, resources);
        PrismaScore { resources }
    }

    /// Execute the Storage query logic using the DataStore handle.
    async fn execute(&mut self) -> PrismaResult<Box<dyn Any + Send>> {
        info!("Executing Storage Task {}", self.id);
        debug!("Storage Task params: {:?}", self.params);

        // Convert parameters from HashMap<String, Value> to &[(&str, &str)]
        // This is lossy if Value is not a string.
        // TODO: Revisit parameter type in DataStore::query or DatabaseQueryParams if needed.
        // Consider making DataStore::query accept HashMap<String, surrealdb::sql::Value> directly.
        let params_vec: Vec<(String, String)> = self.params.parameters.as_ref()
            .map(|map| {
                map.iter()
                   .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string()))) // Only take string values
                   .collect()
            })
            .unwrap_or_default();

        // Create slice of tuples of string slices for the current DataStore::query signature
        let params_slice: Vec<(&str, &str)> = params_vec.iter()
            .map(|(k, v)| (k.as_str(), v.as_str()))
            .collect();

        // Call the query method from the DataStore trait
        // Assuming the query method returns Vec<T>, we need a concrete type or Value.
        // Let's assume we expect JSON Value results for generic queries.
        let result: Vec<Value> = self.storage_service.query(
                &self.params.query_string,
                &params_slice // Pass the converted slice
            ).await
            .map_err(|e| {
                let err_msg = format!("Storage query failed for task {}: {:?}", self.id, e);
                error!("{}", err_msg);
                // Use the new custom error type which implements std::error::Error
                PrismaError::new(EngineOperationError::new(err_msg))
            })?;

        info!("Storage Task {} completed.", self.id);
        // Return the result (Vec<Value>) boxed
        Ok(Box::new(result))
    }

    fn clone_box(&self) -> Box<dyn Task> {
        Box::new(StorageTask {
            id: self.id,
            params: self.params.clone(),
            priority: self.priority,
            storage_service: Arc::clone(&self.storage_service),
        })
    }
}
