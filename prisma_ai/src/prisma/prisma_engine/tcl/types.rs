// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/types.rs
// =================================================================================================
// Purpose: Defines parameter types for different task categories in the Task Creation Layer (TCL).
// These types are used to configure and customize task behavior.
// =================================================================================================
// Internal Dependencies:
// - None
// =================================================================================================
// External Dependencies:
// - serde: For serialization/deserialization of parameter types
// - serde_json: For flexible parameter values
// =================================================================================================
// Module Interactions:
// - Used by llm_task.rs, embedding_task.rs, storage_task.rs to define task parameters
// - Parameter types are re-exported by mod.rs for use by other modules
// - Used by TaskFactory in tcl.rs to create tasks with specific parameters
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use serde_json::Value; // Using serde_json::Value for flexible parameters

// Parameters specific to LLM Inference tasks
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmInferenceParams {
    pub model_id: String, // Identifier for the specific LLM to use
    pub prompt: String,
    pub max_tokens: Option<u32>,
    // Add other LLM parameters like temperature, top_k, top_p, etc.
    pub temperature: Option<f32>,
    pub top_k: Option<i32>,
    pub top_p: Option<f32>,
    // Streaming mode flag
    pub streaming: Option<bool>,
    // Streaming buffer size for token channel
    pub streaming_buffer_size: Option<usize>,
    // Add sampling parameters if needed directly here, or use a separate struct
}

// Parameters specific to Embedding Generation tasks
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingGenerationParams {
    pub model_id: String, // Identifier for the embedding model
    pub input_texts: Vec<String>,
    // Add pooling strategy, normalization flags, etc.
    pub pooling_strategy: Option<String>, // e.g., "mean", "cls"
}

// Parameters specific to Database Query tasks (maps to StorageTask)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseQueryParams {
    pub query_string: String,
    // Using HashMap<String, Value> allows for various parameter types (string, number, bool, etc.)
    pub parameters: Option<HashMap<String, Value>>,
}

// Parameters specific to File Processing tasks (Example)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileProcessingParams {
    pub input_path: String,
    pub output_path: Option<String>,
    pub operation: String, // e.g., "read", "write", "transform"
    pub content: Option<String>, // For write operations
}

// Parameters specific to Network Request tasks (Example)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkRequestParams {
    pub url: String,
    pub method: String, // e.g., "GET", "POST"
    pub headers: Option<HashMap<String, String>>,
    pub body: Option<Value>,
}

// Parameters specific to UI Callback tasks (Example)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiCallbackParams {
    pub callback_id: String,
    pub data: Value, // Flexible data payload
}

// Parameters specific to Internal Engine tasks (Example)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InternalTaskParams {
    pub operation: String, // e.g., "cleanup_cache", "reconfigure"
    pub details: Option<Value>,
}

// You might have a generic TaskParams enum if tasks always fit predefined structures,
// or rely on individual structs like above for flexibility.
// Using individual structs is often clearer for distinct task types.
