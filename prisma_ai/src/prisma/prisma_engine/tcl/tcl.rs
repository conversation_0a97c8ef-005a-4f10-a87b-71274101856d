// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/tcl.rs
// =================================================================================================
// Purpose: Implements the TaskFactory for creating different types of tasks in the PrismaAI system.
// This is the central component of the Task Creation Layer (TCL) that provides a unified interface
// for creating various task types.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses parameter types for task configuration
// - llm_task.rs: For creating LLM inference tasks
// - embedding_task.rs: For creating embedding generation tasks
// - storage_task.rs: For creating database storage tasks
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - rand: For generating random sequence IDs
// - std::sync::Arc and tokio::sync::Mutex: For shared ownership and thread safety
// - crate::err: For error handling
// - crate::prisma::prisma_engine::traits::Task: The main Task trait
// - crate::prisma::prisma_engine::types: For task-related types
// - crate::storage: For database connection
// =================================================================================================
// Module Interactions:
// - Used by PrismaEngine to create tasks of different types
// - Integrates with LLM services for inference and embedding tasks
// - Integrates with storage services for database operations
// - Creates tasks that are executed by the executor module
// =================================================================================================

use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{TaskId, TaskPriority, TaskCategory, PrismaScore, ResourceType, ResourceUsage};
use crate::err::PrismaResult;
use super::types::{LlmInferenceParams, EmbeddingGenerationParams, DatabaseQueryParams};
use std::collections::HashMap;
use std::any::Any;
use async_trait::async_trait;

// Task Creation Layer implementation.
// This module is responsible for creating concrete Task objects through the TaskFactory.
use std::sync::Arc;
use tokio::sync::Mutex;
use rand; // For generating random sequence IDs
use super::llm_task::LlmService;
use super::embedding_task::LlmEmbeddingService;
use crate::storage::SurrealDbConnection;

// We can't derive Debug because the trait objects don't implement Debug
pub struct TaskFactory {
    // Hold references to services needed for task creation
    llm_service: Arc<dyn LlmService>,
    embedding_service: Arc<Mutex<dyn LlmEmbeddingService>>,
    storage_service: Arc<SurrealDbConnection>,
}

// Implement Debug manually
impl std::fmt::Debug for TaskFactory {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TaskFactory")
            .field("llm_service", &"Arc<dyn LlmService>")
            .field("embedding_service", &"Arc<Mutex<dyn LlmEmbeddingService>>")
            .field("storage_service", &"Arc<SurrealDbConnection>")
            .finish()
    }
}

impl TaskFactory {
    pub fn new(
        llm_service: Arc<dyn LlmService>,
        embedding_service: Arc<Mutex<dyn LlmEmbeddingService>>,
        storage_service: Arc<SurrealDbConnection>,
    ) -> Self {
        TaskFactory {
            llm_service,
            embedding_service,
            storage_service,
        }
    }

    // Function to create an LLM task
    pub fn create_llm_task(
        &self,
        params: LlmInferenceParams,
        priority: Option<TaskPriority>,
    ) -> PrismaResult<Box<dyn Task>> {
        // Import the LlmTask from the parent module
        use super::llm_task::LlmTask;
        use crate::SeqId;

        // Generate a sequence ID for this task
        // SeqId is just an i32, so we can create a new one with a random value
        let seq_id = rand::random::<i32>();

        // Create a new LlmTask with the provided parameters
        let task = LlmTask::new(
            params,
            seq_id,
            self.llm_service.clone(),
            priority,
        );

        // Return the task as a boxed Task trait object
        Ok(Box::new(task))
    }

    // Function to create an Embedding task
    pub fn create_embedding_task(
        &self,
        params: EmbeddingGenerationParams,
        priority: Option<TaskPriority>,
    ) -> PrismaResult<Box<dyn Task>> {
        // Import the EmbeddingTask from the parent module
        use super::embedding_task::EmbeddingTask;

        // Create a new EmbeddingTask with the provided parameters
        let task = EmbeddingTask::new(
            params,
            priority,
            self.embedding_service.clone(),
        );

        // Return the task as a boxed Task trait object
        Ok(Box::new(task))
    }

    // Function to create a Database task
    pub fn create_database_task(
        &self,
        params: DatabaseQueryParams,
        priority: Option<TaskPriority>,
    ) -> PrismaResult<Box<dyn Task>> {
        // Import the StorageTask from the parent module
        use super::storage_task::StorageTask;

        // Create a new StorageTask with the provided parameters
        let task = StorageTask::new(
            params,
            priority,
            self.storage_service.clone(),
        );

        // Return the task as a boxed Task trait object
        Ok(Box::new(task))
    }
}

// Task creation is handled by the TaskFactory methods above.
// Each concrete task type is implemented in its own file:
// - LlmTask in llm_task.rs
// - EmbeddingTask in embedding_task.rs
// - StorageTask in storage_task.rs
