// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/traits.rs
// =================================================================================================
// Purpose: Defines traits specific to the internal workings of the Task Creation Layer (TCL) module.
// This file is reserved for traits that are only used within the TCL module.
// =================================================================================================
// Internal Dependencies:
// - None
// =================================================================================================
// External Dependencies:
// - The main `Task` trait is defined in the parent `prisma_engine::traits` module
// =================================================================================================
// Module Interactions:
// - Provides trait definitions that may be used by other files in the TCL module
// - Currently minimal as most functionality uses the main Task trait from prisma_engine::traits
// =================================================================================================

// This file is reserved for traits specific to the internal workings
// of the Task Creation Layer (TCL) module, if any are needed.

// The main `Task` trait implemented by concrete task types (LlmTask, etc.)
// is defined in the parent `prisma_engine::traits` module.

// Currently empty, can be populated as needed.
// For example, a `TaskFactory` trait could be defined here if we
// implement a factory pattern for creating tasks.
