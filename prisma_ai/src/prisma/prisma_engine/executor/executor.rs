// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/executor.rs
// =================================================================================================
// Purpose: Implements the TaskExecutor, which is the core component of the executor module.
// The TaskExecutor is responsible for managing task execution, dispatching tasks to appropriate
// queues based on priority, and coordinating worker tasks that process the queued tasks.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses ExecutorConfig for configuration
// - queue/: Uses queue implementations for task execution (direct, rayon, tokio, priority)
// - execution_strategies/: Uses strategy implementations for task execution
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - tokio: For async runtime, channels, and synchronization primitives
// - tracing: For logging and instrumentation
// - rayon: For parallel task execution
// =================================================================================================
// Module Interactions:
// - Used by PrismaEngine to execute tasks
// - Integrates with queue implementations for task execution
// - Coordinates with execution strategies for different execution models
// - Manages priority queues for different task priorities
// - Provides graceful shutdown and error handling
// =================================================================================================

use async_trait::async_trait;
use tokio::sync::{oneshot, RwLock};
use std::sync::Arc;
use tracing::{info, warn, error, debug, instrument};
use std::any::Any; // For Box<dyn Any + Send>
use std::time::Duration;

use crate::err::{PrismaResult, GenericError};
use crate::prisma::prisma_engine::traits::{Executor, Task, DecisionLogic};
use crate::prisma::prisma_engine::types::{TaskId, ExecutionStrategyType, TaskPriority, EngineConfig, TaskCategory};
use super::types::ExecutorConfig;
use crate::prisma::prisma_engine::decision_maker::RuleBasedDecisionMaker;
use crate::prisma::prisma_engine::types::{SystemScore, PrismaScore};
use std::fmt; // Import fmt for manual Debug impl
use std::default::Default;

// Import queue traits and types from the re-exports
use crate::prisma::prisma_engine::executor::queue::{
    DirectQueue, RayonQueue, TokioQueue, RayonQueueTrait, TokioQueueTrait,
    // Add priority queue imports
    PriorityQueueManager, BackgroundPriorityQueue, StandardPriorityQueue, RealTimePriorityQueue,
    PriorityQueueConfig, PriorityQueueTrait
};

// Concrete implementation of the Executor trait
pub struct TaskExecutor {
    config: ExecutorConfig,
    engine_config: Arc<RwLock<EngineConfig>>, // Access to overall engine config if needed

    // Priority queue manager for task execution
    priority_queue_manager: PriorityQueueManager,

    // Direct, Rayon, and Tokio queues for legacy support and specialized execution
    direct_queue: Arc<DirectQueue>,
    rayon_queue: Arc<RayonQueue>,
    tokio_queue: Arc<TokioQueue>,

    // Decision maker for dynamic strategy selection
    decision_maker: Option<Arc<RuleBasedDecisionMaker>>,

    // Handles for monitoring tasks
    monitor_handles: Vec<tokio::task::JoinHandle<()>>,
}

// Manual Debug implementation for TaskExecutor
impl fmt::Debug for TaskExecutor {
     fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
         f.debug_struct("TaskExecutor")
          .field("config", &self.config)
          .field("engine_config", &"Arc<RwLock<EngineConfig>>") // Indicate presence
          .field("priority_queue_manager", &"PriorityQueueManager") // Indicate presence
          .field("direct_queue", &"Arc<DirectQueue>") // Indicate presence
          .field("rayon_queue", &"Arc<RayonQueue>") // Indicate presence
          .field("tokio_queue", &"Arc<TokioQueue>") // Indicate presence
          .field("monitor_handles", &self.monitor_handles.len()) // Show count
          .finish()
     }
}

impl TaskExecutor {
    /// Pauses the executor, temporarily stopping task processing
    pub async fn pause(&mut self) -> PrismaResult<()> {
        info!("Pausing TaskExecutor...");

        // Pause the priority queue manager
        self.priority_queue_manager.pause_all().await.map_err(|e| {
            let err_msg = format!("Failed to pause priority queues: {}", e);
            error!("{}", err_msg);
            GenericError::from(err_msg)
        })?;

        info!("TaskExecutor paused successfully");
        Ok(())
    }

    /// Resumes the executor after it has been paused
    pub async fn resume(&mut self) -> PrismaResult<()> {
        info!("Resuming TaskExecutor...");

        // Resume the priority queue manager
        self.priority_queue_manager.resume_all().await.map_err(|e| {
            let err_msg = format!("Failed to resume priority queues: {}", e);
            error!("{}", err_msg);
            GenericError::from(err_msg)
        })?;

        info!("TaskExecutor resumed successfully");
        Ok(())
    }

    pub fn new(config: ExecutorConfig, engine_config: Arc<RwLock<EngineConfig>>) -> Self {
        // Create priority queue configuration
        let priority_queue_config = PriorityQueueConfig {
            background_queue_capacity: config.low_priority_queue_capacity,
            standard_queue_capacity: config.normal_priority_queue_capacity,
            realtime_queue_capacity: config.realtime_queue_capacity + config.high_priority_queue_capacity,
            background_concurrency_limit: 4, // Default value, can be made configurable
            standard_task_timeout_ms: 30000, // 30 seconds, can be made configurable
            realtime_preemption_enabled: true,
            background_strategy: ExecutionStrategyType::Rayon,    // Default fallback
            standard_strategy: ExecutionStrategyType::Tokio,      // Default fallback
            realtime_strategy: ExecutionStrategyType::Tokio,      // Default fallback
            enable_dynamic_strategy_selection: true,              // Enable dynamic selection
            enable_realtime_adaptation: true,                     // Enable real-time adaptation
        };

        // Create priority queue manager
        let priority_queue_manager = PriorityQueueManager::new(priority_queue_config);

        // Create specialized queues for direct execution and legacy support
        let direct_queue = Arc::new(DirectQueue::new(
            crate::prisma::prisma_engine::executor::queue::DirectQueueConfig::default()
        ));

        let rayon_queue = Arc::new(RayonQueue::new(
            crate::prisma::prisma_engine::executor::queue::RayonQueueConfig::default()
        ));

        let tokio_queue = Arc::new(TokioQueue::new(
            crate::prisma::prisma_engine::executor::queue::TokioQueueConfig::default()
        ));

        info!("Creating TaskExecutor with priority queue manager and specialized queues");

        TaskExecutor {
            config,
            engine_config,
            priority_queue_manager,
            direct_queue,
            rayon_queue,
            tokio_queue,
            decision_maker: None, // Will be set later via set_decision_maker
            monitor_handles: Vec::new(),
        }
    }

    /// Set the decision maker for dynamic strategy selection
    pub fn set_decision_maker(&mut self, decision_maker: Arc<RuleBasedDecisionMaker>) {
        self.decision_maker = Some(decision_maker);
        info!("Decision maker set for dynamic strategy selection");
    }

    /// Get a reference to the decision maker
    pub fn get_decision_maker(&self) -> Option<&Arc<RuleBasedDecisionMaker>> {
        self.decision_maker.as_ref()
    }

    /// Dynamically select execution strategy based on task and system conditions
    async fn select_dynamic_strategy(
        &self,
        task_category: &TaskCategory,
        task_priority: TaskPriority,
    ) -> PrismaResult<Option<ExecutionStrategyType>> {
        if let Some(decision_maker) = &self.decision_maker {
            // Create mock task and system scores for now
            // In a real implementation, these would come from the monitoring system
            let task_score = PrismaScore::default();
            let system_score = SystemScore::default();

            // Use the decision maker to select strategy
            match decision_maker.decide_strategy(task_category, &task_score, &system_score, task_priority).await {
                Ok(strategy) => {
                    debug!("Dynamic strategy selected: {:?} for category {:?}, priority {:?}",
                           strategy, task_category, task_priority);
                    Ok(Some(strategy))
                },
                Err(e) => {
                    warn!("Failed to select dynamic strategy: {:?}. Using default routing.", e);
                    Ok(None)
                }
            }
        } else {
            debug!("No decision maker available, using default routing");
            Ok(None)
        }
    }

    /// Update priority queue strategies dynamically
    async fn update_queue_strategies(&mut self, strategies: &[(TaskPriority, ExecutionStrategyType)]) -> PrismaResult<()> {
        for (priority, strategy) in strategies {
            match priority {
                TaskPriority::Realtime | TaskPriority::High => {
                    self.priority_queue_manager.realtime.update_execution_strategy(*strategy)?;
                    debug!("Updated RealTime queue strategy to {:?}", strategy);
                },
                TaskPriority::Normal => {
                    self.priority_queue_manager.standard.update_execution_strategy(*strategy)?;
                    debug!("Updated Standard queue strategy to {:?}", strategy);
                },
                TaskPriority::Low => {
                    self.priority_queue_manager.background.update_execution_strategy(*strategy)?;
                    debug!("Updated Background queue strategy to {:?}", strategy);
                },
            }
        }
        Ok(())
    }

    /// Creates a monitoring task for a queue to periodically log statistics
    fn create_queue_monitor<Q, S>(
        queue_name: &'static str,
        queue: Arc<Q>,
        stats_fn: fn(&Q) -> S,
        interval: Duration,
    ) -> tokio::task::JoinHandle<()>
    where
        Q: fmt::Debug + Send + Sync + 'static,
        S: fmt::Debug + 'static,
    {
        let queue_clone = queue.clone();
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(interval).await;

                let stats = stats_fn(&queue_clone);
                debug!("{} queue stats: {:?}", queue_name, stats);
            }
        })
    }
}

#[async_trait]
impl Executor for TaskExecutor {
    /// Submits a task to the appropriate priority queue based on the task's priority.
    ///
    /// This method routes the task to the appropriate priority queue based on its priority level
    /// and handles the execution strategy appropriately.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to execute
    /// * `strategy` - The execution strategy to use (may be overridden by queue configuration)
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    #[instrument(skip(self, task), fields(task_id = %task.id()), level = "debug")]
    async fn submit_task(
        &self,
        task: Box<dyn Task>,
        strategy: ExecutionStrategyType,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        let priority = task.priority();
        let category = task.category();

        println!("Executor received task {} with priority {:?}, category {:?} for strategy {:?}",
                task_id, priority, category, strategy);
        debug!("Executor received task {} with priority {:?}, category {:?} for strategy {:?}",
               task_id, priority, category, strategy);

        // Try dynamic strategy selection first if enabled
        let dynamic_strategy = if self.priority_queue_manager.realtime.get_config().enable_dynamic_strategy_selection {
            match self.select_dynamic_strategy(&category, priority).await {
                Ok(Some(selected_strategy)) => {
                    println!("Dynamic strategy selected: {:?} for task {} (category: {:?}, priority: {:?})",
                             selected_strategy, task_id, category, priority);
                    debug!("Dynamic strategy selected: {:?} for task {} (category: {:?}, priority: {:?})",
                           selected_strategy, task_id, category, priority);
                    Some(selected_strategy)
                },
                Ok(None) => {
                    debug!("No dynamic strategy selected, using default routing for task {}", task_id);
                    None
                },
                Err(e) => {
                    warn!("Dynamic strategy selection failed for task {}: {:?}. Using default routing.", task_id, e);
                    None
                }
            }
        } else {
            debug!("Dynamic strategy selection disabled, using default routing for task {}", task_id);
            None
        };

        // Always use priority-based routing for all tasks
        println!("Using priority-based routing for task {} with priority {:?}", task_id, priority);
        debug!("Using priority-based routing for task {} with priority {:?}", task_id, priority);

        // Store the provided strategy for logging purposes
        println!("Note: Provided strategy {:?} will be used as a hint but priority routing takes precedence", strategy);
        debug!("Note: Provided strategy {:?} will be used as a hint but priority routing takes precedence", strategy);

        let queue_strategy = match priority {
            TaskPriority::Realtime | TaskPriority::High => {
                // Use dynamic strategy if available, otherwise use queue's default strategy
                let strategy = dynamic_strategy.unwrap_or_else(|| self.priority_queue_manager.realtime.execution_strategy());
                println!("Routing to RealTime queue with strategy {:?} (dynamic: {})",
                         strategy, dynamic_strategy.is_some());
                strategy
            },
            TaskPriority::Normal => {
                // Use dynamic strategy if available, otherwise use queue's default strategy
                let strategy = dynamic_strategy.unwrap_or_else(|| self.priority_queue_manager.standard.execution_strategy());
                println!("Routing to Standard queue with strategy {:?} (dynamic: {})",
                         strategy, dynamic_strategy.is_some());
                strategy
            },
            TaskPriority::Low => {
                // Use dynamic strategy if available, otherwise use queue's default strategy
                let strategy = dynamic_strategy.unwrap_or_else(|| self.priority_queue_manager.background.execution_strategy());
                println!("Routing to Background queue with strategy {:?} (dynamic: {})",
                         strategy, dynamic_strategy.is_some());
                println!("Background queue status: {:?}", self.priority_queue_manager.background.get_status());
                strategy
            },
        };

        println!("Priority queue will use strategy {:?} for task {}", queue_strategy, task_id);

        let result = match priority {
            TaskPriority::Realtime | TaskPriority::High => {
                // Realtime and High priority tasks go to the RealTime queue
                println!("Enqueueing task {} in RealTime queue", task_id);
                self.priority_queue_manager.realtime.enqueue(task).await
            },
            TaskPriority::Normal => {
                // Normal priority tasks go to the Standard queue
                println!("Enqueueing task {} in Standard queue", task_id);
                self.priority_queue_manager.standard.enqueue(task).await
            },
            TaskPriority::Low => {
                // Low priority tasks go to the Background queue
                println!("Enqueueing task {} in Background queue", task_id);
                println!("Background queue status before enqueue: {:?}", self.priority_queue_manager.background.get_status());
                let result = self.priority_queue_manager.background.enqueue(task).await;
                println!("Background queue enqueue result: {:?}", result.is_ok());
                if let Err(ref e) = result {
                    println!("Background queue enqueue error: {}", e);
                }
                result
            },
        };

        match &result {
            Ok((id, _)) => {
                println!("Task {} queued successfully in priority queue", id);
                debug!("Task {} queued successfully in priority queue", id);
            },
            Err(e) => {
                println!("Failed to queue task {} in priority queue: {}", task_id, e);
                error!("Failed to queue task {} in priority queue: {}", task_id, e);
            }
        }

        result
    }

    /// Initializes the executor, starting priority queues and configuring thread pools.
    ///
    /// This method sets up the execution infrastructure based on the provided configuration:
    /// - Configures thread pools for Rayon and Tokio if specified in the config
    /// - Starts all priority queues
    /// - Sets up monitoring for queue statistics
    ///
    /// # Arguments
    ///
    /// * `config` - The engine configuration containing global settings
    ///
    /// # Returns
    ///
    /// A `PrismaResult<()>` indicating success or failure
    async fn initialize(&mut self, _config: &EngineConfig) -> PrismaResult<()> {
        info!("Initializing TaskExecutor...");
        println!("Initializing TaskExecutor...");

        // Configure thread pools based on config
        if let Some(rayon_threads) = self.config.rayon_worker_threads {
            // Configure Rayon thread pool if specified
            info!("Configuring Rayon thread pool with {} threads", rayon_threads);
            println!("Configuring Rayon thread pool with {} threads", rayon_threads);
            rayon::ThreadPoolBuilder::new()
                .num_threads(rayon_threads)
                .thread_name(|i| format!("prisma-rayon-{}", i))
                .build_global()
                .map_err(|e| {
                    let err_msg = format!("Failed to configure Rayon thread pool: {}", e);
                    error!("{}", err_msg);
                    println!("{}", err_msg);
                    err_msg
                })?;
        }

        if let Some(tokio_threads) = self.config.tokio_worker_threads {
            // Log Tokio thread pool configuration (Tokio runtime is typically configured at app startup)
            info!("Using Tokio runtime with {} worker threads", tokio_threads);
            println!("Using Tokio runtime with {} worker threads", tokio_threads);
        }

        // Start the Rayon queue first
        info!("Starting Rayon queue...");
        println!("Starting Rayon queue...");

        // Get a mutable reference to the RayonQueue
        let rayon_queue_ref = Arc::get_mut(&mut self.rayon_queue)
            .ok_or_else(|| {
                let err_msg = "Failed to get mutable reference to Rayon queue";
                error!("{}", err_msg);
                println!("{}", err_msg);
                GenericError::from(err_msg)
            })?;

        // Start the Rayon queue
        rayon_queue_ref.start().await.map_err(|e| {
            let err_msg = format!("Failed to start Rayon queue: {}", e);
            error!("{}", err_msg);
            println!("{}", err_msg);
            GenericError::from(err_msg)
        })?;

        info!("Rayon queue started successfully");
        println!("Rayon queue started successfully");

        // Set the RayonQueue reference in the BackgroundPriorityQueue
        info!("Setting RayonQueue reference in BackgroundPriorityQueue...");
        println!("Setting RayonQueue reference in BackgroundPriorityQueue...");

        // Get a mutable reference to the BackgroundPriorityQueue
        let background_queue = &mut self.priority_queue_manager.background;

        // Set the RayonQueue reference
        background_queue.set_rayon_queue(self.rayon_queue.clone());
        println!("RayonQueue reference set in BackgroundPriorityQueue");

        // Start all priority queues
        info!("Starting priority queues...");
        println!("Starting priority queues...");
        self.priority_queue_manager.start_all().await.map_err(|e| {
            let err_msg = format!("Failed to start priority queues: {}", e);
            error!("{}", err_msg);
            println!("{}", err_msg);
            GenericError::from(err_msg)
        })?;
        info!("Priority queues started successfully");
        println!("Priority queues started successfully");

        // Log the status of the Rayon queue
        println!("  Rayon queue status: {:?}", RayonQueue::get_status_from_arc(&self.rayon_queue));

        // Start the Tokio queue
        info!("Starting Tokio queue...");
        println!("Starting Tokio queue...");

        {
            // Create a new scope to limit the lifetime of the mutable borrow
            // Get a mutable reference to the Tokio queue inside the Arc
            let tokio_queue_ref = Arc::get_mut(&mut self.tokio_queue)
                .ok_or_else(|| {
                    let err_msg = "Failed to get mutable reference to Tokio queue";
                    error!("{}", err_msg);
                    println!("{}", err_msg);
                    GenericError::from(err_msg)
                })?;

            // Start the Tokio queue
            tokio_queue_ref.start().await.map_err(|e| {
                let err_msg = format!("Failed to start Tokio queue: {}", e);
                error!("{}", err_msg);
                println!("{}", err_msg);
                GenericError::from(err_msg)
            })?;

            // Log the status of the Tokio queue
            println!("  Tokio queue status: {:?}", tokio_queue_ref.get_status());
        } // mutable borrow of self.tokio_queue ends here

        info!("Tokio queue started successfully");
        println!("Tokio queue started successfully");

        // Set up monitoring for queue statistics
        let stats_interval = Duration::from_secs(60); // Log stats every minute
        info!("Setting up queue monitoring with interval of {} seconds", stats_interval.as_secs());
        println!("Setting up queue monitoring with interval of {} seconds", stats_interval.as_secs());

        // Monitor for background queue
        let background_queue = Arc::new(self.priority_queue_manager.background.clone());
        self.monitor_handles.push(Self::create_queue_monitor(
            "Background",
            background_queue,
            |q: &BackgroundPriorityQueue| q.get_stats(),
            stats_interval,
        ));

        // Monitor for standard queue
        let standard_queue = Arc::new(self.priority_queue_manager.standard.clone());
        self.monitor_handles.push(Self::create_queue_monitor(
            "Standard",
            standard_queue,
            |q: &StandardPriorityQueue| q.get_stats(),
            stats_interval,
        ));

        // Monitor for realtime queue
        let realtime_queue = Arc::new(self.priority_queue_manager.realtime.clone());
        self.monitor_handles.push(Self::create_queue_monitor(
            "RealTime",
            realtime_queue,
            |q: &RealTimePriorityQueue| q.get_stats(),
            stats_interval,
        ));

        // Monitor for direct queue
        let direct_queue_clone = self.direct_queue.clone();
        self.monitor_handles.push(Self::create_queue_monitor(
            "Direct",
            direct_queue_clone,
            |q: &DirectQueue| q.get_stats(),
            stats_interval,
        ));

        // Monitor for rayon queue
        let rayon_queue_clone = self.rayon_queue.clone();
        self.monitor_handles.push(Self::create_queue_monitor(
            "Rayon",
            rayon_queue_clone,
            |q: &RayonQueue| q.get_stats(),
            stats_interval,
        ));

        // Monitor for tokio queue
        let tokio_queue_clone = self.tokio_queue.clone();
        self.monitor_handles.push(Self::create_queue_monitor(
            "Tokio",
            tokio_queue_clone,
            |q: &TokioQueue| q.get_stats(),
            stats_interval,
        ));

        info!("TaskExecutor initialized with {} monitor tasks", self.monitor_handles.len());
        println!("TaskExecutor initialized with {} monitor tasks", self.monitor_handles.len());

        // Log the status of all queues for debugging
        println!("Queue status after initialization:");
        println!("  Background queue: {:?}", self.priority_queue_manager.background.get_status());
        println!("  Standard queue: {:?}", self.priority_queue_manager.standard.get_status());
        println!("  RealTime queue: {:?}", self.priority_queue_manager.realtime.get_status());

        // Get the status of Rayon and Tokio queues using immutable references
        println!("  Rayon queue: {:?}", RayonQueue::get_status_from_arc(&self.rayon_queue));
        println!("  Tokio queue: {:?}", TokioQueue::get_status_from_arc(&self.tokio_queue));

        Ok(())
    }

    /// Shuts down the executor gracefully.
    ///
    /// This method performs a graceful shutdown of the executor by:
    /// 1. Stopping all priority queues
    /// 2. Waiting for a configurable timeout for monitor tasks to finish
    /// 3. Aborting any remaining monitor tasks if they don't finish within the timeout
    ///
    /// # Returns
    ///
    /// A `PrismaResult<()>` indicating success or failure
    async fn shutdown(&mut self) -> PrismaResult<()> {
        info!("Shutting down TaskExecutor...");
        println!("Shutting down TaskExecutor...");

        // Stop all priority queues
        info!("Stopping priority queues...");
        println!("Stopping priority queues...");
        self.priority_queue_manager.stop_all().await.map_err(|e| {
            let err_msg = format!("Failed to stop priority queues: {}", e);
            error!("{}", err_msg);
            println!("{}", err_msg);
            GenericError::from(err_msg)
        })?;
        info!("Priority queues stopped successfully");
        println!("Priority queues stopped successfully");

        // Stop the Rayon queue using the immutable method
        info!("Stopping Rayon queue...");
        println!("Stopping Rayon queue...");

        // Use the stop_immutable method which doesn't require a mutable reference
        RayonQueueTrait::stop_immutable(&*self.rayon_queue).await.map_err(|e| {
            let err_msg = format!("Failed to stop Rayon queue: {}", e);
            error!("{}", err_msg);
            println!("{}", err_msg);
            GenericError::from(err_msg)
        })?;

        info!("Rayon queue stopped successfully");
        println!("Rayon queue stopped successfully");

        // Stop the Tokio queue using the immutable method
        info!("Stopping Tokio queue...");
        println!("Stopping Tokio queue...");

        // Use the stop_immutable method which doesn't require a mutable reference
        TokioQueueTrait::stop_immutable(&*self.tokio_queue).await.map_err(|e| {
            let err_msg = format!("Failed to stop Tokio queue: {}", e);
            error!("{}", err_msg);
            println!("{}", err_msg);
            GenericError::from(err_msg)
        })?;

        info!("Tokio queue stopped successfully");
        println!("Tokio queue stopped successfully");

        // Define a timeout for graceful shutdown
        let shutdown_timeout = Duration::from_secs(30); // 30 seconds timeout

        // Wait for monitor tasks to finish with timeout
        info!("Waiting for {} monitor tasks to finish (timeout: {}s)...",
            self.monitor_handles.len(), shutdown_timeout.as_secs());
        println!("Waiting for {} monitor tasks to finish (timeout: {}s)...",
            self.monitor_handles.len(), shutdown_timeout.as_secs());

        // Create a vector to hold the handles
        let mut handles = Vec::new();

        // Drain the handles from self.monitor_handles
        for handle in self.monitor_handles.drain(..) {
            handles.push(handle);
        }

        // Abort all monitor tasks (they're infinite loops, so they won't finish on their own)
        for handle in handles {
            handle.abort();
        }

        info!("TaskExecutor shut down successfully.");
        println!("TaskExecutor shut down successfully.");
        Ok(())
    }
}

/// Implement Drop to ensure resources are cleaned up even if shutdown is not called explicitly.
///
/// This implementation will:
/// 1. Log a warning if the executor is dropped without an explicit shutdown
/// 2. Abort all monitor tasks to prevent them from running indefinitely
///
/// Note: This is a fallback mechanism. The proper way to shut down the executor is to call
/// the `shutdown` method, which performs a graceful shutdown.
impl Drop for TaskExecutor {
    fn drop(&mut self) {
        // This runs in a synchronous context, so we can't easily call async shutdown.
        // Best practice is to explicitly call shutdown before dropping the executor.
        if !self.monitor_handles.is_empty() {
            warn!("TaskExecutor dropped without explicit shutdown. Monitor tasks will be aborted.");

            // Abort all monitor tasks
            for handle in self.monitor_handles.drain(..) {
                handle.abort(); // Attempt synchronous abort
            }

            warn!("All monitor tasks have been aborted.");
        }
    }
}
