// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/mod.rs
// =================================================================================================
// Purpose: Declares and exports the executor module components, which provide task execution
// services for the PrismaAI system.
// =================================================================================================

// Declare submodules or files within the executor module
pub mod types;
pub mod traits;
pub mod generics;
pub mod executor; // Contains the main TaskExecutor struct
pub mod queue; // Contains queue implementations for different execution strategies
pub mod cache_manager; // Provides cache management services
pub mod context_manager; // Provides context management services
pub mod dispatcher; // Provides task dispatching services
pub mod memory_manager; // Provides memory management services
pub mod results; // Provides result handling utilities
pub mod strategy_worker; // Provides worker implementations for different execution strategies
pub mod managements; // Provides management utilities for the executor system
pub mod agent_integration; // Provides integration with agent-specific functionality
#[cfg(feature = "test_mocks")] // Conditionally compile the mocks module
pub mod mocks;

// Re-export the main TaskExecutor struct and its config
pub use executor::TaskExecutor;
pub use types::ExecutorConfig;

// Re-export queue implementations
pub use queue::direct::DirectQueue;
pub use queue::rayon::RayonQueue;
pub use queue::tokio::TokioQueue;

// Re-export manager implementations
pub use cache_manager::{CacheManager, CacheManagerConfig, CacheManagerStats};
pub use context_manager::{ContextManager, ContextManagerConfig, ContextManagerStats};
pub use dispatcher::{Dispatcher, DispatcherConfig, DispatcherStats};
pub use memory_manager::{MemoryManager, MemoryManagerConfig, MemoryManagerStats, EvictionStrategy};
pub use results::{ResultProcessor, ResultProcessorConfig, ResultProcessorStats};
pub use strategy_worker::{StrategyWorker, DirectStrategyWorker, RayonStrategyWorker, TokioStrategyWorker, StrategyWorkerConfig, StrategyWorkerStats};
pub use managements::{ManagementService, ManagementConfig, ManagementStats};
pub use agent_integration::{AgentIntegration, AgentIntegrationConfig, AgentPriorityMapping, AgentCollaboration, CollaborationStatus};

// Note: execution_strategies submodule is declared in the parent (prisma_engine/mod.rs)
// as it contains functions used by the executor, not necessarily part of its internal structure.
