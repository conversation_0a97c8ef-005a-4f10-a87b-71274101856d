// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/dispatcher.rs
// =================================================================================================
// Purpose: Provides task dispatching services for the executor system. This includes routing tasks
// to appropriate queues based on their priority and execution strategy, as well as managing the
// lifecycle of tasks as they move through the system.
//
// Integration:
// - `executor.rs`: Uses Dispatcher to route tasks to appropriate queues
// - `queue/`: Interfaces with various queue implementations
// - `strategy_worker.rs`: Coordinates with StrategyWorker for task execution
// =================================================================================================

use std::any::Any;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use tokio::sync::{oneshot, Mutex};
use tracing::{debug, info};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{
    ExecutionStrategyType, TaskCategory, TaskId, TaskPriority,
};

use super::queue::{
    direct::DirectQueue,
    DirectQueueTrait,
    priority::{
        PriorityQueueManager,
        PriorityQueueTrait,
    },
    rayon::RayonQueue,
    RayonQueueTrait,
    tokio::TokioQueue,
    TokioQueueTrait,
};

/// Configuration for the Dispatcher
#[derive(Debug, Clone)]
pub struct DispatcherConfig {
    /// Maximum number of tasks that can be dispatched concurrently
    pub max_concurrent_dispatches: usize,

    /// Whether to enable task routing based on priority
    pub enable_priority_routing: bool,

    /// Whether to enable task routing based on strategy
    pub enable_strategy_routing: bool,

    /// Whether to enable task routing based on category
    pub enable_category_routing: bool,
}

impl Default for DispatcherConfig {
    fn default() -> Self {
        Self {
            max_concurrent_dispatches: 1000,
            enable_priority_routing: true,
            enable_strategy_routing: true,
            enable_category_routing: true,
        }
    }
}

/// Statistics for the Dispatcher
#[derive(Debug, Clone)]
pub struct DispatcherStats {
    /// Number of tasks dispatched
    pub tasks_dispatched: usize,

    /// Number of tasks dispatched by priority
    pub tasks_by_priority: HashMap<TaskPriority, usize>,

    /// Number of tasks dispatched by strategy
    pub tasks_by_strategy: HashMap<ExecutionStrategyType, usize>,

    /// Number of tasks dispatched by category
    pub tasks_by_category: HashMap<TaskCategory, usize>,

    /// Number of dispatch errors
    pub dispatch_errors: usize,
}

/// Provides task dispatching services for the executor system
pub struct Dispatcher {
    /// Configuration for the Dispatcher
    config: DispatcherConfig,

    /// Statistics for the Dispatcher
    stats: RwLock<DispatcherStats>,

    /// Direct queue for tasks that don't need prioritization
    direct_queue: Arc<DirectQueue>,

    /// Priority queue manager for prioritized tasks
    priority_queue_manager: Arc<PriorityQueueManager>,

    /// Rayon queue for CPU-bound tasks
    rayon_queue: Arc<Mutex<RayonQueue>>,

    /// Tokio queue for I/O-bound tasks
    tokio_queue: Arc<Mutex<TokioQueue>>,

    /// Mapping from task category to preferred execution strategy
    category_strategy_map: RwLock<HashMap<TaskCategory, ExecutionStrategyType>>,

    /// Mapping from task category to preferred priority
    category_priority_map: RwLock<HashMap<TaskCategory, TaskPriority>>,
}

impl Dispatcher {
    /// Creates a new Dispatcher with the given configuration and queues
    pub fn new(
        config: DispatcherConfig,
        direct_queue: Arc<DirectQueue>,
        priority_queue_manager: Arc<PriorityQueueManager>,
        rayon_queue: Arc<Mutex<RayonQueue>>,
        tokio_queue: Arc<Mutex<TokioQueue>>,
    ) -> Self {
        info!("Creating new Dispatcher with config: {:?}", config);

        // Initialize default category mappings
        let mut category_strategy_map = HashMap::new();
        category_strategy_map.insert(TaskCategory::LLMInference, ExecutionStrategyType::Rayon);
        category_strategy_map.insert(TaskCategory::EmbeddingGeneration, ExecutionStrategyType::Rayon);
        category_strategy_map.insert(TaskCategory::DatabaseQuery, ExecutionStrategyType::Tokio);
        category_strategy_map.insert(TaskCategory::FileProcessing, ExecutionStrategyType::Tokio);
        category_strategy_map.insert(TaskCategory::NetworkRequest, ExecutionStrategyType::Tokio);
        category_strategy_map.insert(TaskCategory::UICallback, ExecutionStrategyType::Direct);
        category_strategy_map.insert(TaskCategory::Internal, ExecutionStrategyType::Direct);

        let mut category_priority_map = HashMap::new();
        category_priority_map.insert(TaskCategory::LLMInference, TaskPriority::High);
        category_priority_map.insert(TaskCategory::EmbeddingGeneration, TaskPriority::Normal);
        category_priority_map.insert(TaskCategory::DatabaseQuery, TaskPriority::Normal);
        category_priority_map.insert(TaskCategory::FileProcessing, TaskPriority::Low);
        category_priority_map.insert(TaskCategory::NetworkRequest, TaskPriority::Low);
        category_priority_map.insert(TaskCategory::UICallback, TaskPriority::High);
        category_priority_map.insert(TaskCategory::Internal, TaskPriority::Normal);

        Self {
            config,
            stats: RwLock::new(DispatcherStats {
                tasks_dispatched: 0,
                tasks_by_priority: HashMap::new(),
                tasks_by_strategy: HashMap::new(),
                tasks_by_category: HashMap::new(),
                dispatch_errors: 0,
            }),
            direct_queue,
            priority_queue_manager,
            rayon_queue,
            tokio_queue,
            category_strategy_map: RwLock::new(category_strategy_map),
            category_priority_map: RwLock::new(category_priority_map),
        }
    }

    /// Dispatches a task to the appropriate queue based on its priority and strategy
    pub async fn dispatch_task(
        &self,
        task: Box<dyn Task>,
        strategy: ExecutionStrategyType,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        let task_priority = task.priority();
        let task_category = task.category();

        debug!(
            "Dispatching task {} with priority {:?}, category {:?}, and strategy {:?}",
            task_id, task_priority, task_category, strategy
        );

        // Determine the actual strategy to use
        let actual_strategy = if self.config.enable_strategy_routing {
            strategy
        } else if self.config.enable_category_routing {
            // Use the strategy mapped to the task's category
            let category_strategy_map = self.category_strategy_map.read().unwrap();
            *category_strategy_map
                .get(&task_category)
                .unwrap_or(&ExecutionStrategyType::Direct)
        } else {
            // Default to the provided strategy
            strategy
        };

        // Determine the actual priority to use
        let actual_priority = if self.config.enable_priority_routing {
            task_priority
        } else if self.config.enable_category_routing {
            // Use the priority mapped to the task's category
            let category_priority_map = self.category_priority_map.read().unwrap();
            *category_priority_map
                .get(&task_category)
                .unwrap_or(&TaskPriority::Normal)
        } else {
            // Default to the task's priority
            task_priority
        };

        debug!(
            "Using strategy {:?} and priority {:?} for task {}",
            actual_strategy, actual_priority, task_id
        );

        // Dispatch the task to the appropriate queue
        let result = match actual_strategy {
            ExecutionStrategyType::Direct => {
                // Use the direct queue
                match DirectQueueTrait::enqueue(&*self.direct_queue, task).await {
                    Ok((task_id, receiver, _)) => Ok((task_id, receiver)),
                    Err(e) => Err(e),
                }
            }
            ExecutionStrategyType::Rayon => {
                // Use the Rayon queue
                RayonQueueTrait::enqueue(&*self.rayon_queue.lock().await, task).await
            }
            ExecutionStrategyType::Tokio => {
                // Use the Tokio queue
                TokioQueueTrait::enqueue(&*self.tokio_queue.lock().await, task).await
            }
        };

        // Update statistics
        match &result {
            Ok(_) => {
                let mut stats = self.stats.write().unwrap();
                stats.tasks_dispatched += 1;

                // Update tasks by priority
                let priority_count = stats.tasks_by_priority.entry(actual_priority).or_insert(0);
                *priority_count += 1;

                // Update tasks by strategy
                let strategy_count = stats.tasks_by_strategy.entry(actual_strategy).or_insert(0);
                *strategy_count += 1;

                // Update tasks by category
                let category_count = stats.tasks_by_category.entry(task_category).or_insert(0);
                *category_count += 1;
            }
            Err(_) => {
                let mut stats = self.stats.write().unwrap();
                stats.dispatch_errors += 1;
            }
        }

        result
    }

    /// Dispatches a task to the appropriate priority queue based on its priority
    pub async fn dispatch_prioritized_task(
        &self,
        task: Box<dyn Task>,
        strategy: ExecutionStrategyType,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        let task_priority = task.priority();
        let task_category = task.category();

        debug!(
            "Dispatching prioritized task {} with priority {:?}, category {:?}, and strategy {:?}",
            task_id, task_priority, task_category, strategy
        );

        // Determine the actual priority to use
        let actual_priority = if self.config.enable_priority_routing {
            task_priority
        } else if self.config.enable_category_routing {
            // Use the priority mapped to the task's category
            let category_priority_map = self.category_priority_map.read().unwrap();
            *category_priority_map
                .get(&task_category)
                .unwrap_or(&TaskPriority::Normal)
        } else {
            // Default to the task's priority
            task_priority
        };

        debug!(
            "Using priority {:?} for prioritized task {}",
            actual_priority, task_id
        );

        // Dispatch the task to the appropriate priority queue
        let result = match actual_priority {
            TaskPriority::Realtime => {
                // Use the real-time priority queue
                PriorityQueueTrait::enqueue(&self.priority_queue_manager.realtime, task).await
            }
            TaskPriority::High => {
                // Use the high priority queue
                PriorityQueueTrait::enqueue(&self.priority_queue_manager.standard, task).await
            }
            TaskPriority::Normal => {
                // Use the normal priority queue
                PriorityQueueTrait::enqueue(&self.priority_queue_manager.standard, task).await
            }
            TaskPriority::Low => {
                // Use the background priority queue
                PriorityQueueTrait::enqueue(&self.priority_queue_manager.background, task).await
            }
        };

        // Update statistics
        match &result {
            Ok(_) => {
                let mut stats = self.stats.write().unwrap();
                stats.tasks_dispatched += 1;

                // Update tasks by priority
                let priority_count = stats.tasks_by_priority.entry(actual_priority).or_insert(0);
                *priority_count += 1;

                // Update tasks by category
                let category_count = stats.tasks_by_category.entry(task_category).or_insert(0);
                *category_count += 1;
            }
            Err(_) => {
                let mut stats = self.stats.write().unwrap();
                stats.dispatch_errors += 1;
            }
        }

        result
    }

    /// Sets the preferred execution strategy for a task category
    pub fn set_category_strategy(
        &self,
        category: TaskCategory,
        strategy: ExecutionStrategyType,
    ) {
        debug!(
            "Setting preferred strategy {:?} for category {:?}",
            strategy, category
        );

        let mut category_strategy_map = self.category_strategy_map.write().unwrap();
        category_strategy_map.insert(category, strategy);
    }

    /// Gets the preferred execution strategy for a task category
    pub fn get_category_strategy(
        &self,
        category: TaskCategory,
    ) -> Option<ExecutionStrategyType> {
        let category_strategy_map = self.category_strategy_map.read().unwrap();
        category_strategy_map.get(&category).copied()
    }

    /// Sets the preferred priority for a task category
    pub fn set_category_priority(&self, category: TaskCategory, priority: TaskPriority) {
        debug!(
            "Setting preferred priority {:?} for category {:?}",
            priority, category
        );

        let mut category_priority_map = self.category_priority_map.write().unwrap();
        category_priority_map.insert(category, priority);
    }

    /// Gets the preferred priority for a task category
    pub fn get_category_priority(&self, category: TaskCategory) -> Option<TaskPriority> {
        let category_priority_map = self.category_priority_map.read().unwrap();
        category_priority_map.get(&category).copied()
    }

    /// Gets the current dispatcher statistics
    pub fn get_stats(&self) -> DispatcherStats {
        self.stats.read().unwrap().clone()
    }
}