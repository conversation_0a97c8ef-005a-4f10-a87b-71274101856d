// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/cache_manager.rs
// =================================================================================================
// Purpose: Provides cache management services for the executor system. This includes allocating
// and retrieving caches for tasks, managing cache lifecycle, and implementing cache eviction
// strategies.
//
// Integration:
// - `executor.rs`: Uses CacheManager to allocate and retrieve caches for tasks
// - `memory_manager.rs`: Coordinates with MemoryManager for memory allocation
// - `context_manager.rs`: Coordinates with ContextManager for context-aware caching
// =================================================================================================

use std::any::Any;
use std::collections::{HashMap, VecDeque};
use std::sync::RwLock;
use std::time::{Duration, Instant};
use tracing::{debug, error, info};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::types::TaskId;

/// Configuration for the CacheManager
#[derive(Debug, Clone)]
pub struct CacheManagerConfig {
    /// Maximum size of the cache in bytes
    pub max_cache_size_bytes: usize,

    /// Maximum number of items to keep in the cache
    pub max_cache_items: usize,

    /// Time-to-live for cache items in seconds
    pub cache_ttl_seconds: u64,

    /// Whether to enable cache pooling
    pub enable_cache_pooling: bool,

    /// Maximum size of the cache pool
    pub max_pool_size: usize,
}

impl Default for CacheManagerConfig {
    fn default() -> Self {
        Self {
            max_cache_size_bytes: 1024 * 1024 * 100, // 100 MB
            max_cache_items: 10000,
            cache_ttl_seconds: 3600, // 1 hour
            enable_cache_pooling: true,
            max_pool_size: 100,
        }
    }
}

/// Metadata for a cache item
#[derive(Debug, Clone)]
struct CacheItemMetadata {
    /// Size of the item in bytes (approximate)
    size_bytes: usize,

    /// When the item was created
    created_at: Instant,

    /// When the item was last accessed
    last_accessed: Instant,

    /// Number of times the item has been accessed
    access_count: usize,

    /// Whether the item is pinned (cannot be evicted)
    pinned: bool,

    /// Task ID associated with this cache item
    task_id: Option<TaskId>,
}

/// A cache item
struct CacheItem {
    /// The cached value
    value: Box<dyn Any + Send + Sync>,

    /// Metadata for the cache item
    metadata: CacheItemMetadata,
}

/// Eviction strategy for the cache
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EvictionStrategy {
    /// Least recently used
    LRU,

    /// Least frequently used
    LFU,

    /// First in, first out
    FIFO,
}

/// Statistics for the CacheManager
#[derive(Debug, Clone)]
pub struct CacheManagerStats {
    /// Current size of the cache in bytes
    pub cache_size_bytes: usize,

    /// Number of items in the cache
    pub cache_items: usize,

    /// Number of cache hits
    pub cache_hits: usize,

    /// Number of cache misses
    pub cache_misses: usize,

    /// Number of cache evictions
    pub cache_evictions: usize,

    /// Number of items in the cache pool
    pub pool_items: usize,

    /// Number of pool hits
    pub pool_hits: usize,

    /// Number of pool misses
    pub pool_misses: usize,
}

/// Provides cache management services for the executor system
pub struct CacheManager {
    /// Configuration for the CacheManager
    config: CacheManagerConfig,

    /// The cache itself
    cache: RwLock<HashMap<String, CacheItem>>,

    /// Access order for LRU eviction
    access_order: RwLock<VecDeque<String>>,

    /// Creation order for FIFO eviction
    creation_order: RwLock<VecDeque<String>>,

    /// Current size of the cache in bytes
    cache_size_bytes: RwLock<usize>,

    /// Statistics for the CacheManager
    stats: RwLock<CacheManagerStats>,

    /// Cache pool for reusing cache allocations
    cache_pool: RwLock<Vec<Box<dyn Any + Send + Sync>>>,

    /// Current eviction strategy
    eviction_strategy: RwLock<EvictionStrategy>,
}

impl CacheManager {
    /// Creates a new CacheManager with the given configuration
    pub fn new(config: CacheManagerConfig) -> Self {
        info!("Creating new CacheManager with config: {:?}", config);

        Self {
            config,
            cache: RwLock::new(HashMap::new()),
            access_order: RwLock::new(VecDeque::new()),
            creation_order: RwLock::new(VecDeque::new()),
            cache_size_bytes: RwLock::new(0),
            stats: RwLock::new(CacheManagerStats {
                cache_size_bytes: 0,
                cache_items: 0,
                cache_hits: 0,
                cache_misses: 0,
                cache_evictions: 0,
                pool_items: 0,
                pool_hits: 0,
                pool_misses: 0,
            }),
            cache_pool: RwLock::new(Vec::new()),
            eviction_strategy: RwLock::new(EvictionStrategy::LRU),
        }
    }

    /// Creates a new CacheManager with default configuration
    pub fn default() -> Self {
        Self::new(CacheManagerConfig::default())
    }

    /// Allocates a cache for a task
    pub fn allocate_cache(&self, task_id: TaskId, size_hint: Option<usize>) -> PrismaResult<String> {
        let cache_key = format!("cache_{}", task_id);
        debug!("Allocating cache for task {} with key {}", task_id, cache_key);

        // Check if we can reuse a cache from the pool
        if self.config.enable_cache_pooling {
            let mut pool = self.cache_pool.write().unwrap();
            if !pool.is_empty() {
                // Pop a cache from the pool
                pool.pop();

                // Update stats
                let mut stats = self.stats.write().unwrap();
                stats.pool_items = pool.len();
                stats.pool_hits += 1;

                debug!("Reused cache from pool for task {}", task_id);
                return Ok(cache_key);
            } else {
                // Update stats
                let mut stats = self.stats.write().unwrap();
                stats.pool_misses += 1;
            }
        }

        // Create a new cache entry
        let size_bytes = size_hint.unwrap_or(0);

        // Check if we need to evict items to make room
        self.ensure_capacity(size_bytes)?;

        // Create a proper cache item with an empty HashMap as the initial value
        // This allows for storing key-value pairs related to the task
        let cache_item = CacheItem {
            value: Box::new(HashMap::<String, Box<dyn Any + Send + Sync>>::new()),
            metadata: CacheItemMetadata {
                size_bytes,
                created_at: Instant::now(),
                last_accessed: Instant::now(),
                access_count: 0,
                pinned: false,
                task_id: Some(task_id),
            },
        };

        // Store the cache item
        {
            let mut cache = self.cache.write().unwrap();
            cache.insert(cache_key.clone(), cache_item);

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            creation_order.push_back(cache_key.clone());

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            access_order.push_back(cache_key.clone());

            // Update cache size
            let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
            *cache_size_bytes += size_bytes;

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = cache.len();
            stats.cache_size_bytes = *cache_size_bytes;
        }

        debug!("Allocated cache for task {} with key {}", task_id, cache_key);
        Ok(cache_key)
    }

    /// Stores a value in the cache
    pub fn store<T: Any + Send + Sync>(&self, key: &str, value: T) -> PrismaResult<()> {
        debug!("Storing value for key: {}", key);

        // Get the size of the value (approximate)
        let size_bytes = std::mem::size_of_val(&value);

        // Check if we need to evict items to make room
        self.ensure_capacity(size_bytes)?;

        // Create a cache item
        let cache_item = CacheItem {
            value: Box::new(value),
            metadata: CacheItemMetadata {
                size_bytes,
                created_at: Instant::now(),
                last_accessed: Instant::now(),
                access_count: 0,
                pinned: false,
                task_id: None,
            },
        };

        // Store the cache item
        {
            let mut cache = self.cache.write().unwrap();

            // If the key already exists, update the cache size
            if let Some(existing_item) = cache.get(key) {
                let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
                *cache_size_bytes -= existing_item.metadata.size_bytes;
                *cache_size_bytes += size_bytes;
            }

            cache.insert(key.to_string(), cache_item);

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            if !creation_order.contains(&key.to_string()) {
                creation_order.push_back(key.to_string());
            }

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }
            access_order.push_back(key.to_string());

            // Update cache size if the key didn't already exist
            if !cache.contains_key(key) {
                let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
                *cache_size_bytes += size_bytes;
            }

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = cache.len();
            stats.cache_size_bytes = *self.cache_size_bytes.read().unwrap();
        }

        Ok(())
    }

    /// Retrieves a value from the cache
    pub fn retrieve<T: Any + Send + Sync + Clone>(&self, key: &str) -> PrismaResult<Option<T>> {
        debug!("Retrieving value for key: {}", key);

        // Check if the key exists in the cache
        let mut cache = self.cache.write().unwrap();
        if let Some(mut item) = cache.remove(key) {
            // Update access metadata
            item.metadata.last_accessed = Instant::now();
            item.metadata.access_count += 1;

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }
            access_order.push_back(key.to_string());

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_hits += 1;

            // Check if the value is of the expected type
            let value_ref = &item.value;
            if let Some(typed_value) = value_ref.downcast_ref::<T>() {
                // Clone the value for returning
                let result_value = typed_value.clone();

                // Put the item back in the cache
                cache.insert(key.to_string(), item);

                debug!("Cache hit for key: {}", key);
                return Ok(Some(result_value));
            } else {
                // Type mismatch, put the original item back
                cache.insert(key.to_string(), item);

                error!("Type mismatch for cache key: {}", key);
                return Err(GenericError::from(format!(
                    "Type mismatch for cache key: {}",
                    key
                )));
            }
        } else {
            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_misses += 1;

            debug!("Cache miss for key: {}", key);
            Ok(None)
        }
    }

    /// Pins a cache item so it cannot be evicted
    pub fn pin(&self, key: &str) -> PrismaResult<()> {
        debug!("Pinning cache item: {}", key);

        let mut cache = self.cache.write().unwrap();
        if let Some(mut item) = cache.remove(key) {
            item.metadata.pinned = true;
            cache.insert(key.to_string(), item);
            debug!("Pinned cache item: {}", key);
            Ok(())
        } else {
            error!("Cache item not found: {}", key);
            Err(GenericError::from(format!("Cache item not found: {}", key)))
        }
    }

    /// Unpins a cache item so it can be evicted
    pub fn unpin(&self, key: &str) -> PrismaResult<()> {
        debug!("Unpinning cache item: {}", key);

        let mut cache = self.cache.write().unwrap();
        if let Some(mut item) = cache.remove(key) {
            item.metadata.pinned = false;
            cache.insert(key.to_string(), item);
            debug!("Unpinned cache item: {}", key);
            Ok(())
        } else {
            error!("Cache item not found: {}", key);
            Err(GenericError::from(format!("Cache item not found: {}", key)))
        }
    }

    /// Evicts a cache item
    pub fn evict(&self, key: &str) -> PrismaResult<bool> {
        debug!("Evicting cache item: {}", key);

        let mut cache = self.cache.write().unwrap();
        if let Some(item) = cache.remove(key) {
            // Update cache size
            let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
            *cache_size_bytes -= item.metadata.size_bytes;

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            if let Some(index) = creation_order.iter().position(|k| k == key) {
                creation_order.remove(index);
            }

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = cache.len();
            stats.cache_size_bytes = *cache_size_bytes;
            stats.cache_evictions += 1;

            // Add to pool if enabled
            if self.config.enable_cache_pooling {
                let mut pool = self.cache_pool.write().unwrap();
                if pool.len() < self.config.max_pool_size {
                    pool.push(item.value);
                    stats.pool_items = pool.len();
                }
            }

            debug!("Evicted cache item: {}", key);
            Ok(true)
        } else {
            debug!("Cache item not found: {}", key);
            Ok(false)
        }
    }

    /// Clears all cache items for a task
    pub fn clear_task_cache(&self, task_id: TaskId) -> PrismaResult<usize> {
        debug!("Clearing cache for task {}", task_id);

        let mut cache = self.cache.write().unwrap();
        let mut keys_to_remove = Vec::new();

        // Find all cache items for this task
        for (key, item) in cache.iter() {
            if let Some(item_task_id) = item.metadata.task_id {
                if item_task_id == task_id {
                    keys_to_remove.push(key.clone());
                }
            }
        }

        // Remove the items
        let mut total_size = 0;
        for key in &keys_to_remove {
            if let Some(item) = cache.remove(key) {
                total_size += item.metadata.size_bytes;

                // Update creation order
                let mut creation_order = self.creation_order.write().unwrap();
                if let Some(index) = creation_order.iter().position(|k| k == key) {
                    creation_order.remove(index);
                }

                // Update access order
                let mut access_order = self.access_order.write().unwrap();
                if let Some(index) = access_order.iter().position(|k| k == key) {
                    access_order.remove(index);
                }

                // Add to pool if enabled
                if self.config.enable_cache_pooling {
                    let mut pool = self.cache_pool.write().unwrap();
                    if pool.len() < self.config.max_pool_size {
                        pool.push(item.value);
                    }
                }
            }
        }

        // Update cache size
        {
            let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
            *cache_size_bytes -= total_size;

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = cache.len();
            stats.cache_size_bytes = *cache_size_bytes;
            stats.cache_evictions += keys_to_remove.len();
            stats.pool_items = self.cache_pool.read().unwrap().len();
        }

        debug!("Cleared {} cache items for task {}", keys_to_remove.len(), task_id);
        Ok(keys_to_remove.len())
    }

    /// Sets the eviction strategy
    pub fn set_eviction_strategy(&self, strategy: EvictionStrategy) {
        info!("Setting eviction strategy to {:?}", strategy);
        let mut eviction_strategy = self.eviction_strategy.write().unwrap();
        *eviction_strategy = strategy;
    }

    /// Gets the current eviction strategy
    pub fn get_eviction_strategy(&self) -> EvictionStrategy {
        *self.eviction_strategy.read().unwrap()
    }

    /// Gets the current cache statistics
    pub fn get_stats(&self) -> CacheManagerStats {
        self.stats.read().unwrap().clone()
    }

    /// Ensures that the cache has enough capacity for a new item
    fn ensure_capacity(&self, size_bytes: usize) -> PrismaResult<()> {
        let current_size = *self.cache_size_bytes.read().unwrap();
        let current_items = self.cache.read().unwrap().len();

        // Check if we need to evict items
        if current_size + size_bytes > self.config.max_cache_size_bytes
            || current_items >= self.config.max_cache_items
        {
            debug!(
                "Cache capacity exceeded, evicting items (size: {} + {} > {}, items: {} >= {})",
                current_size,
                size_bytes,
                self.config.max_cache_size_bytes,
                current_items,
                self.config.max_cache_items
            );

            // Evict items until we have enough space
            let mut evicted_size = 0;
            let mut evicted_count = 0;

            while (current_size - evicted_size + size_bytes > self.config.max_cache_size_bytes
                || current_items - evicted_count >= self.config.max_cache_items)
                && evicted_count < current_items
            {
                // Choose an item to evict based on the strategy
                if let Some(key) = self.choose_item_to_evict()? {
                    if let Some(item) = self.cache.write().unwrap().remove(&key) {
                        // Update eviction stats
                        evicted_size += item.metadata.size_bytes;
                        evicted_count += 1;

                        // Update creation order
                        let mut creation_order = self.creation_order.write().unwrap();
                        if let Some(index) = creation_order.iter().position(|k| k == &key) {
                            creation_order.remove(index);
                        }

                        // Update access order
                        let mut access_order = self.access_order.write().unwrap();
                        if let Some(index) = access_order.iter().position(|k| k == &key) {
                            access_order.remove(index);
                        }

                        // Add to pool if enabled
                        if self.config.enable_cache_pooling {
                            let mut pool = self.cache_pool.write().unwrap();
                            if pool.len() < self.config.max_pool_size {
                                pool.push(item.value);
                            }
                        }

                        debug!("Evicted cache item: {}", key);
                    }
                } else {
                    // No more items to evict
                    break;
                }
            }

            // Update cache size
            {
                let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
                *cache_size_bytes -= evicted_size;

                // Update stats
                let mut stats = self.stats.write().unwrap();
                stats.cache_items = self.cache.read().unwrap().len();
                stats.cache_size_bytes = *cache_size_bytes;
                stats.cache_evictions += evicted_count;
                stats.pool_items = self.cache_pool.read().unwrap().len();
            }

            debug!("Evicted {} items ({} bytes)", evicted_count, evicted_size);
        }

        Ok(())
    }

    /// Chooses an item to evict based on the current eviction strategy
    fn choose_item_to_evict(&self) -> PrismaResult<Option<String>> {
        let strategy = *self.eviction_strategy.read().unwrap();

        match strategy {
            EvictionStrategy::LRU => {
                // Choose the least recently used item
                let access_order = self.access_order.read().unwrap();
                let cache = self.cache.read().unwrap();

                for key in access_order.iter() {
                    if let Some(item) = cache.get(key) {
                        if !item.metadata.pinned {
                            return Ok(Some(key.clone()));
                        }
                    }
                }

                Ok(None)
            }
            EvictionStrategy::LFU => {
                // Choose the least frequently used item
                let cache = self.cache.read().unwrap();
                let mut min_access_count = usize::MAX;
                let mut min_key = None;

                for (key, item) in cache.iter() {
                    if !item.metadata.pinned && item.metadata.access_count < min_access_count {
                        min_access_count = item.metadata.access_count;
                        min_key = Some(key.clone());
                    }
                }

                Ok(min_key)
            }
            EvictionStrategy::FIFO => {
                // Choose the oldest item
                let creation_order = self.creation_order.read().unwrap();
                let cache = self.cache.read().unwrap();

                for key in creation_order.iter() {
                    if let Some(item) = cache.get(key) {
                        if !item.metadata.pinned {
                            return Ok(Some(key.clone()));
                        }
                    }
                }

                Ok(None)
            }
        }
    }

    /// Cleans up expired cache items
    pub fn cleanup_expired(&self) -> PrismaResult<usize> {
        debug!("Cleaning up expired cache items");

        let mut cache = self.cache.write().unwrap();
        let mut keys_to_remove = Vec::new();
        let now = Instant::now();
        let ttl = Duration::from_secs(self.config.cache_ttl_seconds);

        // Find all expired cache items
        for (key, item) in cache.iter() {
            if !item.metadata.pinned && now.duration_since(item.metadata.created_at) > ttl {
                keys_to_remove.push(key.clone());
            }
        }

        // Remove the items
        let mut total_size = 0;
        for key in &keys_to_remove {
            if let Some(item) = cache.remove(key) {
                total_size += item.metadata.size_bytes;

                // Update creation order
                let mut creation_order = self.creation_order.write().unwrap();
                if let Some(index) = creation_order.iter().position(|k| k == key) {
                    creation_order.remove(index);
                }

                // Update access order
                let mut access_order = self.access_order.write().unwrap();
                if let Some(index) = access_order.iter().position(|k| k == key) {
                    access_order.remove(index);
                }

                // Add to pool if enabled
                if self.config.enable_cache_pooling {
                    let mut pool = self.cache_pool.write().unwrap();
                    if pool.len() < self.config.max_pool_size {
                        pool.push(item.value);
                    }
                }
            }
        }

        // Update cache size
        {
            let mut cache_size_bytes = self.cache_size_bytes.write().unwrap();
            *cache_size_bytes -= total_size;

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = cache.len();
            stats.cache_size_bytes = *cache_size_bytes;
            stats.cache_evictions += keys_to_remove.len();
            stats.pool_items = self.cache_pool.read().unwrap().len();
        }

        debug!("Cleaned up {} expired cache items", keys_to_remove.len());
        Ok(keys_to_remove.len())
    }
}