use serde::{Deserialize, Serialize};
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskPriority}; // Use parent types

// Configuration specific to the task executor component
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExecutorConfig {
    // Example: Number of worker threads for different strategies
    pub tokio_worker_threads: Option<usize>, // Defaults to <PERSON><PERSON><PERSON>'s default if None
    pub rayon_worker_threads: Option<usize>, // Defaults to Rayon's default if None

    // Example: Queue capacities
    pub realtime_queue_capacity: usize,
    pub high_priority_queue_capacity: usize,
    pub normal_priority_queue_capacity: usize,
    pub low_priority_queue_capacity: usize,

    // Add other executor-specific configurations if needed
}

impl Default for ExecutorConfig {
    fn default() -> Self {
        ExecutorConfig {
            tokio_worker_threads: None,
            rayon_worker_threads: None,
            realtime_queue_capacity: 100,
            high_priority_queue_capacity: 1000,
            normal_priority_queue_capacity: 5000,
            low_priority_queue_capacity: 10000,
        }
    }
}

// Internal message type for sending tasks to worker loops
// Includes the task itself and the chosen strategy
// pub(super) struct TaskDispatchRequest { // Keep internal to executor module
//     pub task: Box<dyn crate::prisma::prisma_engine::traits::Task>,
//     pub strategy: ExecutionStrategyType,
//     pub priority: TaskPriority, // Include priority for internal queuing
// }

// Add other executor-specific internal types here if needed
