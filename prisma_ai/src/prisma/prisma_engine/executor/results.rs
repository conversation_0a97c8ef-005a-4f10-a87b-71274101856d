// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/results.rs
// =================================================================================================
// Purpose: Provides result handling utilities for the executor module. This includes processing,
// transforming, and managing task execution results.
//
// Integration:
// - `executor.rs`: Uses ResultProcessor to process task execution results
// - `generics.rs`: Uses generic result handling utilities
// - `dispatcher.rs`: Coordinates with Dispatcher for result routing
// =================================================================================================

use std::any::Any;
use std::collections::HashMap;
use std::sync::RwLock;
use std::time::{Duration, Instant};
use tracing::{debug, error, info};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::TaskId;

use super::generics::TaskExecutionMetadata;

/// Configuration for the ResultProcessor
#[derive(Debug, Clone)]
pub struct ResultProcessorConfig {
    /// Maximum number of results to keep in history
    pub max_history_size: usize,

    /// Whether to enable result transformation
    pub enable_transformation: bool,

    /// Whether to enable result caching
    pub enable_caching: bool,

    /// Time-to-live for cached results in seconds
    pub cache_ttl_seconds: u64,
}

impl Default for ResultProcessorConfig {
    fn default() -> Self {
        Self {
            max_history_size: 1000,
            enable_transformation: true,
            enable_caching: true,
            cache_ttl_seconds: 3600, // 1 hour
        }
    }
}

/// Statistics for the ResultProcessor
#[derive(Debug, Clone)]
pub struct ResultProcessorStats {
    /// Number of results processed
    pub results_processed: usize,

    /// Number of successful results
    pub successful_results: usize,

    /// Number of failed results
    pub failed_results: usize,

    /// Number of cached results
    pub cached_results: usize,

    /// Number of cache hits
    pub cache_hits: usize,

    /// Number of transformations applied
    pub transformations_applied: usize,
}

/// A processed result
#[derive(Debug, Clone)]
pub struct ProcessedResult {
    /// ID of the task that produced the result
    pub task_id: TaskId,

    /// Whether the result was successful
    pub is_success: bool,

    /// The error if the result was not successful
    pub error: Option<String>,

    /// Metadata about the task execution
    pub metadata: TaskExecutionMetadata,

    /// When the result was processed
    pub processed_at: Instant,

    /// Duration of the result processing
    pub processing_duration: Duration,

    /// Transformations applied to the result
    pub transformations: Vec<String>,
}

/// A result transformation
pub trait ResultTransformation: Send + Sync {
    /// Applies the transformation to a result
    fn apply(
        &self,
        result: PrismaResult<Box<dyn Any + Send>>,
        metadata: &TaskExecutionMetadata,
    ) -> PrismaResult<Box<dyn Any + Send>>;

    /// Gets the name of the transformation
    fn name(&self) -> &str;
}

/// Provides result handling utilities for the executor module
pub struct ResultProcessor {
    /// Configuration for the ResultProcessor
    config: ResultProcessorConfig,

    /// Statistics for the ResultProcessor
    stats: RwLock<ResultProcessorStats>,

    /// Result history
    history: RwLock<HashMap<TaskId, ProcessedResult>>,

    /// Result cache - stores a marker for successful results and the timestamp
    cache: RwLock<HashMap<String, (String, Instant)>>,

    /// Result transformations
    transformations: RwLock<Vec<Box<dyn ResultTransformation>>>,
}

impl ResultProcessor {
    /// Creates a new ResultProcessor with the given configuration
    pub fn new(config: ResultProcessorConfig) -> Self {
        info!("Creating new ResultProcessor with config: {:?}", config);

        Self {
            config,
            stats: RwLock::new(ResultProcessorStats {
                results_processed: 0,
                successful_results: 0,
                failed_results: 0,
                cached_results: 0,
                cache_hits: 0,
                transformations_applied: 0,
            }),
            history: RwLock::new(HashMap::new()),
            cache: RwLock::new(HashMap::new()),
            transformations: RwLock::new(Vec::new()),
        }
    }

    /// Creates a new ResultProcessor with default configuration
    pub fn default() -> Self {
        Self::new(ResultProcessorConfig::default())
    }

    /// Processes a task execution result
    pub fn process_result(
        &self,
        result: PrismaResult<Box<dyn Any + Send>>,
        metadata: TaskExecutionMetadata,
    ) -> PrismaResult<Box<dyn Any + Send>> {
        let task_id = metadata.task_id;
        debug!("Processing result for task {}", task_id);

        let start_time = Instant::now();
        let mut transformations_applied = Vec::new();

        // Apply transformations if enabled
        let transformed_result = if self.config.enable_transformation {
            let transformations = self.transformations.read().unwrap();
            let mut current_result = result;

            for transformation in transformations.iter() {
                debug!(
                    "Applying transformation {} to result for task {}",
                    transformation.name(),
                    task_id
                );

                match transformation.apply(current_result, &metadata) {
                    Ok(new_result) => {
                        transformations_applied.push(transformation.name().to_string());
                        current_result = Ok(new_result);
                    }
                    Err(e) => {
                        error!(
                            "Transformation {} failed for task {}: {:?}",
                            transformation.name(),
                            task_id,
                            e
                        );
                        current_result = Err(e);
                        break;
                    }
                }
            }

            current_result
        } else {
            result
        };

        // Cache the result if enabled and successful
        if self.config.enable_caching && transformed_result.is_ok() {
            let cache_key = format!("result_{}", task_id);
            let mut cache = self.cache.write().unwrap();

            // Store a marker for the successful result
            cache.insert(
                cache_key,
                (format!("success_{}", task_id), Instant::now()),
            );

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cached_results += 1;
        }

        // Store in history
        let processing_duration = start_time.elapsed();

        // Create a processed result with success/error information
        let (is_success, error) = match &transformed_result {
            Ok(_) => (true, None),
            Err(e) => (false, Some(e.to_string())),
        };

        let processed_result = ProcessedResult {
            task_id,
            is_success,
            error,
            metadata: metadata.clone(),
            processed_at: start_time,
            processing_duration,
            transformations: transformations_applied.clone(),
        };

        let mut history = self.history.write().unwrap();
        history.insert(task_id, processed_result);

        // Trim history if needed
        if history.len() > self.config.max_history_size {
            // Find the oldest result
            let oldest_task_id = history
                .iter()
                .min_by_key(|(_, result)| result.processed_at)
                .map(|(task_id, _)| *task_id);

            if let Some(task_id) = oldest_task_id {
                history.remove(&task_id);
                debug!("Removed oldest result for task {} from history", task_id);
            }
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            stats.results_processed += 1;

            match &transformed_result {
                Ok(_) => {
                    stats.successful_results += 1;
                }
                Err(_) => {
                    stats.failed_results += 1;
                }
            }

            stats.transformations_applied += transformations_applied.len();
        }

        debug!(
            "Processed result for task {} in {:?}",
            task_id,
            processing_duration
        );

        transformed_result
    }

    /// Checks if a result is in the cache
    pub fn is_result_cached(
        &self,
        task_id: TaskId,
    ) -> bool {
        if !self.config.enable_caching {
            return false;
        }

        let cache_key = format!("result_{}", task_id);
        let cache = self.cache.read().unwrap();

        if let Some((_, cached_at)) = cache.get(&cache_key) {
            // Check if the result has expired
            let now = Instant::now();
            let ttl = Duration::from_secs(self.config.cache_ttl_seconds);

            if now.duration_since(*cached_at) > ttl {
                debug!("Cached result for task {} has expired", task_id);
                return false;
            }

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_hits += 1;

            debug!("Cache hit for task {}", task_id);
            return true;
        }

        debug!("Cache miss for task {}", task_id);
        false
    }

    /// Gets a result from the history
    pub fn get_result_from_history(&self, task_id: TaskId) -> Option<ProcessedResult> {
        let history = self.history.read().unwrap();
        history.get(&task_id).cloned()
    }

    /// Adds a result transformation
    pub fn add_transformation(&self, transformation: Box<dyn ResultTransformation>) {
        let transformation_name = transformation.name().to_string();
        let mut transformations = self.transformations.write().unwrap();
        transformations.push(transformation);
        debug!("Added transformation: {}", transformation_name);
    }

    /// Clears the cache
    pub fn clear_cache(&self) -> usize {
        let mut cache = self.cache.write().unwrap();
        let count = cache.len();
        cache.clear();
        debug!("Cleared {} items from cache", count);
        count
    }

    /// Clears the history
    pub fn clear_history(&self) -> usize {
        let mut history = self.history.write().unwrap();
        let count = history.len();
        history.clear();
        debug!("Cleared {} items from history", count);
        count
    }

    /// Gets the current result processor statistics
    pub fn get_stats(&self) -> ResultProcessorStats {
        self.stats.read().unwrap().clone()
    }
}