// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/generics.rs
// =================================================================================================
// Purpose: Provides generic utility functions and types for the executor module. This includes
// common task execution logic, error handling, and other utilities used by multiple components
// of the executor module.
//
// Integration:
// - Used by various components of the executor module
// - Provides common functionality for task execution and management
// =================================================================================================

use std::any::Any;
use std::fmt;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, oneshot};
use tracing::{debug, error, info, warn};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskId, TaskPriority};

/// Result of a task execution
#[derive(Debug)]
pub struct TaskExecutionResult {
    /// ID of the task
    pub task_id: TaskId,

    /// Result of the task execution
    pub result: PrismaResult<Box<dyn Any + Send>>,

    /// Duration of the task execution
    pub duration: Duration,

    /// Strategy used to execute the task
    pub strategy: ExecutionStrategyType,

    /// Priority of the task
    pub priority: TaskPriority,
}

/// Metadata for a task execution
#[derive(Debug, Clone)]
pub struct TaskExecutionMetadata {
    /// ID of the task
    pub task_id: TaskId,

    /// When the task was enqueued
    pub enqueued_at: Instant,

    /// When the task started executing
    pub started_at: Option<Instant>,

    /// When the task finished executing
    pub finished_at: Option<Instant>,

    /// Duration of the task execution
    pub duration: Option<Duration>,

    /// Strategy used to execute the task
    pub strategy: ExecutionStrategyType,

    /// Priority of the task
    pub priority: TaskPriority,

    /// Whether the task execution was successful
    pub success: Option<bool>,

    /// Error message if the task execution failed
    pub error_message: Option<String>,
}

impl TaskExecutionMetadata {
    /// Creates a new TaskExecutionMetadata for a task
    pub fn new(task_id: TaskId, strategy: ExecutionStrategyType, priority: TaskPriority) -> Self {
        Self {
            task_id,
            enqueued_at: Instant::now(),
            started_at: None,
            finished_at: None,
            duration: None,
            strategy,
            priority,
            success: None,
            error_message: None,
        }
    }

    /// Marks the task as started
    pub fn mark_started(&mut self) {
        self.started_at = Some(Instant::now());
    }

    /// Marks the task as finished
    pub fn mark_finished(&mut self, result: &PrismaResult<Box<dyn Any + Send>>) {
        let now = Instant::now();
        self.finished_at = Some(now);

        if let Some(started_at) = self.started_at {
            self.duration = Some(now.duration_since(started_at));
        }

        match result {
            Ok(_) => {
                self.success = Some(true);
            }
            Err(e) => {
                self.success = Some(false);
                self.error_message = Some(format!("{:?}", e));
            }
        }
    }
}

/// A task with execution metadata
pub struct TrackedTask {
    /// The task itself
    pub task: Box<dyn Task>,

    /// Metadata for the task execution
    pub metadata: TaskExecutionMetadata,
}

impl fmt::Debug for TrackedTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("TrackedTask")
            .field("task_id", &self.task.id())
            .field("metadata", &self.metadata)
            .finish()
    }
}

/// Executes a task and returns the result along with execution metadata
pub async fn execute_task_with_tracking(
    task: &mut (dyn Task + Send + Sync),
    strategy: ExecutionStrategyType,
) -> (PrismaResult<Box<dyn Any + Send>>, TaskExecutionMetadata) {
    let task_id = task.id();
    let priority = task.priority();

    debug!(
        "Executing task {} with strategy {:?} and priority {:?}",
        task_id, strategy, priority
    );

    // Create metadata
    let mut metadata = TaskExecutionMetadata::new(task_id, strategy, priority);

    // Mark as started
    metadata.mark_started();

    // Execute the task
    let start_time = Instant::now();
    let result = match strategy {
        ExecutionStrategyType::Direct => {
            crate::prisma::prisma_engine::execution_strategies::execute_direct_task(task).await
        }
        ExecutionStrategyType::Rayon => {
            crate::prisma::prisma_engine::execution_strategies::execute_rayon_task(task).await
        }
        ExecutionStrategyType::Tokio => {
            crate::prisma::prisma_engine::execution_strategies::execute_tokio_task(task).await
        }
    };
    let duration = start_time.elapsed();

    // Mark as finished
    metadata.mark_finished(&result);

    // Log the result
    match &result {
        Ok(_) => {
            debug!(
                "Task {} executed successfully with strategy {:?} in {:?}",
                task_id, strategy, duration
            );
        }
        Err(e) => {
            error!(
                "Task {} failed with strategy {:?} after {:?}: {:?}",
                task_id, strategy, duration, e
            );
        }
    }

    (result, metadata)
}

/// Creates a oneshot channel for a task result
pub fn create_task_result_channel() -> (
    oneshot::Sender<PrismaResult<Box<dyn Any + Send>>>,
    oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
) {
    oneshot::channel()
}

/// Sends a task result through a oneshot channel
pub fn send_task_result(
    sender: oneshot::Sender<PrismaResult<Box<dyn Any + Send>>>,
    result: PrismaResult<Box<dyn Any + Send>>,
    task_id: TaskId,
) {
    if sender.send(result).is_err() {
        warn!("Failed to send result for task {}: receiver dropped", task_id);
    }
}

/// Wraps a task in a mutex for shared access
pub fn wrap_task_in_mutex(task: Box<dyn Task>) -> Arc<Mutex<Box<dyn Task>>> {
    Arc::new(Mutex::new(task))
}

/// Unwraps a result from a oneshot receiver
pub async fn unwrap_task_result<T: 'static>(
    receiver: oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    task_id: TaskId,
) -> PrismaResult<T> {
    match receiver.await {
        Ok(result) => match result {
            Ok(value) => match value.downcast::<T>() {
                Ok(typed_value) => Ok(*typed_value),
                Err(_) => Err(GenericError::from(format!(
                    "Task {} returned a value of the wrong type",
                    task_id
                ))),
            },
            Err(e) => Err(e),
        },
        Err(_) => Err(GenericError::from(format!(
            "Failed to receive result for task {}",
            task_id
        ))),
    }
}

/// Formats a duration for logging
pub fn format_duration(duration: Duration) -> String {
    if duration.as_secs() > 0 {
        format!("{}.{:03}s", duration.as_secs(), duration.subsec_millis())
    } else if duration.as_millis() > 0 {
        format!("{}.{:03}ms", duration.as_millis(), duration.as_micros() % 1000)
    } else {
        format!("{}µs", duration.as_micros())
    }
}
