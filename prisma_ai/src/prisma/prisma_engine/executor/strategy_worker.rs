// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/strategy_worker.rs
// =================================================================================================
// Purpose: Provides worker implementations for different execution strategies. This includes
// workers for direct, Rayon, and Tokio execution strategies.
//
// Integration:
// - `executor.rs`: Uses StrategyWorker to execute tasks with different strategies
// - `queue/`: Interfaces with various queue implementations
// - `dispatcher.rs`: Coordinates with Dispatcher for task routing
// =================================================================================================

use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, oneshot, Mutex};
use tokio::task::JoinHandle;
use tokio::time::interval;
use tracing::{debug, error, info};
use async_trait::async_trait;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskId};

use super::generics::execute_task_with_tracking;
use super::queue::{DirectQueueTrait, RayonQueueTrait, TokioQueueTrait};
use super::queue::{
    direct::DirectQueue,
    rayon::RayonQueue,
    tokio::TokioQueue,
};

/// Configuration for the StrategyWorker
#[derive(Debug, Clone)]
pub struct StrategyWorkerConfig {
    /// Number of worker threads for each strategy
    pub worker_threads: usize,

    /// Maximum number of tasks to process concurrently
    pub max_concurrent_tasks: usize,

    /// Interval for worker heartbeat in milliseconds
    pub heartbeat_interval_ms: u64,

    /// Whether to enable worker monitoring
    pub enable_monitoring: bool,
}

impl Default for StrategyWorkerConfig {
    fn default() -> Self {
        Self {
            worker_threads: 4,
            max_concurrent_tasks: 100,
            heartbeat_interval_ms: 1000, // 1 second
            enable_monitoring: true,
        }
    }
}

/// Statistics for the StrategyWorker
#[derive(Debug, Clone)]
pub struct StrategyWorkerStats {
    /// Number of tasks processed by strategy
    pub tasks_processed: std::collections::HashMap<ExecutionStrategyType, usize>,

    /// Number of tasks currently being processed by strategy
    pub tasks_in_progress: std::collections::HashMap<ExecutionStrategyType, usize>,

    /// Number of worker threads by strategy
    pub worker_threads: std::collections::HashMap<ExecutionStrategyType, usize>,

    /// Number of errors by strategy
    pub errors: std::collections::HashMap<ExecutionStrategyType, usize>,

    /// Average processing time by strategy in milliseconds
    pub avg_processing_time_ms: std::collections::HashMap<ExecutionStrategyType, f64>,
}

/// A worker for a specific execution strategy
#[async_trait]
pub trait StrategyWorker: Send + Sync {
    /// Gets the execution strategy type
    fn strategy_type(&self) -> ExecutionStrategyType;

    /// Starts the worker
    fn start(&self) -> PrismaResult<()>;

    /// Stops the worker
    fn stop(&self) -> PrismaResult<()>;

    /// Gets the worker statistics
    fn get_stats(&self) -> StrategyWorkerStats;

    /// Executes a task with the worker's strategy
    async fn execute_task(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)>;
}

/// A worker for the direct execution strategy
pub struct DirectStrategyWorker {
    /// Configuration for the worker
    config: StrategyWorkerConfig,

    /// Statistics for the worker
    stats: RwLock<StrategyWorkerStats>,

    /// The direct queue
    queue: Arc<DirectQueue>,

    /// Whether the worker is running
    running: RwLock<bool>,

    /// Worker threads
    worker_threads: Mutex<Vec<JoinHandle<()>>>,

    /// Channel for worker control
    control_tx: Mutex<Option<mpsc::Sender<()>>>,
}

impl DirectStrategyWorker {
    /// Creates a new DirectStrategyWorker with the given configuration
    pub fn new(config: StrategyWorkerConfig, queue: Arc<DirectQueue>) -> Self {
        info!("Creating new DirectStrategyWorker with config: {:?}", config);

        let mut tasks_processed = std::collections::HashMap::new();
        tasks_processed.insert(ExecutionStrategyType::Direct, 0);

        let mut tasks_in_progress = std::collections::HashMap::new();
        tasks_in_progress.insert(ExecutionStrategyType::Direct, 0);

        let mut worker_threads = std::collections::HashMap::new();
        worker_threads.insert(ExecutionStrategyType::Direct, 0);

        let mut errors = std::collections::HashMap::new();
        errors.insert(ExecutionStrategyType::Direct, 0);

        let mut avg_processing_time_ms = std::collections::HashMap::new();
        avg_processing_time_ms.insert(ExecutionStrategyType::Direct, 0.0);

        Self {
            config,
            stats: RwLock::new(StrategyWorkerStats {
                tasks_processed,
                tasks_in_progress,
                worker_threads,
                errors,
                avg_processing_time_ms,
            }),
            queue,
            running: RwLock::new(false),
            worker_threads: Mutex::new(Vec::new()),
            control_tx: Mutex::new(None),
        }
    }
}

#[async_trait]
impl StrategyWorker for DirectStrategyWorker {
    fn strategy_type(&self) -> ExecutionStrategyType {
        ExecutionStrategyType::Direct
    }

    fn start(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if *running {
            return Ok(());
        }

        info!("Starting DirectStrategyWorker");

        // Create a channel for worker control
        let (control_tx, mut control_rx) = mpsc::channel(1);
        let mut control_tx_guard = self.control_tx.try_lock().unwrap();
        *control_tx_guard = Some(control_tx);

        // Create worker threads
        let mut worker_threads_guard = self.worker_threads.try_lock().unwrap();
        let queue = self.queue.clone();
        let stats = self.stats.read().unwrap().clone();
        let config = self.config.clone();

        for i in 0..self.config.worker_threads {
            let queue = queue.clone();
            let mut stats = stats.clone();
            // We need to create a new mpsc channel for each worker
            let (worker_tx, mut worker_rx) = mpsc::channel::<()>(1);

            let worker = tokio::spawn(async move {
                info!("DirectStrategyWorker thread {} started", i);

                let mut interval = interval(Duration::from_millis(config.heartbeat_interval_ms));

                loop {
                    tokio::select! {
                        _ = worker_rx.recv() => {
                            info!("DirectStrategyWorker thread {} received stop signal", i);
                            break;
                        }
                        _ = interval.tick() => {
                            // Process tasks from the queue
                            if let Some(mut task) = DirectQueueTrait::dequeue(&*queue).await {
                                let task_id = task.id();
                                debug!("DirectStrategyWorker thread {} processing task {}", i, task_id);

                                // Update stats
                                {
                                    let in_progress = stats.tasks_in_progress.get_mut(&ExecutionStrategyType::Direct).unwrap();
                                    *in_progress += 1;
                                }

                                // Execute the task
                                let start_time = Instant::now();
                                let (result, metadata) = execute_task_with_tracking(&mut *task, ExecutionStrategyType::Direct).await;
                                let duration = start_time.elapsed();

                                // Update stats
                                {
                                    let processed = stats.tasks_processed.get_mut(&ExecutionStrategyType::Direct).unwrap();
                                    *processed += 1;

                                    let in_progress = stats.tasks_in_progress.get_mut(&ExecutionStrategyType::Direct).unwrap();
                                    *in_progress -= 1;

                                    if result.is_err() {
                                        let errors = stats.errors.get_mut(&ExecutionStrategyType::Direct).unwrap();
                                        *errors += 1;
                                    }

                                    // Update average processing time
                                    let avg_time = stats.avg_processing_time_ms.get_mut(&ExecutionStrategyType::Direct).unwrap();
                                    let duration_ms = duration.as_millis() as f64;
                                    *avg_time = (*avg_time * (*processed as f64 - 1.0) + duration_ms) / *processed as f64;
                                }

                                // Send the result
                                if let Err(e) = DirectQueueTrait::send_result(&*queue, task_id, result).await {
                                    error!("Failed to send result for task {}: {:?}", task_id, e);
                                }
                            }
                        }
                    }
                }

                info!("DirectStrategyWorker thread {} stopped", i);
            });

            worker_threads_guard.push(worker);
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            let worker_threads = stats.worker_threads.get_mut(&ExecutionStrategyType::Direct).unwrap();
            *worker_threads = self.config.worker_threads;
        }

        *running = true;
        info!("DirectStrategyWorker started with {} threads", self.config.worker_threads);

        Ok(())
    }

    fn stop(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if !*running {
            return Ok(());
        }

        info!("Stopping DirectStrategyWorker");

        // Send stop signal to all worker threads
        let mut control_tx_guard = self.control_tx.try_lock().unwrap();
        if let Some(control_tx) = control_tx_guard.take() {
            drop(control_tx); // Close the channel
        }

        // Wait for all worker threads to finish
        let mut worker_threads_guard = self.worker_threads.try_lock().unwrap();
        for worker in worker_threads_guard.drain(..) {
            worker.abort();
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            let worker_threads = stats.worker_threads.get_mut(&ExecutionStrategyType::Direct).unwrap();
            *worker_threads = 0;
        }

        *running = false;
        info!("DirectStrategyWorker stopped");

        Ok(())
    }

    fn get_stats(&self) -> StrategyWorkerStats {
        self.stats.read().unwrap().clone()
    }

    async fn execute_task(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        debug!("DirectStrategyWorker executing task {}", task_id);

        // Enqueue the task
        match DirectQueueTrait::enqueue(&*self.queue, task).await {
            Ok((task_id, receiver, _duration)) => {
                debug!("DirectStrategyWorker enqueued task {}", task_id);
                Ok((task_id, receiver))
            }
            Err(e) => {
                error!("DirectStrategyWorker failed to enqueue task {}: {:?}", task_id, e);
                Err(e)
            }
        }
    }
}

/// A worker for the Rayon execution strategy
pub struct RayonStrategyWorker {
    /// Configuration for the worker
    config: StrategyWorkerConfig,

    /// Statistics for the worker
    stats: RwLock<StrategyWorkerStats>,

    /// The Rayon queue
    queue: Arc<Mutex<RayonQueue>>,

    /// Whether the worker is running
    running: RwLock<bool>,

    /// Worker threads
    worker_threads: Mutex<Vec<JoinHandle<()>>>,

    /// Channel for worker control
    control_tx: Mutex<Option<mpsc::Sender<()>>>,
}

impl RayonStrategyWorker {
    /// Creates a new RayonStrategyWorker with the given configuration
    pub fn new(config: StrategyWorkerConfig, queue: Arc<Mutex<RayonQueue>>) -> Self {
        info!("Creating new RayonStrategyWorker with config: {:?}", config);

        let mut tasks_processed = std::collections::HashMap::new();
        tasks_processed.insert(ExecutionStrategyType::Rayon, 0);

        let mut tasks_in_progress = std::collections::HashMap::new();
        tasks_in_progress.insert(ExecutionStrategyType::Rayon, 0);

        let mut worker_threads = std::collections::HashMap::new();
        worker_threads.insert(ExecutionStrategyType::Rayon, 0);

        let mut errors = std::collections::HashMap::new();
        errors.insert(ExecutionStrategyType::Rayon, 0);

        let mut avg_processing_time_ms = std::collections::HashMap::new();
        avg_processing_time_ms.insert(ExecutionStrategyType::Rayon, 0.0);

        Self {
            config,
            stats: RwLock::new(StrategyWorkerStats {
                tasks_processed,
                tasks_in_progress,
                worker_threads,
                errors,
                avg_processing_time_ms,
            }),
            queue,
            running: RwLock::new(false),
            worker_threads: Mutex::new(Vec::new()),
            control_tx: Mutex::new(None),
        }
    }
}

#[async_trait]
impl StrategyWorker for RayonStrategyWorker {
    fn strategy_type(&self) -> ExecutionStrategyType {
        ExecutionStrategyType::Rayon
    }

    fn start(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if *running {
            return Ok(());
        }

        info!("Starting RayonStrategyWorker");

        // Create a channel for worker control
        let (control_tx, mut control_rx) = mpsc::channel(1);
        let mut control_tx_guard = self.control_tx.try_lock().unwrap();
        *control_tx_guard = Some(control_tx);

        // Create worker threads
        let mut worker_threads_guard = self.worker_threads.try_lock().unwrap();
        let queue = self.queue.clone();
        let stats = self.stats.read().unwrap().clone();
        let config = self.config.clone();

        for i in 0..self.config.worker_threads {
            let queue = queue.clone();
            let mut stats = stats.clone();

            // Use a oneshot channel instead of mpsc for control
            let (worker_tx, mut worker_rx) = oneshot::channel::<()>();
            let mut worker_control_channels = Vec::new();
            worker_control_channels.push(worker_tx);

            let worker = tokio::spawn(async move {
                info!("RayonStrategyWorker thread {} started", i);

                let mut interval = interval(Duration::from_millis(config.heartbeat_interval_ms));

                loop {
                    tokio::select! {
                        _ = &mut worker_rx => {
                            info!("RayonStrategyWorker thread {} received stop signal", i);
                            break;
                        }
                        _ = interval.tick() => {
                            // Process tasks from the queue
                            let queue_guard = queue.lock().await;
                            if let Some(mut task) = RayonQueueTrait::dequeue(&*queue_guard).await {
                                let task_id = task.id();
                                debug!("RayonStrategyWorker thread {} processing task {}", i, task_id);

                                // Update stats
                                {
                                    // stats is already a clone, no need to write to it
                                    let in_progress = stats.tasks_in_progress.get_mut(&ExecutionStrategyType::Rayon).unwrap();
                                    *in_progress += 1;
                                }

                                // Execute the task
                                let start_time = Instant::now();
                                let (result, metadata) = execute_task_with_tracking(&mut *task, ExecutionStrategyType::Rayon).await;
                                let duration = start_time.elapsed();

                                // Update stats
                                {
                                    // stats is already a clone, no need to write to it
                                    let processed = stats.tasks_processed.get_mut(&ExecutionStrategyType::Rayon).unwrap();
                                    *processed += 1;

                                    let in_progress = stats.tasks_in_progress.get_mut(&ExecutionStrategyType::Rayon).unwrap();
                                    *in_progress -= 1;

                                    if result.is_err() {
                                        let errors = stats.errors.get_mut(&ExecutionStrategyType::Rayon).unwrap();
                                        *errors += 1;
                                    }

                                    // Update average processing time
                                    let avg_time = stats.avg_processing_time_ms.get_mut(&ExecutionStrategyType::Rayon).unwrap();
                                    let duration_ms = duration.as_millis() as f64;
                                    *avg_time = (*avg_time * (*processed as f64 - 1.0) + duration_ms) / *processed as f64;
                                }

                                // Send the result
                                if let Err(e) = RayonQueueTrait::send_result(&*queue_guard, task_id, result).await {
                                    error!("Failed to send result for task {}: {:?}", task_id, e);
                                }
                            }
                        }
                    }
                }

                info!("RayonStrategyWorker thread {} stopped", i);
            });

            worker_threads_guard.push(worker);
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            let worker_threads = stats.worker_threads.get_mut(&ExecutionStrategyType::Rayon).unwrap();
            *worker_threads = self.config.worker_threads;
        }

        *running = true;
        info!("RayonStrategyWorker started with {} threads", self.config.worker_threads);

        Ok(())
    }

    fn stop(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if !*running {
            return Ok(());
        }

        info!("Stopping RayonStrategyWorker");

        // Send stop signal to all worker threads
        let mut control_tx_guard = self.control_tx.try_lock().unwrap();
        if let Some(control_tx) = control_tx_guard.take() {
            drop(control_tx); // Close the channel
        }

        // Wait for all worker threads to finish
        let mut worker_threads_guard = self.worker_threads.try_lock().unwrap();
        for worker in worker_threads_guard.drain(..) {
            worker.abort();
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            let worker_threads = stats.worker_threads.get_mut(&ExecutionStrategyType::Rayon).unwrap();
            *worker_threads = 0;
        }

        *running = false;
        info!("RayonStrategyWorker stopped");

        Ok(())
    }

    fn get_stats(&self) -> StrategyWorkerStats {
        self.stats.read().unwrap().clone()
    }

    async fn execute_task(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        debug!("RayonStrategyWorker executing task {}", task_id);

        // Enqueue the task
        match tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                RayonQueueTrait::enqueue(&*self.queue.lock().await, task).await
            })
        }) {
            Ok(result) => {
                debug!("RayonStrategyWorker enqueued task {}", task_id);
                Ok(result)
            }
            Err(e) => {
                error!("RayonStrategyWorker failed to enqueue task {}: {:?}", task_id, e);
                Err(e)
            }
        }
    }
}

/// A worker for the Tokio execution strategy
pub struct TokioStrategyWorker {
    /// Configuration for the worker
    config: StrategyWorkerConfig,

    /// Statistics for the worker
    stats: RwLock<StrategyWorkerStats>,

    /// The Tokio queue
    queue: Arc<Mutex<TokioQueue>>,

    /// Whether the worker is running
    running: RwLock<bool>,

    /// Worker threads
    worker_threads: Mutex<Vec<JoinHandle<()>>>,

    /// Channel for worker control
    control_tx: Mutex<Option<mpsc::Sender<()>>>,
}

impl TokioStrategyWorker {
    /// Creates a new TokioStrategyWorker with the given configuration
    pub fn new(config: StrategyWorkerConfig, queue: Arc<Mutex<TokioQueue>>) -> Self {
        info!("Creating new TokioStrategyWorker with config: {:?}", config);

        let mut tasks_processed = std::collections::HashMap::new();
        tasks_processed.insert(ExecutionStrategyType::Tokio, 0);

        let mut tasks_in_progress = std::collections::HashMap::new();
        tasks_in_progress.insert(ExecutionStrategyType::Tokio, 0);

        let mut worker_threads = std::collections::HashMap::new();
        worker_threads.insert(ExecutionStrategyType::Tokio, 0);

        let mut errors = std::collections::HashMap::new();
        errors.insert(ExecutionStrategyType::Tokio, 0);

        let mut avg_processing_time_ms = std::collections::HashMap::new();
        avg_processing_time_ms.insert(ExecutionStrategyType::Tokio, 0.0);

        Self {
            config,
            stats: RwLock::new(StrategyWorkerStats {
                tasks_processed,
                tasks_in_progress,
                worker_threads,
                errors,
                avg_processing_time_ms,
            }),
            queue,
            running: RwLock::new(false),
            worker_threads: Mutex::new(Vec::new()),
            control_tx: Mutex::new(None),
        }
    }
}

#[async_trait]
impl StrategyWorker for TokioStrategyWorker {
    fn strategy_type(&self) -> ExecutionStrategyType {
        ExecutionStrategyType::Tokio
    }

    fn start(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if *running {
            return Ok(());
        }

        info!("Starting TokioStrategyWorker");

        // Create a channel for worker control
        let (control_tx, mut control_rx) = mpsc::channel(1);
        let mut control_tx_guard = self.control_tx.try_lock().unwrap();
        *control_tx_guard = Some(control_tx);

        // Create worker threads
        let mut worker_threads_guard = self.worker_threads.try_lock().unwrap();
        let queue = self.queue.clone();
        let stats = self.stats.read().unwrap().clone();
        let config = self.config.clone();

        for i in 0..self.config.worker_threads {
            let queue = queue.clone();
            let mut stats = stats.clone();

            // Use a oneshot channel instead of mpsc for control
            let (worker_tx, mut worker_rx) = oneshot::channel::<()>();
            let mut worker_control_channels = Vec::new();
            worker_control_channels.push(worker_tx);

            let worker = tokio::spawn(async move {
                info!("TokioStrategyWorker thread {} started", i);

                let mut interval = interval(Duration::from_millis(config.heartbeat_interval_ms));

                loop {
                    tokio::select! {
                        _ = &mut worker_rx => {
                            info!("TokioStrategyWorker thread {} received stop signal", i);
                            break;
                        }
                        _ = interval.tick() => {
                            // Process tasks from the queue
                            let queue_guard = queue.lock().await;
                            if let Some(mut task) = TokioQueueTrait::dequeue(&*queue_guard).await {
                                let task_id = task.id();
                                debug!("TokioStrategyWorker thread {} processing task {}", i, task_id);

                                // Update stats
                                {
                                    // stats is already a clone, no need to write to it
                                    let in_progress = stats.tasks_in_progress.get_mut(&ExecutionStrategyType::Tokio).unwrap();
                                    *in_progress += 1;
                                }

                                // Execute the task
                                let start_time = Instant::now();
                                let (result, metadata) = execute_task_with_tracking(&mut *task, ExecutionStrategyType::Tokio).await;
                                let duration = start_time.elapsed();

                                // Update stats
                                {
                                    // stats is already a clone, no need to write to it
                                    let processed = stats.tasks_processed.get_mut(&ExecutionStrategyType::Tokio).unwrap();
                                    *processed += 1;

                                    let in_progress = stats.tasks_in_progress.get_mut(&ExecutionStrategyType::Tokio).unwrap();
                                    *in_progress -= 1;

                                    if result.is_err() {
                                        let errors = stats.errors.get_mut(&ExecutionStrategyType::Tokio).unwrap();
                                        *errors += 1;
                                    }

                                    // Update average processing time
                                    let avg_time = stats.avg_processing_time_ms.get_mut(&ExecutionStrategyType::Tokio).unwrap();
                                    let duration_ms = duration.as_millis() as f64;
                                    *avg_time = (*avg_time * (*processed as f64 - 1.0) + duration_ms) / *processed as f64;
                                }

                                // Send the result
                                if let Err(e) = TokioQueueTrait::send_result(&*queue_guard, task_id, result).await {
                                    error!("Failed to send result for task {}: {:?}", task_id, e);
                                }
                            }
                        }
                    }
                }

                info!("TokioStrategyWorker thread {} stopped", i);
            });

            worker_threads_guard.push(worker);
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            let worker_threads = stats.worker_threads.get_mut(&ExecutionStrategyType::Tokio).unwrap();
            *worker_threads = self.config.worker_threads;
        }

        *running = true;
        info!("TokioStrategyWorker started with {} threads", self.config.worker_threads);

        Ok(())
    }

    fn stop(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if !*running {
            return Ok(());
        }

        info!("Stopping TokioStrategyWorker");

        // Send stop signal to all worker threads
        let mut control_tx_guard = self.control_tx.try_lock().unwrap();
        if let Some(control_tx) = control_tx_guard.take() {
            drop(control_tx); // Close the channel
        }

        // Wait for all worker threads to finish
        let mut worker_threads_guard = self.worker_threads.try_lock().unwrap();
        for worker in worker_threads_guard.drain(..) {
            worker.abort();
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            let worker_threads = stats.worker_threads.get_mut(&ExecutionStrategyType::Tokio).unwrap();
            *worker_threads = 0;
        }

        *running = false;
        info!("TokioStrategyWorker stopped");

        Ok(())
    }

    fn get_stats(&self) -> StrategyWorkerStats {
        self.stats.read().unwrap().clone()
    }

    async fn execute_task(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(TaskId, oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        debug!("TokioStrategyWorker executing task {}", task_id);

        // Enqueue the task
        match tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                TokioQueueTrait::enqueue(&*self.queue.lock().await, task).await
            })
        }) {
            Ok(result) => {
                debug!("TokioStrategyWorker enqueued task {}", task_id);
                Ok(result)
            }
            Err(e) => {
                error!("TokioStrategyWorker failed to enqueue task {}: {:?}", task_id, e);
                Err(e)
            }
        }
    }
}