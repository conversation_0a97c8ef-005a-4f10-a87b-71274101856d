// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/tokio/mod.rs
// =================================================================================================
// Purpose: Re-exports the TokioQueue and other public items from the tokio queue module.
//
// Integration:
// - `tokio.rs`: Re-exports the TokioQueue struct.
// - `traits.rs`: Re-exports the TokioQueueTrait.
// - `types.rs`: Re-exports the TokioQueueConfig and TokioQueueStats.
// =================================================================================================

// Declare the submodules
mod generics;
mod tokio;
mod traits;
mod types;

// Re-export the public items
pub use tokio::TokioQueue;
pub use traits::{TokioQueueTrait, TokioTaskWrapper};
pub use types::{TokioQueueConfig, TokioQueueStats, QueueStatus};

// Also re-export the generic utility functions if they might be useful elsewhere
pub use generics::{execute_task_with_tokio, update_queue_stats};