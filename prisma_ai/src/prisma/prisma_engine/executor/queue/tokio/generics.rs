// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/tokio/generics.rs
// =================================================================================================
// Purpose: Provides generic utility functions and helper types that support the implementation
// of the Tokio Queue module. This includes common task execution logic and queue management
// utilities used by the TokioQueue implementation.
//
// Integration:
// - `tokio.rs`: Uses the functions defined here.
// - `Task`: The helper functions operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` and `GenericError` for error handling.
// - `tracing`: Used for detailed logging during task execution.
// =================================================================================================

use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tokio::task::JoinHandle;
use tracing::{debug, error, info, instrument, trace};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::execution_strategies::execute_tokio_task;
use crate::prisma::prisma_engine::traits::Task;

use super::types::{TokioQueueStats, TokioTask, QueueStatus};

/// Executes a task using Tokio's async runtime, measures its execution time,
/// and logs the outcome.
///
/// # Arguments
///
/// * `task` - The task to execute
///
/// # Returns
///
/// A tuple containing:
/// - `PrismaResult<Box<dyn Any + Send>>`: The result returned by the task's execute method
/// - `Duration`: The time taken for the task execution
#[instrument(
    name = "tokio_execute_task",
    skip(task),
    fields(task_id = %task.id()),
    level = "debug"
)]
pub async fn execute_task_with_tokio(
    task: &mut (dyn Task + Send + Sync),
) -> (PrismaResult<Box<dyn Any + Send>>, Duration) {
    let task_id = task.id();
    println!("Starting task execution with Tokio for task {}", task_id);
    trace!("Starting task execution with Tokio");

    let start_time = Instant::now();
    println!("Calling execute_tokio_task for task {}", task_id);

    // Use a timeout to prevent tasks from running indefinitely
    let result = match tokio::time::timeout(
        std::time::Duration::from_secs(10), // 10 second timeout
        execute_tokio_task(task)
    ).await {
        Ok(result) => result,
        Err(_) => {
            let err_msg = format!("Task {} execution timed out after 10s", task_id);
            println!("{}", err_msg);
            error!("{}", err_msg);
            Err(crate::err::GenericError::from(err_msg))
        }
    };

    let duration = start_time.elapsed();
    println!("Task {} execution completed in {} ms", task_id, duration.as_millis());

    match &result {
        Ok(_) => {
            println!(
                "Task {} execution succeeded with Tokio in {} ms",
                task_id,
                duration.as_millis()
            );
            debug!(
                duration_ms = duration.as_millis(),
                "Task execution succeeded with Tokio"
            );
        }
        Err(e) => {
            println!(
                "Task {} execution failed with Tokio in {} ms: {}",
                task_id,
                duration.as_millis(),
                e
            );
            error!(
                duration_ms = duration.as_millis(),
                error = %e,
                "Task execution failed with Tokio"
            );
        }
    }

    (result, duration)
}

/// Updates queue statistics based on the execution result.
///
/// # Arguments
///
/// * `stats` - The statistics to update
/// * `result` - The result of the task execution
/// * `duration` - The time taken for the task execution
pub fn update_queue_stats(
    stats: &Arc<RwLock<TokioQueueStats>>,
    result: &PrismaResult<Box<dyn Any + Send>>,
    duration: Duration,
) {
    let mut stats_guard = stats.write().unwrap();
    match result {
        Ok(_) => stats_guard.record_success(duration),
        Err(_) => stats_guard.record_failure(duration),
    }
}

/// Creates a worker loop for processing tasks from a Tokio queue.
///
/// # Arguments
///
/// * `rx` - The receiver for tasks
/// * `stats` - The statistics to update
/// * `status` - The status of the queue
/// * `semaphore` - The semaphore for limiting concurrent tasks
///
/// # Returns
///
/// A JoinHandle for the worker task
pub fn create_worker_loop(
    mut rx: mpsc::Receiver<TokioTask>,
    stats: Arc<RwLock<TokioQueueStats>>,
    status: Arc<RwLock<QueueStatus>>,
    semaphore: Arc<tokio::sync::Semaphore>,
) -> JoinHandle<()> {
    tokio::spawn(async move {
        println!("Tokio worker loop started");
        info!("Tokio worker loop started");

        // Check if the queue is already stopped before entering the loop
        {
            let queue_status = *status.read().unwrap();
            println!("Initial queue status in worker loop: {:?}", queue_status);
            if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                println!("Queue is already stopped, exiting worker loop");
                return;
            }
        }

        // Add a debug ping to check if the loop is still alive
        let ping_interval = tokio::time::Duration::from_secs(5);
        let ping_task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(ping_interval);
            loop {
                interval.tick().await;
                println!("Tokio worker loop ping - still alive");
            }
        });

        println!("Starting to receive tasks from channel");

        // Create a loop that keeps running until explicitly stopped
        loop {
            // Check if the queue is shutting down or stopped
            {
                let queue_status = *status.read().unwrap();
                if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                    println!("Queue is shutting down or stopped, exiting worker loop");
                    break;
                }
            }

            // Try to receive a task with a short timeout
            match tokio::time::timeout(
                std::time::Duration::from_secs(1), // 1 second timeout
                rx.recv()
            ).await {
                Ok(Some(tokio_task)) => {
                    println!("Tokio worker received a task");
                    // Check if the queue is paused or shutting down
                    {
                        let queue_status = *status.read().unwrap();
                        println!("Current queue status: {:?}", queue_status);
                        if queue_status == QueueStatus::Paused {
                            println!("Queue is paused, waiting before processing task");
                            // If paused, wait for a short time and check again
                            tokio::time::sleep(Duration::from_millis(100)).await;
                            continue;
                        }
                        if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                            println!("Queue is shutting down or stopped, exiting worker loop");
                            // If shutting down or stopped, exit the loop
                            break;
                        }
                    }

                    let task_id = tokio_task.task_id;
                    let task_arc = tokio_task.task;
                    let result_sender = tokio_task.result_sender;

                    println!("Tokio worker processing task {}", task_id);
                    debug!("Tokio worker received task {}", task_id);

                    // Clone Arc values before moving them into the spawned task
                    let stats_clone = stats.clone();
                    let semaphore_clone = semaphore.clone();

                    // Spawn a task to process this work to avoid blocking the worker loop
                    tokio::spawn(async move {
                        // Acquire a permit from the semaphore to limit concurrent tasks
                        println!("Acquiring semaphore permit for task {}", task_id);
                        let permit = match semaphore_clone.acquire().await {
                            Ok(permit) => {
                                println!("Successfully acquired semaphore permit for task {}", task_id);
                                permit
                            },
                            Err(e) => {
                                println!("Failed to acquire semaphore permit for task {}: {}", task_id, e);
                                error!("Failed to acquire semaphore permit: {}", e);
                                return;
                            }
                        };

                        // Update active tasks count
                        {
                            let mut stats_guard = stats_clone.write().unwrap();
                            let current_active = stats_guard.active_tasks;
                            println!("Updating active tasks count from {} to {}", current_active, current_active + 1);
                            stats_guard.update_active_tasks(current_active + 1);

                            // Also update queue length
                            let current_length = stats_guard.queue_length;
                            if current_length > 0 {
                                stats_guard.update_queue_length(current_length - 1);
                            }
                        }

                        // Execute the task with a timeout
                        println!("Preparing to execute task {}", task_id);
                        let (result, duration) = {
                            println!("Acquiring lock on task {}", task_id);
                            let mut task_guard = match tokio::time::timeout(
                                std::time::Duration::from_secs(5), // 5 second timeout for acquiring lock
                                task_arc.lock()
                            ).await {
                                Ok(guard) => {
                                    println!("Lock acquired for task {}", task_id);
                                    guard
                                },
                                Err(_) => {
                                    println!("Timeout acquiring lock for task {}", task_id);
                                    let err_msg = format!("Timeout acquiring lock for task {}", task_id);
                                    let result = Err(crate::err::GenericError::from(err_msg));

                                    // Send the error result back to the caller
                                    if let Some(sender) = result_sender {
                                        println!("Sending lock timeout error for task {}", task_id);
                                        let _ = sender.send(result);
                                    }

                                    // Update active tasks count
                                    {
                                        let mut stats_guard = stats_clone.write().unwrap();
                                        let current_active = stats_guard.active_tasks;
                                        if current_active > 0 {
                                            stats_guard.update_active_tasks(current_active - 1);
                                        }
                                    }

                                    // Release the permit
                                    drop(permit);
                                    return;
                                }
                            };

                            let task_ref: &mut dyn Task = &mut **task_guard;
                            println!("Executing task {} with Tokio", task_id);
                            execute_task_with_tokio(task_ref).await
                        };

                        // Update statistics
                        println!("Updating statistics for task {}", task_id);
                        update_queue_stats(&stats_clone, &result, duration);

                        // Update active tasks count
                        {
                            let mut stats_guard = stats_clone.write().unwrap();
                            let current_active = stats_guard.active_tasks;
                            if current_active > 0 {
                                println!("Updating active tasks count from {} to {}", current_active, current_active - 1);
                                stats_guard.update_active_tasks(current_active - 1);
                            }
                        }

                        // Send the result back to the caller
                        if let Some(sender) = result_sender {
                            println!("Sending result for task {}", task_id);
                            if sender.send(result).is_err() {
                                println!("Failed to send result for task {}: receiver dropped", task_id);
                                error!("Failed to send result for task {}: receiver dropped", task_id);
                            } else {
                                println!("Successfully sent result for task {}", task_id);
                            }
                        } else {
                            println!("No result sender for task {}", task_id);
                        }

                        // Release the permit
                        println!("Releasing semaphore permit for task {}", task_id);
                        drop(permit);
                        println!("Task {} processing completed", task_id);
                    });
                },
                Ok(None) => {
                    // Channel closed, but don't exit the loop
                    // Instead, wait for a new channel to be created
                    println!("Channel closed, waiting for new channel");
                    tokio::time::sleep(Duration::from_millis(100)).await;
                    continue;
                },
                Err(_) => {
                    // Timeout occurred, just continue the loop
                    // This allows us to check the queue status periodically
                    continue;
                }
            }
        }

        // Cancel the ping task
        ping_task.abort();

        println!("Tokio worker loop stopped - channel closed or timeout occurred");
        info!("Tokio worker loop stopped");
    })
}