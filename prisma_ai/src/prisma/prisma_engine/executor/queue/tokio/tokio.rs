// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/tokio/tokio.rs
// =================================================================================================
// Purpose: Implements the TokioQueue struct and its methods. The TokioQueue executes tasks
// using Tokio's async runtime, which is optimized for I/O-bound tasks or tasks that benefit from
// async concurrency.
//
// Integration:
// - `traits.rs`: Implements the TokioQueueTrait defined there.
// - `types.rs`: Uses the types defined there.
// - `generics.rs`: Uses the utility functions defined there.
// - `Task`: Operates on Task objects.
// - `err.rs`: Uses PrismaResult for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex, oneshot, Semaphore};
use tokio::task::JoinHandle;
use tracing::{debug, error, info, instrument};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::executor::queue::direct::CloneableTask;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::TaskId;

use super::generics::create_worker_loop;
use super::traits::{TokioQueueTrait, TokioTaskWrapper};
use super::types::{TokioQueueConfig, TokioQueueStats, TokioTask, QueueStatus};

/// A queue that executes tasks using Tokio's async runtime.
///
/// This queue is designed for I/O-bound tasks or tasks that benefit from async concurrency.
pub struct TokioQueue {
    /// Configuration for the TokioQueue
    config: TokioQueueConfig,

    /// Channel sender for enqueueing tasks
    tx: mpsc::Sender<TokioTask>,

    /// Channel receiver for dequeueing tasks
    rx: Option<mpsc::Receiver<TokioTask>>,

    /// Statistics about task execution
    stats: Arc<RwLock<TokioQueueStats>>,

    /// Status of the queue
    status: Arc<RwLock<QueueStatus>>,

    /// Handles for the worker tasks
    worker_handles: Vec<JoinHandle<()>>,

    /// Semaphore for limiting concurrent tasks
    semaphore: Arc<Semaphore>,

    /// Maximum number of concurrent tasks
    max_concurrent_tasks: usize,

    /// Optional dedicated Tokio runtime
    runtime: Option<tokio::runtime::Runtime>,
}

// Implement Debug manually to handle the non-Debug runtime field
impl std::fmt::Debug for TokioQueue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TokioQueue")
            .field("config", &self.config)
            .field("stats", &self.stats)
            .field("status", &self.status)
            .field("worker_handles", &self.worker_handles)
            .field("semaphore", &self.semaphore)
            .field("max_concurrent_tasks", &self.max_concurrent_tasks)
            .field("runtime", &format_args!("Option<tokio::runtime::Runtime>"))
            .finish()
    }
}

impl TokioQueue {
    /// Creates a new TokioQueue with the given configuration.
    pub fn new(config: TokioQueueConfig) -> Self {
        let (tx, rx) = mpsc::channel(config.queue_capacity);

        // Configure Tokio runtime if using a dedicated runtime
        let runtime = if config.use_dedicated_runtime {
            info!("Creating dedicated Tokio runtime with {} concurrent tasks", config.max_concurrent_tasks);

            // Create a dedicated runtime with multi-threaded scheduler
            match tokio::runtime::Builder::new_multi_thread()
                .worker_threads(config.max_concurrent_tasks)
                .thread_name("prisma-tokio-worker")
                .enable_all()
                .build()
            {
                Ok(rt) => {
                    info!("Successfully created dedicated Tokio runtime");
                    Some(rt)
                },
                Err(e) => {
                    error!("Failed to create dedicated Tokio runtime: {}", e);
                    None
                }
            }
        } else {
            None
        };

        TokioQueue {
            max_concurrent_tasks: config.max_concurrent_tasks,
            semaphore: Arc::new(Semaphore::new(config.max_concurrent_tasks)),
            stats: Arc::new(RwLock::new(TokioQueueStats::new())),
            status: Arc::new(RwLock::new(QueueStatus::Stopped)),
            worker_handles: Vec::new(),
            config,
            tx,
            rx: Some(rx),
            runtime,
        }
    }

    /// Creates a new TokioQueue with default configuration.
    pub fn default() -> Self {
        Self::new(TokioQueueConfig::default())
    }

    /// Returns a clone of the current statistics.
    pub fn get_stats(&self) -> TokioQueueStats {
        self.stats.read().unwrap().clone()
    }

    /// Returns the current status of the queue.
    pub fn get_status(&self) -> QueueStatus {
        *self.status.read().unwrap()
    }

    /// Returns the current status of the queue from an Arc reference.
    /// This is useful when you need to get the status without a mutable reference.
    pub fn get_status_from_arc(arc_queue: &Arc<Self>) -> QueueStatus {
        *arc_queue.status.read().unwrap()
    }
}

#[async_trait]
impl TokioQueueTrait for TokioQueue {
    #[instrument(
        name = "tokio_queue_enqueue",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        let status = *self.status.read().unwrap();
        println!("Enqueue called with queue status: {:?}", status);
        if status != QueueStatus::Running {
            let err_msg = format!("Cannot enqueue task: Tokio queue is not running (status: {:?})", status);
            println!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        let task_id = task.id();
        println!("Enqueuing task {} in Tokio queue", task_id);
        debug!("Enqueuing task {} in Tokio queue", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();
        println!("Created oneshot channel for task {}", task_id);

        // Create a TokioTask
        let tokio_task = TokioTask {
            task_id,
            task: Arc::new(Mutex::new(task)),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };
        println!("Created TokioTask for task {}", task_id);

        // Send the task to the channel with a timeout
        println!("Sending task {} to channel", task_id);
        match tokio::time::timeout(
            std::time::Duration::from_secs(5), // 5 second timeout for sending
            self.tx.send(tokio_task)
        ).await {
            Ok(send_result) => {
                if let Err(e) = send_result {
                    let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
                    println!("{}", err_msg);
                    error!("{}", err_msg);
                    return Err(GenericError::from(err_msg));
                }
                println!("Successfully sent task {} to channel", task_id);
            },
            Err(_) => {
                let err_msg = format!("Timeout while trying to enqueue task {}", task_id);
                println!("{}", err_msg);
                error!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            println!("Updating queue length from {} to {}", current_length, current_length + 1);
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in Tokio queue", task_id);
        println!("Task {} enqueued successfully in Tokio queue", task_id);
        Ok((task_id, result_receiver))
    }

    #[instrument(
        name = "tokio_queue_enqueue_arc",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        let status = *self.status.read().unwrap();
        println!("Enqueue_arc called with queue status: {:?}", status);
        if status != QueueStatus::Running {
            let err_msg = format!("Cannot enqueue task: Tokio queue is not running (status: {:?})", status);
            println!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        let task_id = task.id();
        println!("Enqueuing task {} in Tokio queue (Arc)", task_id);
        debug!("Enqueuing task {} in Tokio queue (Arc)", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();
        println!("Created oneshot channel for task {}", task_id);

        // Create a TokioTask with the Arc<T> directly
        let tokio_task = TokioTask {
            task_id,
            task: Arc::new(Mutex::new(Box::new(TokioTaskWrapper(task)))),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };
        println!("Created TokioTask for task {} (Arc)", task_id);

        // Send the task to the channel with a timeout
        println!("Sending task {} to channel (Arc)", task_id);
        match tokio::time::timeout(
            std::time::Duration::from_secs(5), // 5 second timeout for sending
            self.tx.send(tokio_task)
        ).await {
            Ok(send_result) => {
                if let Err(e) = send_result {
                    let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
                    println!("{}", err_msg);
                    error!("{}", err_msg);
                    return Err(GenericError::from(err_msg));
                }
                println!("Successfully sent task {} to channel (Arc)", task_id);
            },
            Err(_) => {
                let err_msg = format!("Timeout while trying to enqueue task {}", task_id);
                println!("{}", err_msg);
                error!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            println!("Updating queue length from {} to {}", current_length, current_length + 1);
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in Tokio queue (Arc)", task_id);
        println!("Task {} enqueued successfully in Tokio queue (Arc)", task_id);
        Ok((task_id, result_receiver))
    }

    fn queue_length(&self) -> usize {
        self.stats.read().unwrap().queue_length
    }

    fn is_full(&self) -> bool {
        self.queue_length() >= self.capacity()
    }

    fn capacity(&self) -> usize {
        self.config.queue_capacity
    }

    async fn start(&mut self) -> PrismaResult<()> {
        // Check if the queue is already running
        {
            let status = *self.status.read().unwrap();
            if status == QueueStatus::Running {
                return Ok(());
            }
            if status == QueueStatus::ShuttingDown {
                return Err(GenericError::from(
                    "Cannot start queue: Queue is shutting down",
                ));
            }
        }

        info!("Starting Tokio queue");
        println!("Starting Tokio queue");

        // Update status to Running before creating the worker loop
        // This ensures the worker loop will start processing tasks immediately
        {
            println!("Updating queue status to Running");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
            println!("Queue status updated to Running");
        }

        // Create a new channel with a larger capacity to ensure it doesn't close immediately
        println!("Creating new channel with capacity {}", self.config.queue_capacity);
        let (tx, rx) = mpsc::channel::<TokioTask>(self.config.queue_capacity);

        // Store the sender in self.tx
        self.tx = tx;

        // Create a new channel for future use (in case we need to restart)
        let (new_tx, new_rx) = mpsc::channel::<TokioTask>(self.config.queue_capacity);

        // Store the new receiver for future use
        self.rx = Some(new_rx);

        // Create worker with the current receiver
        println!("Creating worker loop");
        let worker_handle = create_worker_loop(
            rx,
            self.stats.clone(),
            self.status.clone(),
            self.semaphore.clone(),
        );

        // Store the worker handle
        self.worker_handles.push(worker_handle);

        info!("Tokio queue started");
        println!("Tokio queue started");
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        // Check if the queue is already stopped
        {
            let status = *self.status.read().unwrap();
            if status == QueueStatus::Stopped {
                println!("Queue is already stopped, returning early");
                return Ok(());
            }
        }

        info!("Stopping Tokio queue");
        println!("Stopping Tokio queue");

        // Update status to shutting down
        {
            println!("Updating queue status to ShuttingDown");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::ShuttingDown;
            println!("Queue status updated to ShuttingDown");
        }

        // Wait a moment for the worker loop to notice the status change
        println!("Waiting for worker loops to notice status change");
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Abort all worker tasks
        println!("Aborting {} worker tasks", self.worker_handles.len());
        for handle in self.worker_handles.drain(..) {
            println!("Aborting worker handle");
            handle.abort();
        }

        // Wait for worker tasks to be aborted
        println!("Waiting for worker tasks to be aborted");
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Create a new channel with a larger capacity
        println!("Creating new channel with capacity {}", self.config.queue_capacity);
        let (tx, rx) = mpsc::channel(self.config.queue_capacity);
        self.tx = tx;
        self.rx = Some(rx);
        println!("New channel created");

        // Update status to stopped
        {
            println!("Updating queue status to Stopped");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Stopped;
            println!("Queue status updated to Stopped");
        }

        // Reset queue statistics
        {
            let mut stats = self.stats.write().unwrap();
            println!("Resetting queue length to 0");
            stats.update_queue_length(0);
            println!("Resetting active tasks to 0");
            stats.update_active_tasks(0);

            // Also reset other stats to ensure clean state
            stats.tasks_processed = 0;
            stats.successful_tasks = 0;
            stats.failed_tasks = 0;
            stats.total_execution_time = Duration::from_secs(0);
            stats.average_execution_time = None;
            stats.max_execution_time = None;
            stats.min_execution_time = None;
        }

        info!("Tokio queue stopped");
        println!("Tokio queue stopped");
        Ok(())
    }

    async fn stop_immutable(&self) -> PrismaResult<()> {
        // Check if the queue is already stopped
        {
            let status = *self.status.read().unwrap();
            if status == QueueStatus::Stopped {
                println!("Queue is already stopped (immutable), returning early");
                return Ok(());
            }
        }

        info!("Stopping Tokio queue (immutable)");
        println!("Stopping Tokio queue (immutable)");

        // Update status to shutting down
        {
            println!("Updating queue status to ShuttingDown");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::ShuttingDown;
            println!("Queue status updated to ShuttingDown");
        }

        // Wait a moment for the worker loop to notice the status change
        println!("Waiting for worker loops to notice status change");
        tokio::time::sleep(Duration::from_millis(200)).await;

        // We can't abort worker tasks or create a new channel without a mutable reference,
        // but we can update the status to signal the workers to stop

        // Update status to stopped
        {
            println!("Updating queue status to Stopped");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Stopped;
            println!("Queue status updated to Stopped");
        }

        // Reset queue statistics
        {
            let mut stats = self.stats.write().unwrap();
            println!("Resetting queue length to 0");
            stats.update_queue_length(0);
            println!("Resetting active tasks to 0");
            stats.update_active_tasks(0);

            // Also reset other stats to ensure clean state
            stats.tasks_processed = 0;
            stats.successful_tasks = 0;
            stats.failed_tasks = 0;
            stats.total_execution_time = Duration::from_secs(0);
            stats.average_execution_time = None;
            stats.max_execution_time = None;
            stats.min_execution_time = None;
        }

        info!("Tokio queue stopped (immutable)");
        println!("Tokio queue stopped (immutable)");
        Ok(())
    }

    async fn pause(&mut self) -> PrismaResult<()> {
        // Check if the queue is running
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Running {
                return Err(GenericError::from(
                    "Cannot pause queue: Queue is not running",
                ));
            }
        }

        info!("Pausing Tokio queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Paused;
        }

        info!("Tokio queue paused");
        Ok(())
    }

    async fn resume(&mut self) -> PrismaResult<()> {
        // Check if the queue is paused
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Paused {
                return Err(GenericError::from(
                    "Cannot resume queue: Queue is not paused",
                ));
            }
        }

        info!("Resuming Tokio queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
        }

        info!("Tokio queue resumed");
        Ok(())
    }

    fn set_max_concurrent_tasks(&mut self, max_concurrent_tasks: usize) -> PrismaResult<()> {
        // Check if the number of concurrent tasks is valid
        if max_concurrent_tasks == 0 {
            return Err(GenericError::from(
                "Cannot set max concurrent tasks: Number must be greater than 0",
            ));
        }

        info!("Setting Tokio queue max concurrent tasks to {}", max_concurrent_tasks);

        // Update the max concurrent tasks
        self.max_concurrent_tasks = max_concurrent_tasks;

        // Create a new semaphore with the updated max concurrent tasks
        self.semaphore = Arc::new(Semaphore::new(max_concurrent_tasks));

        // If using a dedicated runtime, reconfigure it
        if self.config.use_dedicated_runtime {
            info!("Reconfiguring dedicated Tokio runtime with {} concurrent tasks", max_concurrent_tasks);

            // We need to stop the queue first to safely reconfigure the runtime
            let current_status = *self.status.read().unwrap();

            // Drop the existing runtime if any
            self.runtime = None;

            // Create a new runtime with updated configuration
            let new_runtime = tokio::runtime::Builder::new_multi_thread()
                .worker_threads(max_concurrent_tasks)
                .thread_name("prisma-tokio-worker")
                .enable_all()
                .build();

            match new_runtime {
                Ok(rt) => {
                    info!("Successfully reconfigured dedicated Tokio runtime");
                    self.runtime = Some(rt);
                },
                Err(e) => {
                    error!("Failed to reconfigure dedicated Tokio runtime: {}", e);
                }
            }

            // If the queue was running, we need to restart the worker tasks
            if current_status == QueueStatus::Running {
                // Abort all existing worker tasks
                for handle in self.worker_handles.drain(..) {
                    handle.abort();
                }

                // Create a new channel
                let (new_tx, new_rx) = mpsc::channel(self.config.queue_capacity);
                self.tx = new_tx;

                // Create a new worker
                if let Some(rx) = self.rx.take() {
                    let worker_handle = create_worker_loop(
                        rx,
                        self.stats.clone(),
                        self.status.clone(),
                        self.semaphore.clone(),
                    );

                    // Store the worker handle
                    self.worker_handles.push(worker_handle);
                }

                // Store the new receiver
                self.rx = Some(new_rx);
            }
        }

        Ok(())
    }

    fn max_concurrent_tasks(&self) -> usize {
        self.max_concurrent_tasks
    }

    /// Dequeues a task from the queue.
    ///
    /// # Returns
    ///
    /// Some(task) if a task was dequeued, or None if the queue is empty.
    async fn dequeue(&self) -> Option<Box<dyn Task>> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return None;
        }

        // This method is primarily used for testing and manual task management
        // In normal operation, tasks are processed by the worker loop

        // We can't directly access the receiver here since it's owned by the worker loop
        // In a real implementation, we would need to use a different approach to dequeue tasks
        // For now, we'll just return None to indicate that no task was dequeued
        debug!("Dequeue operation not fully implemented for TokioQueue");

        // This is a simplified implementation that doesn't actually dequeue anything
        None
    }

    /// Sends a result for a task.
    ///
    /// # Arguments
    ///
    /// * `task_id` - The ID of the task
    /// * `result` - The result of the task
    ///
    /// # Returns
    ///
    /// Ok(()) if the result was sent successfully, or an error if it failed.
    async fn send_result(
        &self,
        task_id: TaskId,
        result: PrismaResult<Box<dyn Any + Send>>,
    ) -> PrismaResult<()> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return Err(GenericError::from(format!(
                "Failed to send result for task {}: Queue not running",
                task_id
            )));
        }

        debug!("Sending result for task {}", task_id);

        // In a real implementation, we would need to find the task in a task registry
        // and send the result to its result_sender. Since we don't have a task registry
        // in this implementation (tasks are processed immediately by the worker loop),
        // we'll just log the result.

        // Log the result
        match &result {
            Ok(_) => debug!("Task {} completed successfully", task_id),
            Err(e) => error!("Task {} failed: {}", task_id, e),
        }

        // Update stats
        {
            let mut stats = self.stats.write().unwrap();
            match &result {
                Ok(_) => stats.record_success(Duration::from_secs(0)), // We don't know the actual duration
                Err(_) => stats.record_failure(Duration::from_secs(0)), // We don't know the actual duration
            }
        }

        Ok(())
    }
}