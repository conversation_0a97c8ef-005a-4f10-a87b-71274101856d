// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/tokio/types.rs
// =================================================================================================
// Purpose: Defines the types needed for the tokio queue implementation. The tokio queue executes tasks
// using Tokio's async runtime, which is optimized for I/O-bound tasks or tasks that benefit from
// async concurrency.
//
// Integration:
// - `tokio.rs`: Uses the types defined here.
// - `Task`: The types operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` for error handling.
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::any::Any;
use std::fmt;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, oneshot};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::TaskId;

/// Configuration for the TokioQueue.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokioQueueConfig {
    /// Maximum capacity of the queue
    pub queue_capacity: usize,

    /// Maximum number of concurrent tasks
    pub max_concurrent_tasks: usize,

    /// Whether to use a dedicated runtime for this queue
    pub use_dedicated_runtime: bool,
}

impl Default for TokioQueueConfig {
    fn default() -> Self {
        TokioQueueConfig {
            queue_capacity: 10000,
            max_concurrent_tasks: num_cpus::get() * 2, // Default to 2x CPU cores
            use_dedicated_runtime: false,
        }
    }
}

/// Statistics for the TokioQueue.
#[derive(Debug, Clone, Default)]
pub struct TokioQueueStats {
    /// Total number of tasks processed
    pub tasks_processed: usize,

    /// Number of tasks currently in the queue
    pub queue_length: usize,

    /// Number of tasks currently being processed
    pub active_tasks: usize,

    /// Number of tasks that completed successfully
    pub successful_tasks: usize,

    /// Number of tasks that failed
    pub failed_tasks: usize,

    /// Total execution time of all tasks
    pub total_execution_time: Duration,

    /// Average execution time per task
    pub average_execution_time: Option<Duration>,

    /// Maximum execution time of any task
    pub max_execution_time: Option<Duration>,

    /// Minimum execution time of any task
    pub min_execution_time: Option<Duration>,
}

impl TokioQueueStats {
    /// Creates a new, empty TokioQueueStats.
    pub fn new() -> Self {
        Self::default()
    }

    /// Records a successful task execution.
    pub fn record_success(&mut self, duration: Duration) {
        self.tasks_processed += 1;
        self.successful_tasks += 1;
        self.total_execution_time += duration;

        // Update max execution time
        if let Some(max) = self.max_execution_time {
            if duration > max {
                self.max_execution_time = Some(duration);
            }
        } else {
            self.max_execution_time = Some(duration);
        }

        // Update min execution time
        if let Some(min) = self.min_execution_time {
            if duration < min {
                self.min_execution_time = Some(duration);
            }
        } else {
            self.min_execution_time = Some(duration);
        }

        // Update average execution time
        self.average_execution_time = Some(self.total_execution_time / self.tasks_processed as u32);
    }

    /// Records a failed task execution.
    pub fn record_failure(&mut self, duration: Duration) {
        self.tasks_processed += 1;
        self.failed_tasks += 1;
        self.total_execution_time += duration;

        // Update max execution time
        if let Some(max) = self.max_execution_time {
            if duration > max {
                self.max_execution_time = Some(duration);
            }
        } else {
            self.max_execution_time = Some(duration);
        }

        // Update min execution time
        if let Some(min) = self.min_execution_time {
            if duration < min {
                self.min_execution_time = Some(duration);
            }
        } else {
            self.min_execution_time = Some(duration);
        }

        // Update average execution time
        self.average_execution_time = Some(self.total_execution_time / self.tasks_processed as u32);
    }

    /// Updates the queue length.
    pub fn update_queue_length(&mut self, length: usize) {
        self.queue_length = length;
    }

    /// Updates the number of active tasks.
    pub fn update_active_tasks(&mut self, active: usize) {
        self.active_tasks = active;
    }
}

/// Status of the TokioQueue.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum QueueStatus {
    /// The queue is running and processing tasks
    Running,

    /// The queue is paused and not processing tasks
    Paused,

    /// The queue is shutting down
    ShuttingDown,

    /// The queue is stopped
    Stopped,
}

/// A task for the TokioQueue.
pub struct TokioTask {
    /// The ID of the task
    pub task_id: TaskId,

    /// The task itself
    pub task: Arc<Mutex<Box<dyn Task>>>,

    /// Channel to send the result back to the caller
    pub result_sender: Option<oneshot::Sender<PrismaResult<Box<dyn Any + Send>>>>,

    /// When the task was enqueued
    pub enqueued_at: Instant,
}

impl fmt::Debug for TokioTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("TokioTask")
            .field("task_id", &self.task_id)
            .field("task", &format_args!("Arc<Mutex<Box<dyn Task>>>"))
            .field("enqueued_at", &self.enqueued_at)
            .finish()
    }
}