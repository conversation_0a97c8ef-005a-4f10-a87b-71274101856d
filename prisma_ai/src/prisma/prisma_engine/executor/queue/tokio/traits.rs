// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/tokio/traits.rs
// =================================================================================================
// Purpose: Defines the traits for the tokio queue implementation. The tokio queue executes tasks
// using Tokio's async runtime, which is optimized for I/O-bound tasks or tasks that benefit from
// async concurrency.
//
// Integration:
// - `tokio.rs`: Implements the traits defined here.
// - `Task`: The traits operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::Arc;
use tokio::sync::oneshot;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{TaskId, TaskPriority, TaskCategory, PrismaScore};
use crate::prisma::prisma_engine::executor::queue::direct::CloneableTask;

/// A wrapper struct that implements Task for any CloneableTask.
/// This allows us to wrap an Arc<T> where T: CloneableTask and use it as a Task.
pub struct TokioTaskWrapper<T: CloneableTask + 'static>(pub Arc<T>);

#[async_trait]
impl<T: CloneableTask + 'static> Task for TokioTaskWrapper<T> {
    fn id(&self) -> TaskId {
        self.0.id()
    }

    fn category(&self) -> TaskCategory {
        self.0.category()
    }

    fn priority(&self) -> TaskPriority {
        self.0.priority()
    }

    fn get_prisma_score(&self) -> PrismaScore {
        self.0.get_prisma_score()
    }

    async fn execute(&mut self) -> PrismaResult<Box<dyn Any + Send>> {
        // Clone the inner task and execute it
        let mut inner_clone = (*self.0).clone();
        inner_clone.execute().await
    }

    fn clone_box(&self) -> Box<dyn Task> {
        Box::new(TokioTaskWrapper(Arc::clone(&self.0)))
    }
}

/// Trait defining the interface for a queue that executes tasks using Tokio's async runtime.
///
/// Tokio is optimized for I/O-bound tasks or tasks that benefit from async concurrency.
#[async_trait]
pub trait TokioQueueTrait: Send + Sync {
    /// Enqueues a task for execution using Tokio's async runtime.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to execute
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )>;

    /// Enqueues a task wrapped in an Arc for execution using Tokio's async runtime.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to execute, wrapped in an Arc
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )>;

    /// Dequeues a task from the queue.
    ///
    /// # Returns
    ///
    /// Some(task) if a task was dequeued, or None if the queue is empty.
    async fn dequeue(&self) -> Option<Box<dyn Task>>;

    /// Sends a result for a task.
    ///
    /// # Arguments
    ///
    /// * `task_id` - The ID of the task
    /// * `result` - The result of the task
    ///
    /// # Returns
    ///
    /// Ok(()) if the result was sent successfully, or an error if it failed.
    async fn send_result(
        &self,
        task_id: TaskId,
        result: PrismaResult<Box<dyn Any + Send>>,
    ) -> PrismaResult<()>;

    /// Returns the current queue length.
    ///
    /// # Returns
    ///
    /// The number of tasks currently in the queue.
    fn queue_length(&self) -> usize;

    /// Returns whether the queue is empty.
    ///
    /// # Returns
    ///
    /// `true` if the queue is empty, `false` otherwise.
    fn is_empty(&self) -> bool {
        self.queue_length() == 0
    }

    /// Returns whether the queue is full.
    ///
    /// # Returns
    ///
    /// `true` if the queue is full, `false` otherwise.
    fn is_full(&self) -> bool;

    /// Returns the maximum capacity of the queue.
    ///
    /// # Returns
    ///
    /// The maximum number of tasks the queue can hold.
    fn capacity(&self) -> usize;

    /// Starts the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stops the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Stops the queue worker(s) through an immutable reference.
    /// This is useful when you need to stop the queue but can't get a mutable reference to it.
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn stop_immutable(&self) -> PrismaResult<()>;

    /// Pauses the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn pause(&mut self) -> PrismaResult<()>;

    /// Resumes the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn resume(&mut self) -> PrismaResult<()>;

    /// Sets the maximum number of concurrent tasks.
    ///
    /// # Arguments
    ///
    /// * `max_concurrent_tasks` - The maximum number of concurrent tasks
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    fn set_max_concurrent_tasks(&mut self, max_concurrent_tasks: usize) -> PrismaResult<()>;

    /// Gets the maximum number of concurrent tasks.
    ///
    /// # Returns
    ///
    /// The maximum number of concurrent tasks.
    fn max_concurrent_tasks(&self) -> usize;
}