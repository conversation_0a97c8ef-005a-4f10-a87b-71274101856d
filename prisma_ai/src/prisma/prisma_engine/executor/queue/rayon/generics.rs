// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/rayon/generics.rs
// =================================================================================================
// Purpose: Provides generic utility functions and helper types that support the implementation
// of the Rayon Queue module. This includes common task execution logic and queue management
// utilities used by the RayonQueue implementation.
//
// Integration:
// - `rayon.rs`: Uses the functions defined here.
// - `Task`: The helper functions operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` and `GenericError` for error handling.
// - `tracing`: Used for detailed logging during task execution.
// =================================================================================================

use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tokio::task::JoinHandle;
use tracing::{debug, error, info, instrument, trace};
use rayon::ThreadPool;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::execution_strategies::execute_rayon_task;
use crate::prisma::prisma_engine::executor::queue::priority::QueueStatus;
use crate::prisma::prisma_engine::traits::Task;

use super::types::{RayonQueueConfig, RayonQueueStats, RayonTask};

/// Executes a task using Rayon's thread pool, measures its execution time,
/// and logs the outcome.
///
/// # Arguments
///
/// * `task` - The task to execute
/// * `dedicated_pool` - Optional dedicated thread pool to use for execution
///
/// # Returns
///
/// A tuple containing:
/// - `PrismaResult<Box<dyn Any + Send>>`: The result returned by the task's execute method
/// - `Duration`: The time taken for the task execution
#[instrument(
    name = "rayon_execute_task",
    skip(task, dedicated_pool),
    fields(task_id = %task.id()),
    level = "debug"
)]
pub async fn execute_task_with_rayon(
    task: &mut (dyn Task + Send + Sync),
    dedicated_pool: Option<&ThreadPool>,
) -> (PrismaResult<Box<dyn Any + Send>>, Duration) {
    let task_id = task.id();
    trace!("Starting task execution with Rayon");
    println!("Starting task execution with Rayon for task {}", task_id);

    let start_time = Instant::now();

    // Use the dedicated pool if provided, otherwise use the global pool
    let result = if let Some(pool) = dedicated_pool {
        println!("Using dedicated Rayon thread pool for task {}", task_id);
        debug!("Using dedicated Rayon thread pool for task {}", task_id);

        // Create a oneshot channel to receive the result
        let (tx, rx) = tokio::sync::oneshot::channel();

        // Clone the task for execution in the thread pool
        let mut task_clone = task.clone_box();
        println!("Task cloned for Rayon execution: {}", task_id);

        // Execute the task in the dedicated thread pool
        println!("Spawning task {} on Rayon thread pool", task_id);
        pool.spawn(move || {
            println!("Inside Rayon thread pool for task {}", task_id);

            // Create a new runtime for this task
            println!("Creating new Tokio runtime for task {}", task_id);
            let rt = match tokio::runtime::Builder::new_current_thread()
                .enable_all()
                .build() {
                    Ok(rt) => rt,
                    Err(e) => {
                        let err_msg = format!("Failed to create Tokio runtime for task {}: {}", task_id, e);
                        println!("{}", err_msg);
                        let _ = tx.send(Err(crate::err::GenericError::from(err_msg)));
                        return;
                    }
                };

            // Execute the task
            println!("Executing task {} on new runtime", task_id);
            let result = rt.block_on(task_clone.execute());
            println!("Task {} execution completed with result: {:?}", task_id, result.is_ok());

            // Send the result back
            println!("Sending result for task {} back to main thread", task_id);
            if tx.send(result).is_err() {
                println!("Failed to send result for task {}: receiver dropped", task_id);
            } else {
                println!("Successfully sent result for task {}", task_id);
            }
        });

        println!("Waiting for result from Rayon thread pool for task {}", task_id);
        // Wait for the result with a timeout
        match tokio::time::timeout(std::time::Duration::from_secs(10), rx).await {
            Ok(result) => match result {
                Ok(result) => {
                    println!("Successfully received result for task {}", task_id);
                    result
                },
                Err(e) => {
                    let err_msg = format!("Failed to receive result from Rayon thread pool: {}", e);
                    println!("{}", err_msg);
                    error!("{}", err_msg);
                    Err(crate::err::GenericError::from(err_msg))
                }
            },
            Err(_) => {
                let err_msg = format!("Timeout waiting for result from Rayon thread pool for task {}", task_id);
                println!("{}", err_msg);
                error!("{}", err_msg);
                Err(crate::err::GenericError::from(err_msg))
            }
        }
    } else {
        // Use the global pool
        println!("Using global Rayon pool for task {}", task_id);

        // Create a oneshot channel to receive the result
        let (tx, rx) = tokio::sync::oneshot::channel();

        // Clone the task for execution in the global pool
        let mut task_clone = task.clone_box();
        println!("Task cloned for global Rayon execution: {}", task_id);

        // Execute the task in the global thread pool
        println!("Spawning task {} on global Rayon thread pool", task_id);
        rayon::spawn(move || {
            println!("Inside global Rayon thread pool for task {}", task_id);

            // Create a new runtime for this task
            println!("Creating new Tokio runtime for task {} in global pool", task_id);
            let rt = match tokio::runtime::Builder::new_current_thread()
                .enable_all()
                .build() {
                    Ok(rt) => rt,
                    Err(e) => {
                        let err_msg = format!("Failed to create Tokio runtime for task {}: {}", task_id, e);
                        println!("{}", err_msg);
                        let _ = tx.send(Err(crate::err::GenericError::from(err_msg)));
                        return;
                    }
                };

            // Execute the task
            println!("Executing task {} on new runtime in global pool", task_id);
            let result = rt.block_on(task_clone.execute());
            println!("Task {} execution completed with result: {:?}", task_id, result.is_ok());

            // Send the result back
            println!("Sending result for task {} back to main thread from global pool", task_id);
            if tx.send(result).is_err() {
                println!("Failed to send result for task {}: receiver dropped", task_id);
            } else {
                println!("Successfully sent result for task {} from global pool", task_id);
            }
        });

        println!("Waiting for result from global Rayon thread pool for task {}", task_id);
        // Wait for the result with a timeout
        match tokio::time::timeout(std::time::Duration::from_secs(10), rx).await {
            Ok(result) => match result {
                Ok(result) => {
                    println!("Successfully received result for task {} from global pool", task_id);
                    result
                },
                Err(e) => {
                    let err_msg = format!("Failed to receive result from global Rayon thread pool: {}", e);
                    println!("{}", err_msg);
                    error!("{}", err_msg);
                    Err(crate::err::GenericError::from(err_msg))
                }
            },
            Err(_) => {
                let err_msg = format!("Timeout waiting for result from global Rayon thread pool for task {}", task_id);
                println!("{}", err_msg);
                error!("{}", err_msg);
                Err(crate::err::GenericError::from(err_msg))
            }
        }
    };

    let duration = start_time.elapsed();

    match &result {
        Ok(_) => {
            println!(
                "Task {} execution succeeded with Rayon in {} ms",
                task_id,
                duration.as_millis()
            );
            debug!(
                duration_ms = duration.as_millis(),
                "Task execution succeeded with Rayon"
            );
        }
        Err(e) => {
            println!(
                "Task {} execution failed with Rayon in {} ms: {}",
                task_id,
                duration.as_millis(),
                e
            );
            error!(
                duration_ms = duration.as_millis(),
                error = %e,
                "Task execution failed with Rayon"
            );
        }
    }

    (result, duration)
}

/// Updates queue statistics based on the execution result.
///
/// # Arguments
///
/// * `stats` - The statistics to update
/// * `result` - The result of the task execution
/// * `duration` - The time taken for the task execution
pub fn update_queue_stats(
    stats: &Arc<RwLock<RayonQueueStats>>,
    result: &PrismaResult<Box<dyn Any + Send>>,
    duration: Duration,
) {
    let mut stats_guard = stats.write().unwrap();
    match result {
        Ok(_) => stats_guard.record_success(duration),
        Err(_) => stats_guard.record_failure(duration),
    }
}

/// Creates a worker loop for processing tasks from a Rayon queue.
///
/// # Arguments
///
/// * `rx` - The receiver for tasks
/// * `stats` - The statistics to update
/// * `status` - The status of the queue
/// * `dedicated_pool` - Optional dedicated thread pool to use for execution
/// * `config` - Configuration for the RayonQueue
///
/// # Returns
///
/// A JoinHandle for the worker task
pub fn create_worker_loop(
    mut rx: mpsc::Receiver<RayonTask>,
    stats: Arc<RwLock<RayonQueueStats>>,
    status: Arc<RwLock<QueueStatus>>,
    dedicated_pool: Option<Arc<ThreadPool>>,
    config: RayonQueueConfig,
) -> JoinHandle<()> {
    tokio::spawn(async move {
        println!("Rayon worker loop started");
        info!("Rayon worker loop started");

        // Create a semaphore to limit concurrent tasks if max_concurrent_tasks is set
        let semaphore = if let Some(max_tasks) = config.max_concurrent_tasks {
            println!("Creating semaphore with {} max concurrent tasks", max_tasks);
            Some(Arc::new(tokio::sync::Semaphore::new(max_tasks)))
        } else {
            println!("No max_concurrent_tasks set, not using semaphore");
            None
        };

        println!("Rayon worker loop waiting for tasks");

        // Check if the queue is already stopped before entering the loop
        {
            let queue_status = *status.read().unwrap();
            println!("Initial queue status in worker loop: {:?}", queue_status);
            if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                println!("Queue is already stopped, exiting worker loop");
                return;
            }
        }

        // Add a debug ping to check if the loop is still alive
        let ping_interval = tokio::time::Duration::from_secs(5);
        let ping_task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(ping_interval);
            loop {
                interval.tick().await;
                println!("Rayon worker loop ping - still alive");
            }
        });

        println!("Starting to receive tasks from channel");

        // Create a loop that keeps running until explicitly stopped
        loop {
            // Check if the queue is shutting down or stopped
            {
                let queue_status = *status.read().unwrap();
                if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                    println!("Queue is shutting down or stopped, exiting worker loop");
                    break;
                }
            }

            // Try to receive a task with a short timeout
            println!("Waiting for task with 1 second timeout");
            match tokio::time::timeout(
                std::time::Duration::from_secs(1), // 1 second timeout
                rx.recv()
            ).await {
                Ok(Some(rayon_task)) => {
                    println!("Rayon worker received a task");
                    // Check if the queue is paused or shutting down
                    {
                        let queue_status = *status.read().unwrap();
                        println!("Current queue status: {:?}", queue_status);
                        if queue_status == QueueStatus::Paused {
                            println!("Queue is paused, waiting before processing task");
                            // If paused, wait for a short time and check again
                            tokio::time::sleep(Duration::from_millis(100)).await;
                            continue;
                        }
                        if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                            println!("Queue is shutting down or stopped, exiting worker loop");
                            // If shutting down or stopped, exit the loop
                            break;
                        }
                    }

                    let task_id = rayon_task.task_id;
                    let task_arc = rayon_task.task;
                    let result_sender = rayon_task.result_sender;

                    println!("Rayon worker processing task {}", task_id);
                    debug!("Rayon worker received task {}", task_id);

                    // Update active tasks count
                    {
                        let mut stats_guard = stats.write().unwrap();
                        let current_active = stats_guard.active_tasks;
                        println!("Updating active tasks count from {} to {}", current_active, current_active + 1);
                        stats_guard.update_active_tasks(current_active + 1);
                    }

                    // Update queue length in stats
                    {
                        let mut stats_guard = stats.write().unwrap();
                        let current_length = stats_guard.queue_length;
                        if current_length > 0 {
                            println!("Updating queue length from {} to {}", current_length, current_length - 1);
                            stats_guard.update_queue_length(current_length - 1);
                        }
                    }

                    // Acquire a permit from the semaphore if it exists
                    let _permit = if let Some(sem) = &semaphore {
                        println!("Acquiring semaphore permit for task {}", task_id);
                        match sem.clone().acquire_owned().await {
                            Ok(permit) => {
                                println!("Successfully acquired semaphore permit for task {}", task_id);
                                Some(permit)
                            },
                            Err(e) => {
                                println!("Failed to acquire semaphore permit: {}", e);
                                error!("Failed to acquire semaphore permit: {}", e);
                                None
                            }
                        }
                    } else {
                        println!("No semaphore in use for task {}", task_id);
                        None
                    };

                    // Execute the task with the dedicated pool if available
                    println!("Preparing to execute task {}", task_id);
                    let (result, duration) = {
                        println!("Acquiring lock on task {}", task_id);
                        let mut task_guard = task_arc.lock().await;
                        println!("Lock acquired for task {}", task_id);
                        let task_ref: &mut dyn Task = &mut **task_guard;

                        // Pass the dedicated pool reference if available
                        let pool_ref = dedicated_pool.as_ref().map(|p| p.as_ref());
                        println!("Using {} pool for task {}", if pool_ref.is_some() { "dedicated" } else { "global" }, task_id);

                        // Apply timeout if configured
                        if let Some(timeout) = config.task_timeout {
                            println!("Using timeout of {:?} for task {}", timeout, task_id);
                            match tokio::time::timeout(
                                timeout,
                                execute_task_with_rayon(task_ref, pool_ref)
                            ).await {
                                Ok(result) => {
                                    println!("Task {} completed within timeout", task_id);
                                    result
                                },
                                Err(_) => {
                                    let err_msg = format!("Task {} execution timed out after {:?}", task_id, timeout);
                                    println!("{}", err_msg);
                                    error!("{}", err_msg);
                                    (Err(crate::err::GenericError::from(err_msg)), timeout)
                                }
                            }
                        } else {
                            println!("No timeout set for task {}", task_id);
                            execute_task_with_rayon(task_ref, pool_ref).await
                        }
                    };

                    // Update statistics
                    println!("Updating statistics for task {}", task_id);
                    update_queue_stats(&stats, &result, duration);

                    // Update active tasks count
                    {
                        let mut stats_guard = stats.write().unwrap();
                        let current_active = stats_guard.active_tasks;
                        println!("Updating active tasks count from {} to {}", current_active, current_active - 1);
                        stats_guard.update_active_tasks(current_active - 1);
                    }

                    // Send the result back to the caller
                    if let Some(sender) = result_sender {
                        println!("Sending result for task {}", task_id);
                        if sender.send(result).is_err() {
                            println!("Failed to send result for task {}: receiver dropped", task_id);
                            error!("Failed to send result for task {}: receiver dropped", task_id);
                        } else {
                            println!("Successfully sent result for task {}", task_id);
                        }
                    } else {
                        println!("No result sender for task {}", task_id);
                    }

                    // The permit is automatically dropped here, releasing the semaphore
                    println!("Task {} processing completed", task_id);
                },
                Ok(None) => {
                    // Channel closed
                    println!("Channel closed, exiting worker loop");
                    break;
                },
                Err(_) => {
                    // Timeout occurred, just continue the loop
                    // This allows us to check the queue status periodically
                    println!("Timeout waiting for task, continuing loop");

                    // Check the queue status
                    {
                        let queue_status = *status.read().unwrap();
                        println!("Queue status after timeout: {:?}", queue_status);
                    }

                    // Check the queue length
                    {
                        let stats_guard = stats.read().unwrap();
                        println!("Current queue length: {}", stats_guard.queue_length);
                        println!("Current active tasks: {}", stats_guard.active_tasks);
                    }

                    continue;
                }
            }
        }

        // Cancel the ping task
        ping_task.abort();

        println!("Rayon worker loop stopped - channel closed or timeout occurred");
        info!("Rayon worker loop stopped");
    })
}