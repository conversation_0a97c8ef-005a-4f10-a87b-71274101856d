// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/rayon/mod.rs
// =================================================================================================
// Purpose: Re-exports the RayonQueue and other public items from the rayon queue module.
//
// Integration:
// - `rayon.rs`: Re-exports the RayonQueue struct.
// - `traits.rs`: Re-exports the RayonQueueTrait.
// - `types.rs`: Re-exports the RayonQueueConfig and RayonQueueStats.
// =================================================================================================

// Declare the submodules
mod generics;
mod rayon;
mod traits;
mod types;

// Re-export the public items
pub use rayon::RayonQueue;
pub use traits::{RayonQueueTrait, RayonTaskWrapper};
pub use types::{RayonQueueConfig, RayonQueueStats};

// Also re-export the generic utility functions if they might be useful elsewhere
pub use generics::{execute_task_with_rayon, update_queue_stats};