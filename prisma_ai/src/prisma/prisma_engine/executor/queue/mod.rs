// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/mod.rs
// =================================================================================================
// Purpose: Re-exports the queue modules and their public items.
//
// Integration:
// - `direct`: Re-exports the DirectQueue and related items.
// - `priority`: Will re-export the PriorityQueue and related items.
// - `rayon`: Will re-export the RayonQueue and related items.
// - `tokio`: Will re-export the TokioQueue and related items.
// =================================================================================================

// Declare the submodules
pub mod direct;
pub mod priority;
pub mod rayon;
pub mod tokio;

// Re-export the public items from each submodule
pub use direct::{DirectQueue, DirectQueueTrait, CloneableTask, DirectQueueConfig, DirectQueueStats};
pub use priority::{
    BackgroundPriorityQueue, StandardPriorityQueue, RealTimePriorityQueue,
    PriorityQueueManager, PriorityQueueConfig, PriorityQueueStats, QueueStatus,
    BackgroundQueueTrait, StandardQueueTrait, RealTimeQueueTrait, PriorityQueueTrait,
    CloneableTaskWrapper,
};
pub use rayon::{RayonQueue, RayonQueueTrait, RayonQueueConfig, RayonQueueStats, RayonTaskWrapper};
pub use tokio::{TokioQueue, TokioQueueTrait, TokioQueueConfig, TokioQueueStats, TokioTaskWrapper};