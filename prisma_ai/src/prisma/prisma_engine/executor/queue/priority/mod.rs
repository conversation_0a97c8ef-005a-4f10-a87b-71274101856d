// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/mod.rs
// =================================================================================================
// Purpose: Re-exports the priority queue implementations and other public items from the priority
// queue module. This module provides priority-based task queues for different priority levels.
//
// Integration:
// - `background.rs`: Implements the BackgroundPriorityQueue for low-priority tasks.
// - `standard.rs`: Implements the StandardPriorityQueue for normal-priority tasks.
// - `real_time.rs`: Implements the RealTimePriorityQueue for high-priority and realtime tasks.
// - `traits.rs`: Defines the traits for the priority queue implementations.
// - `types.rs`: Defines the types needed for the priority queue implementations.
// - `generics.rs`: Provides generic utility functions for priority queues.
// =================================================================================================

// Declare the submodules
mod background;
mod generics;
mod real_time;
mod standard;
mod traits;
mod types;

// Re-export the public items
pub use background::BackgroundPriorityQueue;
pub use real_time::RealTimePriorityQueue;
pub use standard::StandardPriorityQueue;
pub use traits::{
    BackgroundQueueTrait, PriorityQueueTrait, RealTimeQueueTrait, StandardQueueTrait,
    CloneableTaskWrapper,
};
pub use types::{PriorityQueueConfig, PriorityQueueStats, QueueStatus};

/// A struct that manages all priority queues.
///
/// This struct provides a unified interface for working with priority queues
/// of different priority levels.
pub struct PriorityQueueManager {
    /// The background (low-priority) queue
    pub background: BackgroundPriorityQueue,

    /// The standard (normal-priority) queue
    pub standard: StandardPriorityQueue,

    /// The real-time (high-priority) queue
    pub realtime: RealTimePriorityQueue,
}

impl PriorityQueueManager {
    /// Creates a new PriorityQueueManager with the given configuration.
    pub fn new(config: PriorityQueueConfig) -> Self {
        PriorityQueueManager {
            background: BackgroundPriorityQueue::new(config.clone()),
            standard: StandardPriorityQueue::new(config.clone()),
            realtime: RealTimePriorityQueue::new(config),
        }
    }

    /// Creates a new PriorityQueueManager with default configuration.
    pub fn default() -> Self {
        Self::new(PriorityQueueConfig::default())
    }

    /// Starts all priority queues.
    pub async fn start_all(&mut self) -> Result<(), String> {
        // Start the background queue
        if let Err(e) = self.background.start().await {
            return Err(format!("Failed to start background queue: {}", e));
        }

        // Start the standard queue
        if let Err(e) = self.standard.start().await {
            return Err(format!("Failed to start standard queue: {}", e));
        }

        // Start the real-time queue
        if let Err(e) = self.realtime.start().await {
            return Err(format!("Failed to start real-time queue: {}", e));
        }

        Ok(())
    }

    /// Stops all priority queues.
    pub async fn stop_all(&mut self) -> Result<(), String> {
        // Stop the background queue
        if let Err(e) = self.background.stop().await {
            return Err(format!("Failed to stop background queue: {}", e));
        }

        // Stop the standard queue
        if let Err(e) = self.standard.stop().await {
            return Err(format!("Failed to stop standard queue: {}", e));
        }

        // Stop the real-time queue
        if let Err(e) = self.realtime.stop().await {
            return Err(format!("Failed to stop real-time queue: {}", e));
        }

        Ok(())
    }

    /// Pauses all priority queues.
    pub async fn pause_all(&mut self) -> Result<(), String> {
        // Pause the background queue
        if let Err(e) = self.background.pause().await {
            return Err(format!("Failed to pause background queue: {}", e));
        }

        // Pause the standard queue
        if let Err(e) = self.standard.pause().await {
            return Err(format!("Failed to pause standard queue: {}", e));
        }

        // Pause the real-time queue
        if let Err(e) = self.realtime.pause().await {
            return Err(format!("Failed to pause real-time queue: {}", e));
        }

        Ok(())
    }

    /// Resumes all priority queues.
    pub async fn resume_all(&mut self) -> Result<(), String> {
        // Resume the background queue
        if let Err(e) = self.background.resume().await {
            return Err(format!("Failed to resume background queue: {}", e));
        }

        // Resume the standard queue
        if let Err(e) = self.standard.resume().await {
            return Err(format!("Failed to resume standard queue: {}", e));
        }

        // Resume the real-time queue
        if let Err(e) = self.realtime.resume().await {
            return Err(format!("Failed to resume real-time queue: {}", e));
        }

        Ok(())
    }

    /// Returns the total number of tasks in all queues.
    pub fn total_queue_length(&self) -> usize {
        self.background.queue_length() + self.standard.queue_length() + self.realtime.queue_length()
    }

    /// Returns whether all queues are empty.
    pub fn all_empty(&self) -> bool {
        self.background.is_empty() && self.standard.is_empty() && self.realtime.is_empty()
    }
}