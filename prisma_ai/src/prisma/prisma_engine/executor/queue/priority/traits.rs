// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/traits.rs
// =================================================================================================
// Purpose: Defines the traits for the priority queue implementation. The priority queue manages
// tasks based on their priority levels and ensures they are executed in the appropriate order.
//
// Integration:
// - `background.rs`, `standard.rs`, `real_time.rs`: Implement the traits defined here.
// - `Task`: The traits operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::oneshot;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{
    TaskId, TaskPriority, ExecutionStrategyType, TaskCategory, PrismaScore
};
use crate::prisma::prisma_engine::executor::queue::direct::CloneableTask;

/// A wrapper struct that implements Task for any CloneableTask.
/// This allows us to wrap an Arc<T> where T: CloneableTask and use it as a Task.
pub struct CloneableTaskWrapper<T: CloneableTask + 'static>(pub Arc<T>);

#[async_trait]
impl<T: CloneableTask + 'static> Task for CloneableTaskWrapper<T> {
    fn id(&self) -> TaskId {
        self.0.id()
    }

    fn category(&self) -> TaskCategory {
        self.0.category()
    }

    fn priority(&self) -> TaskPriority {
        self.0.priority()
    }

    fn get_prisma_score(&self) -> PrismaScore {
        self.0.get_prisma_score()
    }

    async fn execute(&mut self) -> PrismaResult<Box<dyn Any + Send>> {
        // Clone the inner task and execute it
        let mut inner_clone = (*self.0).clone();
        inner_clone.execute().await
    }

    fn clone_box(&self) -> Box<dyn Task> {
        Box::new(CloneableTaskWrapper(Arc::clone(&self.0)))
    }
}

/// Trait defining the interface for a priority-based task queue.
///
/// A priority queue manages tasks based on their priority levels and ensures
/// they are executed in the appropriate order.
#[async_trait]
pub trait PriorityQueueTrait: Send + Sync {
    /// Returns the priority level this queue handles.
    ///
    /// # Returns
    ///
    /// The priority level(s) this queue handles.
    fn priority_level(&self) -> Vec<TaskPriority>;

    /// Returns the execution strategy this queue uses.
    ///
    /// # Returns
    ///
    /// The execution strategy this queue uses.
    fn execution_strategy(&self) -> ExecutionStrategyType;

    /// Dynamically updates the execution strategy for this queue.
    /// This allows the queue to adapt its execution strategy based on system conditions.
    ///
    /// # Arguments
    ///
    /// * `strategy` - The new execution strategy to use
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    fn update_execution_strategy(&mut self, strategy: ExecutionStrategyType) -> PrismaResult<()>;

    /// Returns whether this queue supports dynamic strategy updates.
    ///
    /// # Returns
    ///
    /// `true` if the queue supports dynamic strategy updates, `false` otherwise.
    fn supports_dynamic_strategy(&self) -> bool {
        true // Default implementation supports dynamic strategy updates
    }

    /// Enqueues a task for execution based on its priority.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to enqueue
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )>;

    /// Enqueues a task wrapped in an Arc for execution based on its priority.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to enqueue, wrapped in an Arc
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )>;

    /// Returns the current queue length.
    ///
    /// # Returns
    ///
    /// The number of tasks currently in the queue.
    fn queue_length(&self) -> usize;

    /// Returns whether the queue is empty.
    ///
    /// # Returns
    ///
    /// `true` if the queue is empty, `false` otherwise.
    fn is_empty(&self) -> bool {
        self.queue_length() == 0
    }

    /// Returns whether the queue is full.
    ///
    /// # Returns
    ///
    /// `true` if the queue is full, `false` otherwise.
    fn is_full(&self) -> bool;

    /// Returns the maximum capacity of the queue.
    ///
    /// # Returns
    ///
    /// The maximum number of tasks the queue can hold.
    fn capacity(&self) -> usize;

    /// Starts the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stops the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Pauses the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn pause(&mut self) -> PrismaResult<()>;

    /// Resumes the queue worker(s).
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    async fn resume(&mut self) -> PrismaResult<()>;
}

/// Trait for a queue that can handle background (low-priority) tasks.
#[async_trait]
pub trait BackgroundQueueTrait: PriorityQueueTrait {
    /// Sets the maximum number of background tasks that can run concurrently.
    ///
    /// # Arguments
    ///
    /// * `limit` - The maximum number of concurrent background tasks
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    fn set_concurrency_limit(&mut self, limit: usize) -> PrismaResult<()>;

    /// Gets the current concurrency limit.
    ///
    /// # Returns
    ///
    /// The maximum number of concurrent background tasks.
    fn concurrency_limit(&self) -> usize;
}

/// Trait for a queue that can handle standard (normal-priority) tasks.
#[async_trait]
pub trait StandardQueueTrait: PriorityQueueTrait {
    /// Sets the task execution timeout.
    ///
    /// # Arguments
    ///
    /// * `timeout` - The timeout duration
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    fn set_task_timeout(&mut self, timeout: Duration) -> PrismaResult<()>;

    /// Gets the current task execution timeout.
    ///
    /// # Returns
    ///
    /// The current task execution timeout.
    fn task_timeout(&self) -> Duration;
}

/// Trait for a queue that can handle real-time (high-priority) tasks.
#[async_trait]
pub trait RealTimeQueueTrait: PriorityQueueTrait {
    /// Sets whether to preempt lower-priority tasks.
    ///
    /// # Arguments
    ///
    /// * `preempt` - Whether to preempt lower-priority tasks
    ///
    /// # Returns
    ///
    /// A result indicating success or failure.
    fn set_preemption(&mut self, preempt: bool) -> PrismaResult<()>;

    /// Gets whether preemption is enabled.
    ///
    /// # Returns
    ///
    /// `true` if preemption is enabled, `false` otherwise.
    fn preemption_enabled(&self) -> bool;
}