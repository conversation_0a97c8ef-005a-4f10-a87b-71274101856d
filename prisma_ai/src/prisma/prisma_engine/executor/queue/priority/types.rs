// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/types.rs
// =================================================================================================
// Purpose: Defines the types needed for the priority queue implementation. The priority queue manages
// tasks based on their priority levels and ensures they are executed in the appropriate order.
//
// Integration:
// - `background.rs`, `standard.rs`, `real_time.rs`: Use the types defined here.
// - `Task`: The types operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` for error handling.
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::any::Any;
use std::cmp::Ordering;
use std::fmt;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, oneshot};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskId, TaskPriority};

/// Configuration for the priority queues.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriorityQueueConfig {
    /// Maximum capacity for the background (low-priority) queue
    pub background_queue_capacity: usize,

    /// Maximum capacity for the standard (normal-priority) queue
    pub standard_queue_capacity: usize,

    /// Maximum capacity for the real-time (high-priority) queue
    pub realtime_queue_capacity: usize,

    /// Maximum number of concurrent background tasks
    pub background_concurrency_limit: usize,

    /// Timeout for standard tasks
    pub standard_task_timeout_ms: u64,

    /// Whether to enable preemption for real-time tasks
    pub realtime_preemption_enabled: bool,

    /// Execution strategy for background tasks (can be overridden dynamically)
    pub background_strategy: ExecutionStrategyType,

    /// Execution strategy for standard tasks (can be overridden dynamically)
    pub standard_strategy: ExecutionStrategyType,

    /// Execution strategy for real-time tasks (can be overridden dynamically)
    pub realtime_strategy: ExecutionStrategyType,

    /// Whether to enable dynamic strategy selection based on system conditions
    pub enable_dynamic_strategy_selection: bool,

    /// Whether to enable real-time adaptation based on system load and task characteristics
    pub enable_realtime_adaptation: bool,
}

impl Default for PriorityQueueConfig {
    fn default() -> Self {
        PriorityQueueConfig {
            background_queue_capacity: 10000,
            standard_queue_capacity: 5000,
            realtime_queue_capacity: 1000,
            background_concurrency_limit: 4,
            standard_task_timeout_ms: 30000, // 30 seconds
            realtime_preemption_enabled: true,
            background_strategy: ExecutionStrategyType::Rayon,    // Default fallback
            standard_strategy: ExecutionStrategyType::Tokio,      // Default fallback
            realtime_strategy: ExecutionStrategyType::Tokio,      // Default fallback
            enable_dynamic_strategy_selection: true,              // Enable dynamic selection by default
            enable_realtime_adaptation: true,                     // Enable real-time adaptation by default
        }
    }
}

/// Status of a priority queue.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum QueueStatus {
    /// Queue is running and processing tasks
    Running,
    /// Queue is paused and not processing tasks
    Paused,
    /// Queue is stopped and not accepting new tasks
    Stopped,
    /// Queue is shutting down
    ShuttingDown,
}

/// Statistics for a priority queue.
#[derive(Debug, Clone, Default)]
pub struct PriorityQueueStats {
    /// Total number of tasks processed
    pub tasks_processed: usize,

    /// Number of tasks currently in the queue
    pub queue_length: usize,

    /// Number of tasks currently being processed
    pub active_tasks: usize,

    /// Number of tasks that completed successfully
    pub successful_tasks: usize,

    /// Number of tasks that failed
    pub failed_tasks: usize,

    /// Number of tasks that timed out
    pub timed_out_tasks: usize,

    /// Total execution time of all tasks
    pub total_execution_time: Duration,

    /// Average execution time per task
    pub average_execution_time: Option<Duration>,

    /// Maximum execution time of any task
    pub max_execution_time: Option<Duration>,

    /// Minimum execution time of any task
    pub min_execution_time: Option<Duration>,
}

impl PriorityQueueStats {
    /// Creates a new, empty PriorityQueueStats.
    pub fn new() -> Self {
        Self::default()
    }

    /// Records a successful task execution.
    pub fn record_success(&mut self, duration: Duration) {
        self.tasks_processed += 1;
        self.successful_tasks += 1;
        self.total_execution_time += duration;

        // Update max execution time
        if let Some(max) = self.max_execution_time {
            if duration > max {
                self.max_execution_time = Some(duration);
            }
        } else {
            self.max_execution_time = Some(duration);
        }

        // Update min execution time
        if let Some(min) = self.min_execution_time {
            if duration < min {
                self.min_execution_time = Some(duration);
            }
        } else {
            self.min_execution_time = Some(duration);
        }

        // Update average execution time
        self.average_execution_time = Some(self.total_execution_time / self.tasks_processed as u32);
    }

    /// Records a failed task execution.
    pub fn record_failure(&mut self, duration: Duration) {
        self.tasks_processed += 1;
        self.failed_tasks += 1;
        self.total_execution_time += duration;

        // Update max execution time
        if let Some(max) = self.max_execution_time {
            if duration > max {
                self.max_execution_time = Some(duration);
            }
        } else {
            self.max_execution_time = Some(duration);
        }

        // Update min execution time
        if let Some(min) = self.min_execution_time {
            if duration < min {
                self.min_execution_time = Some(duration);
            }
        } else {
            self.min_execution_time = Some(duration);
        }

        // Update average execution time
        self.average_execution_time = Some(self.total_execution_time / self.tasks_processed as u32);
    }

    /// Records a timed out task.
    pub fn record_timeout(&mut self, duration: Duration) {
        self.tasks_processed += 1;
        self.timed_out_tasks += 1;
        self.total_execution_time += duration;

        // Update max execution time
        if let Some(max) = self.max_execution_time {
            if duration > max {
                self.max_execution_time = Some(duration);
            }
        } else {
            self.max_execution_time = Some(duration);
        }

        // Update min execution time
        if let Some(min) = self.min_execution_time {
            if duration < min {
                self.min_execution_time = Some(duration);
            }
        } else {
            self.min_execution_time = Some(duration);
        }

        // Update average execution time
        self.average_execution_time = Some(self.total_execution_time / self.tasks_processed as u32);
    }

    /// Updates the queue length.
    pub fn update_queue_length(&mut self, length: usize) {
        self.queue_length = length;
    }

    /// Updates the number of active tasks.
    pub fn update_active_tasks(&mut self, active: usize) {
        self.active_tasks = active;
    }
}

/// A task with its priority for use in priority queues.
pub struct PrioritizedTask {
    /// The priority of the task
    pub priority: TaskPriority,

    /// The ID of the task
    pub task_id: TaskId,

    /// The task itself
    pub task: Arc<Mutex<Box<dyn Task>>>,

    /// Channel to send the result back to the caller
    pub result_sender: Option<oneshot::Sender<PrismaResult<Box<dyn Any + Send>>>>,

    /// When the task was enqueued
    pub enqueued_at: Instant,
}

impl fmt::Debug for PrioritizedTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("PrioritizedTask")
            .field("priority", &self.priority)
            .field("task_id", &self.task_id)
            .field("task", &format_args!("Arc<Mutex<Box<dyn Task>>>"))
            .field("enqueued_at", &self.enqueued_at)
            .finish()
    }
}

impl Ord for PrioritizedTask {
    fn cmp(&self, other: &Self) -> Ordering {
        // Higher priority comes first
        other.priority.cmp(&self.priority)
            // For same priority, earlier enqueued task comes first (FIFO)
            .then_with(|| self.enqueued_at.cmp(&other.enqueued_at))
    }
}

impl PartialOrd for PrioritizedTask {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl PartialEq for PrioritizedTask {
    fn eq(&self, other: &Self) -> bool {
        self.priority == other.priority && self.enqueued_at == other.enqueued_at
    }
}

impl Eq for PrioritizedTask {}