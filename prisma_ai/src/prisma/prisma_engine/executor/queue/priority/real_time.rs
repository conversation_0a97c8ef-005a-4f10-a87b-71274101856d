// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/real_time.rs
// =================================================================================================
// Purpose: Implements the RealTimePriorityQueue for handling high-priority and realtime tasks.
// This queue is designed for tasks that require immediate execution with the highest priority.
//
// Integration:
// - `traits.rs`: Implements the PriorityQueueTrait and RealTimeQueueTrait defined there.
// - `types.rs`: Uses the types defined there.
// - `generics.rs`: Uses the utility functions defined there.
// - `Task`: Operates on Task objects.
// - `err.rs`: Uses PrismaResult for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::Instant;
use tokio::sync::{mpsc, Mutex, oneshot};
use tokio::task::JoinHandle;
use tracing::{debug, error, info, instrument, warn};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::executor::queue::direct::CloneableTask;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskId, TaskPriority};

use super::generics::create_worker_loop;
use super::traits::{CloneableTaskWrapper, PriorityQueueTrait, RealTimeQueueTrait};
use super::types::{PrioritizedTask, PriorityQueueConfig, PriorityQueueStats, QueueStatus};

/// A queue for handling high-priority and realtime tasks.
///
/// This queue is designed for tasks that require immediate execution with the highest priority.
/// It supports preemption of lower-priority tasks to ensure that high-priority tasks are
/// executed as soon as possible.
#[derive(Debug)]
pub struct RealTimePriorityQueue {
    /// Configuration for the queue
    config: PriorityQueueConfig,

    /// Channel sender for enqueueing tasks
    tx: mpsc::Sender<PrioritizedTask>,

    /// Channel receiver for dequeueing tasks
    rx: Option<mpsc::Receiver<PrioritizedTask>>,

    /// Whether to preempt lower-priority tasks
    preemption_enabled: bool,

    /// Statistics about task execution
    stats: Arc<RwLock<PriorityQueueStats>>,

    /// Status of the queue
    status: Arc<RwLock<QueueStatus>>,

    /// Handles for the worker tasks
    worker_handles: Vec<JoinHandle<()>>,
}

impl Clone for RealTimePriorityQueue {
    fn clone(&self) -> Self {
        // Create a new channel with the same capacity
        let (tx, rx) = mpsc::channel(self.config.realtime_queue_capacity);

        RealTimePriorityQueue {
            config: self.config.clone(),
            tx,
            rx: Some(rx),
            preemption_enabled: self.preemption_enabled,
            stats: self.stats.clone(),
            status: self.status.clone(),
            worker_handles: Vec::new(), // Empty vector for worker handles
        }
    }
}

impl RealTimePriorityQueue {
    /// Creates a new RealTimePriorityQueue with the given configuration.
    pub fn new(config: PriorityQueueConfig) -> Self {
        let (tx, rx) = mpsc::channel(config.realtime_queue_capacity);

        RealTimePriorityQueue {
            preemption_enabled: config.realtime_preemption_enabled,
            stats: Arc::new(RwLock::new(PriorityQueueStats::new())),
            status: Arc::new(RwLock::new(QueueStatus::Stopped)),
            worker_handles: Vec::new(),
            config,
            tx,
            rx: Some(rx),
        }
    }

    /// Creates a new RealTimePriorityQueue with default configuration.
    pub fn default() -> Self {
        Self::new(PriorityQueueConfig::default())
    }

    /// Returns a clone of the current statistics.
    pub fn get_stats(&self) -> PriorityQueueStats {
        self.stats.read().unwrap().clone()
    }

    /// Returns the current status of the queue.
    pub fn get_status(&self) -> QueueStatus {
        *self.status.read().unwrap()
    }

    /// Returns a reference to the queue configuration.
    pub fn get_config(&self) -> &PriorityQueueConfig {
        &self.config
    }
}

#[async_trait]
impl PriorityQueueTrait for RealTimePriorityQueue {
    fn priority_level(&self) -> Vec<TaskPriority> {
        vec![TaskPriority::High, TaskPriority::Realtime]
    }

    fn execution_strategy(&self) -> ExecutionStrategyType {
        self.config.realtime_strategy
    }

    fn update_execution_strategy(&mut self, strategy: ExecutionStrategyType) -> PrismaResult<()> {
        debug!("Updating RealTime queue execution strategy from {:?} to {:?}",
               self.config.realtime_strategy, strategy);
        self.config.realtime_strategy = strategy;
        Ok(())
    }

    #[instrument(
        name = "realtime_queue_enqueue",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return Err(GenericError::from(
                "Cannot enqueue task: RealTime queue is not running",
            ));
        }

        let task_id = task.id();
        let priority = task.priority();

        // Verify that the task has the correct priority
        if priority != TaskPriority::High && priority != TaskPriority::Realtime {
            warn!(
                "Task {} has priority {:?} but was submitted to RealTime queue",
                task_id, priority
            );
        }

        debug!("Enqueuing task {} in RealTime queue", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();

        // Create a PrioritizedTask
        let prioritized_task = PrioritizedTask {
            priority,
            task_id,
            task: Arc::new(Mutex::new(task)),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };

        // Send the task to the channel
        if let Err(e) = self.tx.send(prioritized_task).await {
            let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in RealTime queue", task_id);
        Ok((task_id, result_receiver))
    }

    #[instrument(
        name = "realtime_queue_enqueue_arc",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return Err(GenericError::from(
                "Cannot enqueue task: RealTime queue is not running",
            ));
        }

        let task_id = task.id();
        let priority = task.priority();

        // Verify that the task has the correct priority
        if priority != TaskPriority::High && priority != TaskPriority::Realtime {
            warn!(
                "Task {} has priority {:?} but was submitted to RealTime queue",
                task_id, priority
            );
        }

        debug!("Enqueuing task {} in RealTime queue (Arc)", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();

        // Create a PrioritizedTask with the Arc<T> directly
        // We'll handle the task execution in the worker loop
        let prioritized_task = PrioritizedTask {
            priority,
            task_id,
            task: Arc::new(Mutex::new(Box::new(CloneableTaskWrapper(task)))),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };

        // Send the task to the channel
        if let Err(e) = self.tx.send(prioritized_task).await {
            let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in RealTime queue (Arc)", task_id);
        Ok((task_id, result_receiver))
    }

    fn queue_length(&self) -> usize {
        self.stats.read().unwrap().queue_length
    }

    fn is_full(&self) -> bool {
        self.queue_length() >= self.capacity()
    }

    fn capacity(&self) -> usize {
        self.config.realtime_queue_capacity
    }

    async fn start(&mut self) -> PrismaResult<()> {
        // Check if the queue is already running
        {
            let status = *self.status.read().unwrap();
            println!("Starting RealTime queue with current status: {:?}", status);
            if status == QueueStatus::Running {
                println!("RealTime queue is already running, returning early");
                return Ok(());
            }
            if status == QueueStatus::ShuttingDown {
                let err_msg = "Cannot start queue: Queue is shutting down";
                println!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        }

        info!("Starting RealTime queue");
        println!("Starting RealTime queue");

        // Create a new channel for the worker loop
        println!("Creating new channel with capacity {}", self.config.realtime_queue_capacity);
        let (tx, mut rx) = mpsc::channel(self.config.realtime_queue_capacity);

        // Store the sender in self.tx for future enqueues
        self.tx = tx;
        println!("New channel created");

        // Create worker
        println!("Creating worker loop");
        let worker_handle = create_worker_loop(
            rx,
            self.config.realtime_strategy,
            self.stats.clone(),
            self.status.clone(),
            None, // No timeout for realtime tasks
        );

        // Store the worker handle
        println!("Storing worker handle");
        self.worker_handles.push(worker_handle);
        println!("Worker handle stored");

        // Update status
        {
            println!("Updating queue status to Running");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
            println!("Queue status updated to Running");
        }

        // Wait a short time to ensure the worker loop has started
        println!("Waiting for worker loop to initialize");
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        println!("Worker loop initialization wait complete");

        println!("RealTime queue started");
        info!("RealTime queue started");
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        // Check if the queue is already stopped
        {
            let status = *self.status.read().unwrap();
            if status == QueueStatus::Stopped {
                return Ok(());
            }
        }

        info!("Stopping RealTime queue");

        // Update status to shutting down
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::ShuttingDown;
        }

        // Abort all worker tasks
        for handle in self.worker_handles.drain(..) {
            handle.abort();
        }

        // Create a new channel
        let (tx, rx) = mpsc::channel(self.config.realtime_queue_capacity);
        self.tx = tx;
        self.rx = Some(rx);

        // Update status to stopped
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Stopped;
        }

        info!("RealTime queue stopped");
        Ok(())
    }

    async fn pause(&mut self) -> PrismaResult<()> {
        // Check if the queue is running
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Running {
                return Err(GenericError::from(
                    "Cannot pause queue: Queue is not running",
                ));
            }
        }

        info!("Pausing RealTime queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Paused;
        }

        info!("RealTime queue paused");
        Ok(())
    }

    async fn resume(&mut self) -> PrismaResult<()> {
        // Check if the queue is paused
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Paused {
                return Err(GenericError::from(
                    "Cannot resume queue: Queue is not paused",
                ));
            }
        }

        info!("Resuming RealTime queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
        }

        info!("RealTime queue resumed");
        Ok(())
    }
}

#[async_trait]
impl RealTimeQueueTrait for RealTimePriorityQueue {
    fn set_preemption(&mut self, preempt: bool) -> PrismaResult<()> {
        info!("Setting RealTime queue preemption to {}", preempt);

        // Update preemption setting
        self.preemption_enabled = preempt;

        Ok(())
    }

    fn preemption_enabled(&self) -> bool {
        self.preemption_enabled
    }
}