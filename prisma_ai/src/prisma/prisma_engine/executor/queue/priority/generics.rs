// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/generics.rs
// =================================================================================================
// Purpose: Provides generic utility functions and helper types that support the implementation
// of the Priority Queue module. This includes common task execution logic and queue management
// utilities used by the different priority queue implementations.
//
// Integration:
// - `background.rs`, `standard.rs`, `real_time.rs`: Use the functions defined here.
// - `Task`: The helper functions operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` and `PrismaError` for error handling.
// - `tracing`: Used for detailed logging during task execution.
// =================================================================================================

use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tokio::task::JoinHandle;
use tokio::time::timeout;
use tracing::{debug, error, info, instrument, trace};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::execution_strategies::{
    execute_direct_task, execute_rayon_task, execute_tokio_task,
};
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::ExecutionStrategyType;

use super::types::{PrioritizedTask, PriorityQueueStats, QueueStatus};

/// Executes a task using the specified execution strategy, measures its execution time,
/// and logs the outcome.
///
/// # Arguments
///
/// * `task` - The task to execute
/// * `strategy` - The execution strategy to use
///
/// # Returns
///
/// A tuple containing:
/// - `PrismaResult<Box<dyn Any + Send>>`: The result returned by the task's execute method
/// - `Duration`: The time taken for the task execution
#[instrument(
    name = "execute_task_with_strategy",
    skip(task),
    fields(task_id = %task.id()),
    level = "debug"
)]
pub async fn execute_task_with_strategy(
    task: &mut (dyn Task + Send + Sync),
    strategy: ExecutionStrategyType,
) -> (PrismaResult<Box<dyn Any + Send>>, Duration) {
    let task_id = task.id();
    println!("Starting task execution with strategy: {:?} for task {}", strategy, task_id);
    trace!("Starting task execution with strategy: {:?}", strategy);

    let start_time = Instant::now();
    let result = match strategy {
        ExecutionStrategyType::Direct => {
            println!("Executing task {} with Direct strategy", task_id);
            execute_direct_task(task).await
        },
        ExecutionStrategyType::Tokio => {
            println!("Executing task {} with Tokio strategy", task_id);
            execute_tokio_task(task).await
        },
        ExecutionStrategyType::Rayon => {
            println!("Executing task {} with Rayon strategy", task_id);
            execute_rayon_task(task).await
        },
    };
    let duration = start_time.elapsed();
    println!("Task {} execution completed in {:?} with result: {:?}", task_id, duration, result.is_ok());

    match &result {
        Ok(_) => {
            debug!(
                duration_ms = duration.as_millis(),
                "Task execution succeeded with strategy: {:?}",
                strategy
            );
        }
        Err(e) => {
            error!(
                duration_ms = duration.as_millis(),
                error = %e,
                "Task execution failed with strategy: {:?}",
                strategy
            );
        }
    }

    (result, duration)
}

/// Executes a task with a timeout, using the specified execution strategy.
///
/// # Arguments
///
/// * `task` - The task to execute
/// * `strategy` - The execution strategy to use
/// * `timeout_duration` - The maximum time to wait for the task to complete
///
/// # Returns
///
/// A tuple containing:
/// - `PrismaResult<Box<dyn Any + Send>>`: The result returned by the task's execute method,
///   or a timeout error if the task took too long
/// - `Duration`: The time taken for the task execution (or the timeout duration if it timed out)
#[instrument(
    name = "execute_task_with_timeout",
    skip(task),
    fields(task_id = %task.id()),
    level = "debug"
)]
pub async fn execute_task_with_timeout(
    task: &mut (dyn Task + Send + Sync),
    strategy: ExecutionStrategyType,
    timeout_duration: Duration,
) -> (PrismaResult<Box<dyn Any + Send>>, Duration) {
    let task_id = task.id();
    trace!(
        "Starting task execution with strategy: {:?} and timeout: {:?}",
        strategy,
        timeout_duration
    );

    let start_time = Instant::now();
    let result = match timeout(
        timeout_duration,
        execute_task_with_strategy(task, strategy),
    )
    .await
    {
        Ok((result, _)) => result,
        Err(_) => {
            let err_msg = format!(
                "Task {} execution timed out after {:?}",
                task_id, timeout_duration
            );
            error!("{}", err_msg);
            Err(GenericError::from(err_msg))
        }
    };
    let duration = start_time.elapsed();

    (result, duration)
}

/// Updates queue statistics based on the execution result.
///
/// # Arguments
///
/// * `stats` - The statistics to update
/// * `result` - The result of the task execution
/// * `duration` - The time taken for the task execution
pub fn update_queue_stats(
    stats: &Arc<RwLock<PriorityQueueStats>>,
    result: &PrismaResult<Box<dyn Any + Send>>,
    duration: Duration,
) {
    let mut stats_guard = stats.write().unwrap();
    match result {
        Ok(_) => stats_guard.record_success(duration),
        Err(e) => {
            // Check if it's a timeout error
            if e.to_string().contains("timed out") {
                stats_guard.record_timeout(duration);
            } else {
                stats_guard.record_failure(duration);
            }
        }
    }
}

/// Creates a worker loop for processing tasks from a priority queue.
///
/// # Arguments
///
/// * `rx` - The receiver for tasks
/// * `strategy` - The execution strategy to use
/// * `stats` - The statistics to update
/// * `status` - The status of the queue
/// * `timeout_duration` - Optional timeout duration for task execution
///
/// # Returns
///
/// A JoinHandle for the worker task
pub fn create_worker_loop(
    mut rx: mpsc::Receiver<PrioritizedTask>,
    strategy: ExecutionStrategyType,
    stats: Arc<RwLock<PriorityQueueStats>>,
    status: Arc<RwLock<QueueStatus>>,
    timeout_duration: Option<Duration>,
) -> JoinHandle<()> {
    tokio::spawn(async move {
        info!("Worker loop started for strategy: {:?}", strategy);
        println!("Priority queue worker loop started for strategy: {:?}", strategy);

        // Add a timeout for receiving tasks to prevent blocking indefinitely
        let receive_timeout = Duration::from_secs(1);

        // Add a debug ping to check if the loop is still alive
        let ping_interval = tokio::time::Duration::from_secs(5);
        let ping_task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(ping_interval);
            loop {
                interval.tick().await;
                debug!("Priority queue worker loop ping - strategy: {:?}", strategy);
                println!("Priority queue worker loop ping - strategy: {:?}", strategy);
            }
        });

        loop {
            // Check if the queue is shutting down or stopped before trying to receive
            {
                let queue_status = *status.read().unwrap();
                if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                    debug!("Worker loop exiting due to queue status: {:?}", queue_status);
                    println!("Priority queue worker loop exiting due to queue status: {:?}", queue_status);
                    break;
                }

                if queue_status == QueueStatus::Paused {
                    // If paused, wait for a short time and check again
                    tokio::time::sleep(Duration::from_millis(100)).await;
                    continue;
                }
            }

            // Try to receive a task with a timeout
            match tokio::time::timeout(receive_timeout, rx.recv()).await {
                Ok(Some(prioritized_task)) => {
                    let task_id = prioritized_task.task_id;
                    let task_arc = prioritized_task.task;
                    let result_sender = prioritized_task.result_sender;
                    let priority = prioritized_task.priority;

                    println!("Priority queue worker received task {} with priority {:?} for strategy {:?}",
                        task_id, priority, strategy);
                    debug!(
                        "Worker received task {} with priority {:?} for strategy {:?}",
                        task_id, priority, strategy
                    );

                    // Update queue length in stats (decrement since we're processing a task)
                    {
                        let mut stats_guard = stats.write().unwrap();
                        let current_length = stats_guard.queue_length;
                        if current_length > 0 {
                            println!("Updating queue length from {} to {}", current_length, current_length - 1);
                            stats_guard.update_queue_length(current_length - 1);
                        }
                    }

                    // Update active tasks count
                    {
                        let mut stats_guard = stats.write().unwrap();
                        let current_active = stats_guard.active_tasks;
                        println!("Updating active tasks count from {} to {}", current_active, current_active + 1);
                        stats_guard.update_active_tasks(current_active + 1);
                    }

                    // Execute the task
                    let (result, duration) = {
                        let mut task_guard = task_arc.lock().await;
                        let task_ref: &mut dyn Task = &mut **task_guard;
                        if let Some(timeout_dur) = timeout_duration {
                            execute_task_with_timeout(task_ref, strategy, timeout_dur).await
                        } else {
                            execute_task_with_strategy(task_ref, strategy).await
                        }
                    };

                    // Update statistics
                    update_queue_stats(&stats, &result, duration);

                    // Update active tasks count
                    {
                        let mut stats_guard = stats.write().unwrap();
                        let current_active = stats_guard.active_tasks;
                        stats_guard.update_active_tasks(current_active - 1);
                    }

                    // Send the result back to the caller
                    if let Some(sender) = result_sender {
                        if sender.send(result).is_err() {
                            error!("Failed to send result for task {}: receiver dropped", task_id);
                        }
                    }
                },
                Ok(None) => {
                    // Channel closed, exit the loop
                    debug!("Worker loop exiting due to closed channel for strategy: {:?}", strategy);
                    println!("Priority queue worker loop exiting due to closed channel for strategy: {:?}", strategy);
                    break;
                },
                Err(_) => {
                    // Timeout occurred, check status again
                    trace!("Timeout waiting for task, checking status again for strategy: {:?}", strategy);
                    println!("Timeout waiting for task, checking status again for strategy: {:?}", strategy);
                    continue;
                }
            }
        }

        // Cancel the ping task
        ping_task.abort();

        info!("Worker loop stopped for strategy: {:?}", strategy);
    })
}