// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/direct/traits.rs
// =================================================================================================
// Purpose: Defines the traits for the direct queue implementation. The direct queue executes tasks
// immediately in the current async context without any queuing or scheduling.
//
// Integration:
// - `direct.rs`: Implements the traits defined here.
// - `Task`: The traits operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::oneshot;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::TaskId;

/// A trait that extends the Task trait with cloning capability.
/// This is needed for the DirectQueue to work with Arc<T> where T: Task.
pub trait CloneableTask: Task + Clone {}

// Implement CloneableTask for any type that implements Task and Clone
impl<T: Task + Clone> CloneableTask for T {}

/// Trait defining the interface for a queue that executes tasks directly
/// in the current async context without any queuing or scheduling.
#[async_trait]
pub trait DirectQueueTrait: Send + Sync {
    /// Enqueues a task for immediate execution in the current async context.
    ///
    /// Since this is a direct queue, "enqueuing" actually means executing the task
    /// immediately and returning the result.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to execute
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    /// - `Duration`: The time taken to execute the task
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
        Duration,
    )>;

    /// Enqueues a task wrapped in an Arc for immediate execution.
    ///
    /// This variant is useful when the task is shared between multiple components.
    ///
    /// # Arguments
    ///
    /// * `task` - The task to execute, wrapped in an Arc
    ///
    /// # Returns
    ///
    /// A tuple containing:
    /// - `TaskId`: The ID of the task
    /// - `oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>`: A receiver for the task result
    /// - `Duration`: The time taken to execute the task
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
        Duration,
    )>;

    /// Dequeues a task from the queue.
    ///
    /// # Returns
    ///
    /// Some(task) if a task was dequeued, or None if the queue is empty.
    async fn dequeue(&self) -> Option<Box<dyn Task>>;

    /// Sends a result for a task.
    ///
    /// # Arguments
    ///
    /// * `task_id` - The ID of the task
    /// * `result` - The result of the task
    ///
    /// # Returns
    ///
    /// Ok(()) if the result was sent successfully, or an error if it failed.
    async fn send_result(
        &self,
        task_id: TaskId,
        result: PrismaResult<Box<dyn Any + Send>>,
    ) -> PrismaResult<()>;
}