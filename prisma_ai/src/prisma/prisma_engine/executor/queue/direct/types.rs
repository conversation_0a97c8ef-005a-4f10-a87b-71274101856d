// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/direct/types.rs
// =================================================================================================
// Purpose: Defines the types needed for the direct queue implementation. The direct queue executes
// tasks immediately in the current async context without any queuing or scheduling.
//
// Integration:
// - `direct.rs`: Uses the types defined here.
// - `Task`: The types operate on `Task` objects.
// - `err.rs`: Uses `PrismaResult` for error handling.
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::any::Any;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::oneshot;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::TaskId;

/// Configuration for the DirectQueue.
///
/// Currently empty as the DirectQueue doesn't require any configuration,
/// but included for future extensibility and consistency with other queue types.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectQueueConfig {
    // No configuration needed for direct queue at the moment
    // Placeholder for potential future configuration options
}

impl Default for DirectQueueConfig {
    fn default() -> Self {
        DirectQueueConfig {}
    }
}

/// Result of a direct task execution.
///
/// Contains the task ID, a receiver for the task result, and the execution duration.
#[derive(Debug)]
pub struct DirectTaskResult {
    /// The ID of the task that was executed
    pub task_id: TaskId,

    /// A receiver for the task result
    pub result_receiver: oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,

    /// The time taken to execute the task
    pub execution_duration: Duration,
}

/// Statistics for the DirectQueue.
///
/// Tracks metrics about task execution through the direct queue.
#[derive(Debug, Clone, Default)]
pub struct DirectQueueStats {
    /// Total number of tasks executed
    pub tasks_executed: usize,

    /// Total execution time of all tasks
    pub total_execution_time: Duration,

    /// Number of successful task executions
    pub successful_executions: usize,

    /// Number of failed task executions
    pub failed_executions: usize,
}

impl DirectQueueStats {
    /// Creates a new, empty DirectQueueStats.
    pub fn new() -> Self {
        Self::default()
    }

    /// Records a successful task execution.
    pub fn record_success(&mut self, duration: Duration) {
        self.tasks_executed += 1;
        self.successful_executions += 1;
        self.total_execution_time += duration;
    }

    /// Records a failed task execution.
    pub fn record_failure(&mut self, duration: Duration) {
        self.tasks_executed += 1;
        self.failed_executions += 1;
        self.total_execution_time += duration;
    }

    /// Returns the average execution time of all tasks.
    pub fn average_execution_time(&self) -> Option<Duration> {
        if self.tasks_executed == 0 {
            None
        } else {
            Some(self.total_execution_time / self.tasks_executed as u32)
        }
    }
}