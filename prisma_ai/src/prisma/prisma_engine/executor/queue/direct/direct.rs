// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/direct/direct.rs
// =================================================================================================
// Purpose: Implements the DirectQueue struct and its methods. The DirectQueue executes tasks
// immediately in the current async context without any queuing or scheduling.
//
// Integration:
// - `traits.rs`: Implements the DirectQueueTrait defined there.
// - `types.rs`: Uses the types defined there.
// - `generics.rs`: Uses the execute_task_timed function defined there.
// - `Task`: Operates on Task objects.
// - `err.rs`: Uses PrismaResult for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::Duration;
use tokio::sync::oneshot;
use tracing::{debug, error, info, instrument};

use crate::err::{PrismaResult, GenericError};
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::TaskId;

use super::generics::execute_task_timed;
use super::traits::DirectQueueTrait;
use super::types::{DirectQueueConfig, DirectQueueStats};

/// A queue that executes tasks directly in the current async context without any queuing or scheduling.
///
/// This is suitable for very short, non-blocking tasks that need to be executed immediately.
/// It doesn't actually queue tasks - it executes them immediately and returns the result.
#[derive(Debug)]
pub struct DirectQueue {
    /// Configuration for the DirectQueue
    config: DirectQueueConfig,

    /// Statistics about task execution
    stats: Arc<RwLock<DirectQueueStats>>,
}

impl DirectQueue {
    /// Creates a new DirectQueue with the given configuration.
    pub fn new(config: DirectQueueConfig) -> Self {
        info!("Creating new DirectQueue");
        DirectQueue {
            config,
            stats: Arc::new(RwLock::new(DirectQueueStats::new())),
        }
    }

    /// Creates a new DirectQueue with default configuration.
    pub fn default() -> Self {
        Self::new(DirectQueueConfig::default())
    }

    /// Returns a clone of the current statistics.
    pub fn get_stats(&self) -> DirectQueueStats {
        self.stats.read().unwrap().clone()
    }

    /// Updates the statistics based on the execution result.
    fn update_stats(&self, result: &PrismaResult<Box<dyn Any + Send>>, duration: Duration) {
        let mut stats = self.stats.write().unwrap();
        match result {
            Ok(_) => stats.record_success(duration),
            Err(_) => stats.record_failure(duration),
        }
    }
}

#[async_trait]
impl DirectQueueTrait for DirectQueue {
    /// Enqueues a task for immediate execution in the current async context.
    ///
    /// Since this is a direct queue, "enqueuing" actually means executing the task
    /// immediately and returning the result.
    #[instrument(
        name = "direct_queue_enqueue",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue(
        &self,
        mut task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
        Duration,
    )> {
        let task_id = task.id();
        debug!("Enqueuing task {} for direct execution", task_id);

        // Create a oneshot channel to send the result
        let (sender, receiver) = oneshot::channel();

        // Execute the task directly
        let (result, duration) = execute_task_timed(&mut *task).await;

        // Update statistics
        self.update_stats(&result, duration);

        // Send the result through the channel
        if sender.send(result).is_err() {
            let err_msg = format!("Failed to send result for task {}: receiver dropped", task_id);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        Ok((task_id, receiver, duration))
    }

    /// Enqueues a task wrapped in an Arc for immediate execution.
    ///
    /// This variant is useful when the task is shared between multiple components.
    #[instrument(
        name = "direct_queue_enqueue_arc",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue_arc<T: super::traits::CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
        Duration,
    )> {
        let task_id = task.id();
        debug!("Enqueuing task {} for direct execution (Arc)", task_id);

        // Create a oneshot channel to send the result
        let (sender, receiver) = oneshot::channel();

        // Clone the task from the Arc<T>
        let mut task_clone = (*task).clone();

        // Execute the task directly
        let start_time = std::time::Instant::now();
        let result = task_clone.execute().await;
        let duration = start_time.elapsed();

        // Update statistics
        self.update_stats(&result, duration);

        // Send the result through the channel
        if sender.send(result).is_err() {
            let err_msg = format!("Failed to send result for task {}: receiver dropped", task_id);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        Ok((task_id, receiver, duration))
    }

    /// Dequeues a task from the queue.
    ///
    /// Since this is a direct queue, there's no actual queue to dequeue from.
    /// This method is provided for compatibility with other queue implementations.
    ///
    /// # Returns
    ///
    /// None, since there's no queue to dequeue from.
    async fn dequeue(&self) -> Option<Box<dyn Task>> {
        None
    }

    /// Sends a result for a task.
    ///
    /// Since this is a direct queue, results are sent directly when the task is executed.
    /// This method is provided for compatibility with other queue implementations.
    ///
    /// # Arguments
    ///
    /// * `task_id` - The ID of the task
    /// * `result` - The result of the task
    ///
    /// # Returns
    ///
    /// Ok(()) if the result was sent successfully, or an error if it failed.
    async fn send_result(
        &self,
        _task_id: TaskId,
        _result: PrismaResult<Box<dyn Any + Send>>,
    ) -> PrismaResult<()> {
        // Results are sent directly when the task is executed
        Ok(())
    }
}