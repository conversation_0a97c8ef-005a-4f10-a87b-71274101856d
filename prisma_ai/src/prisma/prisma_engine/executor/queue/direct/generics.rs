// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/direct/generics.rs
// =================================================================================================
// Purpose: Provides generic utility functions and helper types that support the implementation
// of the Direct Queue module. This primarily includes the core task execution logic used by
// the `DirectQueue`.
//
// Integration:
// - `direct.rs`: The `DirectQueue::enqueue` method uses the `execute_task_timed` function defined here.
// - `Task`: The helper function operates on `Task` objects.
// - `err.rs`: Uses `PrismaResult` and `PrismaError`.
// - `tracing`: Used for detailed logging during task execution.
// =================================================================================================

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use std::any::Any;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tracing::{debug, error, instrument, trace}; // Using tracing for logging

/// Executes a task directly, measures its execution time, and logs the outcome.
///
/// This helper function encapsulates the common pattern of:
/// 1. Recording the start time.
/// 2. Calling the task's `execute` method.
/// 3. Recording the end time and calculating duration.
/// 4. Logging the result (success or failure) along with duration.
/// 5. Returning the result and duration.
///
/// It uses `tracing` spans for observability.
///
/// # Arguments
///
/// * `task`: The task to execute (`Box<dyn Task>`).
///
/// # Returns
///
/// A tuple containing:
/// - `PrismaResult<Box<dyn Any + Send>>`: The result returned by `task.execute()`.
/// - `Duration`: The time taken for the `task.execute()` call.
#[instrument(
    name = "direct_task_execution",
    skip(task),
    fields(task_id = %task.id()),
    level = "debug"
)]
pub async fn execute_task_timed(
    task: &mut (dyn Task + Send + Sync),
) -> (PrismaResult<Box<dyn Any + Send>>, Duration) {
    let _task_id = task.id(); // Capture task_id for logging clarity
    trace!("Starting direct execution.");

    let start_time = Instant::now();
    let result = task.execute().await; // Execute the task's core logic
    let duration = start_time.elapsed();

    match &result {
        Ok(_) => {
            debug!(duration_ms = duration.as_millis(), "Direct execution succeeded.");
        }
        Err(e) => {
            // Log the specific error details
            error!(duration_ms = duration.as_millis(), error = %e, "Direct execution failed.");
        }
    }

    (result, duration)
}

/// Executes a task wrapped in an Arc<Mutex<Box<dyn Task>>>, measures its execution time, and logs the outcome.
///
/// This variant is useful when the task is shared between multiple components.
///
/// # Arguments
///
/// * `task_arc`: The task to execute, wrapped in an Arc<Mutex<Box<dyn Task>>>.
///
/// # Returns
///
/// A tuple containing:
/// - `PrismaResult<Box<dyn Any + Send>>`: The result returned by `task.execute()`.
/// - `Duration`: The time taken for the `task.execute()` call.
#[instrument(
    name = "direct_task_execution_arc",
    skip(task_arc),
    level = "debug"
)]
pub async fn execute_task_arc_timed(
    task_arc: Arc<Mutex<Box<dyn Task>>>,
) -> (PrismaResult<Box<dyn Any + Send>>, Duration) {
    let task_id = {
        let task = task_arc.lock().await;
        task.id()
    };
    tracing::Span::current().record("task_id", &tracing::field::display(task_id));

    trace!("Starting direct execution (Arc).");

    let start_time = Instant::now();
    let result = {
        let mut task_guard = task_arc.lock().await;
        task_guard.execute().await
    };
    let duration = start_time.elapsed();

    match &result {
        Ok(_) => {
            debug!(duration_ms = duration.as_millis(), "Direct execution (Arc) succeeded.");
        }
        Err(e) => {
            // Log the specific error details
            error!(duration_ms = duration.as_millis(), error = %e, "Direct execution (Arc) failed.");
        }
    }

    (result, duration)
}

// No other generic utilities seem necessary for this simple direct queue at the moment.
// Placeholder for potential future additions:
// pub fn some_other_direct_helper() { /* ... */ }
