// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/direct/mod.rs
// =================================================================================================
// Purpose: Re-exports the DirectQueue and other public items from the direct queue module.
//
// Integration:
// - `direct.rs`: Re-exports the DirectQueue struct.
// - `traits.rs`: Re-exports the DirectQueueTrait.
// - `types.rs`: Re-exports the DirectQueueConfig and DirectQueueStats.
// =================================================================================================

// Declare the submodules
mod direct;
mod generics;
mod traits;
mod types;

// Re-export the public items
pub use direct::DirectQueue;
pub use traits::{DirectQueueTrait, CloneableTask};
pub use types::{DirectQueueConfig, DirectQueueStats};

// Also re-export the generic utility functions if they might be useful elsewhere
pub use generics::{execute_task_timed, execute_task_arc_timed};