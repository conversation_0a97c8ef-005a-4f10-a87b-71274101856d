// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/agent_integration.rs
// =================================================================================================
// Purpose: Provides integration between the executor module and agent-specific functionality.
// This includes task execution, state management, priority mapping, collaboration features,
// and communication between agents.
//
// Integration:
// - `executor.rs`: Uses AgentIntegration to connect with agent-specific functionality
// - `agent_manager`: Interfaces with the agent manager module
// - `memory_manager.rs`: Uses MemoryManager for agent-specific memory operations
// - `context_manager.rs`: Uses ContextManager for agent-specific context
// =================================================================================================

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use tokio::sync::Mutex;
use tracing::{debug, info};

use crate::prisma::prisma_engine::agent_manager::AgentManager;
use crate::prisma::prisma_engine::agent_manager::types::AgentId;
use crate::prisma::prisma_engine::types::{TaskId, TaskPriority};
use crate::err::{GenericError, PrismaResult};

use super::memory_manager::MemoryManager;
use super::context_manager::ContextManager;

/// Configuration for the AgentIntegration
#[derive(Debug, Clone)]
pub struct AgentIntegrationConfig {
    /// Whether to enable agent collaboration
    pub enable_collaboration: bool,

    /// Whether to enable agent communication
    pub enable_communication: bool,

    /// Maximum number of agents that can collaborate on a task
    pub max_collaborators: usize,

    /// Default priority for agent tasks
    pub default_priority: TaskPriority,
}

impl Default for AgentIntegrationConfig {
    fn default() -> Self {
        Self {
            enable_collaboration: true,
            enable_communication: true,
            max_collaborators: 5,
            default_priority: TaskPriority::Normal,
        }
    }
}

/// Mapping between agent roles and task priorities
#[derive(Debug, Clone)]
pub struct AgentPriorityMapping {
    /// Default priority for tasks without a specific mapping
    pub default_priority: TaskPriority,

    /// Mapping from agent roles to task priorities
    pub role_priority_map: HashMap<String, TaskPriority>,
}

impl Default for AgentPriorityMapping {
    fn default() -> Self {
        let mut role_priority_map = HashMap::new();
        // Example mappings
        role_priority_map.insert("admin".to_string(), TaskPriority::High);
        role_priority_map.insert("system".to_string(), TaskPriority::Realtime);
        role_priority_map.insert("user".to_string(), TaskPriority::Normal);
        role_priority_map.insert("background".to_string(), TaskPriority::Low);

        Self {
            default_priority: TaskPriority::Normal,
            role_priority_map,
        }
    }
}

/// Represents a collaboration between agents
#[derive(Debug)]
pub struct AgentCollaboration {
    /// ID of the collaboration
    pub id: String,

    /// IDs of the agents involved in the collaboration
    pub agent_ids: Vec<AgentId>,

    /// ID of the task being collaborated on
    pub task_id: TaskId,

    /// Status of the collaboration
    pub status: CollaborationStatus,

    /// Results from each agent
    pub agent_results: HashMap<AgentId, Option<String>>,
}

/// Status of an agent collaboration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CollaborationStatus {
    /// The collaboration is pending
    Pending,

    /// The collaboration is in progress
    InProgress,

    /// The collaboration is complete
    Complete,

    /// The collaboration failed
    Failed,
}

/// Provides integration between the executor module and agent-specific functionality
pub struct AgentIntegration {
    /// Configuration for the AgentIntegration
    config: AgentIntegrationConfig,

    /// Reference to the AgentManager
    agent_manager: Arc<Mutex<AgentManager>>,

    /// Reference to the MemoryManager
    memory_manager: Arc<MemoryManager>,

    /// Reference to the ContextManager
    context_manager: Arc<ContextManager>,

    /// Mapping between agent roles and task priorities
    priority_mapping: RwLock<AgentPriorityMapping>,

    /// Active collaborations between agents
    collaborations: RwLock<HashMap<String, AgentCollaboration>>,
}

impl AgentIntegration {
    /// Creates a new AgentIntegration
    pub fn new(
        config: AgentIntegrationConfig,
        agent_manager: Arc<Mutex<AgentManager>>,
        memory_manager: Arc<MemoryManager>,
        context_manager: Arc<ContextManager>,
    ) -> Self {
        info!("Creating new AgentIntegration with config: {:?}", config);

        Self {
            config,
            agent_manager,
            memory_manager,
            context_manager,
            priority_mapping: RwLock::new(AgentPriorityMapping::default()),
            collaborations: RwLock::new(HashMap::new()),
        }
    }

    /// Gets the priority for a task based on the agent's role
    pub async fn get_task_priority(&self, agent_id: &AgentId) -> PrismaResult<TaskPriority> {
        let agent_manager = self.agent_manager.lock().await;
        let agent_result = agent_manager.get_agent(agent_id).await?;
        let agent = agent_result.ok_or_else(|| {
            GenericError::from(format!("Agent with ID {} not found", agent_id))
        })?;

        let priority_mapping = self.priority_mapping.read().unwrap();

        // Get the first role if any
        let priority = if !agent.roles.is_empty() {
            if let Some(role) = agent.roles.first() {
                priority_mapping.role_priority_map
                    .get(&role.name)
                    .copied()
                    .unwrap_or(priority_mapping.default_priority)
            } else {
                priority_mapping.default_priority
            }
        } else {
            priority_mapping.default_priority
        };

        debug!("Determined priority {:?} for agent {}", priority, agent_id);
        Ok(priority)
    }

    /// Updates the priority mapping for agent roles
    pub fn update_priority_mapping(&self, mapping: AgentPriorityMapping) {
        info!("Updating agent priority mapping");
        let mut priority_mapping = self.priority_mapping.write().unwrap();
        *priority_mapping = mapping;
    }

    /// Creates a new collaboration between agents
    pub async fn create_collaboration(
        &self,
        task_id: TaskId,
        agent_ids: Vec<AgentId>,
    ) -> PrismaResult<String> {
        if !self.config.enable_collaboration {
            return Err(GenericError::from("Agent collaboration is disabled"));
        }

        if agent_ids.len() > self.config.max_collaborators {
            return Err(GenericError::from(format!(
                "Too many collaborators: {} (max: {})",
                agent_ids.len(),
                self.config.max_collaborators
            )));
        }

        // Verify that all agents exist
        let agent_manager = self.agent_manager.lock().await;
        for agent_id in &agent_ids {
            let agent_result = agent_manager.get_agent(agent_id).await?;
            if agent_result.is_none() {
                return Err(GenericError::from(format!(
                    "Agent with ID {} not found",
                    agent_id
                )));
            }
        }

        // Create a new collaboration
        let collaboration_id = format!("collab_{}", uuid::Uuid::new_v4());
        let collaboration = AgentCollaboration {
            id: collaboration_id.clone(),
            agent_ids: agent_ids.clone(),
            task_id,
            status: CollaborationStatus::Pending,
            agent_results: agent_ids.iter().map(|id| (id.clone(), None)).collect(),
        };

        // Store the collaboration
        {
            let mut collaborations = self.collaborations.write().unwrap();
            collaborations.insert(collaboration_id.clone(), collaboration);
        }

        info!(
            "Created collaboration {} for task {} with agents {:?}",
            collaboration_id, task_id, agent_ids
        );

        Ok(collaboration_id)
    }

    /// Updates the status of a collaboration
    pub fn update_collaboration_status(
        &self,
        collaboration_id: &str,
        status: CollaborationStatus,
    ) -> PrismaResult<()> {
        let mut collaborations = self.collaborations.write().unwrap();
        let collaboration = collaborations.get_mut(collaboration_id).ok_or_else(|| {
            GenericError::from(format!("Collaboration with ID {} not found", collaboration_id))
        })?;

        collaboration.status = status;
        info!(
            "Updated collaboration {} status to {:?}",
            collaboration_id, status
        );

        Ok(())
    }

    /// Adds a result from an agent to a collaboration
    pub fn add_collaboration_result(
        &self,
        collaboration_id: &str,
        agent_id: &AgentId,
        result: String,
    ) -> PrismaResult<()> {
        let mut collaborations = self.collaborations.write().unwrap();
        let collaboration = collaborations.get_mut(collaboration_id).ok_or_else(|| {
            GenericError::from(format!("Collaboration with ID {} not found", collaboration_id))
        })?;

        if !collaboration.agent_ids.contains(agent_id) {
            return Err(GenericError::from(format!(
                "Agent {} is not part of collaboration {}",
                agent_id, collaboration_id
            )));
        }

        collaboration.agent_results.insert(agent_id.clone(), Some(result));
        debug!(
            "Added result from agent {} to collaboration {}",
            agent_id, collaboration_id
        );

        // Check if all agents have submitted results
        let all_results_submitted = collaboration.agent_results.values().all(|r| r.is_some());
        if all_results_submitted {
            collaboration.status = CollaborationStatus::Complete;
            info!(
                "All results submitted for collaboration {}, marking as complete",
                collaboration_id
            );
        }

        Ok(())
    }

    /// Gets the results of a collaboration
    pub fn get_collaboration_results(
        &self,
        collaboration_id: &str,
    ) -> PrismaResult<HashMap<AgentId, Option<String>>> {
        let collaborations = self.collaborations.read().unwrap();
        let collaboration = collaborations.get(collaboration_id).ok_or_else(|| {
            GenericError::from(format!("Collaboration with ID {} not found", collaboration_id))
        })?;

        Ok(collaboration.agent_results.clone())
    }

    /// Sends a message from one agent to another
    pub async fn send_agent_message(
        &self,
        from_agent_id: &AgentId,
        to_agent_id: &AgentId,
        message: String,
    ) -> PrismaResult<()> {
        if !self.config.enable_communication {
            return Err(GenericError::from("Agent communication is disabled"));
        }

        // Verify that both agents exist
        let agent_manager = self.agent_manager.lock().await;
        let from_agent_result = agent_manager.get_agent(from_agent_id).await?;
        if from_agent_result.is_none() {
            return Err(GenericError::from(format!(
                "Agent with ID {} not found",
                from_agent_id
            )));
        }

        let to_agent_result = agent_manager.get_agent(to_agent_id).await?;
        if to_agent_result.is_none() {
            return Err(GenericError::from(format!(
                "Agent with ID {} not found",
                to_agent_id
            )));
        }

        // In a real implementation, this would send the message through a messaging system
        // For now, we'll just log it
        info!(
            "Message from agent {} to agent {}: {}",
            from_agent_id, to_agent_id, message
        );

        Ok(())
    }

    /// Gets agent-specific memory for a task
    pub async fn get_agent_memory(
        &self,
        agent_id: &AgentId,
        task_id: TaskId,
        key: &str,
    ) -> PrismaResult<Option<String>> {
        // Verify that the agent exists
        let agent_manager = self.agent_manager.lock().await;
        let agent_result = agent_manager.get_agent(agent_id).await?;
        if agent_result.is_none() {
            return Err(GenericError::from(format!(
                "Agent with ID {} not found",
                agent_id
            )));
        }

        // Create a memory key specific to this agent and task
        let memory_key = format!("agent_{}:task_{}:{}", agent_id, task_id, key);

        // Retrieve the memory
        let memory_option = self.memory_manager.retrieve::<String>(&memory_key)?;

        debug!(
            "Retrieved memory for agent {} and task {}: {:?}",
            agent_id, task_id, memory_option
        );

        Ok(memory_option)
    }

    /// Sets agent-specific memory for a task
    pub async fn set_agent_memory(
        &self,
        agent_id: &AgentId,
        task_id: TaskId,
        key: &str,
        value: String,
    ) -> PrismaResult<()> {
        // Verify that the agent exists
        let agent_manager = self.agent_manager.lock().await;
        let agent_result = agent_manager.get_agent(agent_id).await?;
        if agent_result.is_none() {
            return Err(GenericError::from(format!(
                "Agent with ID {} not found",
                agent_id
            )));
        }

        // Create a memory key specific to this agent and task
        let memory_key = format!("agent_{}:task_{}:{}", agent_id, task_id, key);

        // Store the string value directly
        self.memory_manager.store(&memory_key, value)?;

        debug!(
            "Stored memory for agent {} and task {}",
            agent_id, task_id
        );

        Ok(())
    }

    /// Gets agent-specific context for a task
    pub async fn get_agent_context(
        &self,
        agent_id: &AgentId,
        task_id: TaskId,
    ) -> PrismaResult<HashMap<String, String>> {
        // Verify that the agent exists
        let agent_manager = self.agent_manager.lock().await;
        let agent_result = agent_manager.get_agent(agent_id).await?;
        if agent_result.is_none() {
            return Err(GenericError::from(format!(
                "Agent with ID {} not found",
                agent_id
            )));
        }

        // Create a context key specific to this agent and task
        let context_key = format!("agent_{}:task_{}", agent_id, task_id);

        // Retrieve the context
        let context = self.context_manager.get_context(&context_key)?;

        debug!(
            "Retrieved context for agent {} and task {}: {:?}",
            agent_id, task_id, context
        );

        Ok(context)
    }

    /// Sets agent-specific context for a task
    pub async fn set_agent_context(
        &self,
        agent_id: &AgentId,
        task_id: TaskId,
        context: HashMap<String, String>,
    ) -> PrismaResult<()> {
        // Verify that the agent exists
        let agent_manager = self.agent_manager.lock().await;
        let agent_result = agent_manager.get_agent(agent_id).await?;
        if agent_result.is_none() {
            return Err(GenericError::from(format!(
                "Agent with ID {} not found",
                agent_id
            )));
        }

        // Create a context key specific to this agent and task
        let context_key = format!("agent_{}:task_{}", agent_id, task_id);

        // Store the context
        self.context_manager.set_context(&context_key, context)?;

        debug!(
            "Stored context for agent {} and task {}",
            agent_id, task_id
        );

        Ok(())
    }
}