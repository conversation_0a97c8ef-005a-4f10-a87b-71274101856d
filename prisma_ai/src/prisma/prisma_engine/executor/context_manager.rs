// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/context_manager.rs
// =================================================================================================
// Purpose: Provides context management services for the executor system. This includes creating,
// retrieving, and managing execution contexts for tasks, as well as providing context-aware
// execution environments.
//
// Integration:
// - `executor.rs`: Uses ContextManager to create and manage execution contexts
// - `memory_manager.rs`: Coordinates with MemoryManager for context-aware memory operations
// - `cache_manager.rs`: Coordinates with CacheManager for context-aware caching
// =================================================================================================

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tracing::{debug, error, info, warn};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::types::TaskId;

/// Configuration for the ContextManager
#[derive(Debug, Clone)]
pub struct ContextManagerConfig {
    /// Maximum number of contexts to keep in memory
    pub max_contexts: usize,

    /// Time-to-live for contexts in seconds
    pub context_ttl_seconds: u64,

    /// Whether to enable context inheritance
    pub enable_context_inheritance: bool,

    /// Whether to enable context sharing between tasks
    pub enable_context_sharing: bool,
}

impl Default for ContextManagerConfig {
    fn default() -> Self {
        Self {
            max_contexts: 1000,
            context_ttl_seconds: 3600, // 1 hour
            enable_context_inheritance: true,
            enable_context_sharing: true,
        }
    }
}

/// Metadata for a context
#[derive(Debug, Clone)]
struct ContextMetadata {
    /// When the context was created
    created_at: Instant,

    /// When the context was last accessed
    last_accessed: Instant,

    /// Number of times the context has been accessed
    access_count: usize,

    /// Task ID associated with this context
    task_id: Option<TaskId>,

    /// Parent context key, if any
    parent_context: Option<String>,
}

/// Statistics for the ContextManager
#[derive(Debug, Clone)]
pub struct ContextManagerStats {
    /// Number of contexts in memory
    pub context_count: usize,

    /// Number of context hits
    pub context_hits: usize,

    /// Number of context misses
    pub context_misses: usize,

    /// Number of context evictions
    pub context_evictions: usize,
}

/// Provides context management services for the executor system
pub struct ContextManager {
    /// Configuration for the ContextManager
    config: ContextManagerConfig,

    /// The contexts themselves
    contexts: RwLock<HashMap<String, (HashMap<String, String>, ContextMetadata)>>,

    /// Statistics for the ContextManager
    stats: RwLock<ContextManagerStats>,
}

impl ContextManager {
    /// Creates a new ContextManager with the given configuration
    pub fn new(config: ContextManagerConfig) -> Self {
        info!("Creating new ContextManager with config: {:?}", config);

        Self {
            config,
            contexts: RwLock::new(HashMap::new()),
            stats: RwLock::new(ContextManagerStats {
                context_count: 0,
                context_hits: 0,
                context_misses: 0,
                context_evictions: 0,
            }),
        }
    }

    /// Creates a new ContextManager with default configuration
    pub fn default() -> Self {
        Self::new(ContextManagerConfig::default())
    }

    /// Creates a new context for a task
    pub fn create_context(
        &self,
        task_id: TaskId,
        parent_context: Option<String>,
    ) -> PrismaResult<String> {
        let context_key = format!("context_{}", task_id);
        debug!(
            "Creating context for task {} with key {}",
            task_id, context_key
        );

        // Check if we need to evict contexts to make room
        self.ensure_capacity()?;

        // Create a new context
        let mut context = HashMap::new();

        // Inherit from parent context if enabled and parent exists
        if self.config.enable_context_inheritance && parent_context.is_some() {
            let parent_key = parent_context.as_ref().unwrap();
            let contexts = self.contexts.read().unwrap();

            if let Some((parent_context, _)) = contexts.get(parent_key) {
                // Copy values from parent context
                for (key, value) in parent_context {
                    context.insert(key.clone(), value.clone());
                }

                debug!(
                    "Inherited {} values from parent context {}",
                    parent_context.len(),
                    parent_key
                );
            } else {
                warn!("Parent context {} not found", parent_key);
            }
        }

        // Create context metadata
        let metadata = ContextMetadata {
            created_at: Instant::now(),
            last_accessed: Instant::now(),
            access_count: 0,
            task_id: Some(task_id),
            parent_context,
        };

        // Store the context
        {
            let mut contexts = self.contexts.write().unwrap();
            contexts.insert(context_key.clone(), (context, metadata));

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_count = contexts.len();
        }

        debug!("Created context for task {} with key {}", task_id, context_key);
        Ok(context_key)
    }

    /// Gets a context by key
    pub fn get_context(&self, key: &str) -> PrismaResult<HashMap<String, String>> {
        debug!("Getting context with key {}", key);

        let mut contexts = self.contexts.write().unwrap();
        if let Some((context, mut metadata)) = contexts.remove(key) {
            // Update access metadata
            metadata.last_accessed = Instant::now();
            metadata.access_count += 1;

            // Put the context back
            contexts.insert(key.to_string(), (context.clone(), metadata));

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_hits += 1;

            debug!("Context hit for key {}", key);
            Ok(context)
        } else {
            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_misses += 1;

            debug!("Context miss for key {}", key);
            Ok(HashMap::new())
        }
    }

    /// Sets a context by key
    pub fn set_context(
        &self,
        key: &str,
        context: HashMap<String, String>,
    ) -> PrismaResult<()> {
        debug!("Setting context with key {}", key);

        let mut contexts = self.contexts.write().unwrap();
        if let Some((_, metadata)) = contexts.remove(key) {
            // Update the context with the new values
            contexts.insert(key.to_string(), (context, metadata));
            debug!("Updated existing context with key {}", key);
        } else {
            // Check if we need to evict contexts to make room
            if contexts.len() >= self.config.max_contexts {
                self.evict_oldest_context()?;
            }

            // Create a new context with default metadata
            let metadata = ContextMetadata {
                created_at: Instant::now(),
                last_accessed: Instant::now(),
                access_count: 0,
                task_id: None,
                parent_context: None,
            };

            contexts.insert(key.to_string(), (context, metadata));
            debug!("Created new context with key {}", key);

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_count = contexts.len();
        }

        Ok(())
    }

    /// Sets a value in a context
    pub fn set_context_value(
        &self,
        key: &str,
        value_key: &str,
        value: String,
    ) -> PrismaResult<()> {
        debug!("Setting value {} in context {}", value_key, key);

        let mut contexts = self.contexts.write().unwrap();
        if let Some((mut context, metadata)) = contexts.remove(key) {
            // Update the context with the new value
            context.insert(value_key.to_string(), value);
            contexts.insert(key.to_string(), (context, metadata));
            debug!("Updated value {} in context {}", value_key, key);
            Ok(())
        } else {
            error!("Context not found: {}", key);
            Err(GenericError::from(format!("Context not found: {}", key)))
        }
    }

    /// Gets a value from a context
    pub fn get_context_value(
        &self,
        key: &str,
        value_key: &str,
    ) -> PrismaResult<Option<String>> {
        debug!("Getting value {} from context {}", value_key, key);

        let mut contexts = self.contexts.write().unwrap();
        if let Some((context, mut metadata)) = contexts.remove(key) {
            // Update access metadata
            metadata.last_accessed = Instant::now();
            metadata.access_count += 1;

            // Get the value
            let value = context.get(value_key).cloned();

            // Put the context back
            contexts.insert(key.to_string(), (context, metadata));

            // Update stats
            let mut stats = self.stats.write().unwrap();
            if value.is_some() {
                stats.context_hits += 1;
                debug!("Value {} found in context {}", value_key, key);
            } else {
                stats.context_misses += 1;
                debug!("Value {} not found in context {}", value_key, key);
            }

            Ok(value)
        } else {
            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_misses += 1;

            debug!("Context not found: {}", key);
            Ok(None)
        }
    }

    /// Removes a context
    pub fn remove_context(&self, key: &str) -> PrismaResult<bool> {
        debug!("Removing context with key {}", key);

        let mut contexts = self.contexts.write().unwrap();
        let removed = contexts.remove(key).is_some();

        if removed {
            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_count = contexts.len();
            stats.context_evictions += 1;

            debug!("Removed context with key {}", key);
        } else {
            debug!("Context not found: {}", key);
        }

        Ok(removed)
    }

    /// Clears all contexts for a task
    pub fn clear_task_contexts(&self, task_id: TaskId) -> PrismaResult<usize> {
        debug!("Clearing contexts for task {}", task_id);

        let mut contexts = self.contexts.write().unwrap();
        let mut keys_to_remove = Vec::new();

        // Find all contexts for this task
        for (key, (_, metadata)) in contexts.iter() {
            if let Some(context_task_id) = metadata.task_id {
                if context_task_id == task_id {
                    keys_to_remove.push(key.clone());
                }
            }
        }

        // Remove the contexts
        for key in &keys_to_remove {
            contexts.remove(key);
        }

        // Update stats
        let mut stats = self.stats.write().unwrap();
        stats.context_count = contexts.len();
        stats.context_evictions += keys_to_remove.len();

        debug!(
            "Cleared {} contexts for task {}",
            keys_to_remove.len(),
            task_id
        );
        Ok(keys_to_remove.len())
    }

    /// Gets the current context statistics
    pub fn get_stats(&self) -> ContextManagerStats {
        self.stats.read().unwrap().clone()
    }

    /// Ensures that the context manager has enough capacity for a new context
    fn ensure_capacity(&self) -> PrismaResult<()> {
        let contexts = self.contexts.read().unwrap();

        // Check if we need to evict contexts
        if contexts.len() >= self.config.max_contexts {
            drop(contexts); // Release the read lock
            self.evict_oldest_context()?;
        }

        Ok(())
    }

    /// Evicts the oldest context
    fn evict_oldest_context(&self) -> PrismaResult<()> {
        let mut contexts = self.contexts.write().unwrap();

        // Find the oldest context
        let mut oldest_time = Instant::now();
        let mut oldest_key = None;

        for (key, (_, metadata)) in contexts.iter() {
            if metadata.created_at < oldest_time {
                oldest_time = metadata.created_at;
                oldest_key = Some(key.clone());
            }
        }

        // Remove the oldest context
        if let Some(key) = oldest_key {
            contexts.remove(&key);

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.context_count = contexts.len();
            stats.context_evictions += 1;

            debug!("Evicted oldest context with key {}", key);
        }

        Ok(())
    }

    /// Cleans up expired contexts
    pub fn cleanup_expired(&self) -> PrismaResult<usize> {
        debug!("Cleaning up expired contexts");

        let mut contexts = self.contexts.write().unwrap();
        let mut keys_to_remove = Vec::new();
        let now = Instant::now();
        let ttl = Duration::from_secs(self.config.context_ttl_seconds);

        // Find all expired contexts
        for (key, (_, metadata)) in contexts.iter() {
            if now.duration_since(metadata.created_at) > ttl {
                keys_to_remove.push(key.clone());
            }
        }

        // Remove the contexts
        for key in &keys_to_remove {
            contexts.remove(key);
        }

        // Update stats
        let mut stats = self.stats.write().unwrap();
        stats.context_count = contexts.len();
        stats.context_evictions += keys_to_remove.len();

        debug!("Cleaned up {} expired contexts", keys_to_remove.len());
        Ok(keys_to_remove.len())
    }
}