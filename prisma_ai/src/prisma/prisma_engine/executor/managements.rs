// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/managements.rs
// =================================================================================================
// Purpose: Provides management utilities for the executor module. This includes monitoring,
// controlling, and coordinating the various components of the executor system.
//
// Integration:
// - `executor.rs`: Uses ManagementService to monitor and control the executor
// - `dispatcher.rs`: Coordinates with <PERSON>spatcher for task routing
// - `memory_manager.rs`: Coordinates with MemoryManager for memory management
// - `cache_manager.rs`: Coordinates with CacheManager for cache management
// - `context_manager.rs`: Coordinates with ContextManager for context management
// =================================================================================================

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tokio::task::JoinHandle;
use tokio::time::interval;
use tracing::{debug, error, info};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::TaskId;

use super::cache_manager::{CacheManager, CacheManagerStats};
use super::context_manager::{ContextManager, ContextManagerStats};
use super::dispatcher::{Dispatcher, DispatcherStats};
use super::memory_manager::{MemoryManager, MemoryManagerStats};

/// Configuration for the ManagementService
#[derive(Debug, Clone)]
pub struct ManagementConfig {
    /// Interval for periodic cleanup in seconds
    pub cleanup_interval_seconds: u64,

    /// Whether to enable automatic cleanup
    pub enable_auto_cleanup: bool,

    /// Maximum memory usage in bytes before triggering cleanup
    pub max_memory_usage_bytes: usize,

    /// Maximum cache usage in bytes before triggering cleanup
    pub max_cache_usage_bytes: usize,
}

impl Default for ManagementConfig {
    fn default() -> Self {
        Self {
            cleanup_interval_seconds: 300, // 5 minutes
            enable_auto_cleanup: true,
            max_memory_usage_bytes: 1024 * 1024 * 1024, // 1 GB
            max_cache_usage_bytes: 1024 * 1024 * 100, // 100 MB
        }
    }
}

/// Statistics for the ManagementService
#[derive(Debug, Clone)]
pub struct ManagementStats {
    /// Number of cleanup operations performed
    pub cleanup_operations: usize,

    /// Number of tasks cleaned up
    pub tasks_cleaned_up: usize,

    /// Number of cache items cleaned up
    pub cache_items_cleaned_up: usize,

    /// Number of context items cleaned up
    pub context_items_cleaned_up: usize,

    /// Number of memory items cleaned up
    pub memory_items_cleaned_up: usize,

    /// Last cleanup time
    pub last_cleanup_time: Option<Instant>,

    /// Last cleanup duration
    pub last_cleanup_duration: Option<Duration>,
}

/// Provides management utilities for the executor module
pub struct ManagementService {
    /// Configuration for the ManagementService
    config: ManagementConfig,

    /// Statistics for the ManagementService
    stats: RwLock<ManagementStats>,

    /// Reference to the Dispatcher
    dispatcher: Arc<Dispatcher>,

    /// Reference to the MemoryManager
    memory_manager: Arc<MemoryManager>,

    /// Reference to the CacheManager
    cache_manager: Arc<CacheManager>,

    /// Reference to the ContextManager
    context_manager: Arc<ContextManager>,

    /// Handle for the cleanup task
    cleanup_task: Mutex<Option<JoinHandle<()>>>,

    /// Whether the service is running
    running: RwLock<bool>,
}

impl ManagementService {
    /// Creates a new ManagementService with the given configuration
    pub fn new(
        config: ManagementConfig,
        dispatcher: Arc<Dispatcher>,
        memory_manager: Arc<MemoryManager>,
        cache_manager: Arc<CacheManager>,
        context_manager: Arc<ContextManager>,
    ) -> Self {
        info!("Creating new ManagementService with config: {:?}", config);

        Self {
            config,
            stats: RwLock::new(ManagementStats {
                cleanup_operations: 0,
                tasks_cleaned_up: 0,
                cache_items_cleaned_up: 0,
                context_items_cleaned_up: 0,
                memory_items_cleaned_up: 0,
                last_cleanup_time: None,
                last_cleanup_duration: None,
            }),
            dispatcher,
            memory_manager,
            cache_manager,
            context_manager,
            cleanup_task: Mutex::new(None),
            running: RwLock::new(false),
        }
    }

    /// Starts the management service
    pub async fn start(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if *running {
            return Ok(());
        }

        info!("Starting ManagementService");

        // Start the cleanup task if auto-cleanup is enabled
        if self.config.enable_auto_cleanup {
            let config = self.config.clone();
            let _memory_manager = self.memory_manager.clone();
            let cache_manager = self.cache_manager.clone();
            let context_manager = self.context_manager.clone();
            let stats_lock = Arc::new(RwLock::new(ManagementStats {
                cleanup_operations: 0,
                tasks_cleaned_up: 0,
                cache_items_cleaned_up: 0,
                context_items_cleaned_up: 0,
                memory_items_cleaned_up: 0,
                last_cleanup_time: None,
                last_cleanup_duration: None,
            }));

            let cleanup_task = tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(config.cleanup_interval_seconds));

                loop {
                    interval.tick().await;

                    info!("Running periodic cleanup");
                    let start_time = Instant::now();

                    // Clean up expired cache items
                    let cache_items_cleaned_up = match cache_manager.cleanup_expired() {
                        Ok(count) => count,
                        Err(e) => {
                            error!("Error cleaning up cache: {:?}", e);
                            0
                        }
                    };

                    // Clean up expired contexts
                    let context_items_cleaned_up = match context_manager.cleanup_expired() {
                        Ok(count) => count,
                        Err(e) => {
                            error!("Error cleaning up contexts: {:?}", e);
                            0
                        }
                    };

                    // Update stats
                    let duration = start_time.elapsed();
                    let mut stats = stats_lock.write().unwrap();
                    stats.cleanup_operations += 1;
                    stats.cache_items_cleaned_up += cache_items_cleaned_up;
                    stats.context_items_cleaned_up += context_items_cleaned_up;
                    stats.last_cleanup_time = Some(start_time);
                    stats.last_cleanup_duration = Some(duration);

                    info!(
                        "Periodic cleanup completed in {:?}: {} cache items, {} context items",
                        duration, cache_items_cleaned_up, context_items_cleaned_up
                    );
                }
            });

            let mut cleanup_task_guard = self.cleanup_task.lock().await;
            *cleanup_task_guard = Some(cleanup_task);
        }

        *running = true;
        info!("ManagementService started");

        Ok(())
    }

    /// Stops the management service
    pub async fn stop(&self) -> PrismaResult<()> {
        let mut running = self.running.write().unwrap();
        if !*running {
            return Ok(());
        }

        info!("Stopping ManagementService");

        // Stop the cleanup task
        let mut cleanup_task_guard = self.cleanup_task.lock().await;
        if let Some(task) = cleanup_task_guard.take() {
            task.abort();
            debug!("Cleanup task aborted");
        }

        *running = false;
        info!("ManagementService stopped");

        Ok(())
    }

    /// Runs a manual cleanup operation
    pub async fn run_cleanup(&self) -> PrismaResult<()> {
        info!("Running manual cleanup");
        let start_time = Instant::now();

        // Clean up expired cache items
        let cache_items_cleaned_up = match self.cache_manager.cleanup_expired() {
            Ok(count) => count,
            Err(e) => {
                error!("Error cleaning up cache: {:?}", e);
                0
            }
        };

        // Clean up expired contexts
        let context_items_cleaned_up = match self.context_manager.cleanup_expired() {
            Ok(count) => count,
            Err(e) => {
                error!("Error cleaning up contexts: {:?}", e);
                0
            }
        };

        // Update stats
        let duration = start_time.elapsed();
        let mut stats = self.stats.write().unwrap();
        stats.cleanup_operations += 1;
        stats.cache_items_cleaned_up += cache_items_cleaned_up;
        stats.context_items_cleaned_up += context_items_cleaned_up;
        stats.last_cleanup_time = Some(start_time);
        stats.last_cleanup_duration = Some(duration);

        info!(
            "Manual cleanup completed in {:?}: {} cache items, {} context items",
            duration, cache_items_cleaned_up, context_items_cleaned_up
        );

        Ok(())
    }

    /// Cleans up resources for a specific task
    pub async fn cleanup_task(&self, task_id: TaskId) -> PrismaResult<()> {
        info!("Cleaning up resources for task {}", task_id);

        // Clean up cache for the task
        let cache_items_cleaned_up = match self.cache_manager.clear_task_cache(task_id) {
            Ok(count) => count,
            Err(e) => {
                error!("Error cleaning up cache for task {}: {:?}", task_id, e);
                0
            }
        };

        // Clean up contexts for the task
        let context_items_cleaned_up = match self.context_manager.clear_task_contexts(task_id) {
            Ok(count) => count,
            Err(e) => {
                error!("Error cleaning up contexts for task {}: {:?}", task_id, e);
                0
            }
        };

        // Clean up memory for the task
        let memory_items_cleaned_up = match self.memory_manager.clear_task_memory(task_id) {
            Ok(()) => 1,
            Err(e) => {
                error!("Error cleaning up memory for task {}: {:?}", task_id, e);
                0
            }
        };

        // Update stats
        let mut stats = self.stats.write().unwrap();
        stats.tasks_cleaned_up += 1;
        stats.cache_items_cleaned_up += cache_items_cleaned_up;
        stats.context_items_cleaned_up += context_items_cleaned_up;
        stats.memory_items_cleaned_up += memory_items_cleaned_up;

        info!(
            "Cleaned up resources for task {}: {} cache items, {} context items, {} memory items",
            task_id, cache_items_cleaned_up, context_items_cleaned_up, memory_items_cleaned_up
        );

        Ok(())
    }

    /// Gets the current management statistics
    pub fn get_stats(&self) -> ManagementStats {
        self.stats.read().unwrap().clone()
    }

    /// Gets the current dispatcher statistics
    pub fn get_dispatcher_stats(&self) -> DispatcherStats {
        self.dispatcher.get_stats()
    }

    /// Gets the current memory manager statistics
    pub fn get_memory_manager_stats(&self) -> MemoryManagerStats {
        self.memory_manager.get_stats()
    }

    /// Gets the current cache manager statistics
    pub fn get_cache_manager_stats(&self) -> CacheManagerStats {
        self.cache_manager.get_stats()
    }

    /// Gets the current context manager statistics
    pub fn get_context_manager_stats(&self) -> ContextManagerStats {
        self.context_manager.get_stats()
    }

    /// Gets a comprehensive status report
    pub fn get_status_report(&self) -> HashMap<String, String> {
        let mut report = HashMap::new();

        // Management stats
        let management_stats = self.get_stats();
        report.insert(
            "management.cleanup_operations".to_string(),
            management_stats.cleanup_operations.to_string(),
        );
        report.insert(
            "management.tasks_cleaned_up".to_string(),
            management_stats.tasks_cleaned_up.to_string(),
        );
        report.insert(
            "management.cache_items_cleaned_up".to_string(),
            management_stats.cache_items_cleaned_up.to_string(),
        );
        report.insert(
            "management.context_items_cleaned_up".to_string(),
            management_stats.context_items_cleaned_up.to_string(),
        );
        report.insert(
            "management.memory_items_cleaned_up".to_string(),
            management_stats.memory_items_cleaned_up.to_string(),
        );

        // Dispatcher stats
        let dispatcher_stats = self.get_dispatcher_stats();
        report.insert(
            "dispatcher.tasks_dispatched".to_string(),
            dispatcher_stats.tasks_dispatched.to_string(),
        );
        report.insert(
            "dispatcher.dispatch_errors".to_string(),
            dispatcher_stats.dispatch_errors.to_string(),
        );

        // Memory manager stats
        let memory_stats = self.get_memory_manager_stats();
        report.insert(
            "memory.cache_size_bytes".to_string(),
            memory_stats.cache_size_bytes.to_string(),
        );
        report.insert(
            "memory.cache_items".to_string(),
            memory_stats.cache_items.to_string(),
        );

        // Cache manager stats
        let cache_stats = self.get_cache_manager_stats();
        report.insert(
            "cache.cache_size_bytes".to_string(),
            cache_stats.cache_size_bytes.to_string(),
        );
        report.insert(
            "cache.cache_items".to_string(),
            cache_stats.cache_items.to_string(),
        );
        report.insert(
            "cache.cache_hits".to_string(),
            cache_stats.cache_hits.to_string(),
        );
        report.insert(
            "cache.cache_misses".to_string(),
            cache_stats.cache_misses.to_string(),
        );

        // Context manager stats
        let context_stats = self.get_context_manager_stats();
        report.insert(
            "context.context_count".to_string(),
            context_stats.context_count.to_string(),
        );
        report.insert(
            "context.context_hits".to_string(),
            context_stats.context_hits.to_string(),
        );
        report.insert(
            "context.context_misses".to_string(),
            context_stats.context_misses.to_string(),
        );

        report
    }
}