// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/mod.rs
// =================================================================================================
// Purpose: Entry point for the monitor module. This module is responsible for monitoring system
// resources and internal Prisma Engine components. It provides a unified interface for collecting
// metrics and calculating scores that are used by the decision maker to optimize task execution.
//
// Integration:
// - Internal Dependencies:
//   - prisma_engine.rs: Uses this module to monitor system and internal resources
//   - decision_maker: Uses the scores provided by this module to make execution decisions
//   - system/: Submodule for system resource monitoring
//   - prisma/: Submodule for Prisma-internal monitoring
//
// - External Dependencies:
//   - sysinfo: For cross-platform system information collection
//   - tokio: For async runtime and background monitoring tasks
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all metrics
// - macOS: Limited GPU monitoring through ROCm
// - GPU: NVIDIA support on Linux, ROCm support on macOS
// =================================================================================================

// Declare submodules for different monitoring aspects
pub mod system;
pub mod prisma; // For prisma-internal monitoring (e.g., queues)

// Declare other files in the monitor module
pub mod types;
pub mod traits;
pub mod generics;
pub mod monitor; // Contains the main Monitor struct

// Re-export the main Monitor struct and its config
pub use monitor::Monitor;
pub use types::MonitorConfig;
