// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/mod.rs
// =================================================================================================
// Purpose: Entry point for the Prisma-internal monitoring submodule. This module is responsible for
// monitoring internal Prisma Engine components such as task queues, execution status, and other
// internal metrics that are not directly related to system resources.
//
// Integration:
// - Internal Dependencies:
//   - monitor/monitor.rs: Uses this module to collect Prisma-internal metrics
//   - queue_monitor.rs: Monitors task queue lengths and processing rates
//   - task_monitor.rs: Monitors task execution status and performance
//
// - External Dependencies:
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it monitors internal Prisma components
// =================================================================================================

// Declare submodules for different monitoring aspects
pub mod queue_monitor;
pub mod task_monitor;
pub mod traits;
pub mod types;

// Re-export key components for easier access
pub use queue_monitor::QueueMonitor;
pub use task_monitor::TaskMonitor;
pub use traits::{QueueMonitoring, TaskMonitoring, Monitorable};
pub use types::{
    PrismaMonitorConfig, QueueMetrics, QueueMonitorMetrics,
    TaskMetrics, TaskMonitorMetrics, TaskStatus
};
