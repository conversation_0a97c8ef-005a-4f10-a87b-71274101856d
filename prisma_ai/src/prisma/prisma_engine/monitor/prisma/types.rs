// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/types.rs
// =================================================================================================
// Purpose: Defines types and data structures for Prisma-internal monitoring. These types represent
// the metrics, configurations, and states used by the monitoring components to track and report
// on the internal operations of the Prisma Engine.
//
// Integration:
// - Internal Dependencies:
//   - prisma/mod.rs: Exports these types
//   - prisma/queue_monitor.rs: Uses these types for queue monitoring
//   - prisma/task_monitor.rs: Uses these types for task monitoring
//
// - External Dependencies:
//   - serde: For serialization/deserialization of monitoring data
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it defines data structures, not implementations
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};

use crate::prisma::prisma_engine::types::{TaskCategory, TaskId, TaskPriority};

/// Configuration for Prisma-internal monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrismaMonitorConfig {
    /// Interval in milliseconds for polling queue metrics
    pub queue_poll_interval_ms: u64,

    /// Interval in milliseconds for polling task metrics
    pub task_poll_interval_ms: u64,

    /// Maximum number of tasks to track in history
    pub max_task_history: usize,

    /// Whether to enable detailed task tracking
    pub enable_detailed_task_tracking: bool,
}

impl Default for PrismaMonitorConfig {
    fn default() -> Self {
        Self {
            queue_poll_interval_ms: 1000, // 1 second
            task_poll_interval_ms: 1000,  // 1 second
            max_task_history: 1000,       // Track last 1000 tasks
            enable_detailed_task_tracking: true,
        }
    }
}

/// Metrics for a specific task queue
#[derive(Debug, Clone, Serialize)]
pub struct QueueMetrics {
    /// Current length of the queue
    pub length: usize,

    /// Maximum observed length of the queue
    pub max_length: usize,

    /// Average processing time for tasks in this queue
    pub avg_processing_time_ms: f64,

    /// Number of tasks processed by this queue
    pub tasks_processed: usize,

    /// Number of tasks that failed processing in this queue
    pub tasks_failed: usize,

    /// Timestamp of the last update
    #[serde(skip)]
    pub last_updated: Instant,
}

// Custom deserialization for QueueMetrics
impl<'de> Deserialize<'de> for QueueMetrics {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        // Define a temporary struct that matches QueueMetrics but without Instant
        #[derive(Deserialize)]
        struct QueueMetricsTemp {
            length: usize,
            max_length: usize,
            avg_processing_time_ms: f64,
            tasks_processed: usize,
            tasks_failed: usize,
        }

        // Deserialize into the temporary struct
        let temp = QueueMetricsTemp::deserialize(deserializer)?;

        // Convert to the real struct with Instant::now()
        Ok(QueueMetrics {
            length: temp.length,
            max_length: temp.max_length,
            avg_processing_time_ms: temp.avg_processing_time_ms,
            tasks_processed: temp.tasks_processed,
            tasks_failed: temp.tasks_failed,
            last_updated: Instant::now(),
        })
    }
}

impl Default for QueueMetrics {
    fn default() -> Self {
        Self {
            length: 0,
            max_length: 0,
            avg_processing_time_ms: 0.0,
            tasks_processed: 0,
            tasks_failed: 0,
            last_updated: Instant::now(),
        }
    }
}

/// Metrics for all task queues in the system
#[derive(Debug, Clone)]
pub struct QueueMonitorMetrics {
    /// Metrics for each queue, keyed by queue name
    pub queue_metrics: HashMap<String, QueueMetrics>,

    /// Total number of tasks across all queues
    pub total_tasks: usize,

    /// Total number of failed tasks across all queues
    pub total_failed: usize,

    /// Timestamp of the last update
    pub last_updated: Instant,
}

impl Default for QueueMonitorMetrics {
    fn default() -> Self {
        Self {
            queue_metrics: HashMap::new(),
            total_tasks: 0,
            total_failed: 0,
            last_updated: Instant::now(),
        }
    }
}

/// Status of a task
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskStatus {
    /// Task is waiting in a queue
    Queued,

    /// Task is currently being processed
    Processing,

    /// Task completed successfully
    Completed,

    /// Task failed
    Failed,

    /// Task was cancelled
    Cancelled,
}

/// Metrics for a specific task
#[derive(Debug, Clone, Serialize)]
pub struct TaskMetrics {
    /// ID of the task
    pub task_id: TaskId,

    /// Category of the task
    pub category: TaskCategory,

    /// Priority of the task
    pub priority: TaskPriority,

    /// Current status of the task
    pub status: TaskStatus,

    /// Time the task was created
    #[serde(skip)]
    pub created_at: Instant,

    /// Time the task started processing
    #[serde(skip)]
    pub started_at: Option<Instant>,

    /// Time the task completed processing
    #[serde(skip)]
    pub completed_at: Option<Instant>,

    /// Duration the task spent in queue
    pub queue_time: Option<Duration>,

    /// Duration the task spent processing
    pub processing_time: Option<Duration>,

    /// Name of the queue this task was processed in
    pub queue_name: Option<String>,

    /// Error message if the task failed
    pub error_message: Option<String>,
}

// Custom deserialization for TaskMetrics
impl<'de> Deserialize<'de> for TaskMetrics {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        // Define a temporary struct that matches TaskMetrics but without Instant fields
        #[derive(Deserialize)]
        struct TaskMetricsTemp {
            task_id: TaskId,
            category: TaskCategory,
            priority: TaskPriority,
            status: TaskStatus,
            queue_time: Option<Duration>,
            processing_time: Option<Duration>,
            queue_name: Option<String>,
            error_message: Option<String>,
        }

        // Deserialize into the temporary struct
        let temp = TaskMetricsTemp::deserialize(deserializer)?;

        // Convert to the real struct with Instant::now() for created_at
        Ok(TaskMetrics {
            task_id: temp.task_id,
            category: temp.category,
            priority: temp.priority,
            status: temp.status,
            created_at: Instant::now(),
            started_at: None,
            completed_at: None,
            queue_time: temp.queue_time,
            processing_time: temp.processing_time,
            queue_name: temp.queue_name,
            error_message: temp.error_message,
        })
    }
}

impl TaskMetrics {
    /// Create a new TaskMetrics instance for a task
    pub fn new(task_id: TaskId, category: TaskCategory, priority: TaskPriority) -> Self {
        Self {
            task_id,
            category,
            priority,
            status: TaskStatus::Queued,
            created_at: Instant::now(),
            started_at: None,
            completed_at: None,
            queue_time: None,
            processing_time: None,
            queue_name: None,
            error_message: None,
        }
    }
}

/// Metrics for all tasks in the system
#[derive(Debug, Clone)]
pub struct TaskMonitorMetrics {
    /// Metrics for active tasks, keyed by task ID
    pub active_tasks: HashMap<TaskId, TaskMetrics>,

    /// Metrics for completed tasks, in order of completion
    pub completed_tasks: Vec<TaskMetrics>,

    /// Total number of tasks processed
    pub total_tasks: usize,

    /// Total number of successful tasks
    pub successful_tasks: usize,

    /// Total number of failed tasks
    pub failed_tasks: usize,

    /// Average queue time across all tasks
    pub avg_queue_time_ms: f64,

    /// Average processing time across all tasks
    pub avg_processing_time_ms: f64,

    /// Timestamp of the last update
    pub last_updated: Instant,
}

impl Default for TaskMonitorMetrics {
    fn default() -> Self {
        Self {
            active_tasks: HashMap::new(),
            completed_tasks: Vec::new(),
            total_tasks: 0,
            successful_tasks: 0,
            failed_tasks: 0,
            avg_queue_time_ms: 0.0,
            avg_processing_time_ms: 0.0,
            last_updated: Instant::now(),
        }
    }
}