// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/task_monitor.rs
// =================================================================================================
// Purpose: Monitors the execution of tasks within the Prisma Engine. Tracks metrics such as
// execution times, success/failure rates, resource usage, and task throughput. This information
// is used to optimize task execution and provide insights into system performance.
//
// Integration:
// - Internal Dependencies:
//   - prisma/mod.rs: Exports this module
//   - monitor/monitor.rs: Uses this module to collect task metrics
//   - executor/task_executor.rs: Provides task execution data for monitoring
//
// - External Dependencies:
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it monitors internal Prisma components
// =================================================================================================

use async_trait::async_trait;

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};
use serde_json;

use crate::err::{PrismaResult, types::PrismaError};
use crate::err::types::errors_mod::SystemMonitorError;
use crate::prisma::prisma_engine::types::TaskId;
use super::traits::{TaskMonitoring, Monitorable};
use super::types::{PrismaMonitorConfig, TaskMetrics, TaskMonitorMetrics, TaskStatus};

/// Monitors task execution within the Prisma Engine
#[derive(Debug)]
pub struct TaskMonitor {
    /// Configuration for the task monitor
    config: PrismaMonitorConfig,

    /// Metrics for all tasks
    metrics: Arc<RwLock<TaskMonitorMetrics>>,

    /// Background task for monitoring
    monitoring_task: Option<JoinHandle<()>>,

    /// Whether the monitor is running
    is_running: bool,
}

impl TaskMonitor {
    /// Create a new TaskMonitor with the given configuration
    pub fn new(config: PrismaMonitorConfig) -> Self {
        info!("Creating TaskMonitor with config: {:?}", config);
        Self {
            config,
            metrics: Arc::new(RwLock::new(TaskMonitorMetrics::default())),
            monitoring_task: None,
            is_running: false,
        }
    }

    /// Run the monitoring loop in the background
    async fn run_monitoring_loop(
        metrics_arc: Arc<RwLock<TaskMonitorMetrics>>,
        poll_interval: Duration,
    ) {
        info!("Starting task monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            // In a real implementation, this would query the executor for the current state
            // of all tasks. For now, we'll just update the timestamp and clean up old tasks.
            let mut metrics = metrics_arc.write().await;
            metrics.last_updated = Instant::now();

            // Clean up completed tasks if we have too many
            if metrics.completed_tasks.len() > 1000 {
                let excess = metrics.completed_tasks.len() - 1000;
                metrics.completed_tasks.drain(0..excess);
                debug!("Cleaned up {} old completed tasks", excess);
            }

            debug!("Updated task metrics: {} active tasks, {} completed tasks",
                metrics.active_tasks.len(), metrics.completed_tasks.len());
        }
    }

    /// Calculate average times from all tasks
    async fn calculate_averages(&self) -> PrismaResult<()> {
        let mut metrics = self.metrics.write().await;

        let mut total_queue_time_ms = 0.0;
        let mut total_processing_time_ms = 0.0;
        let mut queue_time_count = 0;
        let mut processing_time_count = 0;

        // Calculate from active tasks
        for task in metrics.active_tasks.values() {
            if let Some(queue_time) = task.queue_time {
                total_queue_time_ms += queue_time.as_millis() as f64;
                queue_time_count += 1;
            }

            if let Some(processing_time) = task.processing_time {
                total_processing_time_ms += processing_time.as_millis() as f64;
                processing_time_count += 1;
            }
        }

        // Calculate from completed tasks
        for task in &metrics.completed_tasks {
            if let Some(queue_time) = task.queue_time {
                total_queue_time_ms += queue_time.as_millis() as f64;
                queue_time_count += 1;
            }

            if let Some(processing_time) = task.processing_time {
                total_processing_time_ms += processing_time.as_millis() as f64;
                processing_time_count += 1;
            }
        }

        // Update averages
        metrics.avg_queue_time_ms = if queue_time_count > 0 {
            total_queue_time_ms / queue_time_count as f64
        } else {
            0.0
        };

        metrics.avg_processing_time_ms = if processing_time_count > 0 {
            total_processing_time_ms / processing_time_count as f64
        } else {
            0.0
        };

        debug!("Calculated averages: queue_time={}ms, processing_time={}ms",
            metrics.avg_queue_time_ms, metrics.avg_processing_time_ms);

        Ok(())
    }
}

#[async_trait]
impl TaskMonitoring for TaskMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        if self.is_running {
            warn!("TaskMonitor is already running");
            return Ok(());
        }

        info!("Starting TaskMonitor");

        let metrics_arc = Arc::clone(&self.metrics);
        let poll_interval = Duration::from_millis(self.config.task_poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::run_monitoring_loop(metrics_arc, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        self.is_running = true;

        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        if !self.is_running {
            warn!("TaskMonitor is not running");
            return Ok(());
        }

        info!("Stopping TaskMonitor");

        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("Task monitoring task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("Task monitoring task cancelled as expected"),
                Err(e) => error!("Error waiting for task monitoring task to stop: {:?}", e),
            }
        }

        self.is_running = false;

        Ok(())
    }

    async fn get_metrics(&self) -> PrismaResult<TaskMonitorMetrics> {
        let metrics = self.metrics.read().await;
        Ok(metrics.clone())
    }

    async fn get_task_metrics(&self, task_id: &TaskId) -> PrismaResult<Option<TaskMetrics>> {
        let metrics = self.metrics.read().await;

        // Check active tasks first
        if let Some(task_metrics) = metrics.active_tasks.get(task_id) {
            return Ok(Some(task_metrics.clone()));
        }

        // Then check completed tasks
        for task_metrics in &metrics.completed_tasks {
            if task_metrics.task_id == *task_id {
                return Ok(Some(task_metrics.clone()));
            }
        }

        // Task not found
        Ok(None)
    }

    async fn record_task_created(&mut self, metrics: TaskMetrics) -> PrismaResult<()> {
        let task_id = metrics.task_id;

        let mut task_metrics = self.metrics.write().await;

        // Check if the task already exists
        if task_metrics.active_tasks.contains_key(&task_id) {
            return Err(PrismaError::new(SystemMonitorError::OperationFailed(format!("Task {} already exists", task_id))));
        }

        // Add the task to active tasks
        task_metrics.active_tasks.insert(task_id, metrics);
        task_metrics.total_tasks += 1;

        debug!("Recorded new task: {}", task_id);

        Ok(())
    }

    async fn update_task_status(&mut self, task_id: &TaskId, status: TaskStatus) -> PrismaResult<()> {
        let mut task_metrics = self.metrics.write().await;

        if let Some(metrics) = task_metrics.active_tasks.get_mut(task_id) {
            metrics.status = status;

            debug!("Updated task {} status to {:?}", task_id, status);

            return Ok(());
        }

        Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(format!("Task {} not found", task_id))))
    }

    async fn record_task_started(&mut self, task_id: &TaskId, queue_name: &str) -> PrismaResult<()> {
        let mut task_metrics = self.metrics.write().await;

        if let Some(metrics) = task_metrics.active_tasks.get_mut(task_id) {
            metrics.status = TaskStatus::Processing;
            metrics.started_at = Some(Instant::now());
            metrics.queue_name = Some(queue_name.to_string());

            // Calculate queue time
            if let Some(started_at) = metrics.started_at {
                metrics.queue_time = Some(started_at.duration_since(metrics.created_at));
            }

            debug!("Recorded task {} started in queue {}", task_id, queue_name);

            return Ok(());
        }

        Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(format!("Task {} not found", task_id))))
    }

    async fn record_task_completed(&mut self, task_id: &TaskId, success: bool, error_message: Option<String>) -> PrismaResult<()> {
        let mut task_metrics = self.metrics.write().await;

        if let Some(mut metrics) = task_metrics.active_tasks.remove(task_id) {
            // Update status
            metrics.status = if success {
                TaskStatus::Completed
            } else {
                TaskStatus::Failed
            };

            // Record completion time
            metrics.completed_at = Some(Instant::now());

            // Calculate processing time
            if let (Some(started_at), Some(completed_at)) = (metrics.started_at, metrics.completed_at) {
                metrics.processing_time = Some(completed_at.duration_since(started_at));
            }

            // Record error message if any
            metrics.error_message = error_message;

            // Update success/failure counts
            if success {
                task_metrics.successful_tasks += 1;
            } else {
                task_metrics.failed_tasks += 1;
            }

            // Add to completed tasks
            task_metrics.completed_tasks.push(metrics);

            // Trim completed tasks if needed
            if task_metrics.completed_tasks.len() > self.config.max_task_history {
                task_metrics.completed_tasks.remove(0);
            }

            debug!("Recorded task {} completed with success={}", task_id, success);

            // Recalculate averages
            drop(task_metrics); // Release the lock
            self.calculate_averages().await?;

            return Ok(());
        }

        Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(format!("Task {} not found", task_id))))
    }
}

#[async_trait]
impl Monitorable for TaskMonitor {
    fn get_name(&self) -> &str {
        "TaskMonitor"
    }

    async fn get_status(&self) -> PrismaResult<String> {
        if self.is_running {
            Ok("Running".to_string())
        } else {
            Ok("Stopped".to_string())
        }
    }

    async fn get_metrics_json(&self) -> PrismaResult<String> {
        let metrics = self.metrics.read().await;

        // Convert to a simplified structure that can be serialized
        let simplified_metrics = serde_json::json!({
            "active_tasks": metrics.active_tasks.len(),
            "completed_tasks": metrics.completed_tasks.len(),
            "total_tasks": metrics.total_tasks,
            "successful_tasks": metrics.successful_tasks,
            "failed_tasks": metrics.failed_tasks,
            "avg_queue_time_ms": metrics.avg_queue_time_ms,
            "avg_processing_time_ms": metrics.avg_processing_time_ms,
            "last_updated": format!("{:?} ago", metrics.last_updated.elapsed()),
            "active_task_details": metrics.active_tasks.iter().map(|(id, metrics)| {
                serde_json::json!({
                    "id": id,
                    "category": format!("{:?}", metrics.category),
                    "priority": format!("{:?}", metrics.priority),
                    "status": format!("{:?}", metrics.status),
                    "queue": metrics.queue_name,
                })
            }).collect::<Vec<_>>(),
        });

        match serde_json::to_string_pretty(&simplified_metrics) {
            Ok(json) => Ok(json),
            Err(e) => Err(PrismaError::new(SystemMonitorError::OperationFailed(format!("Failed to serialize metrics: {}", e)))),
        }
    }
}