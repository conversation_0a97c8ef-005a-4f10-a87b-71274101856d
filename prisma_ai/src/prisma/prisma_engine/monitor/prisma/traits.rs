// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/traits.rs
// =================================================================================================
// Purpose: Defines traits for Prisma-internal monitoring components. These traits establish the
// interfaces that different monitoring components must implement, ensuring consistent behavior
// and interoperability across the monitoring subsystem.
//
// Integration:
// - Internal Dependencies:
//   - prisma/mod.rs: Exports these traits
//   - prisma/queue_monitor.rs: Implements these traits for queue monitoring
//   - prisma/task_monitor.rs: Implements these traits for task monitoring
//
// - External Dependencies:
//   - async_trait: For async trait methods
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it defines interfaces, not implementations
// =================================================================================================

use async_trait::async_trait;
use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::TaskId;
use super::types::{QueueMonitorMetrics, TaskMonitorMetrics, TaskMetrics, TaskStatus};

/// Trait for monitoring task queues
#[async_trait]
pub trait QueueMonitoring: Send + Sync {
    /// Start monitoring task queues
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop monitoring task queues
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Get the current queue metrics
    async fn get_metrics(&self) -> PrismaResult<QueueMonitorMetrics>;

    /// Update metrics for a specific queue
    async fn update_queue_metrics(&mut self, queue_name: &str, length: usize) -> PrismaResult<()>;

    /// Record a task being processed by a queue
    async fn record_task_processed(&mut self, queue_name: &str, processing_time_ms: f64, success: bool) -> PrismaResult<()>;
}

/// Trait for monitoring task execution
#[async_trait]
pub trait TaskMonitoring: Send + Sync {
    /// Start monitoring tasks
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop monitoring tasks
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Get the current task metrics
    async fn get_metrics(&self) -> PrismaResult<TaskMonitorMetrics>;

    /// Get metrics for a specific task
    async fn get_task_metrics(&self, task_id: &TaskId) -> PrismaResult<Option<TaskMetrics>>;

    /// Record a new task being created
    async fn record_task_created(&mut self, metrics: TaskMetrics) -> PrismaResult<()>;

    /// Update the status of a task
    async fn update_task_status(&mut self, task_id: &TaskId, status: TaskStatus) -> PrismaResult<()>;

    /// Record a task starting execution
    async fn record_task_started(&mut self, task_id: &TaskId, queue_name: &str) -> PrismaResult<()>;

    /// Record a task completing execution
    async fn record_task_completed(&mut self, task_id: &TaskId, success: bool, error_message: Option<String>) -> PrismaResult<()>;
}

/// Trait for components that can be monitored
#[async_trait]
pub trait Monitorable: Send + Sync {
    /// Get the name of the component
    fn get_name(&self) -> &str;

    /// Get the current status of the component
    async fn get_status(&self) -> PrismaResult<String>;

    /// Get metrics for the component as a JSON string
    async fn get_metrics_json(&self) -> PrismaResult<String>;
}