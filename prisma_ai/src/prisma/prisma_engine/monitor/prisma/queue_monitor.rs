// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/queue_monitor.rs
// =================================================================================================
// Purpose: Monitors the state of task queues within the Prisma Engine. Tracks metrics such as
// queue lengths, processing rates, wait times, and queue health. This information is used by
// the decision maker to optimize task scheduling and resource allocation.
//
// Integration:
// - Internal Dependencies:
//   - prisma/mod.rs: Exports this module
//   - monitor/monitor.rs: Uses this module to collect queue metrics
//   - executor/queue_manager.rs: Provides queue data for monitoring
//
// - External Dependencies:
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it monitors internal Prisma components
// =================================================================================================

use async_trait::async_trait;

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};
use serde_json;

use crate::err::{PrismaResult, types::PrismaError};
use crate::err::types::errors_mod::SystemMonitorError;
use super::traits::{QueueMonitoring, Monitorable};
use super::types::{PrismaMonitorConfig, QueueMetrics, QueueMonitorMetrics};

/// Monitors task queues within the Prisma Engine
#[derive(Debug)]
pub struct QueueMonitor {
    /// Configuration for the queue monitor
    config: PrismaMonitorConfig,

    /// Metrics for all queues
    metrics: Arc<RwLock<QueueMonitorMetrics>>,

    /// Background task for monitoring
    monitoring_task: Option<JoinHandle<()>>,

    /// Whether the monitor is running
    is_running: bool,
}

impl QueueMonitor {
    /// Create a new QueueMonitor with the given configuration
    pub fn new(config: PrismaMonitorConfig) -> Self {
        info!("Creating QueueMonitor with config: {:?}", config);
        Self {
            config,
            metrics: Arc::new(RwLock::new(QueueMonitorMetrics::default())),
            monitoring_task: None,
            is_running: false,
        }
    }

    /// Run the monitoring loop in the background
    async fn run_monitoring_loop(
        metrics_arc: Arc<RwLock<QueueMonitorMetrics>>,
        poll_interval: Duration,
    ) {
        info!("Starting queue monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            // In a real implementation, this would query the executor's queue manager
            // for the current state of all queues. For now, we'll just update the timestamp.
            let mut metrics = metrics_arc.write().await;
            metrics.last_updated = Instant::now();

            // Calculate totals
            let mut total_tasks = 0;
            let mut total_failed = 0;

            for (_, queue_metrics) in metrics.queue_metrics.iter() {
                total_tasks += queue_metrics.tasks_processed;
                total_failed += queue_metrics.tasks_failed;
            }

            metrics.total_tasks = total_tasks;
            metrics.total_failed = total_failed;

            debug!("Updated queue metrics: {} queues, {} total tasks, {} failed tasks",
                metrics.queue_metrics.len(), total_tasks, total_failed);
        }
    }

    /// Get a specific queue's metrics, creating a new entry if it doesn't exist
    async fn get_or_create_queue_metrics(&self, queue_name: &str) -> PrismaResult<QueueMetrics> {
        let metrics = self.metrics.read().await;

        if let Some(queue_metrics) = metrics.queue_metrics.get(queue_name) {
            return Ok(queue_metrics.clone());
        }

        // If we get here, the queue doesn't exist yet, so we need to create it
        drop(metrics); // Release the read lock

        let mut metrics = self.metrics.write().await;

        // Check again in case another thread created it while we were waiting for the write lock
        if let Some(queue_metrics) = metrics.queue_metrics.get(queue_name) {
            return Ok(queue_metrics.clone());
        }

        // Create a new entry
        let queue_metrics = QueueMetrics::default();
        metrics.queue_metrics.insert(queue_name.to_string(), queue_metrics.clone());

        debug!("Created new queue metrics for queue: {}", queue_name);

        Ok(queue_metrics)
    }
}

#[async_trait]
impl QueueMonitoring for QueueMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        if self.is_running {
            warn!("QueueMonitor is already running");
            return Ok(());
        }

        info!("Starting QueueMonitor");

        let metrics_arc = Arc::clone(&self.metrics);
        let poll_interval = Duration::from_millis(self.config.queue_poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::run_monitoring_loop(metrics_arc, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        self.is_running = true;

        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        if !self.is_running {
            warn!("QueueMonitor is not running");
            return Ok(());
        }

        info!("Stopping QueueMonitor");

        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("Queue monitoring task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("Queue monitoring task cancelled as expected"),
                Err(e) => error!("Error waiting for queue monitoring task to stop: {:?}", e),
            }
        }

        self.is_running = false;

        Ok(())
    }

    async fn get_metrics(&self) -> PrismaResult<QueueMonitorMetrics> {
        let metrics = self.metrics.read().await;
        Ok(metrics.clone())
    }

    async fn update_queue_metrics(&mut self, queue_name: &str, length: usize) -> PrismaResult<()> {
        // Ensure the queue exists
        let _ = self.get_or_create_queue_metrics(queue_name).await?;

        let mut metrics = self.metrics.write().await;

        if let Some(queue_metrics) = metrics.queue_metrics.get_mut(queue_name) {
            queue_metrics.length = length;
            queue_metrics.max_length = queue_metrics.max_length.max(length);
            queue_metrics.last_updated = Instant::now();

            debug!("Updated metrics for queue {}: length={}, max_length={}",
                queue_name, length, queue_metrics.max_length);

            return Ok(());
        }

        // This should never happen since we just created the queue if it didn't exist
        Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(format!("Queue {} not found", queue_name))))
    }

    async fn record_task_processed(&mut self, queue_name: &str, processing_time_ms: f64, success: bool) -> PrismaResult<()> {
        // Ensure the queue exists
        let _ = self.get_or_create_queue_metrics(queue_name).await?;

        let mut metrics = self.metrics.write().await;

        if let Some(queue_metrics) = metrics.queue_metrics.get_mut(queue_name) {
            // Update the average processing time
            let old_avg = queue_metrics.avg_processing_time_ms;
            let old_count = queue_metrics.tasks_processed;

            queue_metrics.tasks_processed += 1;
            if !success {
                queue_metrics.tasks_failed += 1;
            }

            // Calculate the new average
            if old_count == 0 {
                queue_metrics.avg_processing_time_ms = processing_time_ms;
            } else {
                queue_metrics.avg_processing_time_ms =
                    (old_avg * old_count as f64 + processing_time_ms) / queue_metrics.tasks_processed as f64;
            }

            queue_metrics.last_updated = Instant::now();

            debug!("Recorded task processed in queue {}: success={}, processing_time={}ms, avg_time={}ms",
                queue_name, success, processing_time_ms, queue_metrics.avg_processing_time_ms);

            return Ok(());
        }

        // This should never happen since we just created the queue if it didn't exist
        Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(format!("Queue {} not found", queue_name))))
    }
}

#[async_trait]
impl Monitorable for QueueMonitor {
    fn get_name(&self) -> &str {
        "QueueMonitor"
    }

    async fn get_status(&self) -> PrismaResult<String> {
        if self.is_running {
            Ok("Running".to_string())
        } else {
            Ok("Stopped".to_string())
        }
    }

    async fn get_metrics_json(&self) -> PrismaResult<String> {
        let metrics = self.metrics.read().await;

        // Convert to a simplified structure that can be serialized
        let simplified_metrics = serde_json::json!({
            "queues": metrics.queue_metrics.len(),
            "total_tasks": metrics.total_tasks,
            "total_failed": metrics.total_failed,
            "last_updated": format!("{:?} ago", metrics.last_updated.elapsed()),
            "queue_details": metrics.queue_metrics.iter().map(|(name, metrics)| {
                serde_json::json!({
                    "name": name,
                    "length": metrics.length,
                    "max_length": metrics.max_length,
                    "avg_processing_time_ms": metrics.avg_processing_time_ms,
                    "tasks_processed": metrics.tasks_processed,
                    "tasks_failed": metrics.tasks_failed,
                })
            }).collect::<Vec<_>>(),
        });

        match serde_json::to_string_pretty(&simplified_metrics) {
            Ok(json) => Ok(json),
            Err(e) => Err(PrismaError::new(SystemMonitorError::OperationFailed(format!("Failed to serialize metrics: {}", e)))),
        }
    }
}