// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/system_info.rs
// =================================================================================================
// Purpose: Main implementation of system resource monitoring. This file contains the core logic for
// collecting system metrics, calculating system scores, and providing this information to the
// decision maker. It orchestrates the monitoring of CPU, memory, disk, network, and GPU resources.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports this module
//   - monitor/monitor.rs: Uses this module to collect system metrics
//   - cpu.rs, memory.rs, disk.rs, network.rs: Used for resource-specific monitoring
//
// - External Dependencies:
//   - sysinfo: For cross-platform system information collection
//   - tokio: For async runtime and background monitoring tasks
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all metrics
// =================================================================================================

use async_trait::async_trait;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{info, error, warn, debug};
use std::collections::HashMap;

// Use crate-level error types and engine types/traits
use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{SystemScore, ResourceType, ResourceUsage};
use crate::prisma::prisma_engine::monitor::types::MonitorConfig; // Use config from parent monitor module
use super::traits::{
    SystemMonitoring, ResourceMonitor,
    CpuMonitoring, MemoryMonitoring, DiskMonitoring, NetworkMonitoring
};
use super::types::{SystemMetrics, SystemMonitorConfig};
use super::{
    cpu::CpuMonitor,
    memory::MemoryMonitor,
    disk::DiskMonitor,
    network::NetworkMonitor,
};

/// Main system monitoring component that orchestrates all resource monitors
#[derive(Debug)]
pub struct SystemInfoMonitor {
    /// Configuration for the system monitor
    config: MonitorConfig,
    /// CPU monitor
    cpu_monitor: Option<CpuMonitor>,
    /// Memory monitor
    memory_monitor: Option<MemoryMonitor>,
    /// Disk monitor
    disk_monitor: Option<DiskMonitor>,
    /// Network monitor
    network_monitor: Option<NetworkMonitor>,
    /// Last calculated system score
    last_system_score: Arc<RwLock<SystemScore>>,
    /// Last collected system metrics
    last_system_metrics: Arc<RwLock<SystemMetrics>>,
    /// Background monitoring task
    monitoring_task: Option<JoinHandle<()>>,
}

impl SystemInfoMonitor {
    /// Creates a new SystemInfoMonitor.
    pub fn new(config: MonitorConfig) -> Self {
        info!("Creating SystemInfoMonitor with config: {:?}", config);

        // Create system monitor config from parent config
        let sys_config = SystemMonitorConfig {
            poll_interval_ms: config.poll_interval_ms,
            monitor_cpu: true,
            monitor_memory: true,
            monitor_disk: true,
            monitor_network: true,
        };

        // Create resource monitors
        let cpu_monitor = Some(CpuMonitor::new(sys_config.clone()));
        let memory_monitor = Some(MemoryMonitor::new(sys_config.clone()));
        let disk_monitor = Some(DiskMonitor::new(sys_config.clone()));
        let network_monitor = Some(NetworkMonitor::new(sys_config.clone()));

        // Initialize empty system score and metrics
        let last_system_score = Arc::new(RwLock::new(SystemScore {
            availability: HashMap::new()
        }));

        let last_system_metrics = Arc::new(RwLock::new(SystemMetrics {
            cpu: None,
            memory: None,
            disk: None,
            network: None,
            timestamp: SystemTime::now(),
        }));

        SystemInfoMonitor {
            config,
            cpu_monitor,
            memory_monitor,
            disk_monitor,
            network_monitor,
            last_system_score,
            last_system_metrics,
            monitoring_task: None,
        }
    }

    /// Fetches the current system resource information from all monitors.
    async fn fetch_system_score(&self) -> SystemScore {
        let mut current_availability = HashMap::new();

        // Get CPU availability
        if let Some(cpu) = &self.cpu_monitor {
            if let Ok(usage) = cpu.get_availability().await {
                current_availability.insert(ResourceType::CPU, usage);
            } else {
                current_availability.insert(ResourceType::CPU, ResourceUsage(100.0));
            }
        } else {
            current_availability.insert(ResourceType::CPU, ResourceUsage(100.0));
        }

        // Get Memory availability
        if let Some(memory) = &self.memory_monitor {
            if let Ok(usage) = memory.get_availability().await {
                current_availability.insert(ResourceType::Memory, usage);
            } else {
                current_availability.insert(ResourceType::Memory, ResourceUsage(100.0));
            }
        } else {
            current_availability.insert(ResourceType::Memory, ResourceUsage(100.0));
        }

        // Get Disk availability
        if let Some(disk) = &self.disk_monitor {
            if let Ok(usage) = disk.get_availability().await {
                current_availability.insert(ResourceType::DiskIO, usage);
            } else {
                current_availability.insert(ResourceType::DiskIO, ResourceUsage(100.0));
            }
        } else {
            current_availability.insert(ResourceType::DiskIO, ResourceUsage(100.0));
        }

        // Get Network availability
        if let Some(network) = &self.network_monitor {
            if let Ok(usage) = network.get_availability().await {
                current_availability.insert(ResourceType::NetworkBandwidth, usage);
            } else {
                current_availability.insert(ResourceType::NetworkBandwidth, ResourceUsage(100.0));
            }
        } else {
            current_availability.insert(ResourceType::NetworkBandwidth, ResourceUsage(100.0));
        }

        // GPU is not monitored in this implementation
        current_availability.insert(ResourceType::GPU, ResourceUsage(100.0));

        debug!("Refreshed system info: CPU Avail={:.1}%, Mem Avail={:.1}%, Disk Avail={:.1}%, Net Avail={:.1}%",
               current_availability.get(&ResourceType::CPU).map_or(0.0, |r| r.0),
               current_availability.get(&ResourceType::Memory).map_or(0.0, |r| r.0),
               current_availability.get(&ResourceType::DiskIO).map_or(0.0, |r| r.0),
               current_availability.get(&ResourceType::NetworkBandwidth).map_or(0.0, |r| r.0));

        SystemScore { availability: current_availability }
    }

    /// Fetches detailed system metrics from all monitors.
    async fn fetch_system_metrics(&self) -> SystemMetrics {
        let mut metrics = SystemMetrics {
            cpu: None,
            memory: None,
            disk: None,
            network: None,
            timestamp: SystemTime::now(),
        };

        // Get CPU metrics
        if let Some(cpu) = &self.cpu_monitor {
            if let Ok(cpu_metrics) = cpu.get_cpu_metrics().await {
                metrics.cpu = Some(cpu_metrics);
            }
        }

        // Get Memory metrics
        if let Some(memory) = &self.memory_monitor {
            if let Ok(memory_metrics) = memory.get_memory_metrics().await {
                metrics.memory = Some(memory_metrics);
            }
        }

        // Get Disk metrics
        if let Some(disk) = &self.disk_monitor {
            if let Ok(disk_metrics) = disk.get_disk_metrics().await {
                metrics.disk = Some(disk_metrics);
            }
        }

        // Get Network metrics
        if let Some(network) = &self.network_monitor {
            if let Ok(network_metrics) = network.get_network_metrics().await {
                metrics.network = Some(network_metrics);
            }
        }

        metrics
    }

    /// The core monitoring loop that runs in the background.
    async fn run_monitoring_loop(
        monitor: Arc<RwLock<SystemInfoMonitor>>,
        poll_interval: Duration
    ) {
        info!("Starting system info monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            let (current_score, current_metrics) = {
                let monitor_guard = monitor.read().await;
                let score = monitor_guard.fetch_system_score().await;
                let metrics = monitor_guard.fetch_system_metrics().await;
                (score, metrics)
            };

            // Update shared score and metrics
            {
                let monitor_guard = monitor.read().await;
                *monitor_guard.last_system_score.write().await = current_score;
                *monitor_guard.last_system_metrics.write().await = current_metrics;
            }
        }
    }

    /// Start all monitoring components.
    pub async fn start(&mut self) -> PrismaResult<()> {
        if self.monitoring_task.is_some() {
            warn!("System info monitoring task is already running.");
            return Ok(());
        }
        info!("Starting SystemInfoMonitor background task.");

        // Start CPU monitor
        if let Some(cpu) = &mut self.cpu_monitor {
            cpu.start().await?;
        }

        // Start Memory monitor
        if let Some(memory) = &mut self.memory_monitor {
            memory.start().await?;
        }

        // Start Disk monitor
        if let Some(disk) = &mut self.disk_monitor {
            disk.start().await?;
        }

        // Start Network monitor
        if let Some(network) = &mut self.network_monitor {
            network.start().await?;
        }

        // Create a shared reference to self for the monitoring loop
        let monitor = Arc::new(RwLock::new(self.clone()));
        let poll_interval = Duration::from_millis(self.config.poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::run_monitoring_loop(monitor, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        Ok(())
    }

    /// Stop all monitoring components.
    pub async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping SystemInfoMonitor background task.");

        // Stop main monitoring task
        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("System info monitoring task stopped gracefully."),
                Err(e) if e.is_cancelled() => info!("System info monitoring task cancelled as expected."),
                Err(e) => error!("Error waiting for system info monitoring task to stop: {:?}", e),
            }
        } else {
            warn!("System info monitoring task was not running.");
        }

        // Stop CPU monitor
        if let Some(cpu) = &mut self.cpu_monitor {
            cpu.stop().await?;
        }

        // Stop Memory monitor
        if let Some(memory) = &mut self.memory_monitor {
            memory.stop().await?;
        }

        // Stop Disk monitor
        if let Some(disk) = &mut self.disk_monitor {
            disk.stop().await?;
        }

        // Stop Network monitor
        if let Some(network) = &mut self.network_monitor {
            network.stop().await?;
        }

        self.monitoring_task = None;
        Ok(())
    }

    /// Get the latest cached system score.
    pub async fn get_last_score(&self) -> SystemScore {
        self.last_system_score.read().await.clone()
    }

    /// Get the latest cached system metrics.
    pub async fn get_last_metrics(&self) -> SystemMetrics {
        self.last_system_metrics.read().await.clone()
    }

    /// Get a reference to the CPU monitor, if available.
    pub fn get_cpu_monitor(&self) -> Option<&CpuMonitor> {
        self.cpu_monitor.as_ref()
    }

    /// Get a reference to the memory monitor, if available.
    pub fn get_memory_monitor(&self) -> Option<&MemoryMonitor> {
        self.memory_monitor.as_ref()
    }

    /// Get a reference to the disk monitor, if available.
    pub fn get_disk_monitor(&self) -> Option<&DiskMonitor> {
        self.disk_monitor.as_ref()
    }

    /// Get a reference to the network monitor, if available.
    pub fn get_network_monitor(&self) -> Option<&NetworkMonitor> {
        self.network_monitor.as_ref()
    }
}

// Implement Clone for SystemInfoMonitor
impl Clone for SystemInfoMonitor {
    fn clone(&self) -> Self {
        // Create a new instance with the same configuration
        // but without starting the monitors
        let mut new_monitor = SystemInfoMonitor::new(self.config.clone());

        // Share the last system score and metrics
        new_monitor.last_system_score = Arc::clone(&self.last_system_score);
        new_monitor.last_system_metrics = Arc::clone(&self.last_system_metrics);

        new_monitor
    }
}

// Implement SystemMonitoring trait for SystemInfoMonitor
#[async_trait]
impl SystemMonitoring for SystemInfoMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        self.start().await
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        self.stop().await
    }

    async fn get_system_score(&self) -> PrismaResult<SystemScore> {
        Ok(self.get_last_score().await)
    }

    async fn get_system_metrics(&self) -> PrismaResult<SystemMetrics> {
        Ok(self.get_last_metrics().await)
    }

    fn get_config(&self) -> SystemMonitorConfig {
        SystemMonitorConfig {
            poll_interval_ms: self.config.poll_interval_ms,
            monitor_cpu: self.cpu_monitor.is_some(),
            monitor_memory: self.memory_monitor.is_some(),
            monitor_disk: self.disk_monitor.is_some(),
            monitor_network: self.network_monitor.is_some(),
        }
    }

    fn set_config(&mut self, config: SystemMonitorConfig) -> PrismaResult<()> {
        self.config.poll_interval_ms = config.poll_interval_ms;

        // Update individual monitor configurations
        if let Some(cpu) = &mut self.cpu_monitor {
            cpu.set_poll_interval(Duration::from_millis(config.poll_interval_ms));
        }

        if let Some(memory) = &mut self.memory_monitor {
            memory.set_poll_interval(Duration::from_millis(config.poll_interval_ms));
        }

        if let Some(disk) = &mut self.disk_monitor {
            disk.set_poll_interval(Duration::from_millis(config.poll_interval_ms));
        }

        if let Some(network) = &mut self.network_monitor {
            network.set_poll_interval(Duration::from_millis(config.poll_interval_ms));
        }

        Ok(())
    }
}

// Implement Drop to ensure all tasks are stopped
impl Drop for SystemInfoMonitor {
    fn drop(&mut self) {
        if let Some(handle) = self.monitoring_task.take() {
            info!("SystemInfoMonitor dropped, aborting monitoring task.");
            handle.abort();
        }
    }
}
