// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/disk.rs
// =================================================================================================
// Purpose: Monitors disk resources and provides detailed disk metrics. This file contains the logic
// for collecting disk-specific information such as disk space, I/O rates, read/write operations,
// and disk health. It provides this information to the system_info module for system score calculation.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports this module
//   - system/system_info.rs: Uses this module for disk monitoring
//   - system/types.rs: Uses types defined in this module
//
// - External Dependencies:
//   - sysinfo: For cross-platform disk information collection
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all disk metrics including detailed I/O statistics
// - macOS: Support for most disk metrics, limited I/O statistics
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};
use sysinfo::{System, DiskKind, Disks};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use super::traits::{ResourceMonitor, DiskMonitoring};
use super::types::{DiskInfo, DiskMetrics, SystemMonitorConfig};

/// Disk monitor that collects and provides disk metrics
#[derive(Debug)]
pub struct DiskMonitor {
    /// Configuration for the disk monitor
    config: SystemMonitorConfig,
    /// System information from sysinfo
    system: Arc<RwLock<System>>,
    /// Cached disk metrics
    metrics: Arc<RwLock<DiskMetrics>>,
    /// Background monitoring task
    monitoring_task: Option<JoinHandle<()>>,
    /// Previous disk I/O stats for calculating rates
    previous_stats: Arc<RwLock<Option<(SystemTime, HashMap<String, (u64, u64)>)>>>,
}

impl DiskMonitor {
    /// Create a new disk monitor
    pub fn new(config: SystemMonitorConfig) -> Self {
        info!("Creating DiskMonitor with poll interval: {}ms", config.poll_interval_ms);

        // Initialize system with disk information
        let system = System::new();

        // Initialize disk metrics with empty values
        let metrics = DiskMetrics {
            disks: HashMap::new(),
            total_iops: None,
            total_throughput_bytes_per_sec: None,
            timestamp: SystemTime::now(),
        };

        DiskMonitor {
            config,
            system: Arc::new(RwLock::new(system)),
            metrics: Arc::new(RwLock::new(metrics)),
            monitoring_task: None,
            previous_stats: Arc::new(RwLock::new(None)),
        }
    }

    /// Collect disk metrics from the system
    async fn collect_metrics(
        system: &mut System,
        previous_stats: &mut Option<(SystemTime, HashMap<String, (u64, u64)>)>,
    ) -> DiskMetrics {
        // Refresh disk information
        system.refresh_all();

        // Create a Disks instance to get disk information
        let disks = Disks::new_with_refreshed_list();

        let now = SystemTime::now();
        let mut disk_map = HashMap::new();
        let mut total_read_bytes = 0u64;
        let mut total_write_bytes = 0u64;

        // Calculate disk I/O rates based on previous measurements
        let _elapsed_secs = if let Some((prev_time, prev_disk_stats)) = previous_stats {
            let elapsed = now.duration_since(*prev_time).unwrap_or(Duration::from_secs(1));
            let elapsed_secs = elapsed.as_secs_f64();

            // Process each disk
            for disk in disks.list() {
                let mount_point = disk.mount_point().to_string_lossy().to_string();
                let name = mount_point.clone();

                // Get disk space information
                let total_bytes = disk.total_space();
                let available_bytes = disk.available_space();
                let used_bytes = total_bytes.saturating_sub(available_bytes);
                let usage_percent = if total_bytes > 0 {
                    (used_bytes as f64 / total_bytes as f64) * 100.0
                } else {
                    0.0
                };

                // Get disk type
                let disk_type = match disk.kind() {
                    DiskKind::SSD => Some("SSD".to_string()),
                    DiskKind::HDD => Some("HDD".to_string()),
                    _ => Some("Unknown".to_string()),
                };

                // In sysinfo 0.30.13, Disk doesn't have a usage() method
                // We'll use default values for read/write bytes
                let read_bytes = 0u64;
                let written_bytes = 0u64;

                // Calculate I/O rates based on previous measurements
                let (read_bytes_per_sec, write_bytes_per_sec) = if let Some((prev_read, prev_write)) = prev_disk_stats.get(&name) {
                    let read_diff = read_bytes.saturating_sub(*prev_read);
                    let write_diff = written_bytes.saturating_sub(*prev_write);

                    let read_rate = read_diff as f64 / elapsed_secs;
                    let write_rate = write_diff as f64 / elapsed_secs;

                    (Some(read_rate), Some(write_rate))
                } else {
                    (None, None)
                };

                // Estimate IOPS (this is a rough approximation)
                // Assuming average I/O size of 4KB for calculation
                const AVG_IO_SIZE: f64 = 4.0 * 1024.0;
                let read_iops = read_bytes_per_sec.map(|r| r / AVG_IO_SIZE);
                let write_iops = write_bytes_per_sec.map(|w| w / AVG_IO_SIZE);

                // Update totals for throughput calculation
                if let Some(read_rate) = read_bytes_per_sec {
                    total_read_bytes += read_rate as u64;
                }
                if let Some(write_rate) = write_bytes_per_sec {
                    total_write_bytes += write_rate as u64;
                }

                // Create disk info
                let disk_info = DiskInfo {
                    name,
                    total_bytes,
                    available_bytes,
                    used_bytes,
                    usage_percent,
                    disk_type,
                    read_iops,
                    write_iops,
                    read_bytes_per_sec,
                    write_bytes_per_sec,
                };

                disk_map.insert(mount_point, disk_info);
            }

            elapsed_secs
        } else {
            // First run, just collect initial stats
            for disk in disks.list() {
                let mount_point = disk.mount_point().to_string_lossy().to_string();
                let name = mount_point.clone();

                // Get disk space information
                let total_bytes = disk.total_space();
                let available_bytes = disk.available_space();
                let used_bytes = total_bytes.saturating_sub(available_bytes);
                let usage_percent = if total_bytes > 0 {
                    (used_bytes as f64 / total_bytes as f64) * 100.0
                } else {
                    0.0
                };

                // Get disk type
                let disk_type = match disk.kind() {
                    DiskKind::SSD => Some("SSD".to_string()),
                    DiskKind::HDD => Some("HDD".to_string()),
                    _ => Some("Unknown".to_string()),
                };

                // Create disk info with no I/O rates yet
                let disk_info = DiskInfo {
                    name,
                    total_bytes,
                    available_bytes,
                    used_bytes,
                    usage_percent,
                    disk_type,
                    read_iops: None,
                    write_iops: None,
                    read_bytes_per_sec: None,
                    write_bytes_per_sec: None,
                };

                disk_map.insert(mount_point, disk_info);
            }

            1.0 // Default elapsed time for first run
        };

        // Update previous stats for next calculation
        let mut new_prev_stats = HashMap::new();
        for disk in disks.list() {
            let name = disk.mount_point().to_string_lossy().to_string();
            // In sysinfo 0.30.13, Disk doesn't have a usage() method
            // We'll use default values for read/write bytes
            new_prev_stats.insert(name, (0u64, 0u64));
        }
        *previous_stats = Some((now, new_prev_stats));

        // Calculate total IOPS and throughput
        // Assuming average I/O size of 4KB for IOPS calculation
        const AVG_IO_SIZE: f64 = 4.0 * 1024.0;
        let total_read_iops = total_read_bytes as f64 / AVG_IO_SIZE;
        let total_write_iops = total_write_bytes as f64 / AVG_IO_SIZE;
        let total_iops = Some(total_read_iops + total_write_iops);
        let total_throughput_bytes_per_sec = Some(total_read_bytes as f64 + total_write_bytes as f64);

        DiskMetrics {
            disks: disk_map,
            total_iops,
            total_throughput_bytes_per_sec,
            timestamp: now,
        }
    }

    /// Background monitoring loop
    async fn monitoring_loop(
        system: Arc<RwLock<System>>,
        metrics: Arc<RwLock<DiskMetrics>>,
        previous_stats: Arc<RwLock<Option<(SystemTime, HashMap<String, (u64, u64)>)>>>,
        poll_interval: Duration,
    ) {
        info!("Starting disk monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            // Collect new metrics
            let new_metrics = {
                let mut sys = system.write().await;
                let mut prev_stats = previous_stats.write().await;
                Self::collect_metrics(&mut sys, &mut prev_stats).await
            };

            // Update cached metrics
            {
                let mut metrics_guard = metrics.write().await;
                *metrics_guard = new_metrics.clone();
            }

            // Log disk metrics
            let disk_count = new_metrics.disks.len();
            let avg_usage = if disk_count > 0 {
                new_metrics.disks.values()
                    .map(|d| d.usage_percent)
                    .sum::<f64>() / disk_count as f64
            } else {
                0.0
            };

            debug!(
                "Disk metrics updated: {} disks, avg usage={:.1}%",
                disk_count,
                avg_usage,
            );
        }
    }
}

#[async_trait]
impl ResourceMonitor for DiskMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        if self.monitoring_task.is_some() {
            warn!("Disk monitoring task is already running");
            return Ok(());
        }

        info!("Starting disk monitoring task");

        let system_clone = Arc::clone(&self.system);
        let metrics_clone = Arc::clone(&self.metrics);
        let previous_stats_clone = Arc::clone(&self.previous_stats);
        let poll_interval = Duration::from_millis(self.config.poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::monitoring_loop(system_clone, metrics_clone, previous_stats_clone, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping disk monitoring task");

        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("Disk monitoring task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("Disk monitoring task cancelled as expected"),
                Err(e) => error!("Error waiting for disk monitoring task to stop: {:?}", e),
            }
        } else {
            warn!("Disk monitoring task was not running");
        }

        Ok(())
    }

    fn get_resource_type(&self) -> ResourceType {
        ResourceType::DiskIO
    }

    async fn get_availability(&self) -> PrismaResult<ResourceUsage> {
        // Calculate disk availability as inverse of average usage
        let metrics = self.metrics.read().await;
        if metrics.disks.is_empty() {
            return Ok(ResourceUsage(100.0));
        }

        let avg_usage = metrics.disks.values()
            .map(|d| d.usage_percent)
            .sum::<f64>() / metrics.disks.len() as f64;

        Ok(ResourceUsage(100.0 - avg_usage))
    }

    fn get_poll_interval(&self) -> Duration {
        Duration::from_millis(self.config.poll_interval_ms)
    }

    fn set_poll_interval(&mut self, interval: Duration) {
        self.config.poll_interval_ms = interval.as_millis() as u64;
    }
}

#[async_trait]
impl DiskMonitoring for DiskMonitor {
    async fn get_disk_metrics(&self) -> PrismaResult<DiskMetrics> {
        Ok(self.metrics.read().await.clone())
    }

    async fn get_total_disk_space(&self) -> PrismaResult<u64> {
        let metrics = self.metrics.read().await;
        let total = metrics.disks.values()
            .map(|d| d.total_bytes)
            .sum();
        Ok(total)
    }

    async fn get_available_disk_space(&self) -> PrismaResult<u64> {
        let metrics = self.metrics.read().await;
        let available = metrics.disks.values()
            .map(|d| d.available_bytes)
            .sum();
        Ok(available)
    }

    async fn get_disk_usage(&self) -> PrismaResult<f64> {
        let metrics = self.metrics.read().await;
        if metrics.disks.is_empty() {
            return Ok(0.0);
        }

        let avg_usage = metrics.disks.values()
            .map(|d| d.usage_percent)
            .sum::<f64>() / metrics.disks.len() as f64;

        Ok(avg_usage)
    }

    async fn get_disk_iops(&self) -> PrismaResult<Option<f64>> {
        Ok(self.metrics.read().await.total_iops)
    }
}

impl Drop for DiskMonitor {
    fn drop(&mut self) {
        if let Some(handle) = self.monitoring_task.take() {
            info!("DiskMonitor dropped, aborting monitoring task");
            handle.abort();
        }
    }
}
