// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/network.rs
// =================================================================================================
// Purpose: Monitors network resources and provides detailed network metrics. This file contains the
// logic for collecting network-specific information such as bandwidth usage, packet rates, connection
// counts, and network latency. It provides this information to the system_info module for system
// score calculation.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports this module
//   - system/system_info.rs: Uses this module for network monitoring
//   - system/types.rs: Uses types defined in this module
//
// - External Dependencies:
//   - sysinfo: For cross-platform network information collection
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all network metrics including detailed connection statistics
// - macOS: Support for most network metrics, limited connection details
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};
use sysinfo::{System, Networks, NetworkData};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use super::traits::{ResourceMonitor, NetworkMonitoring};
use super::types::{NetworkInterfaceInfo, NetworkMetrics, SystemMonitorConfig};

/// Network monitor that collects and provides network metrics
#[derive(Debug)]
pub struct NetworkMonitor {
    /// Configuration for the network monitor
    config: SystemMonitorConfig,
    /// System information from sysinfo
    system: Arc<RwLock<System>>,
    /// Cached network metrics
    metrics: Arc<RwLock<NetworkMetrics>>,
    /// Background monitoring task
    monitoring_task: Option<JoinHandle<()>>,
    /// Previous network stats for calculating rates
    previous_stats: Arc<RwLock<Option<(SystemTime, HashMap<String, (u64, u64, u64, u64)>)>>>,
}

impl NetworkMonitor {
    /// Create a new network monitor
    pub fn new(config: SystemMonitorConfig) -> Self {
        info!("Creating NetworkMonitor with poll interval: {}ms", config.poll_interval_ms);

        // Initialize system with network information
        let system = System::new();

        // Initialize network metrics with empty values
        let metrics = NetworkMetrics {
            interfaces: HashMap::new(),
            total_rx_bytes_per_sec: 0.0,
            total_tx_bytes_per_sec: 0.0,
            timestamp: SystemTime::now(),
        };

        NetworkMonitor {
            config,
            system: Arc::new(RwLock::new(system)),
            metrics: Arc::new(RwLock::new(metrics)),
            monitoring_task: None,
            previous_stats: Arc::new(RwLock::new(None)),
        }
    }

    /// Collect network metrics from the system
    async fn collect_metrics(
        system: &mut System,
        previous_stats: &mut Option<(SystemTime, HashMap<String, (u64, u64, u64, u64)>)>,
    ) -> NetworkMetrics {
        // Refresh system information
        system.refresh_all();
        
        // Create a Networks instance to get network information
        let mut networks = Networks::new_with_refreshed_list();
        
        let now = SystemTime::now();
        let mut interfaces = HashMap::new();
        let mut total_rx_bytes_per_sec = 0.0;
        let mut total_tx_bytes_per_sec = 0.0;

        // Calculate network rates based on previous measurements
        let elapsed_secs = if let Some((prev_time, prev_net_stats)) = previous_stats {
            let elapsed = now.duration_since(*prev_time).unwrap_or(Duration::from_secs(1));
            let elapsed_secs = elapsed.as_secs_f64();

            // Process each network interface
            for (if_name, network) in networks.iter() {
                let name = if_name.to_string();

                // Get network traffic information
                let rx_bytes = network.total_received();
                let tx_bytes = network.total_transmitted();
                let rx_packets = network.packets_received();
                let tx_packets = network.packets_transmitted();
                let rx_errors = network.errors_on_received();
                let tx_errors = network.errors_on_transmitted();

                // Calculate rates based on previous measurements
                let (rx_bytes_per_sec, tx_bytes_per_sec, rx_packets_per_sec, tx_packets_per_sec) =
                    if let Some((prev_rx_bytes, prev_tx_bytes, prev_rx_packets, prev_tx_packets)) = prev_net_stats.get(&name) {
                        let rx_bytes_diff = rx_bytes.saturating_sub(*prev_rx_bytes);
                        let tx_bytes_diff = tx_bytes.saturating_sub(*prev_tx_bytes);
                        let rx_packets_diff = rx_packets.saturating_sub(*prev_rx_packets);
                        let tx_packets_diff = tx_packets.saturating_sub(*prev_tx_packets);

                        let rx_bytes_rate = rx_bytes_diff as f64 / elapsed_secs;
                        let tx_bytes_rate = tx_bytes_diff as f64 / elapsed_secs;
                        let rx_packets_rate = rx_packets_diff as f64 / elapsed_secs;
                        let tx_packets_rate = tx_packets_diff as f64 / elapsed_secs;

                        (rx_bytes_rate, tx_bytes_rate, rx_packets_rate, tx_packets_rate)
                    } else {
                        (0.0, 0.0, 0.0, 0.0)
                    };

                // Add to totals
                total_rx_bytes_per_sec += rx_bytes_per_sec;
                total_tx_bytes_per_sec += tx_bytes_per_sec;

                // Create network interface info
                let interface_info = NetworkInterfaceInfo {
                    name: name.clone(),
                    is_up: true, // sysinfo doesn't provide this directly
                    mac_address: Some(network.mac_address().to_string()),
                    ip_addresses: Vec::new(), // sysinfo doesn't provide this directly
                    rx_bytes_per_sec,
                    tx_bytes_per_sec,
                    rx_packets_per_sec,
                    tx_packets_per_sec,
                    rx_errors,
                    tx_errors,
                };

                interfaces.insert(name, interface_info);
            }

            elapsed_secs
        } else {
            // First run, just collect initial stats
            for (if_name, network) in networks.iter() {
                let name = if_name.to_string();

                // Create network interface info with zeros for rates
                let interface_info = NetworkInterfaceInfo {
                    name: name.clone(),
                    is_up: true,
                    mac_address: Some(network.mac_address().to_string()),
                    ip_addresses: Vec::new(),
                    rx_bytes_per_sec: 0.0,
                    tx_bytes_per_sec: 0.0,
                    rx_packets_per_sec: 0.0,
                    tx_packets_per_sec: 0.0,
                    rx_errors: 0,
                    tx_errors: 0,
                };

                interfaces.insert(name, interface_info);
            }

            1.0 // Default elapsed time for first run
        };

        // Update previous stats for next calculation
        let mut new_prev_stats = HashMap::new();
        for (if_name, network) in networks.iter() {
            let name = if_name.to_string();
            new_prev_stats.insert(
                name,
                (
                    network.total_received(),
                    network.total_transmitted(),
                    network.packets_received(),
                    network.packets_transmitted(),
                ),
            );
        }
        *previous_stats = Some((now, new_prev_stats));

        NetworkMetrics {
            interfaces,
            total_rx_bytes_per_sec,
            total_tx_bytes_per_sec,
            timestamp: now,
        }
    }

    /// Background monitoring loop
    async fn monitoring_loop(
        system: Arc<RwLock<System>>,
        metrics: Arc<RwLock<NetworkMetrics>>,
        previous_stats: Arc<RwLock<Option<(SystemTime, HashMap<String, (u64, u64, u64, u64)>)>>>,
        poll_interval: Duration,
    ) {
        info!("Starting network monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            // Collect new metrics
            let new_metrics = {
                let mut sys = system.write().await;
                let mut prev_stats = previous_stats.write().await;
                Self::collect_metrics(&mut sys, &mut prev_stats).await
            };

            // Update cached metrics
            {
                let mut metrics_guard = metrics.write().await;
                *metrics_guard = new_metrics.clone();
            }

            // Log network metrics
            debug!(
                "Network metrics updated: {} interfaces, RX={:.2} MB/s, TX={:.2} MB/s",
                new_metrics.interfaces.len(),
                new_metrics.total_rx_bytes_per_sec / 1024.0 / 1024.0,
                new_metrics.total_tx_bytes_per_sec / 1024.0 / 1024.0,
            );
        }
    }
}

#[async_trait]
impl ResourceMonitor for NetworkMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        if self.monitoring_task.is_some() {
            warn!("Network monitoring task is already running");
            return Ok(());
        }

        info!("Starting network monitoring task");

        let system_clone = Arc::clone(&self.system);
        let metrics_clone = Arc::clone(&self.metrics);
        let previous_stats_clone = Arc::clone(&self.previous_stats);
        let poll_interval = Duration::from_millis(self.config.poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::monitoring_loop(system_clone, metrics_clone, previous_stats_clone, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping network monitoring task");

        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("Network monitoring task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("Network monitoring task cancelled as expected"),
                Err(e) => error!("Error waiting for network monitoring task to stop: {:?}", e),
            }
        } else {
            warn!("Network monitoring task was not running");
        }

        Ok(())
    }

    fn get_resource_type(&self) -> ResourceType {
        ResourceType::NetworkBandwidth
    }

    async fn get_availability(&self) -> PrismaResult<ResourceUsage> {
        // Calculate network availability based on bandwidth usage
        // This is a simplified approach - in a real system, you'd need to know the max bandwidth
        // For now, we'll assume a reasonable threshold and calculate availability as a percentage

        let metrics = self.metrics.read().await;
        let total_bandwidth = metrics.total_rx_bytes_per_sec + metrics.total_tx_bytes_per_sec;

        // Assume 1 Gbps (125 MB/s) as the maximum bandwidth
        const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;

        let usage_percent = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
        let availability = 100.0 - usage_percent;

        Ok(ResourceUsage(availability))
    }

    fn get_poll_interval(&self) -> Duration {
        Duration::from_millis(self.config.poll_interval_ms)
    }

    fn set_poll_interval(&mut self, interval: Duration) {
        self.config.poll_interval_ms = interval.as_millis() as u64;
    }
}

#[async_trait]
impl NetworkMonitoring for NetworkMonitor {
    async fn get_network_metrics(&self) -> PrismaResult<NetworkMetrics> {
        Ok(self.metrics.read().await.clone())
    }

    async fn get_network_bandwidth_usage(&self) -> PrismaResult<(f64, f64)> {
        let metrics = self.metrics.read().await;
        Ok((metrics.total_rx_bytes_per_sec, metrics.total_tx_bytes_per_sec))
    }

    async fn get_network_interfaces(&self) -> PrismaResult<Vec<String>> {
        let metrics = self.metrics.read().await;
        Ok(metrics.interfaces.keys().cloned().collect())
    }
}

impl Drop for NetworkMonitor {
    fn drop(&mut self) {
        if let Some(handle) = self.monitoring_task.take() {
            info!("NetworkMonitor dropped, aborting monitoring task");
            handle.abort();
        }
    }
}
