// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/types.rs
// =================================================================================================
// Purpose: Defines types and data structures for system resource monitoring. These types represent
// the metrics, configurations, and states used by the monitoring components to track and report
// on system resources such as CPU, memory, disk, network, and GPU.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports these types
//   - system/cpu.rs, memory.rs, disk.rs, network.rs, gpu.rs: Use these types
//   - system/system_info.rs: Uses these types for system score calculation
//
// - External Dependencies:
//   - serde: For serialization/deserialization of monitoring data
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it defines data structures, not implementations
// - Some types may have platform-specific fields that are conditionally compiled
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;

// Import crate-level types
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};

/// Configuration for system monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMonitorConfig {
    /// Interval in milliseconds for polling system resources
    pub poll_interval_ms: u64,
    /// Whether to monitor CPU resources
    pub monitor_cpu: bool,
    /// Whether to monitor memory resources
    pub monitor_memory: bool,
    /// Whether to monitor disk resources
    pub monitor_disk: bool,
    /// Whether to monitor network resources
    pub monitor_network: bool,
}

impl Default for SystemMonitorConfig {
    fn default() -> Self {
        Self {
            poll_interval_ms: 5000, // Default to 5 seconds
            monitor_cpu: true,
            monitor_memory: true,
            monitor_disk: true,
            monitor_network: true,
        }
    }
}

/// CPU metrics collected by the CPU monitor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuMetrics {
    /// Overall CPU usage percentage (0-100)
    pub usage_percent: f64,
    /// Per-core CPU usage percentages
    pub core_usage_percent: Vec<f64>,
    /// Number of physical cores
    pub physical_cores: usize,
    /// Number of logical cores (including hyperthreading)
    pub logical_cores: usize,
    /// CPU frequency in MHz (if available)
    pub frequency_mhz: Option<f64>,
    /// CPU load averages (1, 5, 15 minutes)
    pub load_average: Option<(f64, f64, f64)>,
    /// CPU temperature in Celsius (if available)
    pub temperature_celsius: Option<f64>,
    /// Timestamp when these metrics were collected
    pub timestamp: std::time::SystemTime,
}

/// Memory metrics collected by the memory monitor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryMetrics {
    /// Total physical memory in bytes
    pub total_bytes: u64,
    /// Available memory in bytes
    pub available_bytes: u64,
    /// Used memory in bytes
    pub used_bytes: u64,
    /// Free memory in bytes
    pub free_bytes: u64,
    /// Memory usage percentage (0-100)
    pub usage_percent: f64,
    /// Total swap memory in bytes
    pub swap_total_bytes: u64,
    /// Used swap memory in bytes
    pub swap_used_bytes: u64,
    /// Swap usage percentage (0-100)
    pub swap_usage_percent: f64,
    /// Timestamp when these metrics were collected
    pub timestamp: std::time::SystemTime,
}

/// Disk metrics collected by the disk monitor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskMetrics {
    /// Metrics for each disk or mount point
    pub disks: HashMap<String, DiskInfo>,
    /// Overall disk I/O operations per second
    pub total_iops: Option<f64>,
    /// Overall disk throughput in bytes per second
    pub total_throughput_bytes_per_sec: Option<f64>,
    /// Timestamp when these metrics were collected
    pub timestamp: std::time::SystemTime,
}

/// Information about a specific disk or mount point
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskInfo {
    /// Mount point or name
    pub name: String,
    /// Total space in bytes
    pub total_bytes: u64,
    /// Available space in bytes
    pub available_bytes: u64,
    /// Used space in bytes
    pub used_bytes: u64,
    /// Disk usage percentage (0-100)
    pub usage_percent: f64,
    /// Disk type (SSD, HDD, etc.) if available
    pub disk_type: Option<String>,
    /// Read operations per second
    pub read_iops: Option<f64>,
    /// Write operations per second
    pub write_iops: Option<f64>,
    /// Read throughput in bytes per second
    pub read_bytes_per_sec: Option<f64>,
    /// Write throughput in bytes per second
    pub write_bytes_per_sec: Option<f64>,
}

/// Network metrics collected by the network monitor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkMetrics {
    /// Metrics for each network interface
    pub interfaces: HashMap<String, NetworkInterfaceInfo>,
    /// Total received bytes per second across all interfaces
    pub total_rx_bytes_per_sec: f64,
    /// Total transmitted bytes per second across all interfaces
    pub total_tx_bytes_per_sec: f64,
    /// Timestamp when these metrics were collected
    pub timestamp: std::time::SystemTime,
}

/// Information about a specific network interface
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterfaceInfo {
    /// Interface name
    pub name: String,
    /// Whether the interface is up
    pub is_up: bool,
    /// MAC address if available
    pub mac_address: Option<String>,
    /// IP addresses associated with this interface
    pub ip_addresses: Vec<String>,
    /// Received bytes per second
    pub rx_bytes_per_sec: f64,
    /// Transmitted bytes per second
    pub tx_bytes_per_sec: f64,
    /// Received packets per second
    pub rx_packets_per_sec: f64,
    /// Transmitted packets per second
    pub tx_packets_per_sec: f64,
    /// Errors in receiving
    pub rx_errors: u64,
    /// Errors in transmitting
    pub tx_errors: u64,
}

/// Aggregated system metrics from all monitors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMetrics {
    /// CPU metrics
    pub cpu: Option<CpuMetrics>,
    /// Memory metrics
    pub memory: Option<MemoryMetrics>,
    /// Disk metrics
    pub disk: Option<DiskMetrics>,
    /// Network metrics
    pub network: Option<NetworkMetrics>,
    /// Timestamp when these metrics were collected
    pub timestamp: std::time::SystemTime,
}