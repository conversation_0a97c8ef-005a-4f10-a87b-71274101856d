// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/mod.rs
// =================================================================================================
// Purpose: Entry point for the system monitoring submodule. This module is responsible for
// monitoring system resources such as CPU, memory, disk, network, and GPU. It provides a unified
// interface for collecting system metrics and calculating system scores.
//
// Integration:
// - Internal Dependencies:
//   - monitor/monitor.rs: Uses this module to collect system metrics
//   - system_info.rs: Main implementation of system monitoring
//   - cpu.rs, memory.rs, disk.rs, network.rs: Resource-specific monitoring
//
// - External Dependencies:
//   - sysinfo: For cross-platform system information collection
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all metrics
// - macOS: Support for CPU, memory, disk, and network metrics
// =================================================================================================

// Declare files within the system monitoring submodule
pub mod system_info;
pub mod cpu;
pub mod memory;
pub mod disk;
pub mod network;
pub mod traits;
pub mod types;

// Re-export the main struct
pub use system_info::SystemInfoMonitor;

// Re-export resource monitors
pub use cpu::CpuMonitor;
pub use memory::MemoryMonitor;
pub use disk::DiskMonitor;
pub use network::NetworkMonitor;

// Re-export traits
pub use traits::{
    ResourceMonitor, SystemMonitoring,
    CpuMonitoring, MemoryMonitoring, DiskMonitoring, NetworkMonitoring
};
