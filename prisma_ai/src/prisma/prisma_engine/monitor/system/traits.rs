// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/traits.rs
// =================================================================================================
// Purpose: Defines traits for system monitoring components. These traits establish the interfaces
// that different resource monitors must implement, ensuring consistent behavior and interoperability
// across the monitoring subsystem. Includes traits for resource monitoring, metric collection, and
// score calculation.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports these traits
//   - system/cpu.rs, memory.rs, disk.rs, network.rs, gpu.rs: Implement these traits
//   - system/system_info.rs: Uses these traits to interact with resource monitors
//
// - External Dependencies:
//   - async_trait: For async trait methods
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it defines interfaces, not implementations
// =================================================================================================

use async_trait::async_trait;
use std::time::Duration;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage, SystemScore};
use super::types::{
    CpuMetrics, MemoryMetrics, DiskMetrics, NetworkMetrics, SystemMetrics,
    SystemMonitorConfig
};

/// Base trait for all resource monitors
#[async_trait]
pub trait ResourceMonitor: Send + Sync {
    /// Start the monitoring process
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop the monitoring process
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Get the resource type this monitor is responsible for
    fn get_resource_type(&self) -> ResourceType;

    /// Get the current resource availability as a percentage (0-100)
    async fn get_availability(&self) -> PrismaResult<ResourceUsage>;

    /// Get the monitoring interval
    fn get_poll_interval(&self) -> Duration;

    /// Set the monitoring interval
    fn set_poll_interval(&mut self, interval: Duration);
}

/// Trait for CPU monitoring
#[async_trait]
pub trait CpuMonitoring: ResourceMonitor {
    /// Get detailed CPU metrics
    async fn get_cpu_metrics(&self) -> PrismaResult<CpuMetrics>;

    /// Get the number of physical CPU cores
    async fn get_physical_core_count(&self) -> PrismaResult<usize>;

    /// Get the number of logical CPU cores (including hyperthreading)
    async fn get_logical_core_count(&self) -> PrismaResult<usize>;

    /// Get the current CPU usage as a percentage (0-100)
    async fn get_cpu_usage(&self) -> PrismaResult<f64>;

    /// Get the CPU load averages (1, 5, 15 minutes)
    async fn get_load_average(&self) -> PrismaResult<Option<(f64, f64, f64)>>;
}

/// Trait for memory monitoring
#[async_trait]
pub trait MemoryMonitoring: ResourceMonitor {
    /// Get detailed memory metrics
    async fn get_memory_metrics(&self) -> PrismaResult<MemoryMetrics>;

    /// Get the total physical memory in bytes
    async fn get_total_memory(&self) -> PrismaResult<u64>;

    /// Get the available memory in bytes
    async fn get_available_memory(&self) -> PrismaResult<u64>;

    /// Get the current memory usage as a percentage (0-100)
    async fn get_memory_usage(&self) -> PrismaResult<f64>;

    /// Get the total swap memory in bytes
    async fn get_total_swap(&self) -> PrismaResult<u64>;

    /// Get the used swap memory in bytes
    async fn get_used_swap(&self) -> PrismaResult<u64>;
}

/// Trait for disk monitoring
#[async_trait]
pub trait DiskMonitoring: ResourceMonitor {
    /// Get detailed disk metrics
    async fn get_disk_metrics(&self) -> PrismaResult<DiskMetrics>;

    /// Get the total disk space in bytes
    async fn get_total_disk_space(&self) -> PrismaResult<u64>;

    /// Get the available disk space in bytes
    async fn get_available_disk_space(&self) -> PrismaResult<u64>;

    /// Get the current disk usage as a percentage (0-100)
    async fn get_disk_usage(&self) -> PrismaResult<f64>;

    /// Get the current disk I/O operations per second
    async fn get_disk_iops(&self) -> PrismaResult<Option<f64>>;
}

/// Trait for network monitoring
#[async_trait]
pub trait NetworkMonitoring: ResourceMonitor {
    /// Get detailed network metrics
    async fn get_network_metrics(&self) -> PrismaResult<NetworkMetrics>;

    /// Get the current network bandwidth usage in bytes per second
    async fn get_network_bandwidth_usage(&self) -> PrismaResult<(f64, f64)>; // (rx, tx)

    /// Get the list of network interfaces
    async fn get_network_interfaces(&self) -> PrismaResult<Vec<String>>;
}

/// Trait for system monitoring that combines all resource monitors
#[async_trait]
pub trait SystemMonitoring: Send + Sync {
    /// Start all monitoring processes
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop all monitoring processes
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Get the current system score based on resource availability
    async fn get_system_score(&self) -> PrismaResult<SystemScore>;

    /// Get detailed system metrics from all monitors
    async fn get_system_metrics(&self) -> PrismaResult<SystemMetrics>;

    /// Get the monitoring configuration
    fn get_config(&self) -> SystemMonitorConfig;

    /// Set the monitoring configuration
    fn set_config(&mut self, config: SystemMonitorConfig) -> PrismaResult<()>;
}