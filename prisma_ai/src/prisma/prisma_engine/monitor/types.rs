// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/types.rs
// =================================================================================================
// Purpose: Defines types and data structures for the monitor module. These types represent the
// configurations, metrics, and states used by the monitoring components to track and report on
// system resources and internal Prisma Engine components.
//
// Integration:
// - Internal Dependencies:
//   - mod.rs: Exports these types
//   - monitor.rs: Uses MonitorConfig and other types
//   - system/types.rs: Extends these types with system-specific information
//   - prisma/types.rs: Extends these types with Prisma-internal information
//
// - External Dependencies:
//   - serde: For serialization/deserialization of monitoring data
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it defines data structures, not implementations
// =================================================================================================

use serde::{Deserialize, Serialize};

// Configuration specific to the resource monitor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    /// Interval in milliseconds for polling system resources.
    pub poll_interval_ms: u64,
    // Add other monitor-specific configurations if needed,
    // e.g., thresholds, specific resources to track, etc.
}

impl Default for MonitorConfig {
    fn default() -> Self {
        MonitorConfig {
            poll_interval_ms: 5000, // Default to 5 seconds
        }
    }
}

// You can add other monitor-specific types here as needed.
// For example, detailed stats for CPU, memory, etc. from the system module,
// or queue length stats from the prisma module.
