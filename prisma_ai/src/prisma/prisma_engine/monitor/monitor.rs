// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/monitor.rs
// =================================================================================================
// Purpose: Main implementation of the Monitor component. This file contains the core logic for
// aggregating metrics from different monitoring sources (system and Prisma-internal) and providing
// a unified interface for the decision maker to access these metrics.
//
// Integration:
// - Internal Dependencies:
//   - mod.rs: Exports this module
//   - types.rs: Uses MonitorConfig and other types
//   - system/system_info.rs: Uses SystemInfoMonitor for system monitoring
//   - prisma/queue_monitor.rs: Uses QueueMonitor for queue monitoring
//
// - External Dependencies:
//   - async_trait: For async trait methods
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it delegates platform-specific monitoring to submodules
// =================================================================================================

use async_trait::async_trait;
use tracing::info;
use serde_json;
use std::time::Duration;

// Use crate-level error types and engine types/traits
use crate::err::{PrismaResult, types::PrismaError};
use crate::err::types::errors_mod::SystemMonitorError;
use crate::prisma::prisma_engine::types::{SystemScore, ResourceType, ResourceUsage, TaskId};
use crate::prisma::prisma_engine::traits::ResourceMonitor; // Import the main trait

// Import monitor-specific config and sub-monitors
use super::types::MonitorConfig;
use super::system::traits::SystemMonitoring; // Import SystemMonitoring trait
use super::system::types::SystemMetrics; // Import SystemMetrics from the correct path
use super::system::types::SystemMonitorConfig; // Import SystemMonitorConfig
use super::system::SystemInfoMonitor;
use super::system::types::{CpuMetrics, MemoryMetrics, DiskMetrics, NetworkMetrics};
use super::system::traits::{CpuMonitoring, MemoryMonitoring, DiskMonitoring, NetworkMonitoring, ResourceMonitor as SystemResourceMonitor};
use super::prisma::{
    QueueMonitor, TaskMonitor, PrismaMonitorConfig,
    QueueMonitorMetrics, TaskMonitorMetrics, TaskMetrics, TaskStatus,
    traits::{QueueMonitoring, TaskMonitoring, Monitorable}
};

#[derive(Debug)]
pub struct Monitor {
    config: MonitorConfig,
    system_monitor: SystemInfoMonitor,
    queue_monitor: QueueMonitor,
    task_monitor: TaskMonitor,
}

impl Monitor {
    pub fn new(config: MonitorConfig) -> Self {
        info!("Creating aggregate Monitor with config: {:?}", config);

        // Pass the config to the system monitor
        let system_monitor = SystemInfoMonitor::new(config.clone());

        // Create a PrismaMonitorConfig from the MonitorConfig
        // This allows customization of Prisma-internal monitoring
        let prisma_config = PrismaMonitorConfig {
            // Use the same poll interval as the system monitor by default
            queue_poll_interval_ms: config.poll_interval_ms,
            task_poll_interval_ms: config.poll_interval_ms,
            // These could be exposed in MonitorConfig in the future
            max_task_history: 1000,
            enable_detailed_task_tracking: true,
        };

        info!("Created PrismaMonitorConfig: {:?}", prisma_config);

        let queue_monitor = QueueMonitor::new(prisma_config.clone());
        let task_monitor = TaskMonitor::new(prisma_config);

        Monitor {
            config,
            system_monitor,
            queue_monitor,
            task_monitor,
        }
    }

    // Internal helper to combine scores if needed
    async fn aggregate_system_score(&self) -> SystemScore {
        // Get the system monitor's score
        let mut system_score = self.system_monitor.get_last_score().await;

        // Adjust the system score based on Prisma-internal metrics
        // For example, if queues are getting full, we reduce the CPU score
        // to indicate that the system is under load

        // Get queue metrics
        if let Ok(queue_metrics) = self.queue_monitor.get_metrics().await {
            // If any queue is getting too full (over 80% of max observed length),
            // reduce the CPU availability to indicate system load
            for (queue_name, metrics) in &queue_metrics.queue_metrics {
                if metrics.max_length > 0 && metrics.length as f64 / metrics.max_length as f64 > 0.8 {
                    if let Some(cpu_avail) = system_score.availability.get_mut(&ResourceType::CPU) {
                        // Reduce CPU availability by up to 20% based on queue fullness
                        let reduction_factor = 0.8 - (metrics.length as f64 / metrics.max_length as f64 - 0.8);
                        cpu_avail.0 = cpu_avail.0 * reduction_factor;

                        tracing::debug!("Reducing CPU availability due to high queue load in {}: {}% full",
                            queue_name, (metrics.length as f64 / metrics.max_length as f64 * 100.0) as u32);
                    }
                }
            }
        }

        // Get task metrics
        if let Ok(task_metrics) = self.task_monitor.get_metrics().await {
            // If there are many active tasks or high failure rate, adjust memory availability
            let active_task_count = task_metrics.active_tasks.len();
            let failure_rate = if task_metrics.total_tasks > 0 {
                task_metrics.failed_tasks as f64 / task_metrics.total_tasks as f64
            } else {
                0.0
            };

            // Adjust memory availability based on active tasks (more tasks = less memory available)
            if active_task_count > 10 {
                if let Some(mem_avail) = system_score.availability.get_mut(&ResourceType::Memory) {
                    // Reduce memory availability by up to 30% based on active task count
                    let reduction_factor = 1.0 - (active_task_count.min(50) as f64 - 10.0) / 40.0 * 0.3;
                    mem_avail.0 = mem_avail.0 * reduction_factor;

                    tracing::debug!("Reducing Memory availability due to high active task count: {} tasks",
                        active_task_count);
                }
            }

            // Adjust network availability based on failure rate (higher failure = more conservative)
            if failure_rate > 0.1 {
                if let Some(net_avail) = system_score.availability.get_mut(&ResourceType::NetworkBandwidth) {
                    // Reduce network availability by up to 40% based on failure rate
                    let reduction_factor = 1.0 - (failure_rate - 0.1).min(0.4);
                    net_avail.0 = net_avail.0 * reduction_factor;

                    tracing::debug!("Reducing Network availability due to high task failure rate: {:.1}%",
                        failure_rate * 100.0);
                }
            }
        }

        system_score
    }

    /// Gets the latest queue metrics as a JSON string
    pub async fn get_queue_metrics_json(&self) -> PrismaResult<String> {
        self.queue_monitor.get_metrics_json().await
    }

    /// Gets the latest task metrics as a JSON string
    pub async fn get_task_metrics_json(&self) -> PrismaResult<String> {
        self.task_monitor.get_metrics_json().await
    }

    /// Gets the latest queue metrics
    pub async fn get_queue_metrics(&self) -> PrismaResult<QueueMonitorMetrics> {
        self.queue_monitor.get_metrics().await
    }

    /// Gets the latest task metrics
    pub async fn get_task_metrics(&self) -> PrismaResult<TaskMonitorMetrics> {
        self.task_monitor.get_metrics().await
    }

    /// Gets metrics for a specific task
    pub async fn get_task_metrics_by_id(&self, task_id: &TaskId) -> PrismaResult<Option<TaskMetrics>> {
        self.task_monitor.get_task_metrics(task_id).await
    }

    /// Records a new task being created
    pub async fn record_task_created(&mut self, metrics: TaskMetrics) -> PrismaResult<()> {
        self.task_monitor.record_task_created(metrics).await
    }

    /// Updates the status of a task
    pub async fn update_task_status(&mut self, task_id: &TaskId, status: TaskStatus) -> PrismaResult<()> {
        self.task_monitor.update_task_status(task_id, status).await
    }

    /// Records a task starting execution
    pub async fn record_task_started(&mut self, task_id: &TaskId, queue_name: &str) -> PrismaResult<()> {
        self.task_monitor.record_task_started(task_id, queue_name).await
    }

    /// Records a task completing execution
    pub async fn record_task_completed(&mut self, task_id: &TaskId, success: bool, error_message: Option<String>) -> PrismaResult<()> {
        self.task_monitor.record_task_completed(task_id, success, error_message).await
    }

    /// Updates metrics for a specific queue
    pub async fn update_queue_metrics(&mut self, queue_name: &str, length: usize) -> PrismaResult<()> {
        self.queue_monitor.update_queue_metrics(queue_name, length).await
    }

    /// Records a task being processed by a queue
    pub async fn record_task_processed(&mut self, queue_name: &str, processing_time_ms: f64, success: bool) -> PrismaResult<()> {
        self.queue_monitor.record_task_processed(queue_name, processing_time_ms, success).await
    }

    //
    // System Monitoring Methods
    //

    /// Gets detailed system metrics from all monitors
    pub async fn get_system_metrics(&self) -> PrismaResult<SystemMetrics> {
        // Use the SystemMonitoring trait method
        self.system_monitor.get_system_metrics().await
    }

    /// Gets the CPU metrics
    pub async fn get_cpu_metrics(&self) -> PrismaResult<CpuMetrics> {
        if let Some(cpu_monitor) = self.system_monitor.get_cpu_monitor() {
            cpu_monitor.get_cpu_metrics().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "CPU monitor not available".to_string()
            )))
        }
    }

    /// Gets the memory metrics
    pub async fn get_memory_metrics(&self) -> PrismaResult<MemoryMetrics> {
        if let Some(memory_monitor) = self.system_monitor.get_memory_monitor() {
            memory_monitor.get_memory_metrics().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Memory monitor not available".to_string()
            )))
        }
    }

    /// Gets the disk metrics
    pub async fn get_disk_metrics(&self) -> PrismaResult<DiskMetrics> {
        if let Some(disk_monitor) = self.system_monitor.get_disk_monitor() {
            disk_monitor.get_disk_metrics().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Disk monitor not available".to_string()
            )))
        }
    }

    /// Gets the network metrics
    pub async fn get_network_metrics(&self) -> PrismaResult<NetworkMetrics> {
        if let Some(network_monitor) = self.system_monitor.get_network_monitor() {
            network_monitor.get_network_metrics().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Network monitor not available".to_string()
            )))
        }
    }

    /// Gets the current CPU usage as a percentage (0-100)
    pub async fn get_cpu_usage(&self) -> PrismaResult<f64> {
        if let Some(cpu_monitor) = self.system_monitor.get_cpu_monitor() {
            cpu_monitor.get_cpu_usage().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "CPU monitor not available".to_string()
            )))
        }
    }

    /// Gets the current memory usage as a percentage (0-100)
    pub async fn get_memory_usage(&self) -> PrismaResult<f64> {
        if let Some(memory_monitor) = self.system_monitor.get_memory_monitor() {
            memory_monitor.get_memory_usage().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Memory monitor not available".to_string()
            )))
        }
    }

    /// Gets the current disk usage as a percentage (0-100)
    pub async fn get_disk_usage(&self) -> PrismaResult<f64> {
        if let Some(disk_monitor) = self.system_monitor.get_disk_monitor() {
            disk_monitor.get_disk_usage().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Disk monitor not available".to_string()
            )))
        }
    }

    /// Gets the current network bandwidth usage in bytes per second (rx, tx)
    pub async fn get_network_bandwidth_usage(&self) -> PrismaResult<(f64, f64)> {
        if let Some(network_monitor) = self.system_monitor.get_network_monitor() {
            network_monitor.get_network_bandwidth_usage().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Network monitor not available".to_string()
            )))
        }
    }

    //
    // Configuration Methods
    //

    /// Gets the current monitor configuration
    pub fn get_config(&self) -> MonitorConfig {
        self.config.clone()
    }

    /// Gets the current system monitor configuration
    pub fn get_system_config(&self) -> PrismaResult<SystemMonitorConfig> {
        // Use the SystemMonitoring trait method
        Ok(self.system_monitor.get_config())
    }

    /// Gets the current Prisma monitor configuration
    pub fn get_prisma_config(&self) -> PrismaMonitorConfig {
        // Construct a PrismaMonitorConfig from the current state
        PrismaMonitorConfig {
            queue_poll_interval_ms: self.config.poll_interval_ms,
            task_poll_interval_ms: self.config.poll_interval_ms,
            max_task_history: 1000, // This should be stored in the Monitor struct
            enable_detailed_task_tracking: true,
        }
    }

    /// Updates the monitor configuration
    pub async fn update_config(&mut self, config: MonitorConfig) -> PrismaResult<()> {
        info!("Updating Monitor configuration: {:?}", config);

        // Update the main config
        self.config = config.clone();

        // Update the system monitor config
        let system_config = SystemMonitorConfig {
            poll_interval_ms: config.poll_interval_ms,
            monitor_cpu: true,
            monitor_memory: true,
            monitor_disk: true,
            monitor_network: true,
        };

        // Use the SystemMonitoring trait method
        self.system_monitor.set_config(system_config)?;

        // Update the Prisma monitor configs
        let prisma_config = PrismaMonitorConfig {
            queue_poll_interval_ms: config.poll_interval_ms,
            task_poll_interval_ms: config.poll_interval_ms,
            max_task_history: 1000,
            enable_detailed_task_tracking: true,
        };

        // We need to restart the monitors to apply the new configuration
        ResourceMonitor::stop(self).await?;

        // Create new monitors with the updated config
        let system_monitor = SystemInfoMonitor::new(config.clone());
        let queue_monitor = QueueMonitor::new(prisma_config.clone());
        let task_monitor = TaskMonitor::new(prisma_config);

        self.system_monitor = system_monitor;
        self.queue_monitor = queue_monitor;
        self.task_monitor = task_monitor;

        // Restart the monitors
        ResourceMonitor::start(self).await?;

        info!("Monitor configuration updated successfully.");
        Ok(())
    }

    /// Updates the system monitor configuration
    pub async fn update_system_config(&mut self, config: SystemMonitorConfig) -> PrismaResult<()> {
        info!("Updating SystemMonitor configuration: {:?}", config);

        // Update the system monitor config using the SystemMonitoring trait method
        self.system_monitor.set_config(config)?;

        info!("SystemMonitor configuration updated successfully.");
        Ok(())
    }

    /// Enables or disables specific resource monitoring
    pub async fn configure_resource_monitoring(
        &mut self,
        monitor_cpu: bool,
        monitor_memory: bool,
        monitor_disk: bool,
        monitor_network: bool
    ) -> PrismaResult<()> {
        info!("Configuring resource monitoring: CPU={}, Memory={}, Disk={}, Network={}",
            monitor_cpu, monitor_memory, monitor_disk, monitor_network);

        // Get the current system config using the SystemMonitoring trait method
        let mut system_config = self.system_monitor.get_config();

        // Update the config
        system_config.monitor_cpu = monitor_cpu;
        system_config.monitor_memory = monitor_memory;
        system_config.monitor_disk = monitor_disk;
        system_config.monitor_network = monitor_network;

        // Apply the updated config
        self.update_system_config(system_config).await
    }

    // Helper methods for the SystemResourceMonitor trait

    /// Gets the resource type this monitor is responsible for (helper method).
    pub fn get_monitor_resource_type(&self) -> ResourceType {
        // Note: ResourceType::System doesn't exist, so we'll use CPU as a default
        ResourceType::CPU // This is an aggregate monitor
    }

    /// Gets the current resource availability as a percentage (0-100) (helper method).
    pub async fn get_monitor_availability(&self) -> PrismaResult<ResourceUsage> {
        // Calculate an aggregate availability score based on all resources
        let system_score = self.aggregate_system_score().await;

        // Calculate the average availability across all resources
        let mut total_availability = 0.0;
        let mut count = 0;

        for (_, usage) in &system_score.availability {
            total_availability += usage.0;
            count += 1;
        }

        if count > 0 {
            Ok(ResourceUsage(total_availability / count as f64))
        } else {
            // Default to 100% if no resources are being monitored
            Ok(ResourceUsage(100.0))
        }
    }

    /// Gets the monitoring interval (helper method).
    pub fn get_monitor_poll_interval(&self) -> Duration {
        Duration::from_millis(self.config.poll_interval_ms)
    }

    /// Sets the monitoring interval (helper method).
    pub fn set_monitor_poll_interval(&mut self, interval: Duration) {
        self.config.poll_interval_ms = interval.as_millis() as u64;

        // Update the poll interval for all sub-monitors
        if let Some(cpu) = self.system_monitor.get_cpu_monitor() {
            // Use the SystemResourceMonitor trait method
            let _ = SystemResourceMonitor::get_poll_interval(cpu);
        }
    }
}

#[async_trait]
impl ResourceMonitor for Monitor {
    /// Gets the latest aggregated system score.
    async fn get_system_score(&self) -> PrismaResult<SystemScore> {
        // In a more complex scenario, this might query multiple sub-monitors
        // and aggregate the results into a single SystemScore.
        Ok(self.aggregate_system_score().await)
    }

    /// Starts all underlying monitoring tasks.
    async fn start(&mut self) -> PrismaResult<()> {
        info!("Starting aggregate Monitor components...");

        // Start system monitor with detailed error handling
        match self.system_monitor.start().await {
            Ok(_) => info!("System monitor started successfully."),
            Err(e) => {
                // Log the error but continue with other monitors
                tracing::error!("Failed to start system monitor: {:?}", e);
                // Return the error if we want to fail fast
                // return Err(e);
            }
        }

        // Start queue monitor with detailed error handling
        match self.queue_monitor.start().await {
            Ok(_) => info!("Queue monitor started successfully."),
            Err(e) => {
                tracing::error!("Failed to start queue monitor: {:?}", e);
                // Return the error if we want to fail fast
                // return Err(e);
            }
        }

        // Start task monitor with detailed error handling
        match self.task_monitor.start().await {
            Ok(_) => info!("Task monitor started successfully."),
            Err(e) => {
                tracing::error!("Failed to start task monitor: {:?}", e);
                // Return the error if we want to fail fast
                // return Err(e);
            }
        }

        info!("Aggregate Monitor started.");
        Ok(())
    }

    /// Stops all underlying monitoring tasks.
    async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping aggregate Monitor components...");

        // Track any errors that occur during shutdown
        let mut errors = Vec::new();

        // Stop system monitor with detailed error handling
        if let Err(e) = self.system_monitor.stop().await {
            tracing::error!("Failed to stop system monitor: {:?}", e);
            errors.push(format!("System monitor: {}", e));
        } else {
            info!("System monitor stopped successfully.");
        }

        // Stop queue monitor with detailed error handling
        if let Err(e) = self.queue_monitor.stop().await {
            tracing::error!("Failed to stop queue monitor: {:?}", e);
            errors.push(format!("Queue monitor: {}", e));
        } else {
            info!("Queue monitor stopped successfully.");
        }

        // Stop task monitor with detailed error handling
        if let Err(e) = self.task_monitor.stop().await {
            tracing::error!("Failed to stop task monitor: {:?}", e);
            errors.push(format!("Task monitor: {}", e));
        } else {
            info!("Task monitor stopped successfully.");
        }

        // If any errors occurred, return a combined error
        if !errors.is_empty() {
            return Err(PrismaError::new(SystemMonitorError::OperationFailed(
                format!("Failed to stop some monitors: {}", errors.join(", "))
            )));
        }

        info!("Aggregate Monitor stopped.");
        Ok(())
    }
}

#[async_trait]
impl Monitorable for Monitor {
    fn get_name(&self) -> &str {
        "PrismaMonitor"
    }

    async fn get_status(&self) -> PrismaResult<String> {
        // Combine status from all sub-monitors
        let system_status = match self.system_monitor.get_system_score().await {
            Ok(_) => "Running",
            Err(_) => "Error",
        };

        let queue_status = self.queue_monitor.get_status().await?;
        let task_status = self.task_monitor.get_status().await?;

        Ok(format!(
            "System: {}, Queue: {}, Task: {}",
            system_status, queue_status, task_status
        ))
    }

    async fn get_metrics_json(&self) -> PrismaResult<String> {
        // Combine metrics from all sub-monitors
        let system_metrics = match self.system_monitor.get_last_metrics().await {
            metrics => serde_json::to_value(metrics).unwrap_or(serde_json::json!({})),
        };

        let queue_metrics_str = self.queue_monitor.get_metrics_json().await?;
        let queue_metrics: serde_json::Value = serde_json::from_str(&queue_metrics_str)
            .map_err(|e| PrismaError::new(SystemMonitorError::OperationFailed(
                format!("Failed to parse queue metrics: {}", e)
            )))?;

        let task_metrics_str = self.task_monitor.get_metrics_json().await?;
        let task_metrics: serde_json::Value = serde_json::from_str(&task_metrics_str)
            .map_err(|e| PrismaError::new(SystemMonitorError::OperationFailed(
                format!("Failed to parse task metrics: {}", e)
            )))?;

        // Combine all metrics into a single JSON object
        let combined_metrics = serde_json::json!({
            "system": system_metrics,
            "queue": queue_metrics,
            "task": task_metrics,
        });

        serde_json::to_string_pretty(&combined_metrics)
            .map_err(|e| PrismaError::new(SystemMonitorError::OperationFailed(
                format!("Failed to serialize combined metrics: {}", e)
            )))
    }
}

// Implement system-specific monitoring traits for Monitor

// First, implement the system::traits::ResourceMonitor trait for Monitor
// This is different from the main ResourceMonitor trait
#[async_trait]
impl SystemResourceMonitor for Monitor {
    async fn start(&mut self) -> PrismaResult<()> {
        // Delegate to the main start method
        ResourceMonitor::start(self).await
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        // Delegate to the main stop method
        ResourceMonitor::stop(self).await
    }

    fn get_resource_type(&self) -> ResourceType {
        // Use our helper method
        self.get_monitor_resource_type()
    }

    async fn get_availability(&self) -> PrismaResult<ResourceUsage> {
        // Use our helper method
        self.get_monitor_availability().await
    }

    fn get_poll_interval(&self) -> Duration {
        // Use our helper method
        self.get_monitor_poll_interval()
    }

    fn set_poll_interval(&mut self, interval: Duration) {
        // Use our helper method
        self.set_monitor_poll_interval(interval)
    }
}

#[async_trait]
impl CpuMonitoring for Monitor {
    async fn get_cpu_metrics(&self) -> PrismaResult<CpuMetrics> {
        self.get_cpu_metrics().await
    }

    async fn get_physical_core_count(&self) -> PrismaResult<usize> {
        if let Some(cpu_monitor) = self.system_monitor.get_cpu_monitor() {
            cpu_monitor.get_physical_core_count().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "CPU monitor not available".to_string()
            )))
        }
    }

    async fn get_logical_core_count(&self) -> PrismaResult<usize> {
        if let Some(cpu_monitor) = self.system_monitor.get_cpu_monitor() {
            cpu_monitor.get_logical_core_count().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "CPU monitor not available".to_string()
            )))
        }
    }

    async fn get_cpu_usage(&self) -> PrismaResult<f64> {
        self.get_cpu_usage().await
    }

    async fn get_load_average(&self) -> PrismaResult<Option<(f64, f64, f64)>> {
        if let Some(cpu_monitor) = self.system_monitor.get_cpu_monitor() {
            cpu_monitor.get_load_average().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "CPU monitor not available".to_string()
            )))
        }
    }
}

#[async_trait]
impl MemoryMonitoring for Monitor {
    async fn get_memory_metrics(&self) -> PrismaResult<MemoryMetrics> {
        self.get_memory_metrics().await
    }

    async fn get_total_memory(&self) -> PrismaResult<u64> {
        if let Some(memory_monitor) = self.system_monitor.get_memory_monitor() {
            memory_monitor.get_total_memory().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Memory monitor not available".to_string()
            )))
        }
    }

    async fn get_available_memory(&self) -> PrismaResult<u64> {
        if let Some(memory_monitor) = self.system_monitor.get_memory_monitor() {
            memory_monitor.get_available_memory().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Memory monitor not available".to_string()
            )))
        }
    }

    async fn get_memory_usage(&self) -> PrismaResult<f64> {
        self.get_memory_usage().await
    }

    async fn get_total_swap(&self) -> PrismaResult<u64> {
        if let Some(memory_monitor) = self.system_monitor.get_memory_monitor() {
            memory_monitor.get_total_swap().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Memory monitor not available".to_string()
            )))
        }
    }

    async fn get_used_swap(&self) -> PrismaResult<u64> {
        if let Some(memory_monitor) = self.system_monitor.get_memory_monitor() {
            memory_monitor.get_used_swap().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Memory monitor not available".to_string()
            )))
        }
    }
}

#[async_trait]
impl DiskMonitoring for Monitor {
    async fn get_disk_metrics(&self) -> PrismaResult<DiskMetrics> {
        self.get_disk_metrics().await
    }

    async fn get_total_disk_space(&self) -> PrismaResult<u64> {
        if let Some(disk_monitor) = self.system_monitor.get_disk_monitor() {
            disk_monitor.get_total_disk_space().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Disk monitor not available".to_string()
            )))
        }
    }

    async fn get_available_disk_space(&self) -> PrismaResult<u64> {
        if let Some(disk_monitor) = self.system_monitor.get_disk_monitor() {
            disk_monitor.get_available_disk_space().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Disk monitor not available".to_string()
            )))
        }
    }

    async fn get_disk_usage(&self) -> PrismaResult<f64> {
        self.get_disk_usage().await
    }

    async fn get_disk_iops(&self) -> PrismaResult<Option<f64>> {
        if let Some(disk_monitor) = self.system_monitor.get_disk_monitor() {
            disk_monitor.get_disk_iops().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Disk monitor not available".to_string()
            )))
        }
    }
}

#[async_trait]
impl NetworkMonitoring for Monitor {
    async fn get_network_metrics(&self) -> PrismaResult<NetworkMetrics> {
        self.get_network_metrics().await
    }

    async fn get_network_bandwidth_usage(&self) -> PrismaResult<(f64, f64)> {
        self.get_network_bandwidth_usage().await
    }

    async fn get_network_interfaces(&self) -> PrismaResult<Vec<String>> {
        if let Some(network_monitor) = self.system_monitor.get_network_monitor() {
            network_monitor.get_network_interfaces().await
        } else {
            Err(PrismaError::new(SystemMonitorError::ResourceNotAvailable(
                "Network monitor not available".to_string()
            )))
        }
    }
}
