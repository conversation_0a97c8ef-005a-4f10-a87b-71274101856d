use async_trait::async_trait;
use crate::err::PrismaResult; // Use re-exported PrismaResult from crate::err
use super::types::{
    TaskId, TaskPriority, TaskCategory, PrismaScore, SystemScore, ExecutionStrategyType, EngineStatus, EngineConfig
};
use std::any::Any; // To allow tasks to potentially return different types

/// Trait defining a task that can be executed by the Prisma Engine.
#[async_trait]
pub trait Task: Send + Sync {
    fn id(&self) -> TaskId;
    fn category(&self) -> TaskCategory;
    fn priority(&self) -> TaskPriority;
    fn get_prisma_score(&self) -> PrismaScore;
    async fn execute(&mut self) -> PrismaResult<Box<dyn Any + Send>>;

    /// Creates a boxed clone of the task.
    ///
    /// This method is used by the executor to create clones of tasks for execution
    /// in different execution strategies. Implementations should ensure that the
    /// clone has the same state as the original task.
    fn clone_box(&self) -> Box<dyn Task>;

    /// Gets metadata associated with this task.
    ///
    /// This method returns a JSON Value containing metadata about the task,
    /// such as model IDs, parameters, or other task-specific information.
    /// The default implementation returns an empty object.
    fn get_metadata(&self) -> serde_json::Value {
        serde_json::json!({})
    }
}

/// Trait for the system resource monitor.
#[async_trait]
pub trait ResourceMonitor: Send + Sync {
    async fn get_system_score(&self) -> PrismaResult<SystemScore>;
    async fn start(&mut self) -> PrismaResult<()>;
    async fn stop(&mut self) -> PrismaResult<()>;
}

/// Trait for the decision-making logic.
#[async_trait]
pub trait DecisionLogic: Send + Sync {
    async fn decide_strategy(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<ExecutionStrategyType>;
}

/// Trait for the task executor.
#[async_trait]
pub trait Executor: Send + Sync {
    /// Submit a task for execution and return a future that resolves to the task result.
    ///
    /// # Arguments
    /// * `task` - The task to execute
    /// * `strategy` - The execution strategy to use
    ///
    /// # Returns
    /// A tuple containing the task ID and a future that resolves to the task result.
    async fn submit_task(
        &self,
        task: Box<dyn Task>,
        strategy: ExecutionStrategyType,
    ) -> PrismaResult<(TaskId, tokio::sync::oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)>;
    async fn initialize(&mut self, config: &EngineConfig) -> PrismaResult<()>;
    async fn shutdown(&mut self) -> PrismaResult<()>;
}

/// Trait for the main Prisma Engine controller.
#[async_trait]
pub trait EngineController: Send + Sync {
    async fn initialize(&mut self, config: EngineConfig) -> PrismaResult<()>;
    async fn start(&mut self) -> PrismaResult<()>;
    async fn stop(&mut self) -> PrismaResult<()>;
    async fn pause(&mut self) -> PrismaResult<()>;
    async fn resume(&mut self) -> PrismaResult<()>;
    /// Submit a task for execution and return a future that resolves to the task result.
    ///
    /// # Arguments
    /// * `task` - The task to execute
    ///
    /// # Returns
    /// A tuple containing the task ID and a future that resolves to the task result.
    async fn submit_task(&self, task: Box<dyn Task>) -> PrismaResult<(TaskId, tokio::sync::oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)>;
    async fn get_status(&self) -> PrismaResult<EngineStatus>;
}
