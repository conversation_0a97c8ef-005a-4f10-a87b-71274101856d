// Declare the submodules within the prisma_engine
pub mod decision_maker;
pub mod execution_strategies;
pub mod executor;
pub mod generics; // Keep even if empty
pub mod monitor;
pub mod tcl; // Task Creation Layer
pub mod agent_manager; // Make public

// Declare the files containing types, traits, and the main engine struct
pub mod types;
pub mod traits;
pub mod prisma_engine;

// Re-export key items for easier access from outside the prisma_engine module
pub use types::{
    TaskId, TaskPriority, TaskCategory, PrismaScore, SystemScore, ResourceType,
    ResourceUsage, ExecutionStrategyType, EngineConfig, EngineStatus, EngineErrorType,
    EngineResult,
};
pub use traits::{
    Task, ResourceMonitor, DecisionLogic, Executor, EngineController,
};
pub use prisma_engine::PrismaEngine;

// Re-export concrete task types from TCL
pub use tcl::{LlmTask, EmbeddingTask, StorageTask};
