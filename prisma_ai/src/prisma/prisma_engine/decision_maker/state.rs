// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/state.rs
// =================================================================================================
// Purpose: Implements state tracking for LLM models and other stateful components. This file
// contains functionality to monitor and analyze the current state of various system components,
// particularly LLM models, to inform decision-making processes about resource availability
// and optimal execution strategies.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports this module
//   - decision_maker/decision_maker.rs: Uses state information for decision making
//   - decision_maker/rules.rs: May use state information for rule evaluation
//   - decision_maker/types.rs: Uses types defined for state tracking
//   - decision_maker/traits.rs: May implement traits defined for state tracking
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses core engine types
//
//   - LLM Interface Dependencies:
//     - llm/interface/state.rs: Uses State trait for model state management
//     - llm/interface/properties.rs: Uses ModelProperties trait for model characteristics
//     - llm/interface/metadata.rs: Uses ModelMetadata trait for model information
//     - llm/interface/performance.rs: Uses performance metrics for resource monitoring
//     - llm/interface/memory.rs: Uses memory tracking for resource allocation
//     - llm/interface/cache.rs: Uses KV cache management for sequence tracking
//
//   - LLM Implementation Dependencies:
//     - llm/implementation/state_impl.rs: Uses concrete state implementation
//     - llm/implementation/properties_impl.rs: Uses concrete properties implementation
//     - llm/implementation/metadata_impl.rs: Uses concrete metadata implementation
//     - llm/implementation/performance_impl.rs: Uses concrete performance metrics
//     - llm/implementation/memory_impl.rs: Uses concrete memory tracking
//
//   - Agent Manager Dependencies:
//     - prisma_engine/agent_manager/agent_state.rs: Integrates with AgentStateManager
//     - prisma_engine/agent_manager/model_metadata.rs: Uses ModelMetadataManager
//     - prisma_engine/agent_manager/types.rs: Uses AgentId and AgentState types
//     - prisma_engine/agent_manager/agents.rs: Tracks agent-model associations
//
// - Module Interactions:
//   - Monitors LLM model state from llm module
//   - May track agent state from agent_manager module
//   - Provides state information to decision_maker for strategy selection
//
// Platform Considerations:
// - This module is platform-independent as it tracks abstract state information
// - Some state tracking may be platform-specific but is abstracted through interfaces
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use async_trait::async_trait;
use tokio::sync::{RwLock, Mutex};
use tokio::time;
use tracing::{debug, warn, error, info};

use crate::err::types::generics::PrismaResult;
// We'll use the fully qualified path when needed to avoid confusion between error types
use crate::prisma::prisma_engine::agent_manager::traits::AgentStateManager;
use crate::prisma::prisma_engine::agent_manager::model_metadata::ModelMetadataManager;
use crate::prisma::prisma_engine::agent_manager::model_metadata_trait::ModelMetadataManagerTrait;
use crate::prisma::prisma_engine::agent_manager::types::AgentId;

// Import LLM interface traits for real metrics
use crate::llm::interface::MemoryUsage;
use crate::llm::interface::KvCacheMetrics;
use crate::llm::interface::PerformanceMetrics;

use super::traits::StateTracker;
use super::types::{ModelState, StateTrackerConfig};

/// Implementation of the StateTracker trait for LLM models
#[derive(Debug)]
pub struct LlmStateTrackerImpl {
    /// Configuration for the state tracker
    config: StateTrackerConfig,
    /// Current state of all models
    model_states: Arc<RwLock<HashMap<String, ModelState>>>,
    /// Reference to the agent state manager
    agent_state_manager: Option<Arc<RwLock<dyn AgentStateManager>>>,
    /// Reference to the model metadata manager
    model_metadata_manager: Option<Arc<RwLock<ModelMetadataManager>>>,
    /// Whether the state tracker is running
    running: Arc<Mutex<bool>>,
    /// Last update time
    last_update: Arc<RwLock<SystemTime>>,
}

impl LlmStateTrackerImpl {
    /// Create a new LlmStateTrackerImpl with default configuration
    pub fn new() -> Self {
        LlmStateTrackerImpl {
            config: StateTrackerConfig::default(),
            model_states: Arc::new(RwLock::new(HashMap::new())),
            agent_state_manager: None,
            model_metadata_manager: None,
            running: Arc::new(Mutex::new(false)),
            last_update: Arc::new(RwLock::new(SystemTime::now())),
        }
    }

    /// Create a new LlmStateTrackerImpl with custom configuration
    pub fn with_config(config: StateTrackerConfig) -> Self {
        LlmStateTrackerImpl {
            config,
            model_states: Arc::new(RwLock::new(HashMap::new())),
            agent_state_manager: None,
            model_metadata_manager: None,
            running: Arc::new(Mutex::new(false)),
            last_update: Arc::new(RwLock::new(SystemTime::now())),
        }
    }

    /// Set the agent state manager
    pub fn set_agent_state_manager(&mut self, manager: Arc<RwLock<dyn AgentStateManager>>) {
        self.agent_state_manager = Some(manager);
    }

    /// Set the model metadata manager
    pub fn set_model_metadata_manager(&mut self, manager: Arc<RwLock<ModelMetadataManager>>) {
        self.model_metadata_manager = Some(manager);
    }

    /// Start the state tracker
    pub async fn start(&self) -> PrismaResult<()> {
        let mut running = self.running.lock().await;
        if *running {
            return Ok(());
        }

        *running = true;

        // Clone the Arc to move into the task
        let self_arc = Arc::new(self.clone());

        // Spawn a task to update model states periodically
        tokio::spawn(async move {
            let update_interval = Duration::from_millis(self_arc.config.update_interval_ms);
            let mut interval = time::interval(update_interval);

            loop {
                interval.tick().await;

                // Check if we should still be running
                let running = *self_arc.running.lock().await;
                if !running {
                    break;
                }

                // Update model states
                if let Err(e) = self_arc.update_all_model_states().await {
                    error!("Error updating model states: {:?}", e);
                }

                // Update last update time
                *self_arc.last_update.write().await = SystemTime::now();
            }
        });

        Ok(())
    }

    /// Stop the state tracker
    pub async fn stop(&self) -> PrismaResult<()> {
        let mut running = self.running.lock().await;
        *running = false;
        Ok(())
    }

    /// Update all model states
    async fn update_all_model_states(&self) -> PrismaResult<()> {
        // Get all model IDs from the model metadata manager
        let model_ids = if let Some(manager) = &self.model_metadata_manager {
            let metadata_manager = manager.read().await;
            let mut ids = Vec::new();

            // Use the get_metadata method to iterate through all models
            for model_id in metadata_manager.get_all_model_ids().await?.iter() {
                ids.push(model_id.clone());
            }

            ids
        } else {
            // If no model metadata manager is available, use the existing model states
            self.model_states.read().await.keys().cloned().collect::<Vec<_>>()
        };

        // Update each model state
        for model_id in model_ids {
            if let Err(e) = self.update_model_state_internal(&model_id).await {
                warn!("Error updating state for model {}: {:?}", model_id, e);
            }
        }

        // Clean up old models
        self.cleanup_old_models().await?;

        Ok(())
    }

    /// Update a single model state
    async fn update_model_state_internal(&self, model_id: &str) -> PrismaResult<()> {
        // Get model metadata manager
        let metadata_manager = if let Some(manager) = &self.model_metadata_manager {
            manager.read().await
        } else {
            return Err(crate::err::GenericError::from(format!("Model metadata manager not available for model {}", model_id)).into());
        };

        // Get model metadata
        let metadata_value = match metadata_manager.get_metadata(model_id) {
            Some(value) => value,
            None => return Err(crate::err::GenericError::from(format!("Metadata not found for model {}", model_id)).into()),
        };

        // Extract model information from metadata
        let model_info = metadata_manager.get_metadata_field(model_id, "model_info");

        // Get real memory usage from MemoryUsage trait if available
        let memory_usage = if let Some(memory_service) = self.get_memory_service(model_id).await {
            memory_service.total_bytes().await
        } else {
            // Default to metadata value or fallback
            model_info.as_ref()
                .and_then(|info| info.get("memory_usage").and_then(|m| m.as_u64()))
                .unwrap_or(1_000_000_000) // 1 GB fallback
        };

        // Get real KV cache usage from KvCacheMetrics trait if available
        let kv_cache_usage = if let Some(kv_cache_service) = self.get_kv_cache_service(model_id).await {
            kv_cache_service.usage_percentage().await
        } else {
            // Default to metadata value or fallback
            model_info.as_ref()
                .and_then(|info| info.get("kv_cache_usage").and_then(|k| k.as_f64()))
                .unwrap_or(0.0)
        };

        // Get real performance metrics from PerformanceMetrics trait if available
        let (tokens_processed, requests_processed, tokens_per_second) =
            if let Some(perf_service) = self.get_performance_service(model_id).await {
                (
                    perf_service.total_tokens_processed().await,
                    perf_service.total_requests_processed().await,
                    perf_service.tokens_per_second().await
                )
            } else {
                // Default to metadata values or fallbacks
                (
                    model_info.as_ref()
                        .and_then(|info| info.get("tokens_processed").and_then(|t| t.as_u64()))
                        .unwrap_or(0),
                    model_info.as_ref()
                        .and_then(|info| info.get("requests_processed").and_then(|r| r.as_u64()))
                        .unwrap_or(0),
                    model_info.as_ref()
                        .and_then(|info| info.get("tokens_per_second").and_then(|t| t.as_f64()))
                        .unwrap_or(0.0)
                )
            };

        // Get load time from metadata or use default
        let load_time = model_info.as_ref()
            .and_then(|info| info.get("load_time").and_then(|l| l.as_f64()))
            .unwrap_or(2.5); // 2.5 seconds fallback

        // Check if model is loaded
        let loaded = model_info.as_ref()
            .and_then(|info| info.get("loaded").and_then(|l| l.as_bool()))
            .unwrap_or(true);

        // Get agent IDs using this model from AgentStateManager
        let agent_ids = self.get_agents_using_model(model_id).await;

        // Check if the model is active (being used for inference)
        let is_active = self.is_model_active(model_id).await;

        // Check if the model is in use
        let in_use = !agent_ids.is_empty() || is_active;

        // Create or update the model state
        let model_state = ModelState {
            model_id: model_id.to_string(),
            loaded,
            in_use,
            memory_usage,
            last_used: if in_use {
                SystemTime::now()
            } else {
                self.model_states.read().await
                    .get(model_id)
                    .map(|state| state.last_used)
                    .unwrap_or_else(SystemTime::now)
            },
            load_time,
            tokens_processed,
            requests_processed,
            avg_tokens_per_second: tokens_per_second,
            kv_cache_usage,
        };

        // Update the model state
        self.model_states.write().await.insert(model_id.to_string(), model_state);

        debug!("Updated state for model {}: memory={}MB, tokens={}, kv_cache={}%",
               model_id, memory_usage / (1024 * 1024), tokens_processed, kv_cache_usage * 100.0);

        Ok(())
    }

    /// Get the memory service for a model if available
    async fn get_memory_service(&self, _model_id: &str) -> Option<Arc<dyn MemoryUsage>> {
        // In a real implementation, this would retrieve the memory service from a registry
        // For now, we'll return None to use the fallback values
        None
    }

    /// Get the KV cache service for a model if available
    async fn get_kv_cache_service(&self, _model_id: &str) -> Option<Arc<dyn KvCacheMetrics>> {
        // In a real implementation, this would retrieve the KV cache service from a registry
        // For now, we'll return None to use the fallback values
        None
    }

    /// Get the performance metrics service for a model if available
    async fn get_performance_service(&self, _model_id: &str) -> Option<Arc<dyn PerformanceMetrics>> {
        // In a real implementation, this would retrieve the performance service from a registry
        // For now, we'll return None to use the fallback values
        None
    }

    /// Check if a model is currently active (being used for inference)
    async fn is_model_active(&self, _model_id: &str) -> bool {
        // In a real implementation, this would check if the model is currently being used
        // For now, we'll return false
        false
    }

    /// Get a list of agent IDs that are using a specific model
    async fn get_agents_using_model(&self, model_id: &str) -> Vec<AgentId> {
        let agent_ids = Vec::new();

        // In a real implementation, this would query the agent manager to find agents using this model
        // For now, we'll return an empty vector
        debug!("Would check for agents using model {}", model_id);

        agent_ids
    }

    /// Clean up old models
    async fn cleanup_old_models(&self) -> PrismaResult<()> {
        let now = SystemTime::now();
        let mut states = self.model_states.write().await;

        // Get models to unload
        let models_to_unload: Vec<String> = states.iter()
            .filter(|(_, state)| {
                // Don't unload models that are in use
                if state.in_use {
                    return false;
                }

                // Don't unload models that are in the preload list
                if self.config.preload_models && self.config.preload_model_ids.contains(&state.model_id) {
                    return false;
                }

                // Unload models that haven't been used for longer than the TTL
                match now.duration_since(state.last_used) {
                    Ok(duration) => duration > self.config.model_ttl,
                    Err(_) => false, // If the last_used time is in the future, don't unload
                }
            })
            .map(|(id, _)| id.clone())
            .collect();

        // Unload models
        for model_id in models_to_unload {
            debug!("Unloading model {} due to inactivity", model_id);

            // Get model metadata
            if let Some(manager) = &self.model_metadata_manager {
                if let Some(_metadata) = manager.read().await.get_metadata(&model_id) {
                    // Unload the model
                    if let Err(e) = self.unload_model_internal(&model_id).await {
                        warn!("Failed to unload model {}: {:?}", model_id, e);
                    } else {
                        // Update the state to indicate it's unloaded
                        if let Some(state) = states.get_mut(&model_id) {
                            state.loaded = false;
                            debug!("Successfully unloaded model {}", model_id);
                        }
                    }
                }
            }
        }

        // Enforce max loaded models limit
        if self.config.max_loaded_models > 0 {
            let loaded_models: Vec<(String, SystemTime)> = states.iter()
                .filter(|(_, state)| state.loaded && !state.in_use)
                .map(|(id, state)| (id.clone(), state.last_used))
                .collect();

            if loaded_models.len() > self.config.max_loaded_models {
                // Sort by last used time (oldest first)
                let mut sorted_models = loaded_models;
                sorted_models.sort_by(|a, b| a.1.cmp(&b.1));

                // Unload excess models
                let models_to_unload = &sorted_models[0..sorted_models.len() - self.config.max_loaded_models];

                for (model_id, _) in models_to_unload {
                    debug!("Unloading model {} due to max loaded models limit", model_id);

                    // Get model metadata
                    if let Some(manager) = &self.model_metadata_manager {
                        if let Some(_metadata) = manager.read().await.get_metadata(model_id) {
                            // Unload the model
                            if let Err(e) = self.unload_model_internal(model_id).await {
                                warn!("Failed to unload model {}: {:?}", model_id, e);
                            } else {
                                // Update the state to indicate it's unloaded
                                if let Some(state) = states.get_mut(model_id) {
                                    state.loaded = false;
                                    debug!("Successfully unloaded model {} due to max loaded models limit", model_id);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Preload models
    async fn preload_models(&self) -> PrismaResult<()> {
        if !self.config.preload_models || self.config.preload_model_ids.is_empty() {
            return Ok(());
        }

        for model_id in &self.config.preload_model_ids {
            info!("Preloading model {}", model_id);

            // Get model metadata
            if let Some(manager) = &self.model_metadata_manager {
                if let Some(_metadata) = manager.read().await.get_metadata(model_id) {
                    // Check if the model is already loaded
                    let is_loaded = self.is_model_loaded(model_id).await;

                    if !is_loaded {
                        // In a real implementation, this would trigger the model loading process
                        // For now, we'll just update the model state
                        debug!("Model {} not loaded, initiating load process", model_id);

                        // Load the model (in a real implementation, this would call the LLM service)
                        if let Err(e) = self.load_model(model_id).await {
                            warn!("Failed to load model {}: {:?}", model_id, e);
                            continue;
                        }
                    } else {
                        debug!("Model {} already loaded", model_id);
                    }

                    // Update the model state
                    self.update_model_state_internal(model_id).await?;
                } else {
                    warn!("Model {} not found in metadata manager, cannot preload", model_id);
                }
            } else {
                warn!("No metadata manager available, cannot preload model {}", model_id);
            }
        }

        Ok(())
    }

    /// Check if a model is loaded
    async fn is_model_loaded(&self, model_id: &str) -> bool {
        // First check our internal state
        if let Ok(state) = self.get_model_state(model_id).await {
            if state.loaded {
                return true;
            }
        }

        // If not found in our state, check with the metadata manager
        if let Some(manager) = &self.model_metadata_manager {
            let model_info = manager.read().await.get_metadata_field(model_id, "model_info");
            if let Some(info) = model_info {
                return info.get("loaded")
                    .and_then(|l| l.as_bool())
                    .unwrap_or(false);
            }
        }

        false
    }

    /// Load a model
    async fn load_model(&self, model_id: &str) -> PrismaResult<()> {
        // In a real implementation, this would call the LLM service to load the model
        info!("Loading model {}", model_id);

        // Check if the model is already loaded
        if self.is_model_loaded(model_id).await {
            debug!("Model {} is already loaded", model_id);
            return Ok(());
        }

        // Attempt to load the model through the LLM service
        if let Err(e) = self.load_model_internal(model_id).await {
            return Err(e);
        }

        // Update the model state to indicate it's loaded
        let mut states = self.model_states.write().await;
        if let Some(state) = states.get_mut(model_id) {
            state.loaded = true;
            state.last_used = SystemTime::now();
        } else {
            // Create a new model state with initial values
            // In a real implementation, these would come from the model metadata
            let model_state = ModelState {
                model_id: model_id.to_string(),
                loaded: true,
                in_use: false,
                memory_usage: 0, // Will be updated in update_model_state_internal
                last_used: SystemTime::now(),
                load_time: 0.0, // Will be updated in update_model_state_internal
                tokens_processed: 0,
                requests_processed: 0,
                avg_tokens_per_second: 0.0,
                kv_cache_usage: 0.0,
            };

            states.insert(model_id.to_string(), model_state);
        }

        // Update the model state with real metrics
        drop(states); // Release the write lock
        self.update_model_state_internal(model_id).await?;

        info!("Model {} loaded successfully", model_id);
        Ok(())
    }

    /// Internal method to load a model through the LLM service
    async fn load_model_internal(&self, model_id: &str) -> PrismaResult<()> {
        // In a real implementation, this would call the LLM service to load the model
        // For now, we'll simulate loading
        debug!("Loading model {} through LLM service (simulated)", model_id);

        // Simulate loading time
        tokio::time::sleep(Duration::from_millis(100)).await;

        Ok(())
    }

    /// Internal method to unload a model through the LLM service
    async fn unload_model_internal(&self, model_id: &str) -> PrismaResult<()> {
        // In a real implementation, this would call the LLM service to unload the model
        // For now, we'll simulate unloading
        debug!("Unloading model {} through LLM service (simulated)", model_id);

        // Simulate unloading time
        tokio::time::sleep(Duration::from_millis(50)).await;

        Ok(())
    }
}

// Implement Clone for LlmStateTrackerImpl
impl Clone for LlmStateTrackerImpl {
    fn clone(&self) -> Self {
        // Note: This is a shallow clone that shares the same Arc<RwLock> instances
        // This is intentional as we want to share the same state across clones
        LlmStateTrackerImpl {
            config: self.config.clone(),
            model_states: Arc::clone(&self.model_states),
            agent_state_manager: self.agent_state_manager.clone(),
            model_metadata_manager: self.model_metadata_manager.clone(),
            running: Arc::clone(&self.running),
            last_update: Arc::clone(&self.last_update),
        }
    }
}

#[async_trait]
impl StateTracker for LlmStateTrackerImpl {
    async fn get_model_state(&self, model_id: &str) -> PrismaResult<ModelState> {
        // Try to get the model state from the cache
        let states = self.model_states.read().await;
        if let Some(state) = states.get(model_id) {
            return Ok(state.clone());
        }

        // If not found, try to update the model state
        drop(states); // Release the read lock
        self.update_model_state_internal(model_id).await?;

        // Try again
        let states = self.model_states.read().await;
        if let Some(state) = states.get(model_id) {
            Ok(state.clone())
        } else {
            Err(crate::err::types::errors_mod::PrismaError::Generic(format!("Model state not found for model {}", model_id)).into())
        }
    }

    async fn update_model_state(&mut self, model_id: &str, state: ModelState) -> PrismaResult<()> {
        self.model_states.write().await.insert(model_id.to_string(), state);
        Ok(())
    }

    async fn get_all_model_states(&self) -> PrismaResult<HashMap<String, ModelState>> {
        Ok(self.model_states.read().await.clone())
    }

    async fn is_model_available(&self, model_id: &str) -> PrismaResult<bool> {
        let state = self.get_model_state(model_id).await?;
        Ok(state.loaded && !state.in_use)
    }

    async fn get_model_load_time(&self, model_id: &str) -> PrismaResult<f64> {
        let state = self.get_model_state(model_id).await?;
        Ok(state.load_time)
    }

    async fn get_model_memory_usage(&self, model_id: &str) -> PrismaResult<u64> {
        let state = self.get_model_state(model_id).await?;
        Ok(state.memory_usage)
    }
}

/// Create a new StateTracker with default configuration
pub fn create_state_tracker() -> Box<dyn StateTracker> {
    Box::new(LlmStateTrackerImpl::new())
}

/// Create a new StateTracker with custom configuration
pub fn create_state_tracker_with_config(config: StateTrackerConfig) -> Box<dyn StateTracker> {
    Box::new(LlmStateTrackerImpl::with_config(config))
}

/// Create a new StateTracker with agent state manager and model metadata manager
pub fn create_state_tracker_with_managers(
    config: StateTrackerConfig,
    agent_state_manager: Arc<RwLock<dyn AgentStateManager>>,
    model_metadata_manager: Arc<RwLock<ModelMetadataManager>>,
) -> Box<dyn StateTracker> {
    let mut tracker = LlmStateTrackerImpl::with_config(config);
    tracker.set_agent_state_manager(agent_state_manager);
    tracker.set_model_metadata_manager(model_metadata_manager);
    Box::new(tracker)
}