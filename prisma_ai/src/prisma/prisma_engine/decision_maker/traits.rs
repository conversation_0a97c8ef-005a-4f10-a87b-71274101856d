// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/traits.rs
// =================================================================================================
// Purpose: Defines traits specific to the internal workings of the decision_maker module.
// This file contains trait definitions that establish interfaces for different decision-making
// components, allowing for modular and extensible decision logic within the module.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports these traits
//   - decision_maker/decision_maker.rs: May implement these traits
//   - decision_maker/rules.rs: Likely defines rule-related traits
//   - decision_maker/state.rs: May define state-related traits
//
// - External Dependencies:
//   - prisma_engine/traits.rs: Contains the main DecisionLogic trait
//   - async_trait: For async trait methods if needed
//
// Note: The main `DecisionLogic` trait implemented by the aggregate `RuleBasedDecisionMaker` struct
// is defined in the parent `prisma_engine::traits` module.
//
// Platform Considerations:
// - This module is platform-independent as it defines abstract traits
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::fmt::Debug;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    TaskCategory, TaskPriority, PrismaScore, SystemScore, ExecutionStrategyType, ResourceType
};
use super::types::{RuleType, RuleResult, DecisionReason, ModelState};

/// Trait for rule-based decision components
#[async_trait]
pub trait Rule: Send + Sync + Debug {
    /// Get the type of this rule
    fn rule_type(&self) -> RuleType;

    /// Get the name of this rule
    fn name(&self) -> &str;

    /// Get the description of this rule
    fn description(&self) -> &str;

    /// Evaluate the rule based on task and system information
    async fn evaluate(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<RuleResult>;

    /// Check if this rule is enabled
    fn is_enabled(&self) -> bool;

    /// Enable or disable this rule
    fn set_enabled(&mut self, enabled: bool);

    /// Get the priority of this rule (higher priority rules are evaluated first)
    fn priority(&self) -> u32;

    /// Set the priority of this rule
    fn set_priority(&mut self, priority: u32);
}

/// Trait for a collection of rules
#[async_trait]
pub trait RuleSet: Send + Sync + Debug {
    /// Add a rule to the rule set
    fn add_rule(&mut self, rule: Box<dyn Rule>);

    /// Remove a rule from the rule set
    fn remove_rule(&mut self, rule_name: &str) -> Option<Box<dyn Rule>>;

    /// Get a rule by name
    fn get_rule(&self, rule_name: &str) -> Option<&dyn Rule>;

    /// Get a mutable reference to a rule by name
    fn get_rule_mut(&mut self, rule_name: &str) -> Option<&mut dyn Rule>;

    /// Get all rules in this rule set
    fn get_rules(&self) -> Vec<&dyn Rule>;

    /// Get all rules of a specific type
    fn get_rules_by_type(&self, rule_type: RuleType) -> Vec<&dyn Rule>;

    /// Evaluate all rules in this rule set
    async fn evaluate_rules(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<Vec<RuleResult>>;

    /// Evaluate rules of a specific type
    async fn evaluate_rules_by_type(
        &self,
        rule_type: RuleType,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<Vec<RuleResult>>;
}

/// Trait for tracking LLM model state
#[async_trait]
pub trait StateTracker: Send + Sync + Debug {
    /// Get the current state of a model
    async fn get_model_state(&self, model_id: &str) -> PrismaResult<ModelState>;

    /// Update the state of a model
    async fn update_model_state(&mut self, model_id: &str, state: ModelState) -> PrismaResult<()>;

    /// Get all model states
    async fn get_all_model_states(&self) -> PrismaResult<HashMap<String, ModelState>>;

    /// Check if a model is available for use
    async fn is_model_available(&self, model_id: &str) -> PrismaResult<bool>;

    /// Get the estimated load time for a model
    async fn get_model_load_time(&self, model_id: &str) -> PrismaResult<f64>;

    /// Get the estimated memory usage of a model
    async fn get_model_memory_usage(&self, model_id: &str) -> PrismaResult<u64>;
}

/// Trait for components that can make execution strategy decisions
#[async_trait]
pub trait StrategySelector: Send + Sync + Debug {
    /// Select an execution strategy based on task and system information
    async fn select_strategy(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<(ExecutionStrategyType, DecisionReason)>;

    /// Check if a task can be executed with the given system resources
    async fn can_execute(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore,
    ) -> PrismaResult<bool>;

    /// Get the recommended execution strategy for a task category
    fn get_recommended_strategy(&self, task_category: &TaskCategory) -> ExecutionStrategyType;

    /// Set the recommended execution strategy for a task category
    fn set_recommended_strategy(&mut self, task_category: &TaskCategory, strategy: ExecutionStrategyType);
}

/// Trait for components that can adjust task priorities
#[async_trait]
pub trait PriorityAdjuster: Send + Sync + Debug {
    /// Adjust a task's priority based on system conditions
    async fn adjust_priority(
        &self,
        current_priority: TaskPriority,
        task_category: &TaskCategory,
        system_score: &SystemScore,
    ) -> PrismaResult<TaskPriority>;

    /// Check if a priority adjustment is needed
    async fn needs_adjustment(
        &self,
        current_priority: TaskPriority,
        task_category: &TaskCategory,
        system_score: &SystemScore,
    ) -> PrismaResult<bool>;
}

/// Trait for components that can analyze resource constraints
#[async_trait]
pub trait ResourceConstraintAnalyzer: Send + Sync + Debug {
    /// Check if a task's resource requirements can be met
    async fn check_constraints(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore,
    ) -> PrismaResult<bool>;

    /// Get the most constrained resource
    async fn get_most_constrained_resource(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore,
    ) -> PrismaResult<Option<ResourceType>>;

    /// Get the constraint ratio for a resource (required / available)
    async fn get_constraint_ratio(
        &self,
        resource_type: ResourceType,
        task_score: &PrismaScore,
        system_score: &SystemScore,
    ) -> PrismaResult<f64>;
}
