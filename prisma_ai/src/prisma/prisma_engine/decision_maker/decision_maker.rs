// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/decision_maker.rs
// =================================================================================================
// Purpose: Implements the main decision-making logic for the PrismaEngine. This file contains
// the RuleBasedDecisionMaker struct that implements the DecisionLogic trait, providing the core
// functionality for determining the optimal execution strategy for tasks based on their resource
// requirements and current system resource availability.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports this module
//   - decision_maker/types.rs: Uses DecisionMakerConfig and other types
//   - decision_maker/rules.rs: May use rule definitions
//   - decision_maker/state.rs: May use state tracking
//   - prisma_scores: Uses task resource requirement evaluation
//   - system_scores: Uses system resource availability evaluation
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses core engine types
//   - prisma_engine/traits.rs: Implements the DecisionLogic trait
//   - err: Uses PrismaResult for error handling
//   - tracing: For logging and debugging
//
// - Module Interactions:
//   - Receives task information from PrismaEngine
//   - Receives system information from Monitor module via PrismaEngine
//   - Provides execution strategy decisions to PrismaEngine
//   - Influences task routing in Executor module
//
// Platform Considerations:
// - This module is platform-independent as it makes decisions based on abstract scores
// =================================================================================================

use async_trait::async_trait;
use tracing::{debug, warn, info};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use num_cpus;

// Use crate-level error types and engine types/traits
use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, SystemScore, ExecutionStrategyType, TaskCategory, TaskPriority, ResourceType
};
use crate::prisma::prisma_engine::traits::DecisionLogic; // Import the main trait
use crate::prisma::prisma_engine::monitor::system::traits::SystemMonitoring;

// Import decision_maker specific types and logic
use super::types::{DecisionMakerConfig, StateTrackerConfig, DecisionReason, TaskAnalysis, SystemAnalysis, TaskComplexity};
use super::prisma_scores::{
    create_score_evaluator,
    ScoreEvaluator,
    PrismaScoreConfig
};
use super::system_scores::{
    SystemScoreEvaluator,
    create_system_score_evaluator,
    create_system_score_evaluator_with_config,
    SystemScoreConfig,
    DetailedSystemScore,
    is_system_critical,
    get_most_constrained_resource,
    ResourceMetrics
};
use super::rules::{create_rule_set, create_empty_rule_set};
use super::state::{create_state_tracker, create_state_tracker_with_config};
use super::traits::{RuleSet, StateTracker, StrategySelector};

// A rule-based implementation of the DecisionLogic trait
// We can't derive Debug because ScoreEvaluator and SystemScoreEvaluator don't implement Debug
pub struct RuleBasedDecisionMaker {
    /// Configuration for the decision maker
    config: DecisionMakerConfig,
    /// Evaluator for task resource requirements (PrismaScore)
    score_evaluator: Arc<dyn ScoreEvaluator>,
    /// Evaluator for system resource availability (SystemScore)
    system_score_evaluator: Option<Arc<RwLock<Box<dyn SystemScoreEvaluator>>>>,
    /// Rule set for decision making
    rule_set: Option<Arc<RwLock<Box<dyn RuleSet>>>>,
    /// State tracker for LLM models
    state_tracker: Option<Arc<RwLock<Box<dyn StateTracker>>>>,
    /// Learned strategy recommendations for categories
    learned_strategies: Arc<RwLock<HashMap<TaskCategory, ExecutionStrategyType>>>,
}

// Implement Debug manually
impl std::fmt::Debug for RuleBasedDecisionMaker {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("RuleBasedDecisionMaker")
            .field("config", &self.config)
            .field("score_evaluator", &"<dyn ScoreEvaluator>")
            .field("system_score_evaluator", &"<Option<dyn SystemScoreEvaluator>>")
            .field("rule_set", &"<Option<dyn RuleSet>>")
            .field("state_tracker", &"<Option<dyn StateTracker>>")
            .field("learned_strategies", &"<HashMap<TaskCategory, ExecutionStrategyType>>")
            .finish()
    }
}

impl RuleBasedDecisionMaker {
    /// Create a new RuleBasedDecisionMaker with default settings
    pub fn new(config: DecisionMakerConfig) -> Self {
        // Create a PrismaScoreConfig with default values
        let prisma_config = PrismaScoreConfig::default();

        // Create a score evaluator for task resource requirements
        let score_evaluator = create_score_evaluator(Some(prisma_config));

        // Create a default rule set
        let rule_set = create_rule_set();
        let rule_set = Some(Arc::new(RwLock::new(rule_set)));

        // Create a default state tracker
        let state_tracker = create_state_tracker();
        let state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        // System score evaluator is initialized later with set_system_monitor
        RuleBasedDecisionMaker {
            config,
            score_evaluator,
            system_score_evaluator: None,
            rule_set,
            state_tracker,
            learned_strategies: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Set the system monitor for system resource availability evaluation
    pub fn with_system_monitor(
        config: DecisionMakerConfig,
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>
    ) -> Self {
        // Create a PrismaScoreConfig with default values
        let prisma_config = PrismaScoreConfig::default();

        // Create a score evaluator for task resource requirements
        let score_evaluator = create_score_evaluator(Some(prisma_config));

        // Create a system score evaluator with default settings
        let system_score_evaluator = create_system_score_evaluator(system_monitor);
        let system_score_evaluator = Some(Arc::new(RwLock::new(system_score_evaluator)));

        // Create a default rule set
        let rule_set = create_rule_set();
        let rule_set = Some(Arc::new(RwLock::new(rule_set)));

        // Create a default state tracker
        let state_tracker = create_state_tracker();
        let state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        RuleBasedDecisionMaker {
            config,
            score_evaluator,
            system_score_evaluator,
            rule_set,
            state_tracker,
            learned_strategies: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Set the system monitor for system resource availability evaluation with custom config
    pub fn with_system_monitor_and_config(
        config: DecisionMakerConfig,
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>,
        system_score_config: SystemScoreConfig
    ) -> Self {
        // Create a PrismaScoreConfig with default values
        let prisma_config = PrismaScoreConfig::default();

        // Create a score evaluator for task resource requirements
        let score_evaluator = create_score_evaluator(Some(prisma_config));

        // Create a system score evaluator with custom settings
        let system_score_evaluator = create_system_score_evaluator_with_config(system_monitor, system_score_config);
        let system_score_evaluator = Some(Arc::new(RwLock::new(system_score_evaluator)));

        // Create a default rule set
        let rule_set = create_rule_set();
        let rule_set = Some(Arc::new(RwLock::new(rule_set)));

        // Create a default state tracker
        let state_tracker = create_state_tracker();
        let state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        RuleBasedDecisionMaker {
            config,
            score_evaluator,
            system_score_evaluator,
            rule_set,
            state_tracker,
            learned_strategies: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Set the system monitor after initialization
    pub async fn set_system_monitor(
        &mut self,
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>
    ) -> PrismaResult<()> {
        // Create a system score evaluator with default settings
        let system_score_evaluator = create_system_score_evaluator(system_monitor);

        // Store the system score evaluator
        self.system_score_evaluator = Some(Arc::new(RwLock::new(system_score_evaluator)));

        Ok(())
    }

    /// Set the rule set after initialization
    pub async fn set_rule_set(
        &mut self,
        rule_set: Box<dyn RuleSet>
    ) -> PrismaResult<()> {
        // Store the rule set
        self.rule_set = Some(Arc::new(RwLock::new(rule_set)));

        Ok(())
    }

    /// Set the state tracker after initialization
    pub async fn set_state_tracker(
        &mut self,
        state_tracker: Box<dyn StateTracker>
    ) -> PrismaResult<()> {
        // Store the state tracker
        self.state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        Ok(())
    }

    /// Set the state tracker with custom configuration
    pub async fn set_state_tracker_with_config(
        &mut self,
        config: StateTrackerConfig
    ) -> PrismaResult<()> {
        // Create a state tracker with custom configuration
        let state_tracker = create_state_tracker_with_config(config);

        // Store the state tracker
        self.state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        Ok(())
    }

    /// Helper function to check resource availability against task requirements
    async fn check_resource_constraints(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore
    ) -> bool {
        // Convert SystemScore to HashMap<ResourceType, ResourceUsage>
        let mut available_resources = HashMap::new();
        for (resource, usage) in &system_score.availability {
            available_resources.insert(*resource, *usage);
        }

        // Use the score evaluator to check constraints
        match self.score_evaluator.check_resource_constraints(task_score, &available_resources).await {
            Ok(result) => result,
            Err(e) => {
                warn!("Error checking resource constraints: {:?}", e);
                // Default to true if there's an error to avoid blocking tasks
                true
            }
        }
    }

    /// Get the current system score from the system score evaluator
    async fn get_current_system_score(&self) -> Option<DetailedSystemScore> {
        if let Some(evaluator) = &self.system_score_evaluator {
            match evaluator.read().await.get_current_score().await {
                Ok(score) => Some(score),
                Err(e) => {
                    warn!("Error getting current system score: {:?}", e);
                    None
                }
            }
        } else {
            None
        }
    }

    /// Check if the system is in a critical state
    async fn is_system_critical(&self) -> bool {
        if let Some(score) = self.get_current_system_score().await {
            // Use the system_scores module function to check if the system is critical
            is_system_critical(&score, &SystemScoreConfig::default())
        } else {
            // If we can't get the system score, assume it's not critical
            false
        }
    }

    /// Get the most constrained resource in the system
    async fn get_most_constrained_resource(&self) -> Option<ResourceType> {
        if let Some(score) = self.get_current_system_score().await {
            // Use the system_scores module function to get the most constrained resource
            get_most_constrained_resource(&score)
        } else {
            // If we can't get the system score, return None
            None
        }
    }

    /// Get a model's state from the state tracker
    pub async fn get_model_state(&self, model_id: &str) -> PrismaResult<Option<super::types::ModelState>> {
        if let Some(state_tracker) = &self.state_tracker {
            match state_tracker.read().await.get_model_state(model_id).await {
                Ok(state) => Ok(Some(state)),
                Err(e) => {
                    warn!("Error getting model state for {}: {:?}", model_id, e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    /// Check if a model is available
    pub async fn is_model_available(&self, model_id: &str) -> bool {
        if let Some(state_tracker) = &self.state_tracker {
            match state_tracker.read().await.is_model_available(model_id).await {
                Ok(available) => available,
                Err(e) => {
                    warn!("Error checking model availability for {}: {:?}", model_id, e);
                    false
                }
            }
        } else {
            false
        }
    }

    /// Get all model states
    pub async fn get_all_model_states(&self) -> PrismaResult<HashMap<String, super::types::ModelState>> {
        if let Some(state_tracker) = &self.state_tracker {
            state_tracker.read().await.get_all_model_states().await
        } else {
            Ok(HashMap::new())
        }
    }

    /// Add a rule to the rule set
    pub async fn add_rule(&self, rule: Box<dyn super::traits::Rule>) -> PrismaResult<()> {
        if let Some(rule_set) = &self.rule_set {
            rule_set.write().await.add_rule(rule);
            Ok(())
        } else {
            Err(crate::err::types::errors_mod::PrismaError::Generic("No rule set available".to_string()).into())
        }
    }

    /// Get all rules in the rule set
    pub async fn get_rules(&self) -> PrismaResult<Vec<String>> {
        if let Some(rule_set) = &self.rule_set {
            let rule_set_guard = rule_set.read().await;
            let rules = rule_set_guard.get_rules();
            let rule_names = rules.iter().map(|r| r.name().to_string()).collect();
            Ok(rule_names)
        } else {
            Ok(Vec::new())
        }
    }

    /// Enable or disable a rule
    pub async fn set_rule_enabled(&self, rule_name: &str, enabled: bool) -> PrismaResult<()> {
        if let Some(rule_set) = &self.rule_set {
            let mut rule_set = rule_set.write().await;
            if let Some(rule) = rule_set.get_rule_mut(rule_name) {
                rule.set_enabled(enabled);
                Ok(())
            } else {
                Err(crate::err::types::errors_mod::PrismaError::Generic(format!("Rule {} not found", rule_name)).into())
            }
        } else {
            Err(crate::err::types::errors_mod::PrismaError::Generic("No rule set available".to_string()).into())
        }
    }

    /// Start the state tracker
    pub async fn start_state_tracker(&self) -> PrismaResult<()> {
        if let Some(_state_tracker) = &self.state_tracker {
            // We can't directly access the start method because it's not part of the StateTracker trait
            // Instead, we'll create a new state tracker with the same configuration
            // This is a workaround until we modify the StateTracker trait to include start/stop methods

            // For now, just log that we would start the state tracker
            info!("State tracker would be started (implementation pending)");
            Ok(())
        } else {
            warn!("No state tracker available");
            Ok(()) // Not an error, just no-op
        }
    }

    /// Update the configuration
    pub fn update_config(&mut self, config: DecisionMakerConfig) {
        self.config = config;
    }

    /// Get the current configuration
    pub fn get_config(&self) -> &DecisionMakerConfig {
        &self.config
    }

    /// Set a recommended strategy for a specific task category (used by learning system)
    pub async fn set_recommended_strategy(
        &self,
        category: &TaskCategory,
        strategy: ExecutionStrategyType,
    ) {
        let mut learned_strategies = self.learned_strategies.write().await;
        learned_strategies.insert(category.clone(), strategy);
        debug!("Set recommended strategy for {:?}: {:?}", category, strategy);
    }

    /// Get the recommended strategy for a task category (if any)
    pub async fn get_recommended_strategy(&self, category: &TaskCategory) -> Option<ExecutionStrategyType> {
        let learned_strategies = self.learned_strategies.read().await;
        learned_strategies.get(category).copied()
    }

    /// Clear all learned strategy recommendations
    pub async fn clear_learned_strategies(&self) {
        let mut learned_strategies = self.learned_strategies.write().await;
        learned_strategies.clear();
        debug!("Cleared all learned strategy recommendations");
    }

    /// Create a new rule set with the given rules
    pub async fn create_new_rule_set(&mut self, rules: Vec<Box<dyn super::traits::Rule>>) -> PrismaResult<()> {
        // Create a new empty rule set
        let mut rule_set = create_empty_rule_set();

        // Add all rules
        for rule in rules {
            rule_set.add_rule(rule);
        }

        // Set the rule set
        self.rule_set = Some(Arc::new(RwLock::new(rule_set)));

        Ok(())
    }

    /// Extract the model ID from a task score
    fn extract_model_id_from_task(&self, task_score: &PrismaScore) -> String {
        // In a real implementation, we would extract the model ID from the task score
        // For now, we'll use a default model ID

        // Check if there's any metadata in the resources
        for (resource, requirement) in &task_score.resources {
            // LLM models often have specific GPU or memory requirements
            if *resource == ResourceType::GPU || *resource == ResourceType::Memory {
                // In a real implementation, we would extract model information from the requirement
                debug!("Found resource requirement for {:?}: {}", resource, requirement.0);
            }
        }

        // If we can't find a model ID, use a default
        // In a production system, this should be configured or determined from context
        "default_model".to_string()
    }

    /// Analyze task characteristics dynamically
    async fn analyze_task_characteristics(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<TaskAnalysis> {
        // Analyze CPU intensity based on task category and resource requirements
        let is_cpu_intensive = self.is_task_cpu_intensive(task_category, task_score).await;

        // Analyze I/O intensity
        let is_io_intensive = self.is_task_io_intensive(task_category, task_score).await;

        // Analyze memory intensity
        let is_memory_intensive = self.is_task_memory_intensive(task_category, task_score).await;

        // Estimate execution duration based on category and priority
        let estimated_duration_ms = self.estimate_task_duration(task_category, task_priority).await;

        // Calculate resource intensity score
        let resource_intensity = self.calculate_resource_intensity(task_score).await;

        // Calculate parallelization potential
        let parallelization_potential = self.calculate_parallelization_potential(task_category, task_score).await;

        // Determine complexity level
        let complexity_level = self.determine_task_complexity(task_category, task_score, task_priority).await;

        Ok(TaskAnalysis {
            is_cpu_intensive,
            is_io_intensive,
            is_memory_intensive,
            estimated_duration_ms,
            resource_intensity,
            parallelization_potential,
            complexity_level,
        })
    }

    /// Analyze current system state
    async fn analyze_system_state(&self, system_score: &SystemScore) -> PrismaResult<SystemAnalysis> {
        // Get CPU utilization
        let cpu_utilization = system_score.availability
            .get(&ResourceType::CPU)
            .map(|usage| 1.0 - usage.0) // Convert availability to utilization
            .unwrap_or(0.0);

        // Get memory utilization
        let memory_utilization = system_score.availability
            .get(&ResourceType::Memory)
            .map(|usage| 1.0 - usage.0)
            .unwrap_or(0.0);

        // Get I/O utilization
        let io_utilization = system_score.availability
            .get(&ResourceType::DiskIO)
            .map(|usage| 1.0 - usage.0)
            .unwrap_or(0.0);

        // Determine if system is under high load
        let is_high_load = cpu_utilization > 0.8 || memory_utilization > 0.8 || io_utilization > 0.8;

        // Check if system is in critical state
        let is_critical = self.is_system_critical().await;

        // Estimate available resources
        let available_cpu_cores = num_cpus::get();
        let available_memory_mb = 8192; // Default, should be retrieved from system monitor

        // Calculate system health score
        let health_score = self.calculate_system_health_score(cpu_utilization, memory_utilization, io_utilization).await;

        Ok(SystemAnalysis {
            cpu_utilization,
            memory_utilization,
            io_utilization,
            is_high_load,
            is_critical,
            available_cpu_cores,
            available_memory_mb,
            health_score,
        })
    }

    /// Check if a task is CPU-intensive based on category and resource requirements
    async fn is_task_cpu_intensive(&self, task_category: &TaskCategory, task_score: &PrismaScore) -> bool {
        // Check category-based indicators
        let category_cpu_intensive = matches!(
            task_category,
            TaskCategory::EmbeddingGeneration | TaskCategory::FileProcessing
        );

        // Check resource requirements
        let cpu_requirement = task_score.resources
            .get(&ResourceType::CPU)
            .map(|usage| usage.0)
            .unwrap_or(0.0);

        // Task is CPU-intensive if category suggests it or CPU requirement is high
        category_cpu_intensive || cpu_requirement > 0.7
    }

    /// Check if a task is I/O-intensive based on category and resource requirements
    async fn is_task_io_intensive(&self, task_category: &TaskCategory, task_score: &PrismaScore) -> bool {
        // Check category-based indicators
        let category_io_intensive = matches!(
            task_category,
            TaskCategory::DatabaseQuery | TaskCategory::NetworkRequest | TaskCategory::LLMInference
        );

        // Check resource requirements
        let disk_requirement = task_score.resources
            .get(&ResourceType::DiskIO)
            .map(|usage| usage.0)
            .unwrap_or(0.0);

        let network_requirement = task_score.resources
            .get(&ResourceType::NetworkBandwidth)
            .map(|usage| usage.0)
            .unwrap_or(0.0);

        // Task is I/O-intensive if category suggests it or I/O requirements are high
        category_io_intensive || disk_requirement > 0.5 || network_requirement > 0.5
    }

    /// Check if a task is memory-intensive based on category and resource requirements
    async fn is_task_memory_intensive(&self, task_category: &TaskCategory, task_score: &PrismaScore) -> bool {
        // Check category-based indicators
        let category_memory_intensive = matches!(
            task_category,
            TaskCategory::LLMInference | TaskCategory::EmbeddingGeneration
        );

        // Check resource requirements
        let memory_requirement = task_score.resources
            .get(&ResourceType::Memory)
            .map(|usage| usage.0)
            .unwrap_or(0.0);

        // Task is memory-intensive if category suggests it or memory requirement is high
        category_memory_intensive || memory_requirement > 0.6
    }

    /// Estimate task duration based on category and priority
    async fn estimate_task_duration(&self, task_category: &TaskCategory, task_priority: TaskPriority) -> Option<u64> {
        let base_duration = match task_category {
            TaskCategory::Internal => 10,           // 10ms for simple internal tasks
            TaskCategory::DatabaseQuery => 50,     // 50ms for database queries
            TaskCategory::NetworkRequest => 100,   // 100ms for network requests
            TaskCategory::UICallback => 20,        // 20ms for UI callbacks
            TaskCategory::FileProcessing => 500,   // 500ms for file processing
            TaskCategory::EmbeddingGeneration => 200, // 200ms for embedding generation
            TaskCategory::LLMInference => 1000,    // 1000ms for LLM inference
            TaskCategory::Custom(_) => 100,        // 100ms default for custom tasks
        };

        // Adjust based on priority
        let priority_multiplier = match task_priority {
            TaskPriority::Realtime => 0.5,  // Realtime tasks should be faster
            TaskPriority::High => 0.8,      // High priority tasks get more resources
            TaskPriority::Normal => 1.0,    // Normal baseline
            TaskPriority::Low => 1.5,       // Low priority tasks may take longer
        };

        Some((base_duration as f64 * priority_multiplier) as u64)
    }

    /// Calculate resource intensity score (0.0 to 1.0)
    async fn calculate_resource_intensity(&self, task_score: &PrismaScore) -> f64 {
        let mut total_intensity = 0.0;
        let mut resource_count = 0;

        for (_, usage) in &task_score.resources {
            total_intensity += usage.0;
            resource_count += 1;
        }

        if resource_count > 0 {
            (total_intensity / resource_count as f64).min(1.0)
        } else {
            0.0
        }
    }

    /// Calculate parallelization potential (0.0 to 1.0)
    async fn calculate_parallelization_potential(&self, task_category: &TaskCategory, task_score: &PrismaScore) -> f64 {
        // Base parallelization potential by category
        let category_potential = match task_category {
            TaskCategory::EmbeddingGeneration => 0.9,  // High parallelization potential
            TaskCategory::FileProcessing => 0.8,       // Good parallelization potential
            TaskCategory::LLMInference => 0.3,         // Limited parallelization (model-dependent)
            TaskCategory::DatabaseQuery => 0.2,        // Limited parallelization
            TaskCategory::NetworkRequest => 0.1,       // Low parallelization potential
            TaskCategory::UICallback => 0.1,           // Low parallelization potential
            TaskCategory::Internal => 0.5,             // Moderate parallelization potential
            TaskCategory::Custom(_) => 0.5,            // Default moderate potential
        };

        // Adjust based on CPU requirements
        let cpu_requirement = task_score.resources
            .get(&ResourceType::CPU)
            .map(|usage| usage.0)
            .unwrap_or(0.0);

        // Higher CPU requirements suggest better parallelization potential
        let cpu_factor = if cpu_requirement > 0.7 { 1.2_f64 } else { 1.0_f64 };

        (category_potential * cpu_factor).min(1.0_f64)
    }

    /// Determine task complexity level
    async fn determine_task_complexity(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        task_priority: TaskPriority,
    ) -> TaskComplexity {
        // Calculate total resource requirements
        let total_resources: f64 = task_score.resources.values().map(|usage| usage.0).sum();
        let resource_count = task_score.resources.len();

        // Base complexity by category
        let category_complexity = match task_category {
            TaskCategory::Internal => TaskComplexity::Simple,
            TaskCategory::UICallback => TaskComplexity::Simple,
            TaskCategory::DatabaseQuery => TaskComplexity::Moderate,
            TaskCategory::NetworkRequest => TaskComplexity::Moderate,
            TaskCategory::FileProcessing => TaskComplexity::Complex,
            TaskCategory::EmbeddingGeneration => TaskComplexity::Complex,
            TaskCategory::LLMInference => TaskComplexity::VeryComplex,
            TaskCategory::Custom(_) => TaskComplexity::Moderate,
        };

        // Adjust based on resource intensity
        let avg_resource_intensity = if resource_count > 0 {
            total_resources / resource_count as f64
        } else {
            0.0
        };

        // Adjust based on priority (realtime tasks are often simpler or need simpler handling)
        let priority_adjustment = match task_priority {
            TaskPriority::Realtime => -1, // Simplify for realtime
            TaskPriority::High => 0,      // No adjustment
            TaskPriority::Normal => 0,    // No adjustment
            TaskPriority::Low => 1,       // Can be more complex
        };

        // Final complexity determination
        let base_level = match category_complexity {
            TaskComplexity::Simple => 0,
            TaskComplexity::Moderate => 1,
            TaskComplexity::Complex => 2,
            TaskComplexity::VeryComplex => 3,
        };

        let resource_adjustment = if avg_resource_intensity > 0.8 {
            1
        } else if avg_resource_intensity < 0.3 {
            -1
        } else {
            0
        };

        let final_level = (base_level + resource_adjustment + priority_adjustment).clamp(0, 3);

        match final_level {
            0 => TaskComplexity::Simple,
            1 => TaskComplexity::Moderate,
            2 => TaskComplexity::Complex,
            _ => TaskComplexity::VeryComplex,
        }
    }

    /// Calculate system health score (0.0 to 1.0)
    async fn calculate_system_health_score(
        &self,
        cpu_utilization: f64,
        memory_utilization: f64,
        io_utilization: f64,
    ) -> f64 {
        // Calculate individual health scores (higher utilization = lower health)
        let cpu_health = 1.0 - cpu_utilization;
        let memory_health = 1.0 - memory_utilization;
        let io_health = 1.0 - io_utilization;

        // Weighted average (CPU and memory are more important)
        let weighted_health = cpu_health * 0.4 + memory_health * 0.4 + io_health * 0.2;

        weighted_health.clamp(0.0, 1.0)
    }

    /// Make dynamic strategy decision based on task and system analysis
    async fn decide_strategy_dynamically(
        &self,
        task_analysis: &TaskAnalysis,
        system_analysis: &SystemAnalysis,
        _task_priority: TaskPriority,
    ) -> PrismaResult<Option<ExecutionStrategyType>> {
        debug!("Making dynamic strategy decision");
        debug!("Task analysis: {:?}", task_analysis);
        debug!("System analysis: {:?}", system_analysis);

        // 1. Handle critical system state
        if system_analysis.is_critical {
            return Ok(Some(self.decide_critical_system_strategy(task_analysis, system_analysis).await?));
        }

        // 2. Handle high system load
        if system_analysis.is_high_load {
            return Ok(Some(self.decide_high_load_strategy(task_analysis, system_analysis).await?));
        }

        // 3. Dynamic strategy selection based on task characteristics and system state
        let strategy = if task_analysis.is_cpu_intensive && task_analysis.parallelization_potential > 0.6 {
            // CPU-intensive tasks with high parallelization potential
            if system_analysis.cpu_utilization < 0.7 && system_analysis.available_cpu_cores > 2 {
                ExecutionStrategyType::Rayon // Use Rayon for parallel CPU work
            } else {
                ExecutionStrategyType::Tokio // Fall back to Tokio if CPU is busy
            }
        } else if task_analysis.is_io_intensive {
            // I/O-intensive tasks always use Tokio
            ExecutionStrategyType::Tokio
        } else if task_analysis.complexity_level == TaskComplexity::Simple {
            // Simple tasks use direct execution
            ExecutionStrategyType::Direct
        } else {
            // Default to Tokio for moderate complexity tasks
            ExecutionStrategyType::Tokio
        };

        debug!("Dynamic strategy decision: {:?}", strategy);
        Ok(Some(strategy))
    }

    /// Decide strategy for realtime priority tasks
    async fn decide_realtime_strategy(
        &self,
        task_analysis: &TaskAnalysis,
        system_analysis: &SystemAnalysis,
    ) -> PrismaResult<ExecutionStrategyType> {
        // Realtime tasks prioritize low latency
        if task_analysis.complexity_level == TaskComplexity::Simple {
            Ok(ExecutionStrategyType::Direct) // Fastest for simple tasks
        } else if system_analysis.health_score > 0.8 {
            Ok(ExecutionStrategyType::Tokio) // Good system health, use Tokio
        } else {
            Ok(ExecutionStrategyType::Direct) // Fall back to direct for speed
        }
    }

    /// Decide strategy for critical system state
    async fn decide_critical_system_strategy(
        &self,
        task_analysis: &TaskAnalysis,
        _system_analysis: &SystemAnalysis,
    ) -> PrismaResult<ExecutionStrategyType> {
        // In critical state, minimize resource usage
        if task_analysis.complexity_level == TaskComplexity::Simple {
            Ok(ExecutionStrategyType::Direct)
        } else if task_analysis.is_cpu_intensive {
            // Avoid Rayon in critical state to reduce CPU load
            Ok(ExecutionStrategyType::Tokio)
        } else {
            Ok(ExecutionStrategyType::Tokio)
        }
    }

    /// Decide strategy for high system load
    async fn decide_high_load_strategy(
        &self,
        task_analysis: &TaskAnalysis,
        system_analysis: &SystemAnalysis,
    ) -> PrismaResult<ExecutionStrategyType> {
        // Under high load, be more conservative
        if task_analysis.complexity_level == TaskComplexity::Simple {
            Ok(ExecutionStrategyType::Direct)
        } else if task_analysis.is_cpu_intensive && system_analysis.cpu_utilization > 0.8 {
            // High CPU load, avoid Rayon
            Ok(ExecutionStrategyType::Tokio)
        } else if task_analysis.parallelization_potential > 0.7 && system_analysis.cpu_utilization < 0.6 {
            // Still some CPU headroom and high parallelization potential
            Ok(ExecutionStrategyType::Rayon)
        } else {
            Ok(ExecutionStrategyType::Tokio)
        }
    }

    /// Decide strategy for constrained resources
    async fn decide_constrained_strategy(
        &self,
        task_analysis: &TaskAnalysis,
        system_analysis: &SystemAnalysis,
    ) -> PrismaResult<ExecutionStrategyType> {
        // When resources are constrained, prioritize efficiency
        if task_analysis.resource_intensity < 0.3 {
            Ok(ExecutionStrategyType::Direct) // Low resource tasks use direct execution
        } else if task_analysis.is_memory_intensive && system_analysis.memory_utilization > 0.8 {
            Ok(ExecutionStrategyType::Direct) // Avoid additional memory overhead
        } else {
            Ok(ExecutionStrategyType::Tokio) // Default to Tokio for constrained resources
        }
    }

    /// Fallback strategy selection with dynamic analysis
    async fn decide_fallback_strategy(
        &self,
        task_analysis: &TaskAnalysis,
        system_analysis: &SystemAnalysis,
        task_category: &TaskCategory,
    ) -> PrismaResult<ExecutionStrategyType> {
        // This is the final fallback - use dynamic analysis with category hints

        // 1. Check if we have a configured strategy for this category
        if let Some(configured_strategy) = self.config.category_strategies.get(task_category) {
            // Validate the configured strategy against current conditions
            if self.is_strategy_suitable(*configured_strategy, task_analysis, system_analysis).await {
                return Ok(*configured_strategy);
            }
        }

        // 2. Dynamic fallback based on analysis
        let strategy = match task_analysis.complexity_level {
            TaskComplexity::Simple => ExecutionStrategyType::Direct,
            TaskComplexity::Moderate => {
                if task_analysis.is_io_intensive {
                    ExecutionStrategyType::Tokio
                } else {
                    ExecutionStrategyType::Direct
                }
            },
            TaskComplexity::Complex => {
                if task_analysis.is_cpu_intensive && task_analysis.parallelization_potential > 0.5 {
                    ExecutionStrategyType::Rayon
                } else {
                    ExecutionStrategyType::Tokio
                }
            },
            TaskComplexity::VeryComplex => {
                if task_analysis.is_cpu_intensive && system_analysis.cpu_utilization < 0.7 {
                    ExecutionStrategyType::Rayon
                } else {
                    ExecutionStrategyType::Tokio
                }
            },
        };

        debug!("Fallback strategy selected: {:?} for category {:?}", strategy, task_category);
        Ok(strategy)
    }

    /// Check if a strategy is suitable for current conditions
    async fn is_strategy_suitable(
        &self,
        strategy: ExecutionStrategyType,
        task_analysis: &TaskAnalysis,
        system_analysis: &SystemAnalysis,
    ) -> bool {
        match strategy {
            ExecutionStrategyType::Rayon => {
                // Rayon is suitable for CPU-intensive tasks with good parallelization potential
                // and when the system has available CPU resources
                task_analysis.is_cpu_intensive
                    && task_analysis.parallelization_potential > 0.4
                    && system_analysis.cpu_utilization < 0.8
                    && system_analysis.available_cpu_cores > 1
            },
            ExecutionStrategyType::Tokio => {
                // Tokio is suitable for I/O-intensive tasks or when system is under moderate load
                task_analysis.is_io_intensive
                    || system_analysis.cpu_utilization < 0.9
                    || task_analysis.complexity_level != TaskComplexity::Simple
            },
            ExecutionStrategyType::Direct => {
                // Direct execution is suitable for simple tasks or when system is under high load
                task_analysis.complexity_level == TaskComplexity::Simple
                    || task_analysis.resource_intensity < 0.3
                    || system_analysis.is_critical
            },
        }
    }
}

#[async_trait]
impl StrategySelector for RuleBasedDecisionMaker {
    async fn select_strategy(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<(ExecutionStrategyType, DecisionReason)> {
        // Use the decide_strategy method to get the strategy
        let strategy = self.decide_strategy(task_category, task_score, system_score, task_priority).await?;

        // Create a simple reason for the decision
        let reason = DecisionReason {
            description: format!("Strategy {:?} selected for task category {:?}", strategy, task_category),
            details: None,
            resource: None,
            resource_usage: None,
        };

        Ok((strategy, reason))
    }

    async fn can_execute(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore,
    ) -> PrismaResult<bool> {
        // Check resource constraints
        let constraints_met = self.check_resource_constraints(task_score, system_score).await;

        // Check system critical state
        let system_critical = self.is_system_critical().await;

        // Can execute if constraints are met and system is not critical
        Ok(constraints_met && !system_critical)
    }

    fn get_recommended_strategy(&self, task_category: &TaskCategory) -> ExecutionStrategyType {
        // Get the recommended strategy from the config
        self.config.category_strategies
            .get(task_category)
            .copied()
            .unwrap_or(self.config.default_strategy)
    }

    fn set_recommended_strategy(&mut self, task_category: &TaskCategory, strategy: ExecutionStrategyType) {
        // Update the recommended strategy in the config
        self.config.category_strategies.insert(task_category.clone(), strategy);
    }
}

#[async_trait]
impl DecisionLogic for RuleBasedDecisionMaker {
    async fn decide_strategy(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<ExecutionStrategyType> {

        debug!("Deciding strategy for task category: {:?}, priority: {:?}", task_category, task_priority);
        debug!("Task Score: {:?}", task_score);
        debug!("System Score: {:?}", system_score);

        // --- Learned Strategy Check (Highest Priority) ---
        if let Some(learned_strategy) = self.get_recommended_strategy(task_category).await {
            debug!("Using learned strategy for {:?}: {:?}", task_category, learned_strategy);
            return Ok(learned_strategy);
        }

        // --- Rule-Based Decision Making ---
        if self.config.enable_rule_based_decisions {
            if let Some(rule_set) = &self.rule_set {
                // Evaluate all rules in the rule set
                let rule_results = match rule_set.read().await.evaluate_rules(
                    task_category,
                    task_score,
                    system_score,
                    task_priority
                ).await {
                    Ok(results) => results,
                    Err(e) => {
                        warn!("Error evaluating rules: {:?}. Falling back to direct decision making.", e);
                        Vec::new()
                    }
                };

                // Find the first triggered rule with a recommended strategy
                let triggered_rules: Vec<_> = rule_results.iter()
                    .filter(|r| r.triggered && r.recommended_strategy.is_some())
                    .collect();

                if !triggered_rules.is_empty() {
                    // Use the first triggered rule's recommendation
                    let rule = &triggered_rules[0];
                    let strategy = rule.recommended_strategy.unwrap();

                    debug!("Strategy decided by rule {}: {:?} -> {:?}",
                           rule.rule_name, task_category, strategy);
                    debug!("Reason: {}", rule.reason.description);

                    return Ok(strategy);
                }

                debug!("No rules triggered with strategy recommendations. Falling back to direct decision making.");
            } else {
                debug!("No rule set available. Falling back to direct decision making.");
            }
        }

        // --- Dynamic Decision Making (Primary Logic) ---

        // 1. Analyze task characteristics dynamically
        let task_analysis = self.analyze_task_characteristics(task_category, task_score, task_priority).await?;
        debug!("Task analysis: {:?}", task_analysis);

        // 2. Analyze current system state
        let system_analysis = self.analyze_system_state(system_score).await?;
        debug!("System analysis: {:?}", system_analysis);

        // 3. Make dynamic strategy decision based on analysis
        let strategy = self.decide_strategy_dynamically(&task_analysis, &system_analysis, task_priority).await?;

        if let Some(strategy) = strategy {
            debug!("Strategy decided dynamically: {:?} -> {:?}", task_category, strategy);
            return Ok(strategy);
        }

        // 4. Priority Override: Realtime tasks get special handling
        if task_priority == TaskPriority::Realtime {
            let strategy = self.decide_realtime_strategy(&task_analysis, &system_analysis).await?;
            debug!("Strategy decided for realtime priority: {:?}", strategy);
            return Ok(strategy);
        }

        // 5. System Critical Check with Dynamic Adaptation
        let system_critical = self.is_system_critical().await;
        if system_critical {
            let strategy = self.decide_critical_system_strategy(&task_analysis, &system_analysis).await?;
            debug!("Strategy decided for critical system: {:?}", strategy);
            return Ok(strategy);
        }

        // 6. Resource Constraint Check with Dynamic Adaptation
        let constraints_met = self.check_resource_constraints(task_score, system_score).await;
        if !constraints_met {
            let strategy = self.decide_constrained_strategy(&task_analysis, &system_analysis).await?;
            debug!("Strategy decided for constrained resources: {:?}", strategy);
            return Ok(strategy);
        }

        // 4. Model State Check (Using StateTracker)
        if self.config.enable_state_tracking {
            if let Some(state_tracker) = &self.state_tracker {
                // Check if the task is LLM inference
                if matches!(task_category, TaskCategory::LLMInference) {
                    // Extract the model ID from task_score
                    let model_id = self.extract_model_id_from_task(task_score);

                    debug!("Checking availability of model: {}", model_id);

                    // Check if the model is available
                    match state_tracker.read().await.is_model_available(&model_id).await {
                        Ok(available) => {
                            if !available {
                                debug!("Model {} is not available. Using Tokio for loading.", model_id);
                                return Ok(ExecutionStrategyType::Tokio);
                            } else {
                                debug!("Model {} is available for use.", model_id);
                            }
                        },
                        Err(e) => {
                            warn!("Error checking model availability: {:?}. Proceeding with default strategy.", e);
                        }
                    }

                    // Check model memory requirements against system memory
                    if let Ok(model_memory) = state_tracker.read().await.get_model_memory_usage(&model_id).await {
                        // Get available system memory
                        let available_memory = system_score.availability
                            .get(&ResourceType::Memory)
                            .map(|usage| usage.0)
                            .unwrap_or(100.0);

                        // Convert percentage to bytes (rough estimate)
                        let system_memory = if let Some(score) = self.get_current_system_score().await {
                            // Get memory metrics from resource scores
                            if let Some(memory_score) = score.resource_scores.get(&ResourceType::Memory) {
                                if let Some(ResourceMetrics::Memory(memory_metrics)) = &memory_score.raw_metrics {
                                    memory_metrics.total_bytes
                                } else {
                                    // Default to a reasonable value if memory metrics are not available
                                    16 * 1024 * 1024 * 1024 // 16 GB
                                }
                            } else {
                                // Default to a reasonable value if memory score is not available
                                16 * 1024 * 1024 * 1024 // 16 GB
                            }
                        } else {
                            // Default to a reasonable value if we can't get the system score
                            16 * 1024 * 1024 * 1024 // 16 GB
                        };

                        let available_bytes = (system_memory as f64 * available_memory / 100.0) as u64;

                        // Check if we have enough memory
                        if model_memory > available_bytes {
                            warn!("Model {} requires {}MB but only {}MB available. Using Tokio for better memory management.",
                                  model_id, model_memory / (1024 * 1024), available_bytes / (1024 * 1024));
                            return Ok(ExecutionStrategyType::Tokio);
                        }
                    }
                }
            }
        }

        // 5. Category-Based Strategy Selection (Default)
        // 7. Dynamic Fallback Strategy Selection
        let strategy = self.decide_fallback_strategy(&task_analysis, &system_analysis, task_category).await?;

        debug!("Strategy decided by dynamic fallback: {:?} -> {:?}", task_category, strategy);
        Ok(strategy)
    }
}
