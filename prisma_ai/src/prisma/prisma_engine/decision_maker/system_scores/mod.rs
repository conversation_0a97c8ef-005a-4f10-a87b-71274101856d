// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/mod.rs
// =================================================================================================
// Purpose: This submodule handles logic related to interpreting or adjusting the SystemScore
// (system resource availability). It serves as the entry point for SystemScore evaluation
// functionality, exporting the necessary components for use by the decision maker to assess
// current system resource states.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Imports this module
//   - decision_maker/decision_maker.rs: Uses the exported functions
//   - system_scores/logic.rs: Contains core evaluation logic
//   - system_scores/cpu_score.rs: Handles CPU-specific scoring
//   - system_scores/memory_score.rs: Handles memory-specific scoring
//   - system_scores/disk_score.rs: Handles disk-specific scoring
//   - system_scores/network_score.rs: Handles network-specific scoring
//   - system_scores/system_scores.rs: Main implementation
//   - system_scores/types.rs: Defines score-related types
//   - system_scores/traits.rs: Defines score-related traits
//   - system_scores/generics.rs: Provides generic utilities
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore type
//   - prisma_engine/monitor: Receives system information from monitor module
//
// Platform Considerations:
// - This module is platform-independent as it deals with abstract resource scoring
// - The underlying system metrics may vary by platform, but this module works with
//   the abstracted SystemScore regardless of the platform
// =================================================================================================

pub mod logic;
pub mod types;
pub mod traits;
pub mod generics;
pub mod cpu_score;
pub mod memory_score;
pub mod disk_score;
pub mod network_score;
pub mod system_scores;

// Re-export key functions and types for easier access
// From logic.rs
pub use logic::{
    evaluate_system_score,
    calculate_trends,
    predict_future_score,
    is_system_critical,
    get_most_constrained_resource,
    calculate_system_health
};

// From types.rs
pub use types::{
    AvailabilityLevel,
    ResourceThreshold,
    SystemScoreConfig,
    ResourceScore,
    ResourceMetrics,
    ResourceTrend,
    DetailedSystemScore
};

// From traits.rs
pub use traits::{
    ResourceScoreCalculator,
    SystemScoreEvaluator,
    ResourceMonitorIntegration,
    AvailabilityAnalyzer,
    SystemScoreBuilder
};

// From generics.rs
pub use generics::{
    normalize_score,
    apply_weights,
    combine_scores,
    score_difference,
    determine_trend,
    create_system_score,
    ScoreCache,
    MovingAverage
};

// From system_scores.rs
pub use system_scores::{
    create_system_score_evaluator,
    create_system_score_evaluator_with_config
};

// From resource-specific score calculators
pub use cpu_score::CpuScoreCalculator;
pub use memory_score::MemoryScoreCalculator;
pub use disk_score::DiskScoreCalculator;
pub use network_score::NetworkScoreCalculator;
