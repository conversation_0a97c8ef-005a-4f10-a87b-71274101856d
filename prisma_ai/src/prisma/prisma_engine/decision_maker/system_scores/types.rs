// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/types.rs
// =================================================================================================
// Purpose: Defines types specific to SystemScore evaluation and manipulation. This file contains
// data structures that represent different aspects of system resource availability and scoring
// mechanisms used by the decision maker to determine execution strategies based on system state.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports these types
//   - system_scores/logic.rs: Uses these types for score evaluation
//   - system_scores/cpu_score.rs: Uses these types for CPU scoring
//   - system_scores/memory_score.rs: Uses these types for memory scoring
//   - system_scores/disk_score.rs: Uses these types for disk scoring
//   - system_scores/network_score.rs: Uses these types for network scoring
//   - system_scores/traits.rs: Defines traits that operate on these types
//
// - External Dependencies:
//   - prisma_engine/types.rs: Extends the core SystemScore, ResourceType, and ResourceUsage types
//   - prisma_engine/monitor/types.rs: Uses monitor-specific types for metrics
//   - serde: For serialization/deserialization of score types
//   - std::collections: For data structures used in type definitions
//
// Platform Considerations:
// - This module is platform-independent as it defines abstract types
// - Some types may have platform-specific fields that are conditionally compiled
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};

use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage, SystemScore};
use crate::prisma::prisma_engine::monitor::system::types::{
    CpuMetrics, MemoryMetrics, DiskMetrics, NetworkMetrics
};

/// Represents the availability level of a system resource
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum AvailabilityLevel {
    /// Critical availability (<25%)
    Critical,
    /// Low availability (25-50%)
    Low,
    /// Medium availability (50-75%)
    Medium,
    /// High availability (>75%)
    High,
}

impl AvailabilityLevel {
    /// Create an AvailabilityLevel from a percentage value
    pub fn from_percentage(percentage: f64) -> Self {
        match percentage {
            p if p >= 75.0 => AvailabilityLevel::High,
            p if p >= 50.0 => AvailabilityLevel::Medium,
            p if p >= 25.0 => AvailabilityLevel::Low,
            _ => AvailabilityLevel::Critical,
        }
    }

    /// Get the minimum percentage value for this level
    pub fn min_percentage(&self) -> f64 {
        match self {
            AvailabilityLevel::High => 75.0,
            AvailabilityLevel::Medium => 50.0,
            AvailabilityLevel::Low => 25.0,
            AvailabilityLevel::Critical => 0.0,
        }
    }

    /// Get the maximum percentage value for this level
    pub fn max_percentage(&self) -> f64 {
        match self {
            AvailabilityLevel::High => 100.0,
            AvailabilityLevel::Medium => 75.0,
            AvailabilityLevel::Low => 50.0,
            AvailabilityLevel::Critical => 25.0,
        }
    }
}

/// Threshold configuration for a specific resource
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceThreshold {
    /// The resource type this threshold applies to
    pub resource_type: ResourceType,
    /// Threshold for high availability (percentage)
    pub high_threshold: f64,
    /// Threshold for medium availability (percentage)
    pub medium_threshold: f64,
    /// Threshold for low availability (percentage)
    pub low_threshold: f64,
    /// Whether to use this threshold for scoring
    pub enabled: bool,
}

impl Default for ResourceThreshold {
    fn default() -> Self {
        ResourceThreshold {
            resource_type: ResourceType::CPU,
            high_threshold: 75.0,
            medium_threshold: 50.0,
            low_threshold: 25.0,
            enabled: true,
        }
    }
}

/// Configuration for the system score evaluation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemScoreConfig {
    /// Thresholds for each resource type
    pub thresholds: HashMap<ResourceType, ResourceThreshold>,
    /// Weights for each resource type in the final score
    pub weights: HashMap<ResourceType, f64>,
    /// How many historical data points to keep for trend analysis
    pub history_size: usize,
    /// How often to update the score
    pub update_interval: Duration,
}

impl Default for SystemScoreConfig {
    fn default() -> Self {
        let mut thresholds = HashMap::new();
        let mut weights = HashMap::new();

        // CPU thresholds
        thresholds.insert(
            ResourceType::CPU,
            ResourceThreshold {
                resource_type: ResourceType::CPU,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
        );

        // Memory thresholds
        thresholds.insert(
            ResourceType::Memory,
            ResourceThreshold {
                resource_type: ResourceType::Memory,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
        );

        // Disk thresholds
        thresholds.insert(
            ResourceType::DiskIO,
            ResourceThreshold {
                resource_type: ResourceType::DiskIO,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
        );

        // Network thresholds
        thresholds.insert(
            ResourceType::NetworkBandwidth,
            ResourceThreshold {
                resource_type: ResourceType::NetworkBandwidth,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
        );

        // GPU thresholds (not monitored in this implementation)
        thresholds.insert(
            ResourceType::GPU,
            ResourceThreshold {
                resource_type: ResourceType::GPU,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: false,
            },
        );

        // Set weights for each resource type
        weights.insert(ResourceType::CPU, 1.0);
        weights.insert(ResourceType::Memory, 1.0);
        weights.insert(ResourceType::DiskIO, 0.8);
        weights.insert(ResourceType::NetworkBandwidth, 0.7);
        weights.insert(ResourceType::GPU, 0.0); // Not monitored

        SystemScoreConfig {
            thresholds,
            weights,
            history_size: 10,
            update_interval: Duration::from_secs(5),
        }
    }
}

/// Detailed score for a specific resource
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceScore {
    /// The resource type
    pub resource_type: ResourceType,
    /// The availability percentage (0-100)
    pub availability: ResourceUsage,
    /// The availability level
    pub level: AvailabilityLevel,
    /// Timestamp when this score was calculated
    pub timestamp: SystemTime,
    /// Raw metrics used to calculate this score
    pub raw_metrics: Option<ResourceMetrics>,
}

/// Enum to hold different types of resource metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceMetrics {
    /// CPU metrics
    Cpu(CpuMetrics),
    /// Memory metrics
    Memory(MemoryMetrics),
    /// Disk metrics
    Disk(DiskMetrics),
    /// Network metrics
    Network(NetworkMetrics),
    /// No metrics available
    None,
}

/// Represents a trend in resource availability
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceTrend {
    /// Availability is increasing
    Increasing,
    /// Availability is stable
    Stable,
    /// Availability is decreasing
    Decreasing,
    /// Not enough data to determine trend
    Unknown,
}

/// Detailed system score with additional information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedSystemScore {
    /// The basic system score
    pub system_score: SystemScore,
    /// Detailed scores for each resource
    pub resource_scores: HashMap<ResourceType, ResourceScore>,
    /// Trends for each resource
    pub trends: HashMap<ResourceType, ResourceTrend>,
    /// Timestamp when this score was calculated
    pub timestamp: SystemTime,
}

impl DetailedSystemScore {
    /// Create a new DetailedSystemScore from a SystemScore
    pub fn new(system_score: SystemScore) -> Self {
        DetailedSystemScore {
            system_score,
            resource_scores: HashMap::new(),
            trends: HashMap::new(),
            timestamp: SystemTime::now(),
        }
    }

    /// Get the availability level for a specific resource
    pub fn get_resource_level(&self, resource_type: ResourceType) -> AvailabilityLevel {
        self.resource_scores
            .get(&resource_type)
            .map(|score| score.level)
            .unwrap_or(AvailabilityLevel::High)
    }

    /// Get the trend for a specific resource
    pub fn get_resource_trend(&self, resource_type: ResourceType) -> ResourceTrend {
        self.trends
            .get(&resource_type)
            .copied()
            .unwrap_or(ResourceTrend::Unknown)
    }

    /// Get the overall availability level (lowest of all resources)
    pub fn get_overall_level(&self) -> AvailabilityLevel {
        self.resource_scores
            .values()
            .map(|score| score.level)
            .min()
            .unwrap_or(AvailabilityLevel::High)
    }
}