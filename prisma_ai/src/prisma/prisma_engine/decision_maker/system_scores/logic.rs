// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/logic.rs
// =================================================================================================
// Purpose: Implements logic for evaluating and adjusting SystemScore objects, which represent
// the current availability of system resources. This file contains functions to analyze, smooth,
// and interpret system resource availability to help the decision maker determine the optimal
// execution strategy based on current system conditions.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/cpu_score.rs: May provide CPU-specific evaluation
//   - system_scores/memory_score.rs: May provide memory-specific evaluation
//   - system_scores/disk_score.rs: May provide disk-specific evaluation
//   - system_scores/network_score.rs: May provide network-specific evaluation
//   - system_scores/system_scores.rs: Uses this logic in the main implementation
//   - decision_maker/decision_maker.rs: Uses these functions for score evaluation
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, and ResourceUsage types
//   - prisma_engine/monitor: Receives system information from monitor module
//   - std::collections::HashMap: For resource mapping
//
// Platform Considerations:
// - This module is platform-independent as it deals with abstract resource scoring
// - The underlying system metrics may vary by platform, but this module works with
//   the abstracted SystemScore regardless of the platform
// =================================================================================================

use std::collections::HashMap;
use std::time::SystemTime;
use tracing::debug;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{SystemScore, ResourceType, ResourceUsage};
use crate::prisma::prisma_engine::monitor::system::types::SystemMetrics;

use super::types::{
    DetailedSystemScore, ResourceTrend, SystemScoreConfig, ResourceThreshold
};
use super::generics::{apply_weights, create_system_score};
use super::traits::ResourceScoreCalculator;
use super::cpu_score::CpuScoreCalculator;
use super::memory_score::MemoryScoreCalculator;
use super::disk_score::DiskScoreCalculator;
use super::network_score::NetworkScoreCalculator;

/// Evaluate a SystemScore based on the current system metrics
pub async fn evaluate_system_score(
    metrics: &SystemMetrics,
    config: &SystemScoreConfig
) -> PrismaResult<DetailedSystemScore> {
    // Create resource calculators
    let cpu_calculator = CpuScoreCalculator::with_config(
        config.thresholds.get(&ResourceType::CPU)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::CPU,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            }),
        config.weights.get(&ResourceType::CPU).copied().unwrap_or(1.0)
    );

    let memory_calculator = MemoryScoreCalculator::with_config(
        config.thresholds.get(&ResourceType::Memory)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::Memory,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            }),
        config.weights.get(&ResourceType::Memory).copied().unwrap_or(1.0)
    );

    let disk_calculator = DiskScoreCalculator::with_config(
        config.thresholds.get(&ResourceType::DiskIO)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::DiskIO,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            }),
        config.weights.get(&ResourceType::DiskIO).copied().unwrap_or(0.8),
        500.0 * 1024.0 * 1024.0 // 500 MB/s default max I/O rate
    );

    let network_calculator = NetworkScoreCalculator::with_config(
        config.thresholds.get(&ResourceType::NetworkBandwidth)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::NetworkBandwidth,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            }),
        config.weights.get(&ResourceType::NetworkBandwidth).copied().unwrap_or(0.7),
        100.0 * 1024.0 * 1024.0 // 100 MB/s default max bandwidth
    );

    // Calculate resource scores
    let cpu_score = cpu_calculator.calculate_score(metrics).await?;
    let memory_score = memory_calculator.calculate_score(metrics).await?;
    let disk_score = disk_calculator.calculate_score(metrics).await?;
    let network_score = network_calculator.calculate_score(metrics).await?;

    // Collect resource scores
    let mut resource_scores = HashMap::new();
    resource_scores.insert(ResourceType::CPU, cpu_score);
    resource_scores.insert(ResourceType::Memory, memory_score);
    resource_scores.insert(ResourceType::DiskIO, disk_score);
    resource_scores.insert(ResourceType::NetworkBandwidth, network_score);

    // Create availability map
    let mut availability = HashMap::new();
    for (resource_type, score) in &resource_scores {
        availability.insert(*resource_type, score.availability);
    }

    // Apply weights to availability
    let weighted_availability = apply_weights(&availability, &config.weights);

    // Create system score
    let system_score = create_system_score(weighted_availability);

    // Create detailed system score
    let detailed_score = DetailedSystemScore {
        system_score,
        resource_scores,
        trends: HashMap::new(), // Trends will be calculated separately
        timestamp: SystemTime::now(),
    };

    debug!(
        "Evaluated system score: CPU={:.1}%, Memory={:.1}%, Disk={:.1}%, Network={:.1}%",
        detailed_score.resource_scores.get(&ResourceType::CPU).map_or(100.0, |s| s.availability.0),
        detailed_score.resource_scores.get(&ResourceType::Memory).map_or(100.0, |s| s.availability.0),
        detailed_score.resource_scores.get(&ResourceType::DiskIO).map_or(100.0, |s| s.availability.0),
        detailed_score.resource_scores.get(&ResourceType::NetworkBandwidth).map_or(100.0, |s| s.availability.0)
    );

    Ok(detailed_score)
}

/// Calculate resource trends based on historical scores
pub async fn calculate_trends(
    current_score: &DetailedSystemScore,
    historical_scores: &[DetailedSystemScore]
) -> PrismaResult<HashMap<ResourceType, ResourceTrend>> {
    let mut trends = HashMap::new();

    // Create resource calculators
    let cpu_calculator = CpuScoreCalculator::new();
    let memory_calculator = MemoryScoreCalculator::new();
    let disk_calculator = DiskScoreCalculator::new();
    let network_calculator = NetworkScoreCalculator::new();

    // Extract historical resource scores
    let mut historical_cpu_scores = Vec::new();
    let mut historical_memory_scores = Vec::new();
    let mut historical_disk_scores = Vec::new();
    let mut historical_network_scores = Vec::new();

    for score in historical_scores {
        if let Some(cpu_score) = score.resource_scores.get(&ResourceType::CPU) {
            historical_cpu_scores.push(cpu_score.clone());
        }

        if let Some(memory_score) = score.resource_scores.get(&ResourceType::Memory) {
            historical_memory_scores.push(memory_score.clone());
        }

        if let Some(disk_score) = score.resource_scores.get(&ResourceType::DiskIO) {
            historical_disk_scores.push(disk_score.clone());
        }

        if let Some(network_score) = score.resource_scores.get(&ResourceType::NetworkBandwidth) {
            historical_network_scores.push(network_score.clone());
        }
    }

    // Calculate trends for each resource
    if let Some(cpu_score) = current_score.resource_scores.get(&ResourceType::CPU) {
        let cpu_trend = cpu_calculator.calculate_trend(cpu_score, &historical_cpu_scores).await?;
        trends.insert(ResourceType::CPU, cpu_trend);
    }

    if let Some(memory_score) = current_score.resource_scores.get(&ResourceType::Memory) {
        let memory_trend = memory_calculator.calculate_trend(memory_score, &historical_memory_scores).await?;
        trends.insert(ResourceType::Memory, memory_trend);
    }

    if let Some(disk_score) = current_score.resource_scores.get(&ResourceType::DiskIO) {
        let disk_trend = disk_calculator.calculate_trend(disk_score, &historical_disk_scores).await?;
        trends.insert(ResourceType::DiskIO, disk_trend);
    }

    if let Some(network_score) = current_score.resource_scores.get(&ResourceType::NetworkBandwidth) {
        let network_trend = network_calculator.calculate_trend(network_score, &historical_network_scores).await?;
        trends.insert(ResourceType::NetworkBandwidth, network_trend);
    }

    Ok(trends)
}

/// Predict future system score based on current score and trends
pub fn predict_future_score(
    current_score: &DetailedSystemScore,
    trends: &HashMap<ResourceType, ResourceTrend>,
    prediction_time_seconds: u64
) -> PrismaResult<SystemScore> {
    let mut predicted_availability = HashMap::new();

    // Predict availability for each resource
    for (resource_type, score) in &current_score.resource_scores {
        let current_availability = score.availability.0;

        // Get trend for this resource
        let trend = trends.get(resource_type).copied().unwrap_or(ResourceTrend::Stable);

        // Calculate predicted availability based on trend
        let predicted_value = match trend {
            ResourceTrend::Increasing => {
                // Increase by 1% per second, up to 100%
                let increase = prediction_time_seconds as f64 * 1.0;
                (current_availability + increase).min(100.0)
            },
            ResourceTrend::Decreasing => {
                // Decrease by 1% per second, down to 0%
                let decrease = prediction_time_seconds as f64 * 1.0;
                (current_availability - decrease).max(0.0)
            },
            ResourceTrend::Stable => {
                // No change
                current_availability
            },
            ResourceTrend::Unknown => {
                // No change
                current_availability
            },
        };

        predicted_availability.insert(*resource_type, ResourceUsage(predicted_value));
    }

    // Create predicted system score
    Ok(SystemScore {
        availability: predicted_availability,
    })
}

/// Check if a system score is below critical thresholds
pub fn is_system_critical(
    score: &DetailedSystemScore,
    config: &SystemScoreConfig
) -> bool {
    for (resource_type, score) in &score.resource_scores {
        // Skip resources that are not enabled
        if let Some(threshold) = config.thresholds.get(resource_type) {
            if !threshold.enabled {
                continue;
            }

            // Check if availability is below critical threshold
            if score.availability.0 < threshold.low_threshold {
                return true;
            }
        }
    }

    false
}

/// Get the most constrained resource in the system
pub fn get_most_constrained_resource(score: &DetailedSystemScore) -> Option<ResourceType> {
    score.resource_scores
        .iter()
        .min_by(|a, b| a.1.availability.0.partial_cmp(&b.1.availability.0).unwrap())
        .map(|(resource_type, _)| *resource_type)
}

/// Calculate the overall system health as a percentage
pub fn calculate_system_health(score: &DetailedSystemScore) -> f64 {
    let mut total_score = 0.0;
    let mut count = 0;

    for (_, resource_score) in &score.resource_scores {
        total_score += resource_score.availability.0;
        count += 1;
    }

    if count > 0 {
        total_score / count as f64
    } else {
        100.0 // Default to full health if no scores
    }
}
