// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/traits.rs
// =================================================================================================
// Purpose: Defines traits for SystemScore evaluation and manipulation. This file contains
// trait definitions that establish interfaces for different system scoring components, allowing
// for modular and extensible scoring mechanisms within the decision maker.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports these traits
//   - system_scores/logic.rs: May implement these traits
//   - system_scores/cpu_score.rs: Likely implements CPU-specific scoring traits
//   - system_scores/memory_score.rs: Likely implements memory-specific scoring traits
//   - system_scores/disk_score.rs: Likely implements disk-specific scoring traits
//   - system_scores/network_score.rs: Likely implements network-specific scoring traits
//   - system_scores/types.rs: Defines types used by these traits
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, and ResourceUsage types
//   - prisma_engine/traits.rs: May extend or complement engine-level traits
//   - prisma_engine/monitor: May use monitor-related traits
//   - async_trait: For async trait methods if needed
//
// Platform Considerations:
// - This module is platform-independent as it defines abstract traits
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::time::SystemTime;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage, SystemScore};
use crate::prisma::prisma_engine::monitor::system::types::{
    CpuMetrics, MemoryMetrics, DiskMetrics, NetworkMetrics, SystemMetrics
};
use super::types::{
    ResourceScore, DetailedSystemScore, ResourceTrend, AvailabilityLevel,
    ResourceThreshold, SystemScoreConfig, ResourceMetrics
};

/// Trait for components that calculate resource-specific scores
#[async_trait]
pub trait ResourceScoreCalculator: Send + Sync {
    /// Get the resource type this calculator is responsible for
    fn get_resource_type(&self) -> ResourceType;

    /// Calculate a score for the resource based on system metrics
    async fn calculate_score(&self, metrics: &SystemMetrics) -> PrismaResult<ResourceScore>;

    /// Calculate a trend for the resource based on historical scores
    async fn calculate_trend(
        &self,
        current_score: &ResourceScore,
        historical_scores: &[ResourceScore]
    ) -> PrismaResult<ResourceTrend>;

    /// Get the threshold configuration for this resource
    fn get_threshold(&self) -> ResourceThreshold;

    /// Set the threshold configuration for this resource
    fn set_threshold(&mut self, threshold: ResourceThreshold);

    /// Get the weight of this resource in the overall system score
    fn get_weight(&self) -> f64;

    /// Set the weight of this resource in the overall system score
    fn set_weight(&mut self, weight: f64);
}

/// Trait for components that evaluate the overall system score
#[async_trait]
pub trait SystemScoreEvaluator: Send + Sync {
    /// Evaluate the system score based on system metrics
    async fn evaluate_score(&self, metrics: &SystemMetrics) -> PrismaResult<DetailedSystemScore>;

    /// Get the current system score without recalculating
    async fn get_current_score(&self) -> PrismaResult<DetailedSystemScore>;

    /// Get historical system scores
    async fn get_historical_scores(&self) -> PrismaResult<Vec<DetailedSystemScore>>;

    /// Get the configuration for the system score evaluation
    fn get_config(&self) -> SystemScoreConfig;

    /// Set the configuration for the system score evaluation
    fn set_config(&mut self, config: SystemScoreConfig);

    /// Get the resource score calculator for a specific resource type
    fn get_calculator(&self, resource_type: ResourceType) -> Option<&dyn ResourceScoreCalculator>;

    /// Add a resource score calculator
    fn add_calculator(&mut self, calculator: Box<dyn ResourceScoreCalculator>);

    /// Remove a resource score calculator
    fn remove_calculator(&mut self, resource_type: ResourceType) -> Option<Box<dyn ResourceScoreCalculator>>;
}

/// Trait for components that integrate with the monitor module
#[async_trait]
pub trait ResourceMonitorIntegration: Send + Sync {
    /// Get the latest system metrics from the monitor
    async fn get_system_metrics(&self) -> PrismaResult<SystemMetrics>;

    /// Get the latest CPU metrics from the monitor
    async fn get_cpu_metrics(&self) -> PrismaResult<CpuMetrics>;

    /// Get the latest memory metrics from the monitor
    async fn get_memory_metrics(&self) -> PrismaResult<MemoryMetrics>;

    /// Get the latest disk metrics from the monitor
    async fn get_disk_metrics(&self) -> PrismaResult<DiskMetrics>;

    /// Get the latest network metrics from the monitor
    async fn get_network_metrics(&self) -> PrismaResult<NetworkMetrics>;
}

/// Trait for components that analyze resource availability
#[async_trait]
pub trait AvailabilityAnalyzer: Send + Sync {
    /// Analyze the availability of a specific resource
    async fn analyze_availability(
        &self,
        resource_type: ResourceType,
        usage: ResourceUsage
    ) -> PrismaResult<AvailabilityLevel>;

    /// Predict future availability based on historical data
    async fn predict_availability(
        &self,
        resource_type: ResourceType,
        historical_usage: &[ResourceUsage],
        prediction_time: SystemTime
    ) -> PrismaResult<ResourceUsage>;

    /// Get the threshold for a specific availability level
    fn get_threshold(
        &self,
        resource_type: ResourceType,
        level: AvailabilityLevel
    ) -> PrismaResult<f64>;

    /// Set the threshold for a specific availability level
    fn set_threshold(
        &mut self,
        resource_type: ResourceType,
        level: AvailabilityLevel,
        threshold: f64
    ) -> PrismaResult<()>;
}

/// Trait for components that can create a SystemScore from resource scores
pub trait SystemScoreBuilder: Send + Sync {
    /// Build a SystemScore from resource scores
    fn build_system_score(
        &self,
        resource_scores: &HashMap<ResourceType, ResourceScore>
    ) -> PrismaResult<SystemScore>;

    /// Build a DetailedSystemScore from resource scores and trends
    fn build_detailed_score(
        &self,
        resource_scores: &HashMap<ResourceType, ResourceScore>,
        trends: &HashMap<ResourceType, ResourceTrend>
    ) -> PrismaResult<DetailedSystemScore>;
}