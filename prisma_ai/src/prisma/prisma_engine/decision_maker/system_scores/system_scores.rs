// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/system_scores.rs
// =================================================================================================
// Purpose: Serves as the main entry point for SystemScore evaluation and manipulation.
// This file contains the primary implementation that combines CPU, memory, disk, and network
// scoring to provide comprehensive system resource availability analysis for the decision maker.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/logic.rs: Uses core evaluation logic
//   - system_scores/cpu_score.rs: Uses CPU-specific scoring
//   - system_scores/memory_score.rs: Uses memory-specific scoring
//   - system_scores/disk_score.rs: Uses disk-specific scoring
//   - system_scores/network_score.rs: Uses network-specific scoring
//   - system_scores/types.rs: Uses types defined for scoring
//   - system_scores/traits.rs: Implements traits defined for scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore and related types
//   - prisma_engine/monitor: Receives system information from monitor module
//   - prisma_engine/decision_maker/decision_maker.rs: Provides scores to the decision maker
//
// Platform Considerations:
// - This module is platform-independent as it coordinates abstract scoring mechanisms
// - The underlying system metrics may vary by platform, but this module works with
//   the abstracted SystemScore regardless of the platform
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};

use crate::err::types::generics::{PrismaError, PrismaResult};
use crate::prisma::prisma_engine::types::{ResourceType, SystemScore};
use crate::prisma::prisma_engine::monitor::system::types::{
    SystemMetrics, CpuMetrics, MemoryMetrics, DiskMetrics, NetworkMetrics
};
use crate::prisma::prisma_engine::monitor::system::traits::SystemMonitoring;

use super::traits::{
    ResourceScoreCalculator, SystemScoreEvaluator, ResourceMonitorIntegration,
    SystemScoreBuilder
};
use super::types::{
    DetailedSystemScore, ResourceScore, ResourceTrend,
    SystemScoreConfig, ResourceThreshold
};
use super::generics::ScoreCache;
use super::logic::{
    evaluate_system_score, calculate_trends,
    is_system_critical, get_most_constrained_resource, calculate_system_health
};
use super::cpu_score::CpuScoreCalculator;
use super::memory_score::MemoryScoreCalculator;
use super::disk_score::DiskScoreCalculator;
use super::network_score::NetworkScoreCalculator;

/// Main implementation of the SystemScoreEvaluator trait
pub struct SystemScoreEvaluatorImpl {
    /// Configuration for the system score evaluation
    config: SystemScoreConfig,
    /// System monitor for getting system metrics
    system_monitor: Arc<RwLock<dyn SystemMonitoring>>,
    /// Cache for historical system scores
    score_cache: Arc<RwLock<ScoreCache>>,
    /// Background evaluation task
    evaluation_task: Option<JoinHandle<()>>,
    /// Current system score
    current_score: Arc<RwLock<DetailedSystemScore>>,
    /// Resource calculators
    calculators: HashMap<ResourceType, Box<dyn ResourceScoreCalculator>>,
}

impl SystemScoreEvaluatorImpl {
    /// Create a new SystemScoreEvaluatorImpl with default settings
    pub fn new(system_monitor: Arc<RwLock<dyn SystemMonitoring>>) -> Self {
        let config = SystemScoreConfig::default();
        let score_cache = Arc::new(RwLock::new(ScoreCache::new(config.history_size)));

        // Create empty system score
        let current_score = Arc::new(RwLock::new(DetailedSystemScore {
            system_score: SystemScore {
                availability: HashMap::new(),
            },
            resource_scores: HashMap::new(),
            trends: HashMap::new(),
            timestamp: SystemTime::now(),
        }));

        // Create resource calculators
        let mut calculators = HashMap::new();
        calculators.insert(
            ResourceType::CPU,
            Box::new(CpuScoreCalculator::new()) as Box<dyn ResourceScoreCalculator>
        );
        calculators.insert(
            ResourceType::Memory,
            Box::new(MemoryScoreCalculator::new()) as Box<dyn ResourceScoreCalculator>
        );
        calculators.insert(
            ResourceType::DiskIO,
            Box::new(DiskScoreCalculator::new()) as Box<dyn ResourceScoreCalculator>
        );
        calculators.insert(
            ResourceType::NetworkBandwidth,
            Box::new(NetworkScoreCalculator::new()) as Box<dyn ResourceScoreCalculator>
        );

        SystemScoreEvaluatorImpl {
            config,
            system_monitor,
            score_cache,
            evaluation_task: None,
            current_score,
            calculators,
        }
    }

    /// Create a new SystemScoreEvaluatorImpl with custom settings
    pub fn with_config(
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>,
        config: SystemScoreConfig
    ) -> Self {
        let score_cache = Arc::new(RwLock::new(ScoreCache::new(config.history_size)));

        // Create empty system score
        let current_score = Arc::new(RwLock::new(DetailedSystemScore {
            system_score: SystemScore {
                availability: HashMap::new(),
            },
            resource_scores: HashMap::new(),
            trends: HashMap::new(),
            timestamp: SystemTime::now(),
        }));

        // Create resource calculators with custom thresholds and weights
        let mut calculators = HashMap::new();

        // CPU calculator
        let cpu_threshold = config.thresholds.get(&ResourceType::CPU)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::CPU,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            });
        let cpu_weight = config.weights.get(&ResourceType::CPU).copied().unwrap_or(1.0);
        calculators.insert(
            ResourceType::CPU,
            Box::new(CpuScoreCalculator::with_config(cpu_threshold, cpu_weight)) as Box<dyn ResourceScoreCalculator>
        );

        // Memory calculator
        let memory_threshold = config.thresholds.get(&ResourceType::Memory)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::Memory,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            });
        let memory_weight = config.weights.get(&ResourceType::Memory).copied().unwrap_or(1.0);
        calculators.insert(
            ResourceType::Memory,
            Box::new(MemoryScoreCalculator::with_config(memory_threshold, memory_weight)) as Box<dyn ResourceScoreCalculator>
        );

        // Disk calculator
        let disk_threshold = config.thresholds.get(&ResourceType::DiskIO)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::DiskIO,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            });
        let disk_weight = config.weights.get(&ResourceType::DiskIO).copied().unwrap_or(0.8);
        calculators.insert(
            ResourceType::DiskIO,
            Box::new(DiskScoreCalculator::with_config(disk_threshold, disk_weight, 500.0 * 1024.0 * 1024.0)) as Box<dyn ResourceScoreCalculator>
        );

        // Network calculator
        let network_threshold = config.thresholds.get(&ResourceType::NetworkBandwidth)
            .cloned()
            .unwrap_or_else(|| ResourceThreshold {
                resource_type: ResourceType::NetworkBandwidth,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            });
        let network_weight = config.weights.get(&ResourceType::NetworkBandwidth).copied().unwrap_or(0.7);
        calculators.insert(
            ResourceType::NetworkBandwidth,
            Box::new(NetworkScoreCalculator::with_config(network_threshold, network_weight, 100.0 * 1024.0 * 1024.0)) as Box<dyn ResourceScoreCalculator>
        );

        SystemScoreEvaluatorImpl {
            config,
            system_monitor,
            score_cache,
            evaluation_task: None,
            current_score,
            calculators,
        }
    }

    /// Start the background evaluation task
    pub async fn start(&mut self) -> PrismaResult<()> {
        if self.evaluation_task.is_some() {
            warn!("System score evaluation task is already running");
            return Ok(());
        }

        info!("Starting system score evaluation task");

        let system_monitor_clone = Arc::clone(&self.system_monitor);
        let score_cache_clone = Arc::clone(&self.score_cache);
        let current_score_clone = Arc::clone(&self.current_score);
        let config_clone = self.config.clone();
        let update_interval = self.config.update_interval;

        let handle = tokio::spawn(async move {
            Self::evaluation_loop(
                system_monitor_clone,
                score_cache_clone,
                current_score_clone,
                config_clone,
                update_interval
            ).await;
        });

        self.evaluation_task = Some(handle);
        Ok(())
    }

    /// Stop the background evaluation task
    pub async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping system score evaluation task");

        if let Some(handle) = self.evaluation_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("System score evaluation task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("System score evaluation task cancelled as expected"),
                Err(e) => error!("Error waiting for system score evaluation task to stop: {:?}", e),
            }
        } else {
            warn!("System score evaluation task was not running");
        }

        Ok(())
    }

    /// Background evaluation loop
    async fn evaluation_loop(
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>,
        score_cache: Arc<RwLock<ScoreCache>>,
        current_score: Arc<RwLock<DetailedSystemScore>>,
        config: SystemScoreConfig,
        update_interval: Duration
    ) {
        info!("Starting system score evaluation loop with interval {:?}", update_interval);
        let mut interval = time::interval(update_interval);

        loop {
            interval.tick().await;

            // Get system metrics
            let metrics = match system_monitor.read().await.get_system_metrics().await {
                Ok(metrics) => metrics,
                Err(e) => {
                    error!("Failed to get system metrics: {:?}", e);
                    continue;
                }
            };

            // Evaluate system score
            let detailed_score = match evaluate_system_score(&metrics, &config).await {
                Ok(score) => score,
                Err(e) => {
                    error!("Failed to evaluate system score: {:?}", e);
                    continue;
                }
            };

            // Get historical scores
            let historical_scores = {
                let cache = score_cache.read().await;
                cache.get_scores()
            };

            // Calculate trends
            let trends = match calculate_trends(&detailed_score, &historical_scores).await {
                Ok(trends) => trends,
                Err(e) => {
                    error!("Failed to calculate trends: {:?}", e);
                    HashMap::new()
                }
            };

            // Create final detailed score with trends
            let final_score = DetailedSystemScore {
                system_score: detailed_score.system_score.clone(),
                resource_scores: detailed_score.resource_scores.clone(),
                trends,
                timestamp: SystemTime::now(),
            };

            // Update current score
            {
                let mut score_guard = current_score.write().await;
                *score_guard = final_score.clone();
            }

            // Add to cache
            {
                let mut cache = score_cache.write().await;
                cache.add_score(final_score);
            }

            // Log system health
            let health = calculate_system_health(&detailed_score);
            debug!("System health: {:.1}%", health);

            // Check for critical resources
            if is_system_critical(&detailed_score, &config) {
                if let Some(resource) = get_most_constrained_resource(&detailed_score) {
                    warn!(
                        "System resource critical: {:?} at {:.1}%",
                        resource,
                        detailed_score.resource_scores.get(&resource).map_or(0.0, |s| s.availability.0)
                    );
                }
            }
        }
    }
}

#[async_trait]
impl SystemScoreEvaluator for SystemScoreEvaluatorImpl {
    async fn evaluate_score(&self, metrics: &SystemMetrics) -> PrismaResult<DetailedSystemScore> {
        evaluate_system_score(metrics, &self.config).await
    }

    async fn get_current_score(&self) -> PrismaResult<DetailedSystemScore> {
        Ok(self.current_score.read().await.clone())
    }

    async fn get_historical_scores(&self) -> PrismaResult<Vec<DetailedSystemScore>> {
        let cache = self.score_cache.read().await;
        Ok(cache.get_scores())
    }

    fn get_config(&self) -> SystemScoreConfig {
        self.config.clone()
    }

    fn set_config(&mut self, config: SystemScoreConfig) {
        // Store the new history size before updating config
        let new_history_size = config.history_size;

        // Update the config
        self.config = config;

        // Get a clone of the score cache for later use
        let score_cache = self.score_cache.clone();

        // We can't spawn a task here that captures self, so we'll just note that
        // the cache capacity should be updated separately
        debug!("Score cache capacity will be updated to {}", new_history_size);
    }

    fn get_calculator(&self, resource_type: ResourceType) -> Option<&dyn ResourceScoreCalculator> {
        self.calculators.get(&resource_type).map(|c| c.as_ref())
    }

    fn add_calculator(&mut self, calculator: Box<dyn ResourceScoreCalculator>) {
        let resource_type = calculator.get_resource_type();
        self.calculators.insert(resource_type, calculator);
    }

    fn remove_calculator(&mut self, resource_type: ResourceType) -> Option<Box<dyn ResourceScoreCalculator>> {
        self.calculators.remove(&resource_type)
    }
}

#[async_trait]
impl ResourceMonitorIntegration for SystemScoreEvaluatorImpl {
    async fn get_system_metrics(&self) -> PrismaResult<SystemMetrics> {
        self.system_monitor.read().await.get_system_metrics().await
    }

    async fn get_cpu_metrics(&self) -> PrismaResult<CpuMetrics> {
        let metrics = self.system_monitor.read().await.get_system_metrics().await?;
        if let Some(cpu) = metrics.cpu {
            Ok(cpu)
        } else {
            Err(PrismaError::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "CPU metrics not available"
            )))
        }
    }

    async fn get_memory_metrics(&self) -> PrismaResult<MemoryMetrics> {
        let metrics = self.system_monitor.read().await.get_system_metrics().await?;
        if let Some(memory) = metrics.memory {
            Ok(memory)
        } else {
            Err(PrismaError::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Memory metrics not available"
            )))
        }
    }

    async fn get_disk_metrics(&self) -> PrismaResult<DiskMetrics> {
        let metrics = self.system_monitor.read().await.get_system_metrics().await?;
        if let Some(disk) = metrics.disk {
            Ok(disk)
        } else {
            Err(PrismaError::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Disk metrics not available"
            )))
        }
    }

    async fn get_network_metrics(&self) -> PrismaResult<NetworkMetrics> {
        let metrics = self.system_monitor.read().await.get_system_metrics().await?;
        if let Some(network) = metrics.network {
            Ok(network)
        } else {
            Err(PrismaError::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Network metrics not available"
            )))
        }
    }
}

impl SystemScoreBuilder for SystemScoreEvaluatorImpl {
    fn build_system_score(
        &self,
        resource_scores: &HashMap<ResourceType, ResourceScore>
    ) -> PrismaResult<SystemScore> {
        let mut availability = HashMap::new();
        for (resource_type, score) in resource_scores {
            availability.insert(*resource_type, score.availability);
        }

        Ok(SystemScore { availability })
    }

    fn build_detailed_score(
        &self,
        resource_scores: &HashMap<ResourceType, ResourceScore>,
        trends: &HashMap<ResourceType, ResourceTrend>
    ) -> PrismaResult<DetailedSystemScore> {
        let system_score = self.build_system_score(resource_scores)?;

        Ok(DetailedSystemScore {
            system_score,
            resource_scores: resource_scores.clone(),
            trends: trends.clone(),
            timestamp: SystemTime::now(),
        })
    }
}

/// Create a new SystemScoreEvaluator with default settings
pub fn create_system_score_evaluator(
    system_monitor: Arc<RwLock<dyn SystemMonitoring>>
) -> Box<dyn SystemScoreEvaluator> {
    Box::new(SystemScoreEvaluatorImpl::new(system_monitor))
}

/// Create a new SystemScoreEvaluator with custom settings
pub fn create_system_score_evaluator_with_config(
    system_monitor: Arc<RwLock<dyn SystemMonitoring>>,
    config: SystemScoreConfig
) -> Box<dyn SystemScoreEvaluator> {
    Box::new(SystemScoreEvaluatorImpl::with_config(system_monitor, config))
}