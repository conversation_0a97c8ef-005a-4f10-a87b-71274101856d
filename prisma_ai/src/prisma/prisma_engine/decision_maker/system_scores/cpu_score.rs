// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/cpu_score.rs
// =================================================================================================
// Purpose: Implements CPU-specific scoring logic for evaluating system resource availability.
// This file contains functions and structures to analyze CPU metrics and determine appropriate
// execution strategies based on current CPU conditions, helping the decision maker optimize
// task execution for CPU-bound operations.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/logic.rs: Uses CPU scoring in overall evaluation
//   - system_scores/types.rs: Uses types defined for scoring
//   - system_scores/traits.rs: Implements traits defined for scoring
//   - system_scores/generics.rs: Uses generic utilities
//   - system_scores/system_scores.rs: Integrates CPU scoring into overall system scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, ResourceUsage types
//   - prisma_engine/monitor/system/cpu.rs: Receives CPU metrics from monitor module
//   - sysinfo: May use directly for CPU information
//
// Platform Considerations:
// - This module interprets platform-specific CPU metrics but presents them in a
//   platform-independent way through the SystemScore abstraction
// =================================================================================================

use async_trait::async_trait;
use std::time::SystemTime;
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use crate::prisma::prisma_engine::monitor::system::types::{CpuMetrics, SystemMetrics};

use super::traits::ResourceScoreCalculator;
use super::types::{
    ResourceScore, ResourceTrend, AvailabilityLevel, ResourceThreshold,
    ResourceMetrics
};
use super::generics::{determine_trend, MovingAverage};

/// Calculator for CPU resource scores
pub struct CpuScoreCalculator {
    /// Threshold configuration for CPU scoring
    threshold: ResourceThreshold,
    /// Weight of CPU in the overall system score
    weight: f64,
    /// Moving average for smoothing CPU usage
    usage_average: MovingAverage,
    /// Moving average for smoothing load average
    load_average: MovingAverage,
}

impl CpuScoreCalculator {
    /// Create a new CPU score calculator with default settings
    pub fn new() -> Self {
        CpuScoreCalculator {
            threshold: ResourceThreshold {
                resource_type: ResourceType::CPU,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
            weight: 1.0,
            usage_average: MovingAverage::new(5),
            load_average: MovingAverage::new(5),
        }
    }

    /// Create a new CPU score calculator with custom settings
    pub fn with_config(threshold: ResourceThreshold, weight: f64) -> Self {
        CpuScoreCalculator {
            threshold,
            weight,
            usage_average: MovingAverage::new(5),
            load_average: MovingAverage::new(5),
        }
    }

    /// Calculate CPU availability based on CPU metrics
    fn calculate_availability(&self, cpu_metrics: &CpuMetrics) -> ResourceUsage {
        // Get CPU usage percentage
        let usage_percent = cpu_metrics.usage_percent;

        // Smooth the usage with moving average
        let smoothed_usage = self.usage_average.values().last()
            .copied()
            .unwrap_or(usage_percent);

        // Calculate availability (100 - usage)
        let availability = 100.0 - smoothed_usage;

        // Adjust availability based on load average if available
        if let Some((one_min, _, _)) = cpu_metrics.load_average {
            // Get logical core count
            let core_count = cpu_metrics.logical_cores as f64;

            // Calculate load per core
            let load_per_core = one_min / core_count;

            // Smooth the load with moving average
            let smoothed_load = self.load_average.values().last()
                .copied()
                .unwrap_or(load_per_core);

            // Adjust availability based on load per core
            // If load per core > 1, the CPU is overloaded
            if smoothed_load > 1.0 {
                // Reduce availability proportionally to overload
                let load_factor = 1.0 / smoothed_load;
                return ResourceUsage(availability * load_factor);
            }
        }

        ResourceUsage(availability)
    }

    /// Get the availability level based on the availability percentage
    fn get_availability_level(&self, availability: f64) -> AvailabilityLevel {
        if availability >= self.threshold.high_threshold {
            AvailabilityLevel::High
        } else if availability >= self.threshold.medium_threshold {
            AvailabilityLevel::Medium
        } else if availability >= self.threshold.low_threshold {
            AvailabilityLevel::Low
        } else {
            AvailabilityLevel::Critical
        }
    }
}

#[async_trait]
impl ResourceScoreCalculator for CpuScoreCalculator {
    fn get_resource_type(&self) -> ResourceType {
        ResourceType::CPU
    }

    async fn calculate_score(&self, metrics: &SystemMetrics) -> PrismaResult<ResourceScore> {
        // Get CPU metrics from system metrics
        let cpu_metrics = if let Some(cpu) = &metrics.cpu {
            cpu
        } else {
            warn!("No CPU metrics available for scoring");
            return Ok(ResourceScore {
                resource_type: ResourceType::CPU,
                availability: ResourceUsage(100.0),
                level: AvailabilityLevel::High,
                timestamp: SystemTime::now(),
                raw_metrics: Some(ResourceMetrics::None),
            });
        };

        // Create local copies of the values we need
        let usage_percent = cpu_metrics.usage_percent;
        let mut smoothed_usage = usage_percent;
        let mut smoothed_load = 0.0;

        // Use the last values from the moving averages if available
        if let Some(last_usage) = self.usage_average.values().last() {
            smoothed_usage = *last_usage;
        }

        if let Some((one_min, _, _)) = cpu_metrics.load_average {
            let core_count = cpu_metrics.logical_cores as f64;
            let load_per_core = one_min / core_count;

            if let Some(last_load) = self.load_average.values().last() {
                smoothed_load = *last_load;
            } else {
                smoothed_load = load_per_core;
            }
        }

        // Calculate availability
        let availability = self.calculate_availability(cpu_metrics);

        // Get availability level
        let level = self.get_availability_level(availability.0);

        debug!(
            "CPU score: availability={:.1}%, level={:?}, cores={}, usage={:.1}%",
            availability.0,
            level,
            cpu_metrics.logical_cores,
            cpu_metrics.usage_percent
        );

        Ok(ResourceScore {
            resource_type: ResourceType::CPU,
            availability,
            level,
            timestamp: SystemTime::now(),
            raw_metrics: Some(ResourceMetrics::Cpu(cpu_metrics.clone())),
        })
    }

    async fn calculate_trend(
        &self,
        current_score: &ResourceScore,
        historical_scores: &[ResourceScore]
    ) -> PrismaResult<ResourceTrend> {
        // Extract availability values from historical scores
        let mut availability_values: Vec<f64> = historical_scores
            .iter()
            .map(|score| score.availability.0)
            .collect();

        // Add current score to the list
        availability_values.push(current_score.availability.0);

        // Determine trend
        let trend = determine_trend(&availability_values);

        debug!(
            "CPU trend: {:?}, based on {} data points",
            trend,
            availability_values.len()
        );

        Ok(trend)
    }

    fn get_threshold(&self) -> ResourceThreshold {
        self.threshold.clone()
    }

    fn set_threshold(&mut self, threshold: ResourceThreshold) {
        self.threshold = threshold;
    }

    fn get_weight(&self) -> f64 {
        self.weight
    }

    fn set_weight(&mut self, weight: f64) {
        self.weight = weight;
    }
}