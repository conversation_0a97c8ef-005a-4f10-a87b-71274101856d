// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/queue_score.rs
// =================================================================================================
// Purpose: Implements queue-specific scoring logic for evaluating resource requirements.
// This file contains functions and structures to analyze queue states and determine
// appropriate resource allocations, helping the decision maker balance load across
// different execution queues (real-time, standard, background).
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports this module
//   - prisma_scores/logic.rs: Uses queue scoring in overall evaluation
//   - prisma_scores/types.rs: Uses types defined for scoring
//   - prisma_scores/traits.rs: Implements traits defined for scoring
//   - prisma_scores/generics.rs: Uses generic utilities
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore and related types
//   - prisma_engine/executor/queue: Analyzes queue information from executor module
//   - prisma_engine/monitor/prisma: May use queue monitoring data
//
// Platform Considerations:
// - This module is platform-independent as it analyzes abstract queue properties
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, ResourceType, ResourceUsage, TaskPriority
};
use super::traits::QueueScoreAnalyzer;
use super::types::QueueScoreConfig;
use super::generics::{normalize_score, MovingAverage};

/// Represents a queue state for scoring purposes
#[derive(Debug, Clone)]
pub struct QueueState {
    /// Name of the queue
    pub name: String,
    /// Current length of the queue
    pub length: usize,
    /// Average wait time in seconds
    pub avg_wait_time: f64,
    /// Throughput in tasks per second
    pub throughput: f64,
    /// Priority level of the queue
    pub priority: TaskPriority,
    /// Last time the queue was updated
    pub last_updated: Instant,
}

impl QueueState {
    /// Creates a new QueueState
    pub fn new(
        name: String,
        length: usize,
        avg_wait_time: f64,
        throughput: f64,
        priority: TaskPriority
    ) -> Self {
        QueueState {
            name,
            length,
            avg_wait_time,
            throughput,
            priority,
            last_updated: Instant::now(),
        }
    }

    /// Checks if the queue state is stale (older than the specified duration)
    pub fn is_stale(&self, max_age: Duration) -> bool {
        self.last_updated.elapsed() > max_age
    }
}

/// Implementation of the QueueScoreAnalyzer trait for analyzing queue resource requirements
pub struct QueueScoreAnalyzerImpl {
    /// Configuration for queue score analysis
    config: QueueScoreConfig,
    /// Current state of known queues
    queue_states: HashMap<String, QueueState>,
    /// Historical throughput for different queues
    throughput_history: HashMap<String, MovingAverage>,
}

impl QueueScoreAnalyzerImpl {
    /// Creates a new QueueScoreAnalyzerImpl with the specified configuration
    pub fn new(config: QueueScoreConfig) -> Self {
        QueueScoreAnalyzerImpl {
            config,
            queue_states: HashMap::new(),
            throughput_history: HashMap::new(),
        }
    }

    /// Creates a new QueueScoreAnalyzerImpl with default configuration
    pub fn default() -> Self {
        Self::new(QueueScoreConfig::default())
    }

    /// Updates the state of a queue
    pub fn update_queue_state(&mut self, state: QueueState) {
        // Update throughput history
        if let Some(history) = self.throughput_history.get_mut(&state.name) {
            history.add(state.throughput);
        } else {
            let mut new_history = MovingAverage::new(10);
            new_history.add(state.throughput);
            self.throughput_history.insert(state.name.clone(), new_history);
        }

        // Update queue state
        self.queue_states.insert(state.name.clone(), state);
    }

    /// Gets the state of a queue by name
    pub fn get_queue_state(&self, name: &str) -> Option<&QueueState> {
        self.queue_states.get(name)
    }

    /// Removes stale queue states
    pub fn cleanup_stale_states(&mut self, max_age: Duration) {
        let stale_keys: Vec<String> = self.queue_states
            .iter()
            .filter(|(_, state)| state.is_stale(max_age))
            .map(|(key, _)| key.clone())
            .collect();

        for key in stale_keys {
            debug!("Removing stale queue state: {}", key);
            self.queue_states.remove(&key);
        }
    }

    /// Determines the queue state category (short, medium, long) based on length
    fn get_length_category(&self, length: usize) -> &str {
        if length <= self.config.length_thresholds.get("short").copied().unwrap_or(5) {
            "short"
        } else if length <= self.config.length_thresholds.get("medium").copied().unwrap_or(20) {
            "medium"
        } else {
            "long"
        }
    }

    /// Determines the queue state category (short, medium, long) based on wait time
    fn get_wait_time_category(&self, wait_time: f64) -> &str {
        if wait_time <= self.config.wait_time_thresholds.get("short").copied().unwrap_or(1.0) {
            "short"
        } else if wait_time <= self.config.wait_time_thresholds.get("medium").copied().unwrap_or(5.0) {
            "medium"
        } else {
            "long"
        }
    }
}

#[async_trait]
impl QueueScoreAnalyzer for QueueScoreAnalyzerImpl {
    async fn analyze_queue_score(
        &self,
        queue_length: usize,
        avg_wait_time: f64,
        priority: TaskPriority
    ) -> PrismaResult<PrismaScore> {
        debug!("Analyzing queue score with length: {}, wait time: {:.2}s, priority: {:?}",
               queue_length, avg_wait_time, priority);

        // Determine queue state categories
        let length_category = self.get_length_category(queue_length);
        let wait_time_category = self.get_wait_time_category(avg_wait_time);

        debug!("Queue categories - Length: {}, Wait Time: {}", length_category, wait_time_category);

        // Get resource adjustments based on queue state
        let length_adjustments = self.config.resource_adjustments
            .get(length_category)
            .cloned()
            .unwrap_or_else(HashMap::new);

        let wait_time_adjustments = self.config.resource_adjustments
            .get(wait_time_category)
            .cloned()
            .unwrap_or_else(HashMap::new);

        // Combine adjustments, using the maximum value for each resource
        let mut combined_adjustments = HashMap::new();

        // Add all resource types from length adjustments
        for (resource_type, adjustment) in &length_adjustments {
            combined_adjustments.insert(*resource_type, *adjustment);
        }

        // Add or update with wait time adjustments, using max value
        for (resource_type, adjustment) in &wait_time_adjustments {
            combined_adjustments.entry(*resource_type)
                .and_modify(|e| *e = (*e).max(*adjustment))
                .or_insert(*adjustment);
        }

        // Apply priority-based scaling
        let priority_multiplier = match priority {
            TaskPriority::Realtime => 1.5,
            TaskPriority::High => 1.2,
            TaskPriority::Normal => 1.0,
            TaskPriority::Low => 0.8,
        };

        // Create the final score
        let mut resources = HashMap::new();

        // Base resource requirements
        resources.insert(ResourceType::CPU, ResourceUsage(0.2 * priority_multiplier));
        resources.insert(ResourceType::Memory, ResourceUsage(0.2 * priority_multiplier));
        resources.insert(ResourceType::GPU, ResourceUsage(0.0)); // Queues don't directly use GPU
        resources.insert(ResourceType::DiskIO, ResourceUsage(0.1 * priority_multiplier));
        resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.1 * priority_multiplier));

        // Apply combined adjustments
        for (resource_type, adjustment) in combined_adjustments {
            if let Some(usage) = resources.get_mut(&resource_type) {
                usage.0 *= adjustment;
            }
        }

        // Ensure all values are normalized
        let mut score = PrismaScore { resources };
        normalize_score(&mut score);

        debug!("Analyzed queue score: {:?}", score);
        Ok(score)
    }

    async fn adjust_for_throughput(
        &self,
        score: &PrismaScore,
        throughput_history: &[f64]
    ) -> PrismaResult<PrismaScore> {
        if throughput_history.is_empty() {
            return Ok(score.clone());
        }

        // Calculate average throughput
        let avg_throughput: f64 = throughput_history.iter().sum::<f64>() / throughput_history.len() as f64;
        debug!("Adjusting score based on throughput. Average throughput: {:.2} tasks/s", avg_throughput);

        // Adjust resources based on throughput
        let mut adjusted_resources = score.resources.clone();

        // Example adjustment logic:
        // - If avg_throughput < 1 task/s, increase resources by 20%
        // - If avg_throughput < 0.5 task/s, increase by 50%
        // - If avg_throughput > 5 tasks/s, decrease by 10% (queue is processing quickly)
        let multiplier = if avg_throughput < 0.5 {
            1.5 // Significant bottleneck
        } else if avg_throughput < 1.0 {
            1.2 // Moderate bottleneck
        } else if avg_throughput > 5.0 {
            0.9 // Queue is processing quickly
        } else {
            1.0 // Normal throughput
        };

        for (_, usage) in adjusted_resources.iter_mut() {
            usage.0 *= multiplier;
        }

        // Ensure all values are normalized
        let mut adjusted_score = PrismaScore { resources: adjusted_resources };
        normalize_score(&mut adjusted_score);

        Ok(adjusted_score)
    }
}

/// Factory function to create a new QueueScoreAnalyzer
pub fn create_queue_score_analyzer(config: Option<QueueScoreConfig>) -> Arc<dyn QueueScoreAnalyzer> {
    Arc::new(QueueScoreAnalyzerImpl::new(config.unwrap_or_default()))
}