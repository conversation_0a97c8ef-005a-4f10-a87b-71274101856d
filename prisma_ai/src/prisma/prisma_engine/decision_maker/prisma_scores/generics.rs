// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/generics.rs
// =================================================================================================
// Purpose: Provides generic utilities and helper functions for PrismaScore-related operations.
// This file contains reusable components that can be used across different scoring mechanisms
// to avoid code duplication and ensure consistent behavior.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports this module
//   - prisma_scores/logic.rs: May use these utilities for score evaluation
//   - prisma_scores/task_score.rs: May use these utilities for task scoring
//   - prisma_scores/queue_score.rs: May use these utilities for queue scoring
//   - prisma_scores/usage_score.rs: May use these utilities for usage scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore, ResourceType, and ResourceUsage types
//   - std::collections: For data structures used in generic operations
//
// Platform Considerations:
// - This module is platform-independent as it provides generic utilities
// =================================================================================================

use std::collections::HashMap;
use std::time::{Duration, Instant};
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{PrismaScore, ResourceType, ResourceUsage};
use super::types::ScoreWeights;

/// Normalizes a PrismaScore to ensure all resource values are between 0.0 and 1.0
pub fn normalize_score(score: &mut PrismaScore) {
    for (_, usage) in score.resources.iter_mut() {
        if usage.0 < 0.0 {
            usage.0 = 0.0;
        } else if usage.0 > 1.0 {
            usage.0 = 1.0;
        }
    }
}

/// Applies weights to a PrismaScore based on the provided ScoreWeights
pub fn apply_weights(score: &PrismaScore, weights: &ScoreWeights) -> PrismaScore {
    let mut weighted_resources = HashMap::new();

    for (resource_type, usage) in &score.resources {
        let weight = weights.weights.get(resource_type).copied().unwrap_or(1.0);
        let weighted_usage = ResourceUsage(usage.0 * weight);
        weighted_resources.insert(*resource_type, weighted_usage);
    }

    PrismaScore { resources: weighted_resources }
}

/// Combines multiple PrismaScores into a single score using a weighted average
pub fn combine_scores(scores: &[PrismaScore], weights: &[f64]) -> PrismaResult<PrismaScore> {
    if scores.is_empty() {
        return Ok(PrismaScore { resources: HashMap::new() });
    }

    if scores.len() != weights.len() {
        warn!("Score and weight counts don't match: {} scores, {} weights", scores.len(), weights.len());
        return Err("Score and weight counts must match".into());
    }

    let mut combined_resources = HashMap::new();
    let mut weight_sum = 0.0;

    // Calculate the sum of weights
    for &weight in weights {
        weight_sum += weight;
    }

    if weight_sum == 0.0 {
        return Err("Sum of weights cannot be zero".into());
    }

    // Initialize the combined resources with all resource types from all scores
    for score in scores {
        for (resource_type, _) in &score.resources {
            if !combined_resources.contains_key(resource_type) {
                combined_resources.insert(*resource_type, ResourceUsage(0.0));
            }
        }
    }

    // Calculate the weighted average for each resource type
    for (i, score) in scores.iter().enumerate() {
        let weight = weights[i] / weight_sum;

        for (resource_type, usage) in &score.resources {
            let current = combined_resources.get_mut(resource_type).unwrap();
            current.0 += usage.0 * weight;
        }
    }

    Ok(PrismaScore { resources: combined_resources })
}

/// Calculates the difference between two PrismaScores
pub fn score_difference(score1: &PrismaScore, score2: &PrismaScore) -> PrismaScore {
    let mut diff_resources = HashMap::new();

    // Include all resource types from both scores
    let mut all_resource_types = score1.resources.keys().copied().collect::<Vec<_>>();
    for resource_type in score2.resources.keys() {
        if !all_resource_types.contains(resource_type) {
            all_resource_types.push(*resource_type);
        }
    }

    // Calculate the difference for each resource type
    for resource_type in all_resource_types {
        let usage1 = score1.resources.get(&resource_type).map_or(0.0, |u| u.0);
        let usage2 = score2.resources.get(&resource_type).map_or(0.0, |u| u.0);
        diff_resources.insert(resource_type, ResourceUsage((usage1 - usage2).abs()));
    }

    PrismaScore { resources: diff_resources }
}

/// A simple cache for PrismaScore calculations to avoid redundant computation
pub struct ScoreCache<T> {
    cache: HashMap<String, (T, Instant)>,
    ttl: Duration,
}

impl<T: Clone> ScoreCache<T> {
    /// Creates a new ScoreCache with the specified time-to-live
    pub fn new(ttl_seconds: u64) -> Self {
        ScoreCache {
            cache: HashMap::new(),
            ttl: Duration::from_secs(ttl_seconds),
        }
    }

    /// Gets a value from the cache if it exists and is not expired
    pub fn get(&mut self, key: &str) -> Option<T> {
        if let Some((value, timestamp)) = self.cache.get(key) {
            if timestamp.elapsed() < self.ttl {
                debug!("Cache hit for key: {}", key);
                return Some(value.clone());
            } else {
                debug!("Cache expired for key: {}", key);
                self.cache.remove(key);
            }
        }
        None
    }

    /// Puts a value into the cache
    pub fn put(&mut self, key: String, value: T) {
        debug!("Caching value for key: {}", key);
        self.cache.insert(key, (value, Instant::now()));
    }

    /// Clears expired entries from the cache
    pub fn cleanup(&mut self) {
        let expired_keys: Vec<String> = self.cache
            .iter()
            .filter(|(_, (_, timestamp))| timestamp.elapsed() >= self.ttl)
            .map(|(key, _)| key.clone())
            .collect();

        for key in expired_keys {
            debug!("Removing expired cache entry: {}", key);
            self.cache.remove(&key);
        }
    }
}

/// A utility for tracking moving averages of resource usage
pub struct MovingAverage {
    values: Vec<f64>,
    max_size: usize,
    sum: f64,
}

impl MovingAverage {
    /// Creates a new MovingAverage with the specified window size
    pub fn new(window_size: usize) -> Self {
        MovingAverage {
            values: Vec::with_capacity(window_size),
            max_size: window_size,
            sum: 0.0,
        }
    }

    /// Adds a value to the moving average
    pub fn add(&mut self, value: f64) {
        if self.values.len() == self.max_size {
            let old_value = self.values.remove(0);
            self.sum -= old_value;
        }

        self.values.push(value);
        self.sum += value;
    }

    /// Gets the current average
    pub fn average(&self) -> f64 {
        if self.values.is_empty() {
            0.0
        } else {
            self.sum / self.values.len() as f64
        }
    }

    /// Gets the number of values in the moving average
    pub fn count(&self) -> usize {
        self.values.len()
    }

    /// Checks if the moving average is at capacity
    pub fn is_full(&self) -> bool {
        self.values.len() == self.max_size
    }

    /// Clears all values from the moving average
    pub fn clear(&mut self) {
        self.values.clear();
        self.sum = 0.0;
    }
}