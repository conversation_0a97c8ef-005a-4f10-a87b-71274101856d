// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/usage_score.rs
// =================================================================================================
// Purpose: Implements resource usage scoring logic for evaluating efficiency and utilization.
// This file contains functions and structures to analyze how effectively tasks use allocated
// resources, helping the decision maker optimize resource allocation and improve overall
// system efficiency.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports this module
//   - prisma_scores/logic.rs: Uses usage scoring in overall evaluation
//   - prisma_scores/types.rs: Uses types defined for scoring
//   - prisma_scores/traits.rs: Implements traits defined for scoring
//   - prisma_scores/generics.rs: Uses generic utilities
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore, ResourceType, ResourceUsage types
//   - prisma_engine/monitor: Analyzes resource usage data from monitor module
//   - prisma_engine/executor: May analyze task execution efficiency
//
// Platform Considerations:
// - This module is platform-independent as it analyzes abstract resource usage patterns
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::debug;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, ResourceType, ResourceUsage
};
use super::traits::UsageScoreAnalyzer;
use super::types::UsageScoreConfig;
use super::generics::MovingAverage;

/// Represents the usage trend for a resource
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UsageTrend {
    /// Resource usage is increasing
    Increasing,
    /// Resource usage is decreasing
    Decreasing,
    /// Resource usage is stable
    Stable,
    /// Resource usage is fluctuating
    Fluctuating,
    /// Not enough data to determine trend
    Unknown,
}

/// Implementation of the UsageScoreAnalyzer trait for analyzing resource usage patterns
pub struct UsageScoreAnalyzerImpl {
    /// Configuration for usage score analysis
    config: UsageScoreConfig,
    /// Historical usage data for different resources
    usage_history: HashMap<ResourceType, Vec<ResourceUsage>>,
    /// Moving averages for different resources
    moving_averages: HashMap<ResourceType, MovingAverage>,
}

impl UsageScoreAnalyzerImpl {
    /// Creates a new UsageScoreAnalyzerImpl with the specified configuration
    pub fn new(config: UsageScoreConfig) -> Self {
        let mut usage_history = HashMap::new();
        let mut moving_averages = HashMap::new();

        // Initialize history and moving averages for common resource types
        for resource_type in &[
            ResourceType::CPU,
            ResourceType::Memory,
            ResourceType::GPU,
            ResourceType::DiskIO,
            ResourceType::NetworkBandwidth,
        ] {
            usage_history.insert(*resource_type, Vec::new());
            moving_averages.insert(*resource_type, MovingAverage::new(config.history_window));
        }

        UsageScoreAnalyzerImpl {
            config,
            usage_history,
            moving_averages,
        }
    }

    /// Creates a new UsageScoreAnalyzerImpl with default configuration
    pub fn default() -> Self {
        Self::new(UsageScoreConfig::default())
    }

    /// Records a new resource usage data point
    pub fn record_usage(&mut self, resource_type: ResourceType, usage: ResourceUsage) {
        // Add to history
        if let Some(history) = self.usage_history.get_mut(&resource_type) {
            history.push(usage);

            // Trim history if it exceeds the window size
            if history.len() > self.config.history_window {
                history.remove(0);
            }
        }

        // Update moving average
        if let Some(avg) = self.moving_averages.get_mut(&resource_type) {
            avg.add(usage.0);
        }
    }

    /// Gets the current usage level category (low, medium, high) for a resource
    fn get_usage_level(&self, resource_type: ResourceType, usage: f64) -> &str {
        if let Some(thresholds) = self.config.usage_thresholds.get(&resource_type) {
            if usage <= thresholds.get("low").copied().unwrap_or(0.3) {
                "low"
            } else if usage <= thresholds.get("medium").copied().unwrap_or(0.6) {
                "medium"
            } else {
                "high"
            }
        } else {
            // Default thresholds if not configured
            if usage <= 0.3 {
                "low"
            } else if usage <= 0.6 {
                "medium"
            } else {
                "high"
            }
        }
    }

    /// Analyzes the trend of resource usage over time
    fn analyze_trend(&self, resource_type: ResourceType) -> UsageTrend {
        if let Some(history) = self.usage_history.get(&resource_type) {
            if history.len() < 3 {
                return UsageTrend::Unknown;
            }

            // Calculate differences between consecutive points
            let mut increases = 0;
            let mut decreases = 0;

            for i in 1..history.len() {
                let prev = history[i-1].0;
                let curr = history[i].0;

                if curr > prev {
                    increases += 1;
                } else if curr < prev {
                    decreases += 1;
                }
            }

            // Determine trend based on increases and decreases
            let total = increases + decreases;
            if total == 0 {
                UsageTrend::Stable
            } else {
                let increase_ratio = increases as f64 / total as f64;

                if increase_ratio > 0.7 {
                    UsageTrend::Increasing
                } else if increase_ratio < 0.3 {
                    UsageTrend::Decreasing
                } else {
                    UsageTrend::Fluctuating
                }
            }
        } else {
            UsageTrend::Unknown
        }
    }
}

#[async_trait]
impl UsageScoreAnalyzer for UsageScoreAnalyzerImpl {
    async fn analyze_usage_score(
        &self,
        resource_usage: &HashMap<ResourceType, Vec<ResourceUsage>>
    ) -> PrismaResult<PrismaScore> {
        debug!("Analyzing usage score for {} resource types", resource_usage.len());

        let mut resources = HashMap::new();

        for (resource_type, usage_history) in resource_usage {
            if usage_history.is_empty() {
                continue;
            }

            // Calculate average usage
            let avg_usage = usage_history.iter().map(|u| u.0).sum::<f64>() / usage_history.len() as f64;

            // Get usage level category
            let level = self.get_usage_level(*resource_type, avg_usage);
            debug!("Resource {:?} has average usage {:.2} ({} level)", resource_type, avg_usage, level);

            // Analyze trend
            let trend = self.analyze_trend(*resource_type);
            debug!("Resource {:?} has trend {:?}", resource_type, trend);

            // Adjust score based on level and trend
            let base_score = match level {
                "low" => 0.2,
                "medium" => 0.5,
                "high" => 0.8,
                _ => 0.5,
            };

            // Apply trend adjustment
            let trend_adjustment = match trend {
                UsageTrend::Increasing => 0.1, // Allocate more if usage is increasing
                UsageTrend::Decreasing => -0.1, // Allocate less if usage is decreasing
                UsageTrend::Fluctuating => 0.05, // Slight increase for fluctuating usage
                _ => 0.0, // No adjustment for stable or unknown
            };

            // Calculate final score
            let final_score = f64::max(0.0, f64::min(1.0, base_score + trend_adjustment));
            resources.insert(*resource_type, ResourceUsage(final_score));
        }

        // Ensure all resource types are included
        for resource_type in &[
            ResourceType::CPU,
            ResourceType::Memory,
            ResourceType::GPU,
            ResourceType::DiskIO,
            ResourceType::NetworkBandwidth,
        ] {
            if !resources.contains_key(resource_type) {
                resources.insert(*resource_type, ResourceUsage(0.3)); // Default moderate score
            }
        }

        let score = PrismaScore { resources };
        debug!("Analyzed usage score: {:?}", score);

        Ok(score)
    }

    async fn predict_future_usage(
        &self,
        current_usage: &HashMap<ResourceType, ResourceUsage>,
        historical_usage: &HashMap<ResourceType, Vec<ResourceUsage>>
    ) -> PrismaResult<HashMap<ResourceType, ResourceUsage>> {
        debug!("Predicting future usage based on current and historical data");

        let mut predicted_usage = HashMap::new();

        for (resource_type, current) in current_usage {
            let trend = if let Some(history) = historical_usage.get(resource_type) {
                if history.len() >= 3 {
                    // Simple linear prediction based on recent history
                    let recent = &history[history.len() - 3..];
                    let avg_change = (recent[2].0 - recent[0].0) / 2.0;

                    // Predict next value
                    let predicted = f64::max(0.0, f64::min(1.0, current.0 + avg_change));
                    predicted
                } else {
                    // Not enough history, use current value
                    current.0
                }
            } else {
                // No history, use current value
                current.0
            };

            predicted_usage.insert(*resource_type, ResourceUsage(trend));
        }

        // Ensure all resource types are included
        for resource_type in &[
            ResourceType::CPU,
            ResourceType::Memory,
            ResourceType::GPU,
            ResourceType::DiskIO,
            ResourceType::NetworkBandwidth,
        ] {
            if !predicted_usage.contains_key(resource_type) {
                // If we don't have current usage, check if we have history
                if let Some(history) = historical_usage.get(resource_type) {
                    if !history.is_empty() {
                        // Use the last historical value
                        predicted_usage.insert(*resource_type, history[history.len() - 1]);
                    } else {
                        // No history either, use a default value
                        predicted_usage.insert(*resource_type, ResourceUsage(0.3));
                    }
                } else {
                    // No current or historical data, use a default value
                    predicted_usage.insert(*resource_type, ResourceUsage(0.3));
                }
            }
        }

        debug!("Predicted future usage: {:?}", predicted_usage);
        Ok(predicted_usage)
    }
}

/// Factory function to create a new UsageScoreAnalyzer
pub fn create_usage_score_analyzer(config: Option<UsageScoreConfig>) -> Arc<dyn UsageScoreAnalyzer> {
    Arc::new(UsageScoreAnalyzerImpl::new(config.unwrap_or_default()))
}