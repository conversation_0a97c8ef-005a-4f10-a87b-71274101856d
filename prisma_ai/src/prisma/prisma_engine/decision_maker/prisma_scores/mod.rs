// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/mod.rs
// =================================================================================================
// Purpose: This submodule handles logic related to interpreting or adjusting the PrismaScore
// (task resource requirements). It serves as the entry point for PrismaScore evaluation
// functionality, exporting the necessary components for use by the decision maker.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Imports this module
//   - decision_maker/decision_maker.rs: Uses the exported functions
//   - prisma_scores/logic.rs: Contains core evaluation logic
//   - prisma_scores/task_score.rs: Handles task-specific scoring
//   - prisma_scores/queue_score.rs: Handles queue-specific scoring
//   - prisma_scores/usage_score.rs: Handles resource usage scoring
//   - prisma_scores/types.rs: Defines score-related types
//   - prisma_scores/traits.rs: Defines score-related traits
//   - prisma_scores/generics.rs: Provides generic utilities
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore type
//   - prisma_engine/tcl: Receives task information for scoring
//   - prisma_engine/executor: Provides execution context for scoring decisions
//
// Platform Considerations:
// - This module is platform-independent as it deals with abstract resource scoring
// =================================================================================================

pub mod logic;
pub mod types;
pub mod traits;
pub mod generics;
pub mod task_score;
pub mod queue_score;
pub mod usage_score;
pub mod prisma_scores;

// Re-export key functions and types for easier access
// From logic.rs
pub use logic::{
    evaluate_prisma_score,
    combine_all_scores,
    check_resource_constraints,
    calculate_efficiency,
    predict_resource_impact
};

// From types.rs
pub use types::{
    ScoreWeights,
    ResourcePriority,
    TaskScoreConfig,
    QueueScoreConfig,
    UsageScoreConfig,
    PrismaScoreConfig
};

// From traits.rs
pub use traits::{
    ScoreEvaluator,
    TaskScoreAnalyzer,
    QueueScoreAnalyzer,
    UsageScoreAnalyzer
};

// From generics.rs
pub use generics::{
    normalize_score,
    apply_weights,
    combine_scores,
    score_difference,
    ScoreCache,
    MovingAverage
};

// From task_score.rs
pub use task_score::create_task_score_analyzer;

// From queue_score.rs
pub use queue_score::{
    QueueState,
    create_queue_score_analyzer
};

// From usage_score.rs
pub use usage_score::{
    UsageTrend,
    create_usage_score_analyzer
};

// From prisma_scores.rs
pub use prisma_scores::{
    PrismaScoreEvaluator,
    AsAny,
    create_score_evaluator
};
