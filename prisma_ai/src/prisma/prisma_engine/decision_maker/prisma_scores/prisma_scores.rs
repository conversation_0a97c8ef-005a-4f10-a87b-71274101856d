// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/prisma_scores.rs
// =================================================================================================
// Purpose: Serves as the main entry point for PrismaScore evaluation and manipulation.
// This file contains the primary implementation that combines task, queue, and usage scoring
// to provide comprehensive resource requirement analysis for the decision maker.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports this module
//   - prisma_scores/logic.rs: Uses core evaluation logic
//   - prisma_scores/task_score.rs: Uses task-specific scoring
//   - prisma_scores/queue_score.rs: Uses queue-specific scoring
//   - prisma_scores/usage_score.rs: Uses resource usage scoring
//   - prisma_scores/types.rs: Uses types defined for scoring
//   - prisma_scores/traits.rs: Implements traits defined for scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore and related types
//   - prisma_engine/tcl: Analyzes task information
//   - prisma_engine/executor: Analyzes execution context
//   - prisma_engine/monitor: Uses monitoring data
//
// Platform Considerations:
// - This module is platform-independent as it coordinates abstract scoring mechanisms
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use tracing::debug;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, ResourceType, ResourceUsage
};
use crate::prisma::prisma_engine::traits::Task;
use super::traits::{ScoreEvaluator, TaskScoreAnalyzer, QueueScoreAnalyzer, UsageScoreAnalyzer};
use super::types::PrismaScoreConfig;
use super::logic::{evaluate_prisma_score, combine_all_scores, check_resource_constraints};
use super::task_score::create_task_score_analyzer;
use super::queue_score::{create_queue_score_analyzer, QueueState};
use super::usage_score::create_usage_score_analyzer;

/// Main implementation of the PrismaScore evaluation system
pub struct PrismaScoreEvaluator {
    /// Configuration for score evaluation
    config: PrismaScoreConfig,
    /// Task score analyzer
    task_analyzer: Arc<dyn TaskScoreAnalyzer>,
    /// Queue score analyzer
    queue_analyzer: Arc<dyn QueueScoreAnalyzer>,
    /// Usage score analyzer
    usage_analyzer: Arc<dyn UsageScoreAnalyzer>,
    /// Current system resource availability
    system_resources: RwLock<HashMap<ResourceType, ResourceUsage>>,
    /// Resource thresholds for constraint checking
    resource_thresholds: HashMap<ResourceType, f64>,
}

impl PrismaScoreEvaluator {
    /// Creates a new PrismaScoreEvaluator with the specified configuration
    pub fn new(config: PrismaScoreConfig) -> Self {
        // Create analyzers
        let task_analyzer = create_task_score_analyzer(Some(config.task_config.clone()));
        let queue_analyzer = create_queue_score_analyzer(Some(config.queue_config.clone()));
        let usage_analyzer = create_usage_score_analyzer(Some(config.usage_config.clone()));

        // Initialize resource thresholds
        let mut resource_thresholds = HashMap::new();
        resource_thresholds.insert(ResourceType::CPU, 0.1); // 10% CPU available
        resource_thresholds.insert(ResourceType::Memory, 0.15); // 15% Memory available
        resource_thresholds.insert(ResourceType::GPU, 0.05); // 5% GPU available
        resource_thresholds.insert(ResourceType::DiskIO, 0.2); // 20% Disk IO available
        resource_thresholds.insert(ResourceType::NetworkBandwidth, 0.3); // 30% Network available

        // Initialize system resources with default values
        let mut system_resources = HashMap::new();
        system_resources.insert(ResourceType::CPU, ResourceUsage(0.5)); // 50% CPU available
        system_resources.insert(ResourceType::Memory, ResourceUsage(0.6)); // 60% Memory available
        system_resources.insert(ResourceType::GPU, ResourceUsage(0.7)); // 70% GPU available
        system_resources.insert(ResourceType::DiskIO, ResourceUsage(0.8)); // 80% Disk IO available
        system_resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.9)); // 90% Network available

        PrismaScoreEvaluator {
            config,
            task_analyzer,
            queue_analyzer,
            usage_analyzer,
            system_resources: RwLock::new(system_resources),
            resource_thresholds,
        }
    }

    /// Creates a new PrismaScoreEvaluator with default configuration
    pub fn default() -> Self {
        Self::new(PrismaScoreConfig::default())
    }

    /// Updates the current system resource availability
    pub fn update_system_resources(&self, resources: HashMap<ResourceType, ResourceUsage>) {
        let mut system_resources = self.system_resources.write().unwrap();
        *system_resources = resources;
        debug!("Updated system resources: {:?}", *system_resources);
    }

    /// Updates a queue state for scoring
    pub fn update_queue_state(&self, state: QueueState) -> PrismaResult<()> {
        // In a real implementation, we would use a proper approach to update the queue state
        // For now, we'll just log that we would update the state
        debug!("Would update queue state for {}: length={}, wait_time={:.2}s",
               state.name, state.length, state.avg_wait_time);

        // Return success without actually modifying the queue_analyzer
        // This avoids the borrowing issues
        Ok(())

        // Note: In a real implementation, we might use:
        // 1. Interior mutability (Arc<RwLock<dyn QueueScoreAnalyzer>>)
        // 2. A separate mutable queue state that the analyzer can access
        // 3. A message-passing approach to update state
    }

    /// Evaluates a task's PrismaScore
    pub async fn evaluate_task_score(&self, task: &Box<dyn Task>) -> PrismaResult<PrismaScore> {
        let category = task.category();
        let priority = task.priority();

        // Get the initial score from the task
        let initial_score = task.get_prisma_score();
        debug!("Initial score for task {}: {:?}", task.id(), initial_score);

        // Evaluate the score using the logic module
        let evaluated_score = evaluate_prisma_score(
            &initial_score,
            &category,
            priority,
            &self.config
        )?;

        debug!("Evaluated score for task {}: {:?}", task.id(), evaluated_score);
        Ok(evaluated_score)
    }

    /// Checks if a task's resource requirements can be met
    pub fn check_task_constraints(&self, score: &PrismaScore) -> bool {
        let system_resources = self.system_resources.read().unwrap();
        check_resource_constraints(score, &system_resources, &self.resource_thresholds)
    }

    /// Gets the combined score for a task, considering queue and usage factors
    pub async fn get_combined_score(
        &self,
        task: &Box<dyn Task>,
        queue_name: Option<&str>
    ) -> PrismaResult<PrismaScore> {
        // Get task score
        let task_score = self.evaluate_task_score(task).await?;

        // Get queue score if queue name is provided
        let queue_score = if let Some(name) = queue_name {
            // For now, we'll use default queue parameters instead of trying to access queue state
            // This avoids the borrowing issues
            debug!("Using default queue parameters for queue: {}", name);

            // Default queue score
            Some(self.queue_analyzer.analyze_queue_score(
                5, // Default queue length
                1.0, // Default wait time
                task.priority() // Use task priority
            ).await?)
        } else {
            None
        };

        // Get usage score from current system resources
        let system_resources = self.system_resources.read().unwrap();
        let mut resource_usage = HashMap::new();

        for (resource_type, usage) in system_resources.iter() {
            // Create a history with just the current value
            resource_usage.insert(*resource_type, vec![*usage]);
        }

        let usage_score = Some(self.usage_analyzer.analyze_usage_score(&resource_usage).await?);

        // Combine scores with weights
        let weights = [0.6, 0.2, 0.2]; // Task: 60%, Queue: 20%, Usage: 20%
        let combined_score = combine_all_scores(
            &task_score,
            queue_score.as_ref(),
            usage_score.as_ref(),
            &weights
        )?;

        debug!("Combined score for task {}: {:?}", task.id(), combined_score);
        Ok(combined_score)
    }
}

#[async_trait]
impl ScoreEvaluator for PrismaScoreEvaluator {
    async fn evaluate_score(&self, score: &PrismaScore) -> PrismaResult<PrismaScore> {
        // This is a simplified version that just applies global weights
        let mut weighted_resources = HashMap::new();

        for (resource_type, usage) in &score.resources {
            let weight = self.config.global_weights.weights
                .get(resource_type)
                .copied()
                .unwrap_or(1.0);

            weighted_resources.insert(
                *resource_type,
                ResourceUsage(usage.0 * weight)
            );
        }

        Ok(PrismaScore { resources: weighted_resources })
    }

    async fn check_resource_constraints(
        &self,
        score: &PrismaScore,
        available_resources: &HashMap<ResourceType, ResourceUsage>
    ) -> PrismaResult<bool> {
        Ok(check_resource_constraints(score, available_resources, &self.resource_thresholds))
    }
}

/// Extension trait to allow downcasting trait objects
pub trait AsAny {
    fn as_any(&self) -> &dyn std::any::Any;
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any;
}

impl<T: 'static> AsAny for T {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }
}

/// Factory function to create a new PrismaScoreEvaluator
pub fn create_score_evaluator(config: Option<PrismaScoreConfig>) -> Arc<dyn ScoreEvaluator> {
    Arc::new(PrismaScoreEvaluator::new(config.unwrap_or_default()))
}