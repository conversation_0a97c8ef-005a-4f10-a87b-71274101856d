// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/task_score.rs
// =================================================================================================
// Purpose: Implements task-specific scoring logic for evaluating resource requirements.
// This file contains functions and structures to analyze task characteristics and determine
// appropriate resource requirements, helping the decision maker select the optimal execution
// strategy for different types of tasks.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports this module
//   - prisma_scores/logic.rs: Uses task scoring in overall evaluation
//   - prisma_scores/types.rs: Uses types defined for scoring
//   - prisma_scores/traits.rs: Implements traits defined for scoring
//   - prisma_scores/generics.rs: Uses generic utilities
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore, TaskCategory, TaskPriority types
//   - prisma_engine/tcl: Analyzes task information from TCL module
//   - prisma_engine/traits.rs: Uses Task trait for accessing task properties
//
// Platform Considerations:
// - This module is platform-independent as it analyzes abstract task properties
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::debug;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, ResourceType, ResourceUsage, TaskCategory, TaskPriority
};
use crate::prisma::prisma_engine::traits::Task;
use super::traits::TaskScoreAnalyzer;
use super::types::TaskScoreConfig;
use super::generics::{normalize_score, ScoreCache, MovingAverage};

/// Implementation of the TaskScoreAnalyzer trait for analyzing task resource requirements
pub struct TaskScoreAnalyzerImpl {
    /// Configuration for task score analysis
    config: TaskScoreConfig,
    /// Cache for task scores to avoid redundant computation
    score_cache: ScoreCache<PrismaScore>,
    /// Historical execution times for different task categories
    execution_history: HashMap<TaskCategory, MovingAverage>,
}

impl TaskScoreAnalyzerImpl {
    /// Creates a new TaskScoreAnalyzerImpl with the specified configuration
    pub fn new(config: TaskScoreConfig) -> Self {
        let mut execution_history = HashMap::new();

        // Initialize moving averages for common task categories
        execution_history.insert(TaskCategory::LLMInference, MovingAverage::new(10));
        execution_history.insert(TaskCategory::EmbeddingGeneration, MovingAverage::new(10));
        execution_history.insert(TaskCategory::DatabaseQuery, MovingAverage::new(10));

        TaskScoreAnalyzerImpl {
            config,
            score_cache: ScoreCache::new(60), // 60-second TTL
            execution_history,
        }
    }

    /// Creates a new TaskScoreAnalyzerImpl with default configuration
    pub fn default() -> Self {
        Self::new(TaskScoreConfig::default())
    }

    /// Records the execution time for a task category
    pub fn record_execution_time(&mut self, category: &TaskCategory, time_seconds: f64) {
        if let Some(history) = self.execution_history.get_mut(category) {
            debug!("Recording execution time for {:?}: {:.2}s", category, time_seconds);
            history.add(time_seconds);
        } else {
            // Create a new history entry if this is a new category
            let mut new_history = MovingAverage::new(10);
            new_history.add(time_seconds);
            self.execution_history.insert(category.clone(), new_history);
        }
    }

    /// Gets the default resource requirements for a task category
    fn get_default_requirements(&self, category: &TaskCategory) -> HashMap<ResourceType, ResourceUsage> {
        self.config.default_requirements
            .get(category)
            .cloned()
            .unwrap_or_else(|| {
                // If no specific requirements for this category, use a generic default
                let mut resources = HashMap::new();
                resources.insert(ResourceType::CPU, ResourceUsage(0.3));
                resources.insert(ResourceType::Memory, ResourceUsage(0.3));
                resources.insert(ResourceType::GPU, ResourceUsage(0.0));
                resources.insert(ResourceType::DiskIO, ResourceUsage(0.2));
                resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.1));
                resources
            })
    }

    /// Analyzes a task to determine its resource requirements
    pub fn analyze_task(&self, task: &Box<dyn Task>) -> PrismaResult<PrismaScore> {
        let category = task.category();
        let priority = task.priority();

        // For now, we'll skip the caching logic to avoid mutable borrowing issues
        // In a real implementation, we would use interior mutability (RwLock, Mutex, etc.)
        // to allow caching while maintaining the immutable self reference

        // Get the score from the task itself
        let task_score = task.get_prisma_score();

        debug!("Analyzed task {} with category {:?} and priority {:?}",
               task.id(), category, priority);

        Ok(task_score)
    }
}

#[async_trait]
impl TaskScoreAnalyzer for TaskScoreAnalyzerImpl {
    async fn analyze_task_score(
        &self,
        category: &TaskCategory,
        priority: TaskPriority,
        additional_params: Option<HashMap<String, String>>
    ) -> PrismaResult<PrismaScore> {
        debug!("Analyzing task score for {:?} with priority {:?}", category, priority);

        // Get default resource requirements for this category
        let mut resources = self.get_default_requirements(category);

        // Apply priority multiplier
        let multiplier = self.config.priority_multipliers
            .get(&priority)
            .copied()
            .unwrap_or(1.0);

        for usage in resources.values_mut() {
            usage.0 *= multiplier;
        }

        // Apply additional parameters if provided
        if let Some(params) = additional_params {
            // Example: Adjust for input size
            if let Some(input_size_str) = params.get("input_size") {
                if let Ok(input_size) = input_size_str.parse::<usize>() {
                    // Adjust memory and CPU based on input size
                    if input_size > 10000 {
                        if let Some(mem) = resources.get_mut(&ResourceType::Memory) {
                            mem.0 *= 1.5;
                        }
                        if let Some(cpu) = resources.get_mut(&ResourceType::CPU) {
                            cpu.0 *= 1.2;
                        }
                    }
                }
            }

            // Example: Adjust for model size
            if let Some(model_size) = params.get("model_size") {
                match model_size.as_str() {
                    "large" => {
                        if let Some(gpu) = resources.get_mut(&ResourceType::GPU) {
                            gpu.0 *= 1.5;
                        }
                        if let Some(mem) = resources.get_mut(&ResourceType::Memory) {
                            mem.0 *= 1.3;
                        }
                    },
                    "medium" => {
                        // Default multipliers are fine
                    },
                    "small" => {
                        if let Some(gpu) = resources.get_mut(&ResourceType::GPU) {
                            gpu.0 *= 0.7;
                        }
                        if let Some(mem) = resources.get_mut(&ResourceType::Memory) {
                            mem.0 *= 0.8;
                        }
                    },
                    _ => {}
                }
            }
        }

        // Ensure all values are normalized
        let mut score = PrismaScore { resources };
        normalize_score(&mut score);

        debug!("Analyzed task score: {:?}", score);
        Ok(score)
    }

    async fn adjust_for_history(
        &self,
        score: &PrismaScore,
        task_history: &[f64]
    ) -> PrismaResult<PrismaScore> {
        if task_history.is_empty() {
            return Ok(score.clone());
        }

        // Calculate average execution time
        let avg_time: f64 = task_history.iter().sum::<f64>() / task_history.len() as f64;
        debug!("Adjusting score based on history. Average execution time: {:.2}s", avg_time);

        // Adjust resources based on historical execution time
        let mut adjusted_resources = score.resources.clone();

        // Example adjustment logic:
        // - If avg_time > 5s, increase CPU and Memory by 20%
        // - If avg_time > 10s, increase by 50%
        let multiplier = if avg_time > 10.0 {
            1.5
        } else if avg_time > 5.0 {
            1.2
        } else if avg_time < 0.5 {
            0.9 // Slightly reduce for very fast tasks
        } else {
            1.0
        };

        for (_, usage) in adjusted_resources.iter_mut() {
            usage.0 *= multiplier;
        }

        // Ensure all values are normalized
        let mut adjusted_score = PrismaScore { resources: adjusted_resources };
        normalize_score(&mut adjusted_score);

        Ok(adjusted_score)
    }
}

/// Factory function to create a new TaskScoreAnalyzer
pub fn create_task_score_analyzer(config: Option<TaskScoreConfig>) -> Arc<dyn TaskScoreAnalyzer> {
    Arc::new(TaskScoreAnalyzerImpl::new(config.unwrap_or_default()))
}