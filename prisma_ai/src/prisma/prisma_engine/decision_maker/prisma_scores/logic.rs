// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/logic.rs
// =================================================================================================
// Purpose: Implements logic for evaluating and adjusting PrismaScore objects, which represent
// the resource requirements of tasks. This file contains functions to analyze, modify, and
// interpret task resource requirements to help the decision maker determine the optimal
// execution strategy.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports this module
//   - decision_maker/decision_maker.rs: Uses these functions for score evaluation
//   - decision_maker/rules.rs: Uses these functions for rule-based decisions
//   - prisma_scores/task_score.rs: Provides task-specific scoring logic
//   - prisma_scores/queue_score.rs: Provides queue-specific scoring logic
//   - prisma_scores/usage_score.rs: Provides resource usage scoring logic
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore, ResourceType, and ResourceUsage types
//   - prisma_engine/tcl: Receives task information for scoring
//   - prisma_engine/executor: Provides execution context for scoring decisions
//   - std::collections::HashMap: For resource mapping
//
// Platform Considerations:
// - This module is platform-independent as it deals with abstract resource scoring
// =================================================================================================

use tracing::{debug, warn};
use std::collections::HashMap;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, ResourceType, ResourceUsage, TaskCategory, TaskPriority
};
use super::generics::{normalize_score, apply_weights, combine_scores};
use super::types::{ScoreWeights, PrismaScoreConfig};

/// Evaluates and potentially adjusts a task's PrismaScore based on various factors.
/// This is the main entry point for PrismaScore evaluation.
pub fn evaluate_prisma_score(
    initial_score: &PrismaScore,
    task_category: &TaskCategory,
    task_priority: TaskPriority,
    config: &PrismaScoreConfig
) -> PrismaResult<PrismaScore> {
    debug!("Evaluating PrismaScore for {:?} task with priority {:?}", task_category, task_priority);
    debug!("Initial score: {:?}", initial_score);

    // 1. Apply task-specific adjustments
    let task_adjusted_score = adjust_for_task_category(
        initial_score,
        task_category,
        &config.task_config.category_weights
    )?;

    // 2. Apply priority-based adjustments
    let priority_adjusted_score = adjust_for_priority(
        &task_adjusted_score,
        task_priority,
        &config.task_config.priority_multipliers
    )?;

    // 3. Apply global weights
    let weighted_score = apply_weights(&priority_adjusted_score, &config.global_weights);

    // 4. Ensure all values are normalized (0.0 to 1.0)
    let mut final_score = weighted_score;
    normalize_score(&mut final_score);

    debug!("Final evaluated score: {:?}", final_score);
    Ok(final_score)
}

/// Adjusts a PrismaScore based on the task category
fn adjust_for_task_category(
    score: &PrismaScore,
    category: &TaskCategory,
    category_weights: &HashMap<TaskCategory, ScoreWeights>
) -> PrismaResult<PrismaScore> {
    // Get category-specific weights if available, otherwise use default weights
    // Create a longer-lived default value to avoid temporary value issues
    let default_weights = ScoreWeights::default();
    let weights = category_weights.get(category).unwrap_or(&default_weights);

    // Apply category-specific weights to the score
    let weighted_score = apply_weights(score, weights);

    Ok(weighted_score)
}

/// Adjusts a PrismaScore based on the task priority
fn adjust_for_priority(
    score: &PrismaScore,
    priority: TaskPriority,
    priority_multipliers: &HashMap<TaskPriority, f64>
) -> PrismaResult<PrismaScore> {
    // Get the priority multiplier, defaulting to 1.0 if not found
    let multiplier = priority_multipliers.get(&priority).copied().unwrap_or(1.0);

    // Apply the multiplier to all resource usages
    let mut adjusted_resources = HashMap::new();
    for (resource_type, usage) in &score.resources {
        let adjusted_usage = ResourceUsage(usage.0 * multiplier);
        adjusted_resources.insert(*resource_type, adjusted_usage);
    }

    Ok(PrismaScore { resources: adjusted_resources })
}

/// Combines task, queue, and usage scores into a final PrismaScore
pub fn combine_all_scores(
    task_score: &PrismaScore,
    queue_score: Option<&PrismaScore>,
    usage_score: Option<&PrismaScore>,
    weights: &[f64]
) -> PrismaResult<PrismaScore> {
    let mut scores = vec![task_score.clone()];
    let mut actual_weights = vec![weights[0]];

    // Add queue score if available
    if let Some(queue) = queue_score {
        scores.push(queue.clone());
        actual_weights.push(weights.get(1).copied().unwrap_or(1.0));
    }

    // Add usage score if available
    if let Some(usage) = usage_score {
        scores.push(usage.clone());
        actual_weights.push(weights.get(2).copied().unwrap_or(1.0));
    }

    // Combine all scores using weighted average
    combine_scores(&scores, &actual_weights)
}

/// Checks if a PrismaScore exceeds available resources
pub fn check_resource_constraints(
    score: &PrismaScore,
    available_resources: &HashMap<ResourceType, ResourceUsage>,
    thresholds: &HashMap<ResourceType, f64>
) -> bool {
    for (resource_type, required) in &score.resources {
        if required.0 > 0.0 { // Only check if the task requires this resource
            let available = available_resources
                .get(resource_type)
                .map_or(0.0, |usage| usage.0);

            // Get the threshold for this resource type, or use a default
            let threshold = thresholds
                .get(resource_type)
                .copied()
                .unwrap_or(0.1); // Default 10% threshold

            // Check if available resources are below threshold
            if available < threshold {
                warn!(
                    "Resource constraint hit for {:?}: Required {:.1}%, Available {:.1}% (Threshold {:.1}%)",
                    resource_type, required.0 * 100.0, available * 100.0, threshold * 100.0
                );
                return false;
            }
        }
    }

    true // All constraints met
}

/// Calculates the resource efficiency of a task based on its PrismaScore
/// Returns a value between 0.0 and 1.0, where higher values indicate better efficiency
pub fn calculate_efficiency(
    score: &PrismaScore,
    available_resources: &HashMap<ResourceType, ResourceUsage>
) -> f64 {
    let mut total_efficiency = 0.0;
    let mut resource_count = 0;

    for (resource_type, required) in &score.resources {
        if required.0 > 0.0 { // Only consider resources that are actually required
            let available = available_resources
                .get(resource_type)
                .map_or(0.0, |usage| usage.0);

            // Calculate efficiency as the ratio of available to required
            // Higher available means better efficiency
            let efficiency = if required.0 > 0.0 {
                (available / required.0).min(1.0)
            } else {
                1.0 // If not required, efficiency is perfect
            };

            total_efficiency += efficiency;
            resource_count += 1;
        }
    }

    // Return average efficiency across all resources
    if resource_count > 0 {
        total_efficiency / resource_count as f64
    } else {
        1.0 // If no resources are required, efficiency is perfect
    }
}

/// Predicts the impact of executing a task on system resources
pub fn predict_resource_impact(
    score: &PrismaScore,
    current_usage: &HashMap<ResourceType, ResourceUsage>
) -> HashMap<ResourceType, ResourceUsage> {
    let mut predicted_usage = current_usage.clone();

    for (resource_type, required) in &score.resources {
        if let Some(current) = predicted_usage.get_mut(resource_type) {
            // Predict new usage by adding required to current
            // Ensure we don't exceed 100%
            current.0 = (current.0 + required.0).min(1.0);
        } else {
            // If resource type not in current usage, just use required
            predicted_usage.insert(*resource_type, *required);
        }
    }

    predicted_usage
}
