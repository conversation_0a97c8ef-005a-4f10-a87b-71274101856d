// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/types.rs
// =================================================================================================
// Purpose: Defines types specific to PrismaScore evaluation and manipulation. This file contains
// data structures that represent different aspects of task resource requirements and scoring
// mechanisms used by the decision maker to determine execution strategies.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports these types
//   - prisma_scores/logic.rs: Uses these types for score evaluation
//   - prisma_scores/task_score.rs: Uses these types for task scoring
//   - prisma_scores/queue_score.rs: Uses these types for queue scoring
//   - prisma_scores/usage_score.rs: Uses these types for usage scoring
//   - prisma_scores/traits.rs: Defines traits that operate on these types
//
// - External Dependencies:
//   - prisma_engine/types.rs: Extends the core PrismaScore, ResourceType, and ResourceUsage types
//   - serde: For serialization/deserialization of score types
//   - std::collections: For data structures used in type definitions
//
// Platform Considerations:
// - This module is platform-independent as it defines abstract types
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage, TaskCategory, TaskPriority};

/// Weights to apply to different resources when calculating scores
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScoreWeights {
    /// Weights for each resource type
    pub weights: HashMap<ResourceType, f64>,
}

impl Default for ScoreWeights {
    fn default() -> Self {
        let mut weights = HashMap::new();
        weights.insert(ResourceType::CPU, 1.0);
        weights.insert(ResourceType::Memory, 1.0);
        weights.insert(ResourceType::GPU, 1.5);
        weights.insert(ResourceType::DiskIO, 0.8);
        weights.insert(ResourceType::NetworkBandwidth, 0.7);

        ScoreWeights { weights }
    }
}

/// Represents the priority of different resources for a specific task type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourcePriority {
    /// Priority values for each resource type (higher = more important)
    pub priorities: HashMap<ResourceType, u8>,
}

impl Default for ResourcePriority {
    fn default() -> Self {
        let mut priorities = HashMap::new();
        priorities.insert(ResourceType::CPU, 3);
        priorities.insert(ResourceType::Memory, 4);
        priorities.insert(ResourceType::GPU, 5);
        priorities.insert(ResourceType::DiskIO, 2);
        priorities.insert(ResourceType::NetworkBandwidth, 1);

        ResourcePriority { priorities }
    }
}

/// Configuration for task score analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskScoreConfig {
    /// Default resource requirements by task category
    pub default_requirements: HashMap<TaskCategory, HashMap<ResourceType, ResourceUsage>>,
    /// Priority multipliers (how much to scale resources based on priority)
    pub priority_multipliers: HashMap<TaskPriority, f64>,
    /// Resource weights for different task categories
    pub category_weights: HashMap<TaskCategory, ScoreWeights>,
}

impl Default for TaskScoreConfig {
    fn default() -> Self {
        let mut default_requirements = HashMap::new();
        let mut priority_multipliers = HashMap::new();
        let mut category_weights = HashMap::new();

        // Set up default resource requirements for each task category
        let mut llm_resources = HashMap::new();
        llm_resources.insert(ResourceType::CPU, ResourceUsage(0.3));
        llm_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
        llm_resources.insert(ResourceType::GPU, ResourceUsage(0.8));
        llm_resources.insert(ResourceType::DiskIO, ResourceUsage(0.2));
        llm_resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.1));
        default_requirements.insert(TaskCategory::LLMInference, llm_resources);

        let mut embedding_resources = HashMap::new();
        embedding_resources.insert(ResourceType::CPU, ResourceUsage(0.4));
        embedding_resources.insert(ResourceType::Memory, ResourceUsage(0.3));
        embedding_resources.insert(ResourceType::GPU, ResourceUsage(0.6));
        embedding_resources.insert(ResourceType::DiskIO, ResourceUsage(0.1));
        embedding_resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.05));
        default_requirements.insert(TaskCategory::EmbeddingGeneration, embedding_resources);

        let mut db_resources = HashMap::new();
        db_resources.insert(ResourceType::CPU, ResourceUsage(0.2));
        db_resources.insert(ResourceType::Memory, ResourceUsage(0.3));
        db_resources.insert(ResourceType::GPU, ResourceUsage(0.0));
        db_resources.insert(ResourceType::DiskIO, ResourceUsage(0.4));
        db_resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.3));
        default_requirements.insert(TaskCategory::DatabaseQuery, db_resources);

        // Set up priority multipliers
        priority_multipliers.insert(TaskPriority::Low, 0.8);
        priority_multipliers.insert(TaskPriority::Normal, 1.0);
        priority_multipliers.insert(TaskPriority::High, 1.2);
        priority_multipliers.insert(TaskPriority::Realtime, 1.5);

        // Set up category weights (using default weights for now)
        category_weights.insert(TaskCategory::LLMInference, ScoreWeights::default());
        category_weights.insert(TaskCategory::EmbeddingGeneration, ScoreWeights::default());
        category_weights.insert(TaskCategory::DatabaseQuery, ScoreWeights::default());

        TaskScoreConfig {
            default_requirements,
            priority_multipliers,
            category_weights,
        }
    }
}

/// Configuration for queue score analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueueScoreConfig {
    /// Length thresholds for different queue states
    pub length_thresholds: HashMap<String, usize>,
    /// Wait time thresholds in seconds
    pub wait_time_thresholds: HashMap<String, f64>,
    /// Resource adjustments based on queue state
    pub resource_adjustments: HashMap<String, HashMap<ResourceType, f64>>,
}

impl Default for QueueScoreConfig {
    fn default() -> Self {
        let mut length_thresholds = HashMap::new();
        let mut wait_time_thresholds = HashMap::new();
        let mut resource_adjustments = HashMap::new();

        // Queue length thresholds
        length_thresholds.insert("short".to_string(), 5);
        length_thresholds.insert("medium".to_string(), 20);
        length_thresholds.insert("long".to_string(), 50);

        // Wait time thresholds (in seconds)
        wait_time_thresholds.insert("short".to_string(), 1.0);
        wait_time_thresholds.insert("medium".to_string(), 5.0);
        wait_time_thresholds.insert("long".to_string(), 15.0);

        // Resource adjustments for different queue states
        let mut short_adjustments = HashMap::new();
        short_adjustments.insert(ResourceType::CPU, 1.0);
        short_adjustments.insert(ResourceType::Memory, 1.0);
        resource_adjustments.insert("short".to_string(), short_adjustments);

        let mut medium_adjustments = HashMap::new();
        medium_adjustments.insert(ResourceType::CPU, 1.2);
        medium_adjustments.insert(ResourceType::Memory, 1.1);
        resource_adjustments.insert("medium".to_string(), medium_adjustments);

        let mut long_adjustments = HashMap::new();
        long_adjustments.insert(ResourceType::CPU, 1.5);
        long_adjustments.insert(ResourceType::Memory, 1.3);
        resource_adjustments.insert("long".to_string(), long_adjustments);

        QueueScoreConfig {
            length_thresholds,
            wait_time_thresholds,
            resource_adjustments,
        }
    }
}

/// Configuration for usage score analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UsageScoreConfig {
    /// Thresholds for different usage levels
    pub usage_thresholds: HashMap<ResourceType, HashMap<String, f64>>,
    /// How much historical data to consider (in data points)
    pub history_window: usize,
    /// Weights for historical vs current usage
    pub history_weight: f64,
}

impl Default for UsageScoreConfig {
    fn default() -> Self {
        let mut usage_thresholds = HashMap::new();

        // CPU usage thresholds
        let mut cpu_thresholds = HashMap::new();
        cpu_thresholds.insert("low".to_string(), 0.3);
        cpu_thresholds.insert("medium".to_string(), 0.6);
        cpu_thresholds.insert("high".to_string(), 0.8);
        usage_thresholds.insert(ResourceType::CPU, cpu_thresholds);

        // Memory usage thresholds
        let mut mem_thresholds = HashMap::new();
        mem_thresholds.insert("low".to_string(), 0.4);
        mem_thresholds.insert("medium".to_string(), 0.7);
        mem_thresholds.insert("high".to_string(), 0.85);
        usage_thresholds.insert(ResourceType::Memory, mem_thresholds);

        // GPU usage thresholds
        let mut gpu_thresholds = HashMap::new();
        gpu_thresholds.insert("low".to_string(), 0.3);
        gpu_thresholds.insert("medium".to_string(), 0.5);
        gpu_thresholds.insert("high".to_string(), 0.75);
        usage_thresholds.insert(ResourceType::GPU, gpu_thresholds);

        UsageScoreConfig {
            usage_thresholds,
            history_window: 10,
            history_weight: 0.7,
        }
    }
}

/// Combined configuration for all PrismaScore analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrismaScoreConfig {
    /// Task-specific scoring configuration
    pub task_config: TaskScoreConfig,
    /// Queue-specific scoring configuration
    pub queue_config: QueueScoreConfig,
    /// Usage-specific scoring configuration
    pub usage_config: UsageScoreConfig,
    /// Global score weights
    pub global_weights: ScoreWeights,
}

impl Default for PrismaScoreConfig {
    fn default() -> Self {
        PrismaScoreConfig {
            task_config: TaskScoreConfig::default(),
            queue_config: QueueScoreConfig::default(),
            usage_config: UsageScoreConfig::default(),
            global_weights: ScoreWeights::default(),
        }
    }
}