// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/prisma_scores/traits.rs
// =================================================================================================
// Purpose: Defines traits for PrismaScore evaluation and manipulation. This file contains
// trait definitions that establish interfaces for different scoring components, allowing for
// modular and extensible scoring mechanisms within the decision maker.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores/mod.rs: Exports these traits
//   - prisma_scores/logic.rs: May implement these traits
//   - prisma_scores/task_score.rs: Likely implements task-specific scoring traits
//   - prisma_scores/queue_score.rs: Likely implements queue-specific scoring traits
//   - prisma_scores/usage_score.rs: Likely implements usage-specific scoring traits
//   - prisma_scores/types.rs: Defines types used by these traits
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses PrismaScore, ResourceType, and ResourceUsage types
//   - prisma_engine/traits.rs: May extend or complement engine-level traits
//   - async_trait: For async trait methods if needed
//
// Platform Considerations:
// - This module is platform-independent as it defines abstract traits
// =================================================================================================

use async_trait::async_trait;
use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, ResourceType, ResourceUsage, TaskCategory, TaskPriority
};
use std::collections::HashMap;

/// Trait for components that can evaluate PrismaScores
#[async_trait]
pub trait ScoreEvaluator: Send + Sync {
    /// Evaluate a PrismaScore and potentially adjust it based on various factors
    async fn evaluate_score(&self, score: &PrismaScore) -> PrismaResult<PrismaScore>;

    /// Check if a PrismaScore exceeds available resources
    async fn check_resource_constraints(
        &self,
        score: &PrismaScore,
        available_resources: &HashMap<ResourceType, ResourceUsage>
    ) -> PrismaResult<bool>;
}

/// Trait for analyzing task-specific resource requirements
#[async_trait]
pub trait TaskScoreAnalyzer: Send + Sync {
    /// Analyze a task's characteristics to determine appropriate resource requirements
    async fn analyze_task_score(
        &self,
        category: &TaskCategory,
        priority: TaskPriority,
        additional_params: Option<HashMap<String, String>>
    ) -> PrismaResult<PrismaScore>;

    /// Adjust a task's score based on historical execution data
    async fn adjust_for_history(
        &self,
        score: &PrismaScore,
        task_history: &[f64]
    ) -> PrismaResult<PrismaScore>;
}

/// Trait for analyzing queue-specific resource requirements
#[async_trait]
pub trait QueueScoreAnalyzer: Send + Sync {
    /// Analyze queue state to determine appropriate resource allocations
    async fn analyze_queue_score(
        &self,
        queue_length: usize,
        avg_wait_time: f64,
        priority: TaskPriority
    ) -> PrismaResult<PrismaScore>;

    /// Adjust resource allocation based on queue performance metrics
    async fn adjust_for_throughput(
        &self,
        score: &PrismaScore,
        throughput_history: &[f64]
    ) -> PrismaResult<PrismaScore>;
}

/// Trait for analyzing resource usage patterns
#[async_trait]
pub trait UsageScoreAnalyzer: Send + Sync {
    /// Analyze resource usage patterns to optimize resource allocation
    async fn analyze_usage_score(
        &self,
        resource_usage: &HashMap<ResourceType, Vec<ResourceUsage>>
    ) -> PrismaResult<PrismaScore>;

    /// Predict future resource usage based on historical patterns
    async fn predict_future_usage(
        &self,
        current_usage: &HashMap<ResourceType, ResourceUsage>,
        historical_usage: &HashMap<ResourceType, Vec<ResourceUsage>>
    ) -> PrismaResult<HashMap<ResourceType, ResourceUsage>>;
}