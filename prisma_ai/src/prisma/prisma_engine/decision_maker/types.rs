// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/types.rs
// =================================================================================================
// Purpose: Defines types specific to the decision_maker module. This file contains data structures
// that represent configuration options, decision results, rule types, and other components used
// by the decision maker to determine execution strategies.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports these types
//   - decision_maker/decision_maker.rs: Uses these types for decision making
//   - decision_maker/rules.rs: Uses these types for rule definitions
//   - decision_maker/state.rs: May use these types for state tracking
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses core engine types like ExecutionStrategyType
//   - serde: For serialization/deserialization of configuration
//
// Platform Considerations:
// - This module is platform-independent as it defines abstract types
// =================================================================================================

use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use serde::{Deserialize, Serialize};
use crate::prisma::prisma_engine::types::{
    ExecutionStrategyType, TaskPriority, TaskCategory, ResourceType, ResourceUsage
};

/// Configuration specific to the decision maker component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionMakerConfig {
    /// Threshold for high CPU usage (percentage)
    pub high_cpu_threshold: f64,
    /// Threshold for high memory usage (percentage)
    pub high_memory_threshold: f64,
    /// Default execution strategy
    pub default_strategy: ExecutionStrategyType,
    /// Rule evaluation timeout in milliseconds
    pub rule_evaluation_timeout_ms: u64,
    /// Whether to enable priority adjustment
    pub enable_priority_adjustment: bool,
    /// Whether to enable rule-based decision making
    pub enable_rule_based_decisions: bool,
    /// Whether to enable state tracking
    pub enable_state_tracking: bool,
    /// Default strategies for each task category
    pub category_strategies: HashMap<TaskCategory, ExecutionStrategyType>,
    /// Resource thresholds for constraint checking
    pub resource_thresholds: HashMap<ResourceType, f64>,
}

impl Default for DecisionMakerConfig {
    fn default() -> Self {
        let mut category_strategies = HashMap::new();
        category_strategies.insert(TaskCategory::LLMInference, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::EmbeddingGeneration, ExecutionStrategyType::Rayon);
        category_strategies.insert(TaskCategory::DatabaseQuery, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::FileProcessing, ExecutionStrategyType::Rayon);
        category_strategies.insert(TaskCategory::NetworkRequest, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::UICallback, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::Internal, ExecutionStrategyType::Direct);

        let mut resource_thresholds = HashMap::new();
        resource_thresholds.insert(ResourceType::CPU, 0.8);
        resource_thresholds.insert(ResourceType::Memory, 0.85);
        resource_thresholds.insert(ResourceType::GPU, 0.9);
        resource_thresholds.insert(ResourceType::NetworkBandwidth, 0.7);
        resource_thresholds.insert(ResourceType::DiskIO, 0.75);

        DecisionMakerConfig {
            high_cpu_threshold: 80.0,
            high_memory_threshold: 85.0,
            default_strategy: ExecutionStrategyType::Tokio,
            rule_evaluation_timeout_ms: 100,
            enable_priority_adjustment: true,
            enable_rule_based_decisions: true,
            enable_state_tracking: true,
            category_strategies,
            resource_thresholds,
        }
    }
}

/// Types of rules used in decision making
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RuleType {
    /// Rules related to task priority
    Priority,
    /// Rules related to resource constraints
    Resource,
    /// Rules related to task category
    Category,
    /// Rules related to system state
    System,
    /// Rules related to model state
    Model,
    /// Custom rules
    Custom,
}

/// Result of evaluating a rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleResult {
    /// Name of the rule that was evaluated
    pub rule_name: String,
    /// Type of the rule
    pub rule_type: RuleType,
    /// Whether the rule was triggered
    pub triggered: bool,
    /// Recommended execution strategy if the rule was triggered
    pub recommended_strategy: Option<ExecutionStrategyType>,
    /// Recommended priority adjustment if the rule was triggered
    pub priority_adjustment: Option<TaskPriority>,
    /// Reason for the decision
    pub reason: DecisionReason,
    /// Timestamp when the rule was evaluated
    pub timestamp: SystemTime,
}

/// Reason for a decision
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionReason {
    /// Short description of the reason
    pub description: String,
    /// Detailed explanation of the reason
    pub details: Option<String>,
    /// Resource that influenced the decision, if any
    pub resource: Option<ResourceType>,
    /// Resource usage that influenced the decision, if any
    pub resource_usage: Option<ResourceUsage>,
}

/// State of an LLM model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelState {
    /// ID of the model
    pub model_id: String,
    /// Whether the model is loaded
    pub loaded: bool,
    /// Whether the model is in use
    pub in_use: bool,
    /// Memory usage of the model in bytes
    pub memory_usage: u64,
    /// Time when the model was last used
    pub last_used: SystemTime,
    /// Time it takes to load the model in seconds
    pub load_time: f64,
    /// Number of tokens processed by the model
    pub tokens_processed: u64,
    /// Number of requests processed by the model
    pub requests_processed: u64,
    /// Average tokens per second
    pub avg_tokens_per_second: f64,
    /// KV cache usage as a percentage
    pub kv_cache_usage: f64,
}

/// Configuration for rule-based decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleConfig {
    /// Name of the rule
    pub name: String,
    /// Description of the rule
    pub description: String,
    /// Type of the rule
    pub rule_type: RuleType,
    /// Whether the rule is enabled
    pub enabled: bool,
    /// Priority of the rule (higher priority rules are evaluated first)
    pub priority: u32,
    /// Parameters for the rule
    pub parameters: HashMap<String, String>,
}

/// Configuration for state tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateTrackerConfig {
    /// How often to update model states in milliseconds
    pub update_interval_ms: u64,
    /// How long to keep a model loaded after it was last used
    pub model_ttl: Duration,
    /// Maximum number of models to keep loaded
    pub max_loaded_models: usize,
    /// Whether to preload models
    pub preload_models: bool,
    /// Models to preload
    pub preload_model_ids: Vec<String>,
}

impl Default for StateTrackerConfig {
    fn default() -> Self {
        StateTrackerConfig {
            update_interval_ms: 5000,
            model_ttl: Duration::from_secs(300), // 5 minutes
            max_loaded_models: 3,
            preload_models: false,
            preload_model_ids: Vec::new(),
        }
    }
}

/// Configuration for priority adjustment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriorityAdjusterConfig {
    /// Whether to enable priority adjustment
    pub enabled: bool,
    /// Threshold for high system load
    pub high_load_threshold: f64,
    /// Threshold for low system load
    pub low_load_threshold: f64,
    /// Whether to allow upgrading priorities
    pub allow_upgrade: bool,
    /// Whether to allow downgrading priorities
    pub allow_downgrade: bool,
    /// Maximum priority level that can be assigned automatically
    pub max_auto_priority: TaskPriority,
}

impl Default for PriorityAdjusterConfig {
    fn default() -> Self {
        PriorityAdjusterConfig {
            enabled: true,
            high_load_threshold: 0.8,
            low_load_threshold: 0.3,
            allow_upgrade: true,
            allow_downgrade: true,
            max_auto_priority: TaskPriority::High,
        }
    }
}

/// Analysis of task characteristics for dynamic decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskAnalysis {
    /// Whether the task is CPU-intensive
    pub is_cpu_intensive: bool,
    /// Whether the task is I/O-intensive
    pub is_io_intensive: bool,
    /// Whether the task is memory-intensive
    pub is_memory_intensive: bool,
    /// Estimated execution time in milliseconds
    pub estimated_duration_ms: Option<u64>,
    /// Resource intensity score (0.0 to 1.0)
    pub resource_intensity: f64,
    /// Parallelization potential (0.0 to 1.0)
    pub parallelization_potential: f64,
    /// Task complexity level
    pub complexity_level: TaskComplexity,
}

/// Analysis of current system state for dynamic decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAnalysis {
    /// Current CPU utilization (0.0 to 1.0)
    pub cpu_utilization: f64,
    /// Current memory utilization (0.0 to 1.0)
    pub memory_utilization: f64,
    /// Current I/O utilization (0.0 to 1.0)
    pub io_utilization: f64,
    /// Whether the system is under high load
    pub is_high_load: bool,
    /// Whether the system is in critical state
    pub is_critical: bool,
    /// Available CPU cores
    pub available_cpu_cores: usize,
    /// Available memory in MB
    pub available_memory_mb: u64,
    /// System health score (0.0 to 1.0)
    pub health_score: f64,
}

/// Task complexity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskComplexity {
    /// Simple tasks that can be executed directly
    Simple,
    /// Moderate tasks that benefit from async execution
    Moderate,
    /// Complex tasks that benefit from parallel execution
    Complex,
    /// Very complex tasks requiring specialized handling
    VeryComplex,
}
