// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/learning/learning_engine.rs
// =================================================================================================
// Purpose: Implementation of the learning engine for adaptive decision making.
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::time::SystemTime;
use tracing::{debug, info, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{TaskCategory, ExecutionStrategyType};

use super::traits::AdaptationEngine;
use super::types::{
    TaskPerformanceMetrics, StrategyPerformanceStats, AdaptationRecommendation,
    PerformanceTrend, LearningConfig
};

/// Implementation of the adaptation engine
pub struct LearningEngineImpl {
    /// Configuration for learning behavior
    config: LearningConfig,
}

impl LearningEngineImpl {
    /// Create a new learning engine
    pub fn new(config: LearningConfig) -> Self {
        Self { config }
    }

    /// Compare two strategies and determine which is better
    fn compare_strategies(
        &self,
        stats1: &StrategyPerformanceStats,
        stats2: &StrategyPerformanceStats,
    ) -> f64 {
        // Weighted scoring based on multiple factors
        let success_weight = 0.4;
        let speed_weight = 0.3;
        let trend_weight = 0.3;

        // Success rate comparison (higher is better)
        let success_score = stats1.success_rate - stats2.success_rate;

        // Speed comparison (lower execution time is better)
        let speed_score = (stats2.avg_execution_duration.as_millis() as f64 
                          - stats1.avg_execution_duration.as_millis() as f64) / 1000.0;

        // Trend comparison
        let trend_score = self.trend_to_score(stats1.trend) - self.trend_to_score(stats2.trend);

        // Weighted total score
        success_weight * success_score + speed_weight * speed_score + trend_weight * trend_score
    }

    /// Convert performance trend to numeric score
    fn trend_to_score(&self, trend: PerformanceTrend) -> f64 {
        match trend {
            PerformanceTrend::Improving => 1.0,
            PerformanceTrend::Stable => 0.0,
            PerformanceTrend::Degrading => -1.0,
            PerformanceTrend::Unknown => 0.0,
        }
    }

    /// Calculate confidence in a recommendation based on data quality
    fn calculate_confidence(&self, stats: &StrategyPerformanceStats) -> f64 {
        // Base confidence on sample size and recency
        let sample_confidence = (stats.task_count as f64 / self.config.min_samples_for_adaptation as f64).min(1.0);
        
        // Reduce confidence for unknown trends
        let trend_confidence = match stats.trend {
            PerformanceTrend::Unknown => 0.5,
            _ => 1.0,
        };

        // Reduce confidence for low success rates
        let success_confidence = stats.success_rate;

        // Combined confidence (geometric mean)
        (sample_confidence * trend_confidence * success_confidence).powf(1.0 / 3.0)
    }
}

#[async_trait]
impl AdaptationEngine for LearningEngineImpl {
    async fn analyze_performance(
        &self,
        stats: &HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>,
    ) -> PrismaResult<Vec<AdaptationRecommendation>> {
        let mut recommendations = Vec::new();

        // Group stats by category
        let mut category_stats: HashMap<TaskCategory, Vec<&StrategyPerformanceStats>> = HashMap::new();
        for ((strategy, category), stat) in stats {
            category_stats.entry(category.clone()).or_insert_with(Vec::new).push(stat);
        }

        // Analyze each category
        for (category, category_stat_list) in category_stats {
            if category_stat_list.len() < 2 {
                continue; // Need at least 2 strategies to compare
            }

            // Find the best performing strategy for this category
            let mut best_stats = category_stat_list[0];
            for stats in &category_stat_list[1..] {
                if self.compare_strategies(stats, best_stats) > 0.0 {
                    best_stats = stats;
                }
            }

            // Check if we have enough data to make a recommendation
            if best_stats.task_count < self.config.min_samples_for_adaptation {
                continue;
            }

            // Find the current "default" strategy (could be improved by tracking current recommendations)
            let current_strategy = category_stat_list
                .iter()
                .max_by_key(|s| s.task_count)
                .map(|s| s.strategy)
                .unwrap_or(ExecutionStrategyType::Tokio);

            // Only recommend change if the best strategy is different and significantly better
            if best_stats.strategy != current_strategy {
                let current_stats = category_stat_list
                    .iter()
                    .find(|s| s.strategy == current_strategy);

                if let Some(current_stats) = current_stats {
                    let improvement = self.compare_strategies(best_stats, current_stats);
                    
                    if improvement > self.config.significance_threshold {
                        let confidence = self.calculate_confidence(best_stats);
                        
                        let recommendation = AdaptationRecommendation {
                            category: category.clone(),
                            current_strategy,
                            recommended_strategy: best_stats.strategy,
                            confidence,
                            reason: format!(
                                "Strategy {:?} shows {:.1}% better performance than {:?} (success rate: {:.1}% vs {:.1}%, avg time: {:?} vs {:?})",
                                best_stats.strategy,
                                improvement * 100.0,
                                current_strategy,
                                best_stats.success_rate * 100.0,
                                current_stats.success_rate * 100.0,
                                best_stats.avg_execution_duration,
                                current_stats.avg_execution_duration
                            ),
                            performance_data: category_stat_list.iter().cloned().cloned().collect(),
                            timestamp: SystemTime::now(),
                        };

                        recommendations.push(recommendation);
                    }
                }
            }
        }

        info!("Generated {} adaptation recommendations", recommendations.len());
        Ok(recommendations)
    }

    async fn evaluate_strategy_change(
        &self,
        _category: TaskCategory,
        _current_strategy: ExecutionStrategyType,
        _proposed_strategy: ExecutionStrategyType,
        current_stats: &StrategyPerformanceStats,
        proposed_stats: Option<&StrategyPerformanceStats>,
    ) -> PrismaResult<f64> {
        if let Some(proposed_stats) = proposed_stats {
            // Compare the two strategies
            let improvement = self.compare_strategies(proposed_stats, current_stats);
            let confidence = self.calculate_confidence(proposed_stats);
            
            // Return confidence adjusted by improvement magnitude
            Ok((improvement.max(0.0) * confidence).min(1.0))
        } else {
            // No data for proposed strategy - low confidence
            Ok(0.1)
        }
    }

    async fn get_best_strategy(
        &self,
        category: TaskCategory,
        stats: &HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>,
    ) -> PrismaResult<Option<ExecutionStrategyType>> {
        let category_stats: Vec<&StrategyPerformanceStats> = stats
            .iter()
            .filter(|((_, cat), _)| *cat == category)
            .map(|(_, stat)| stat)
            .filter(|stat| stat.task_count >= self.config.min_samples_for_adaptation)
            .collect();

        if category_stats.is_empty() {
            return Ok(None);
        }

        // Find the best performing strategy
        let mut best_stats = category_stats[0];
        for stats in &category_stats[1..] {
            if self.compare_strategies(stats, best_stats) > 0.0 {
                best_stats = stats;
            }
        }

        Ok(Some(best_stats.strategy))
    }

    async fn calculate_trend(
        &self,
        recent_metrics: &[TaskPerformanceMetrics],
        strategy: ExecutionStrategyType,
        category: TaskCategory,
    ) -> PrismaResult<PerformanceTrend> {
        let relevant_metrics: Vec<&TaskPerformanceMetrics> = recent_metrics
            .iter()
            .filter(|m| m.execution_strategy == strategy && m.task_category == category)
            .collect();

        if relevant_metrics.len() < 4 {
            return Ok(PerformanceTrend::Unknown);
        }

        // Sort by completion time
        let mut sorted_metrics = relevant_metrics;
        sorted_metrics.sort_by_key(|m| m.completed_at);

        // Split into older and newer halves
        let mid_point = sorted_metrics.len() / 2;
        let older_metrics = &sorted_metrics[..mid_point];
        let newer_metrics = &sorted_metrics[mid_point..];

        // Calculate average execution times
        let older_avg = older_metrics
            .iter()
            .map(|m| m.execution_duration.as_millis() as f64)
            .sum::<f64>() / older_metrics.len() as f64;

        let newer_avg = newer_metrics
            .iter()
            .map(|m| m.execution_duration.as_millis() as f64)
            .sum::<f64>() / newer_metrics.len() as f64;

        // Determine trend
        let change_ratio = (newer_avg - older_avg) / older_avg;
        
        let trend = if change_ratio < -self.config.significance_threshold {
            PerformanceTrend::Improving // Lower execution time is better
        } else if change_ratio > self.config.significance_threshold {
            PerformanceTrend::Degrading // Higher execution time is worse
        } else {
            PerformanceTrend::Stable
        };

        debug!("Calculated trend for {:?}/{:?}: {:?} (change: {:.2}%)", 
               strategy, category, trend, change_ratio * 100.0);

        Ok(trend)
    }
}

/// Create a new learning engine with default configuration
pub fn create_learning_engine() -> LearningEngineImpl {
    LearningEngineImpl::new(LearningConfig::default())
}

/// Create a new learning engine with custom configuration
pub fn create_learning_engine_with_config(config: LearningConfig) -> LearningEngineImpl {
    LearningEngineImpl::new(config)
}
