// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/learning/traits.rs
// =================================================================================================
// Purpose: Trait definitions for the learning and adaptation system.
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::time::SystemTime;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    TaskCategory, TaskPriority, ExecutionStrategyType, TaskId, PrismaScore, SystemScore
};

use super::types::{
    TaskPerformanceMetrics, StrategyPerformanceStats, AdaptationRecommendation,
    LearningState, SystemLoadSnapshot, PerformanceTrend
};

/// Trait for tracking and analyzing task performance
#[async_trait]
pub trait PerformanceTracker: Send + Sync {
    /// Record the start of a task execution
    async fn record_task_start(
        &mut self,
        task_id: TaskId,
        category: TaskCategory,
        priority: TaskPriority,
        strategy: ExecutionStrategyType,
        system_load: SystemLoadSnapshot,
    ) -> PrismaResult<()>;

    /// Record the completion of a task execution
    async fn record_task_completion(
        &mut self,
        task_id: TaskId,
        success: bool,
        error_message: Option<String>,
        resource_usage: HashMap<crate::prisma::prisma_engine::types::ResourceType, crate::prisma::prisma_engine::types::ResourceUsage>,
    ) -> PrismaResult<()>;

    /// Get performance metrics for a specific task
    async fn get_task_metrics(&self, task_id: TaskId) -> PrismaResult<Option<TaskPerformanceMetrics>>;

    /// Get aggregated performance statistics for a strategy-category combination
    async fn get_strategy_stats(
        &self,
        strategy: ExecutionStrategyType,
        category: TaskCategory,
    ) -> PrismaResult<Option<StrategyPerformanceStats>>;

    /// Get all performance statistics
    async fn get_all_stats(&self) -> PrismaResult<HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>>;

    /// Update performance statistics based on recent metrics
    async fn update_statistics(&mut self) -> PrismaResult<()>;

    /// Clean up old performance data
    async fn cleanup_old_data(&mut self) -> PrismaResult<()>;
}

/// Trait for generating adaptation recommendations based on performance data
#[async_trait]
pub trait AdaptationEngine: Send + Sync {
    /// Analyze performance data and generate adaptation recommendations
    async fn analyze_performance(
        &self,
        stats: &HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>,
    ) -> PrismaResult<Vec<AdaptationRecommendation>>;

    /// Evaluate whether a specific strategy change would be beneficial
    async fn evaluate_strategy_change(
        &self,
        category: TaskCategory,
        current_strategy: ExecutionStrategyType,
        proposed_strategy: ExecutionStrategyType,
        current_stats: &StrategyPerformanceStats,
        proposed_stats: Option<&StrategyPerformanceStats>,
    ) -> PrismaResult<f64>; // Returns confidence score

    /// Get the best strategy for a category based on current performance data
    async fn get_best_strategy(
        &self,
        category: TaskCategory,
        stats: &HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>,
    ) -> PrismaResult<Option<ExecutionStrategyType>>;

    /// Calculate performance trend for a strategy-category combination
    async fn calculate_trend(
        &self,
        recent_metrics: &[TaskPerformanceMetrics],
        strategy: ExecutionStrategyType,
        category: TaskCategory,
    ) -> PrismaResult<PerformanceTrend>;
}

/// Trait for learning-enabled decision making
#[async_trait]
pub trait LearningDecisionMaker: Send + Sync {
    /// Make a strategy decision using both rules and learned performance data
    async fn decide_with_learning(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<ExecutionStrategyType>;

    /// Update the decision maker with new performance data
    async fn update_with_performance(
        &mut self,
        metrics: TaskPerformanceMetrics,
    ) -> PrismaResult<()>;

    /// Apply adaptation recommendations to update decision-making rules
    async fn apply_adaptations(
        &mut self,
        recommendations: Vec<AdaptationRecommendation>,
    ) -> PrismaResult<()>;

    /// Get the current learning state
    async fn get_learning_state(&self) -> PrismaResult<LearningState>;

    /// Check if the decision maker should adapt based on recent performance
    async fn should_adapt(&self) -> PrismaResult<bool>;

    /// Perform adaptation based on recent performance data
    async fn perform_adaptation(&mut self) -> PrismaResult<Vec<AdaptationRecommendation>>;
}

/// Trait for real-time system load monitoring
#[async_trait]
pub trait SystemLoadMonitor: Send + Sync {
    /// Get current system load snapshot
    async fn get_current_load(&self) -> PrismaResult<SystemLoadSnapshot>;

    /// Check if the system is under high load
    async fn is_high_load(&self) -> PrismaResult<bool>;

    /// Get load trend over recent time period
    async fn get_load_trend(&self) -> PrismaResult<PerformanceTrend>;

    /// Predict future load based on current trends
    async fn predict_load(&self, time_ahead: std::time::Duration) -> PrismaResult<SystemLoadSnapshot>;
}

/// Trait for predictive task routing
#[async_trait]
pub trait PredictiveRouter: Send + Sync {
    /// Predict the best strategy for a task based on current system state and learned patterns
    async fn predict_best_strategy(
        &self,
        task_category: &TaskCategory,
        task_priority: TaskPriority,
        current_load: &SystemLoadSnapshot,
        performance_stats: &HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>,
    ) -> PrismaResult<ExecutionStrategyType>;

    /// Predict task execution time for a given strategy
    async fn predict_execution_time(
        &self,
        task_category: &TaskCategory,
        strategy: ExecutionStrategyType,
        current_load: &SystemLoadSnapshot,
    ) -> PrismaResult<std::time::Duration>;

    /// Predict queue wait time for a given priority level
    async fn predict_queue_time(
        &self,
        priority: TaskPriority,
        current_load: &SystemLoadSnapshot,
    ) -> PrismaResult<std::time::Duration>;
}

/// Trait for congestion detection and mitigation
#[async_trait]
pub trait CongestionManager: Send + Sync {
    /// Detect if the system is experiencing congestion
    async fn detect_congestion(&self, load: &SystemLoadSnapshot) -> PrismaResult<bool>;

    /// Get congestion mitigation recommendations
    async fn get_mitigation_strategies(
        &self,
        load: &SystemLoadSnapshot,
    ) -> PrismaResult<Vec<AdaptationRecommendation>>;

    /// Apply load balancing adjustments
    async fn apply_load_balancing(
        &mut self,
        recommendations: Vec<AdaptationRecommendation>,
    ) -> PrismaResult<()>;
}
