// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/learning/types.rs
// =================================================================================================
// Purpose: Type definitions for the learning and adaptation system.
// =================================================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant, SystemTime};

use crate::prisma::prisma_engine::types::{
    TaskCategory, TaskPriority, ExecutionStrategyType, ResourceType, ResourceUsage, TaskId
};

/// Performance metrics for a specific task execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskPerformanceMetrics {
    /// Unique identifier for the task
    pub task_id: TaskId,
    
    /// Category of the task
    pub task_category: TaskCategory,
    
    /// Priority of the task
    pub task_priority: TaskPriority,
    
    /// Strategy used to execute the task
    pub execution_strategy: ExecutionStrategyType,
    
    /// Time taken to execute the task
    pub execution_duration: Duration,
    
    /// Time the task spent waiting in queue
    pub queue_duration: Duration,
    
    /// Whether the task completed successfully
    pub success: bool,
    
    /// Error message if the task failed
    pub error_message: Option<String>,
    
    /// Resource usage during task execution
    pub resource_usage: HashMap<ResourceType, ResourceUsage>,
    
    /// System load at the time of execution
    pub system_load: SystemLoadSnapshot,
    
    /// Timestamp when the task was submitted
    pub submitted_at: SystemTime,
    
    /// Timestamp when the task started executing
    pub started_at: SystemTime,
    
    /// Timestamp when the task completed
    pub completed_at: SystemTime,
}

/// Snapshot of system load at a specific point in time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemLoadSnapshot {
    /// CPU usage percentage (0.0 to 1.0)
    pub cpu_usage: f64,
    
    /// Memory usage percentage (0.0 to 1.0)
    pub memory_usage: f64,
    
    /// Number of active tasks
    pub active_tasks: usize,
    
    /// Queue lengths for different priority levels
    pub queue_lengths: HashMap<TaskPriority, usize>,
    
    /// Timestamp of this snapshot
    pub timestamp: SystemTime,
}

/// Aggregated performance statistics for a strategy-category combination
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyPerformanceStats {
    /// Strategy and category this applies to
    pub strategy: ExecutionStrategyType,
    pub category: TaskCategory,
    
    /// Number of tasks executed
    pub task_count: usize,
    
    /// Number of successful tasks
    pub success_count: usize,
    
    /// Success rate (0.0 to 1.0)
    pub success_rate: f64,
    
    /// Average execution duration
    pub avg_execution_duration: Duration,
    
    /// Average queue duration
    pub avg_queue_duration: Duration,
    
    /// 95th percentile execution duration
    pub p95_execution_duration: Duration,
    
    /// Average resource usage
    pub avg_resource_usage: HashMap<ResourceType, ResourceUsage>,
    
    /// Performance trend (improving, stable, degrading)
    pub trend: PerformanceTrend,
    
    /// Last updated timestamp
    pub last_updated: SystemTime,
}

/// Performance trend indicator
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum PerformanceTrend {
    /// Performance is improving over time
    Improving,
    /// Performance is stable
    Stable,
    /// Performance is degrading over time
    Degrading,
    /// Not enough data to determine trend
    Unknown,
}

/// Learning configuration parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LearningConfig {
    /// Minimum number of samples required before making adaptations
    pub min_samples_for_adaptation: usize,
    
    /// Weight given to recent performance vs historical performance (0.0 to 1.0)
    pub recency_weight: f64,
    
    /// Threshold for considering a performance change significant
    pub significance_threshold: f64,
    
    /// Maximum age of performance data to consider (older data is discarded)
    pub max_data_age: Duration,
    
    /// How often to update performance statistics
    pub update_interval: Duration,
    
    /// How often to adapt decision-making rules
    pub adaptation_interval: Duration,
    
    /// Enable/disable learning features
    pub enable_learning: bool,
    
    /// Enable/disable real-time adaptation
    pub enable_real_time_adaptation: bool,
}

impl Default for LearningConfig {
    fn default() -> Self {
        Self {
            min_samples_for_adaptation: 10,
            recency_weight: 0.7,
            significance_threshold: 0.1, // 10% change
            max_data_age: Duration::from_secs(3600), // 1 hour
            update_interval: Duration::from_secs(30),
            adaptation_interval: Duration::from_secs(300), // 5 minutes
            enable_learning: true,
            enable_real_time_adaptation: true,
        }
    }
}

/// Adaptation recommendation from the learning system
#[derive(Debug, Clone)]
pub struct AdaptationRecommendation {
    /// Category this recommendation applies to
    pub category: TaskCategory,
    
    /// Current recommended strategy
    pub current_strategy: ExecutionStrategyType,
    
    /// New recommended strategy
    pub recommended_strategy: ExecutionStrategyType,
    
    /// Confidence in this recommendation (0.0 to 1.0)
    pub confidence: f64,
    
    /// Reason for the recommendation
    pub reason: String,
    
    /// Supporting performance data
    pub performance_data: Vec<StrategyPerformanceStats>,
    
    /// Timestamp of this recommendation
    pub timestamp: SystemTime,
}

/// Learning state for the adaptive decision maker
#[derive(Debug, Clone)]
pub struct LearningState {
    /// Performance statistics for each strategy-category combination
    pub strategy_stats: HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>,
    
    /// Recent task performance metrics (sliding window)
    pub recent_metrics: Vec<TaskPerformanceMetrics>,
    
    /// Adaptation recommendations that have been made
    pub adaptation_history: Vec<AdaptationRecommendation>,
    
    /// Last time statistics were updated
    pub last_stats_update: SystemTime,
    
    /// Last time adaptations were made
    pub last_adaptation: SystemTime,
    
    /// Configuration for learning behavior
    pub config: LearningConfig,
}

impl Default for LearningState {
    fn default() -> Self {
        Self {
            strategy_stats: HashMap::new(),
            recent_metrics: Vec::new(),
            adaptation_history: Vec::new(),
            last_stats_update: SystemTime::now(),
            last_adaptation: SystemTime::now(),
            config: LearningConfig::default(),
        }
    }
}
