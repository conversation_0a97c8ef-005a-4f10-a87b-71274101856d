// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/learning/mod.rs
// =================================================================================================
// Purpose: Learning and adaptation module for the decision maker. This module implements
// real-time learning capabilities that allow the decision maker to adapt its strategy selection
// based on historical performance data, system resource utilization patterns, and task execution
// success/failure rates.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports this module
//   - decision_maker/decision_maker.rs: Uses learning components
//   - monitor/: Uses performance metrics from monitoring system
//
// - External Dependencies:
//   - tokio: For async runtime and synchronization primitives
//   - serde: For serialization of learning data
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - This module is platform-independent as it operates on abstract performance metrics
// =================================================================================================

pub mod types;
pub mod traits;
pub mod performance_tracker;
pub mod adaptive_decision_maker;
pub mod learning_engine;

// Re-export key types and traits
pub use types::*;
pub use traits::*;
pub use performance_tracker::*;
pub use adaptive_decision_maker::*;
pub use learning_engine::*;
