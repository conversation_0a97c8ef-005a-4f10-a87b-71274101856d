// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/rules.rs
// =================================================================================================
// Purpose: Implements the rule system for decision making. This file contains rule definitions
// and evaluation logic that determine how tasks should be executed based on their resource
// requirements and current system resource availability. It provides a flexible and extensible
// way to define decision-making policies.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports this module
//   - decision_maker/decision_maker.rs: Uses rules for decision making
//   - decision_maker/types.rs: Uses types defined for rules
//   - decision_maker/traits.rs: May implement traits defined for rules
//   - decision_maker/state.rs: May use state information for rule evaluation
//   - prisma_scores: Uses task resource requirement information
//   - system_scores: Uses system resource availability information
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses core engine types
//   - prisma_engine/traits.rs: Complements the DecisionLogic trait
//
// - Module Interactions:
//   - Analyzes task information from TCL module
//   - Analyzes system information from Monitor module
//   - Influences execution strategy decisions in PrismaEngine
//
// Platform Considerations:
// - This module is platform-independent as it evaluates abstract rules
// =================================================================================================

use std::collections::HashMap;
use std::fmt::Debug;
use std::time::SystemTime;
use async_trait::async_trait;
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    TaskCategory, TaskPriority, PrismaScore, SystemScore, ExecutionStrategyType, ResourceType, ResourceUsage
};

use super::traits::{Rule, RuleSet};
use super::types::{RuleType, RuleResult, DecisionReason, RuleConfig};

/// Base implementation for rules
#[derive(Debug)]
pub struct BaseRule {
    /// Name of the rule
    name: String,
    /// Description of the rule
    description: String,
    /// Type of the rule
    rule_type: RuleType,
    /// Whether the rule is enabled
    enabled: bool,
    /// Priority of the rule (higher priority rules are evaluated first)
    priority: u32,
    /// Parameters for the rule
    parameters: HashMap<String, String>,
}

impl BaseRule {
    /// Create a new BaseRule
    pub fn new(
        name: String,
        description: String,
        rule_type: RuleType,
        enabled: bool,
        priority: u32,
        parameters: HashMap<String, String>,
    ) -> Self {
        BaseRule {
            name,
            description,
            rule_type,
            enabled,
            priority,
            parameters,
        }
    }

    /// Create a new BaseRule from a RuleConfig
    pub fn from_config(config: RuleConfig) -> Self {
        BaseRule {
            name: config.name,
            description: config.description,
            rule_type: config.rule_type,
            enabled: config.enabled,
            priority: config.priority,
            parameters: config.parameters,
        }
    }

    /// Get a parameter value as a string
    pub fn get_param_str(&self, key: &str) -> Option<&str> {
        self.parameters.get(key).map(|s| s.as_str())
    }

    /// Get a parameter value as a float
    pub fn get_param_f64(&self, key: &str) -> Option<f64> {
        self.parameters.get(key).and_then(|s| s.parse::<f64>().ok())
    }

    /// Get a parameter value as an integer
    pub fn get_param_i32(&self, key: &str) -> Option<i32> {
        self.parameters.get(key).and_then(|s| s.parse::<i32>().ok())
    }

    /// Get a parameter value as a boolean
    pub fn get_param_bool(&self, key: &str) -> Option<bool> {
        self.parameters.get(key).and_then(|s| s.parse::<bool>().ok())
    }
}

/// Rule for priority-based decisions
#[derive(Debug)]
pub struct PriorityRule {
    /// Base rule implementation
    base: BaseRule,
}

impl PriorityRule {
    /// Create a new PriorityRule
    pub fn new(
        name: String,
        description: String,
        enabled: bool,
        priority: u32,
        parameters: HashMap<String, String>,
    ) -> Self {
        PriorityRule {
            base: BaseRule::new(
                name,
                description,
                RuleType::Priority,
                enabled,
                priority,
                parameters,
            ),
        }
    }

    /// Create a realtime priority rule
    pub fn realtime() -> Self {
        let mut parameters = HashMap::new();
        parameters.insert("strategy".to_string(), "Tokio".to_string());

        PriorityRule::new(
            "RealtimePriorityRule".to_string(),
            "Tasks with Realtime priority always use Tokio".to_string(),
            true,
            100, // Highest priority
            parameters,
        )
    }

    /// Create a high priority rule
    pub fn high() -> Self {
        let mut parameters = HashMap::new();
        parameters.insert("strategy".to_string(), "Tokio".to_string());
        parameters.insert("threshold".to_string(), "0.7".to_string());

        PriorityRule::new(
            "HighPriorityRule".to_string(),
            "High priority tasks prefer Tokio unless system load is low".to_string(),
            true,
            90,
            parameters,
        )
    }
}

#[async_trait]
impl Rule for PriorityRule {
    fn rule_type(&self) -> RuleType {
        self.base.rule_type
    }

    fn name(&self) -> &str {
        &self.base.name
    }

    fn description(&self) -> &str {
        &self.base.description
    }

    async fn evaluate(
        &self,
        _task_category: &TaskCategory,
        _task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<RuleResult> {
        // Get the strategy parameter
        let strategy_str = self.base.get_param_str("strategy").unwrap_or("Tokio");
        let strategy = match strategy_str {
            "Rayon" => ExecutionStrategyType::Rayon,
            "Direct" => ExecutionStrategyType::Direct,
            _ => ExecutionStrategyType::Tokio,
        };

        // Check if the rule should be triggered based on priority
        let triggered = match task_priority {
            TaskPriority::Realtime => {
                // Realtime tasks always trigger this rule if it's the realtime rule
                self.base.name == "RealtimePriorityRule"
            },
            TaskPriority::High => {
                // High priority tasks trigger this rule if it's the high rule
                // and system load is above threshold
                if self.base.name == "HighPriorityRule" {
                    let threshold = self.base.get_param_f64("threshold").unwrap_or(0.7);
                    let cpu_usage = system_score.availability.get(&ResourceType::CPU)
                        .map(|usage| usage.0)
                        .unwrap_or(0.0);

                    cpu_usage > threshold
                } else {
                    false
                }
            },
            _ => false,
        };

        // Create the rule result
        let result = RuleResult {
            rule_name: self.base.name.clone(),
            rule_type: self.base.rule_type,
            triggered,
            recommended_strategy: if triggered { Some(strategy) } else { None },
            priority_adjustment: None,
            reason: DecisionReason {
                description: if triggered {
                    format!("Task priority {} requires {} strategy", task_priority, strategy_str)
                } else {
                    "Priority rule not triggered".to_string()
                },
                details: None,
                resource: None,
                resource_usage: None,
            },
            timestamp: SystemTime::now(),
        };

        Ok(result)
    }

    fn is_enabled(&self) -> bool {
        self.base.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.base.enabled = enabled;
    }

    fn priority(&self) -> u32 {
        self.base.priority
    }

    fn set_priority(&mut self, priority: u32) {
        self.base.priority = priority;
    }
}

/// Rule for resource-based decisions
#[derive(Debug)]
pub struct ResourceRule {
    /// Base rule implementation
    base: BaseRule,
}

impl ResourceRule {
    /// Create a new ResourceRule
    pub fn new(
        name: String,
        description: String,
        enabled: bool,
        priority: u32,
        parameters: HashMap<String, String>,
    ) -> Self {
        ResourceRule {
            base: BaseRule::new(
                name,
                description,
                RuleType::Resource,
                enabled,
                priority,
                parameters,
            ),
        }
    }

    /// Create a CPU constraint rule
    pub fn cpu_constraint() -> Self {
        let mut parameters = HashMap::new();
        parameters.insert("resource".to_string(), "CPU".to_string());
        parameters.insert("threshold".to_string(), "0.8".to_string());
        parameters.insert("strategy".to_string(), "Tokio".to_string());

        ResourceRule::new(
            "CpuConstraintRule".to_string(),
            "Use Tokio for CPU-intensive tasks when CPU is constrained".to_string(),
            true,
            80,
            parameters,
        )
    }

    /// Create a memory constraint rule
    pub fn memory_constraint() -> Self {
        let mut parameters = HashMap::new();
        parameters.insert("resource".to_string(), "Memory".to_string());
        parameters.insert("threshold".to_string(), "0.85".to_string());
        parameters.insert("strategy".to_string(), "Direct".to_string());

        ResourceRule::new(
            "MemoryConstraintRule".to_string(),
            "Use Direct for simple tasks when memory is constrained".to_string(),
            true,
            70,
            parameters,
        )
    }
}

#[async_trait]
impl Rule for ResourceRule {
    fn rule_type(&self) -> RuleType {
        self.base.rule_type
    }

    fn name(&self) -> &str {
        &self.base.name
    }

    fn description(&self) -> &str {
        &self.base.description
    }

    async fn evaluate(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        _task_priority: TaskPriority,
    ) -> PrismaResult<RuleResult> {
        // Get the resource parameter
        let resource_str = self.base.get_param_str("resource").unwrap_or("CPU");
        let resource = match resource_str {
            "CPU" => ResourceType::CPU,
            "Memory" => ResourceType::Memory,
            "GPU" => ResourceType::GPU,
            "NetworkBandwidth" => ResourceType::NetworkBandwidth,
            "DiskIO" => ResourceType::DiskIO,
            _ => ResourceType::CPU,
        };

        // Get the threshold parameter
        let threshold = self.base.get_param_f64("threshold").unwrap_or(0.8);

        // Get the strategy parameter
        let strategy_str = self.base.get_param_str("strategy").unwrap_or("Tokio");
        let strategy = match strategy_str {
            "Rayon" => ExecutionStrategyType::Rayon,
            "Direct" => ExecutionStrategyType::Direct,
            _ => ExecutionStrategyType::Tokio,
        };

        // Check if the resource is constrained
        let resource_usage = system_score.availability.get(&resource)
            .map(|usage| usage.0)
            .unwrap_or(0.0);

        // Check if the task requires this resource
        let task_requires_resource = task_score.resources.get(&resource)
            .map(|usage| usage.0 > 0.0)
            .unwrap_or(false);

        // Determine if the rule should be triggered
        let triggered = resource_usage > threshold && task_requires_resource;

        // For CPU constraint rule, only trigger for CPU-intensive tasks
        let triggered = if self.base.name == "CpuConstraintRule" {
            triggered && matches!(task_category, TaskCategory::EmbeddingGeneration | TaskCategory::FileProcessing)
        } else if self.base.name == "MemoryConstraintRule" {
            triggered && matches!(task_category, TaskCategory::Internal)
        } else {
            triggered
        };

        // Create the rule result
        let result = RuleResult {
            rule_name: self.base.name.clone(),
            rule_type: self.base.rule_type,
            triggered,
            recommended_strategy: if triggered { Some(strategy) } else { None },
            priority_adjustment: None,
            reason: DecisionReason {
                description: if triggered {
                    format!("{} is constrained ({}%), using {} strategy",
                            resource_str, resource_usage * 100.0, strategy_str)
                } else {
                    format!("{} is not constrained ({}%)", resource_str, resource_usage * 100.0)
                },
                details: Some(format!("Threshold: {}%, Task requires resource: {}",
                                     threshold * 100.0, task_requires_resource)),
                resource: Some(resource),
                resource_usage: Some(ResourceUsage(resource_usage)),
            },
            timestamp: SystemTime::now(),
        };

        Ok(result)
    }

    fn is_enabled(&self) -> bool {
        self.base.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.base.enabled = enabled;
    }

    fn priority(&self) -> u32 {
        self.base.priority
    }

    fn set_priority(&mut self, priority: u32) {
        self.base.priority = priority;
    }
}

/// Rule for category-based decisions
#[derive(Debug)]
pub struct CategoryRule {
    /// Base rule implementation
    base: BaseRule,
    /// Default strategies for each category
    category_strategies: HashMap<TaskCategory, ExecutionStrategyType>,
}

impl CategoryRule {
    /// Create a new CategoryRule
    pub fn new(
        name: String,
        description: String,
        enabled: bool,
        priority: u32,
        parameters: HashMap<String, String>,
        category_strategies: HashMap<TaskCategory, ExecutionStrategyType>,
    ) -> Self {
        CategoryRule {
            base: BaseRule::new(
                name,
                description,
                RuleType::Category,
                enabled,
                priority,
                parameters,
            ),
            category_strategies,
        }
    }

    /// Create a default category rule
    pub fn default() -> Self {
        let mut category_strategies = HashMap::new();
        category_strategies.insert(TaskCategory::LLMInference, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::EmbeddingGeneration, ExecutionStrategyType::Rayon);
        category_strategies.insert(TaskCategory::DatabaseQuery, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::FileProcessing, ExecutionStrategyType::Rayon);
        category_strategies.insert(TaskCategory::NetworkRequest, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::UICallback, ExecutionStrategyType::Tokio);
        category_strategies.insert(TaskCategory::Internal, ExecutionStrategyType::Direct);

        CategoryRule::new(
            "DefaultCategoryRule".to_string(),
            "Select strategy based on task category".to_string(),
            true,
            50, // Lower priority than resource and priority rules
            HashMap::new(),
            category_strategies,
        )
    }
}

#[async_trait]
impl Rule for CategoryRule {
    fn rule_type(&self) -> RuleType {
        self.base.rule_type
    }

    fn name(&self) -> &str {
        &self.base.name
    }

    fn description(&self) -> &str {
        &self.base.description
    }

    async fn evaluate(
        &self,
        task_category: &TaskCategory,
        _task_score: &PrismaScore,
        _system_score: &SystemScore,
        _task_priority: TaskPriority,
    ) -> PrismaResult<RuleResult> {
        // Get the strategy for this category
        let strategy = self.category_strategies.get(task_category)
            .copied()
            .unwrap_or(ExecutionStrategyType::Tokio);

        // This rule is always triggered
        let triggered = true;

        // Create the rule result
        let result = RuleResult {
            rule_name: self.base.name.clone(),
            rule_type: self.base.rule_type,
            triggered,
            recommended_strategy: Some(strategy),
            priority_adjustment: None,
            reason: DecisionReason {
                description: format!("Task category {:?} uses {:?} strategy", task_category, strategy),
                details: None,
                resource: None,
                resource_usage: None,
            },
            timestamp: SystemTime::now(),
        };

        Ok(result)
    }

    fn is_enabled(&self) -> bool {
        self.base.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.base.enabled = enabled;
    }

    fn priority(&self) -> u32 {
        self.base.priority
    }

    fn set_priority(&mut self, priority: u32) {
        self.base.priority = priority;
    }
}

/// Implementation of the RuleSet trait
#[derive(Debug)]
pub struct RuleSetImpl {
    /// Rules in this rule set
    rules: Vec<Box<dyn Rule>>,
}

impl RuleSetImpl {
    /// Create a new empty RuleSetImpl
    pub fn new() -> Self {
        RuleSetImpl {
            rules: Vec::new(),
        }
    }

    /// Create a new RuleSetImpl with default rules
    pub fn with_default_rules() -> Self {
        let mut rule_set = RuleSetImpl::new();

        // Add priority rules
        rule_set.add_rule(Box::new(PriorityRule::realtime()));
        rule_set.add_rule(Box::new(PriorityRule::high()));

        // Add resource rules
        rule_set.add_rule(Box::new(ResourceRule::cpu_constraint()));
        rule_set.add_rule(Box::new(ResourceRule::memory_constraint()));

        // Add category rule
        rule_set.add_rule(Box::new(CategoryRule::default()));

        rule_set
    }
}

#[async_trait]
impl RuleSet for RuleSetImpl {
    fn add_rule(&mut self, rule: Box<dyn Rule>) {
        self.rules.push(rule);
    }

    fn remove_rule(&mut self, rule_name: &str) -> Option<Box<dyn Rule>> {
        if let Some(index) = self.rules.iter().position(|r| r.name() == rule_name) {
            Some(self.rules.remove(index))
        } else {
            None
        }
    }

    fn get_rule(&self, rule_name: &str) -> Option<&dyn Rule> {
        self.rules.iter()
            .find(|r| r.name() == rule_name)
            .map(|r| r.as_ref())
    }

    fn get_rule_mut(&mut self, rule_name: &str) -> Option<&mut dyn Rule> {
        self.rules.iter_mut()
            .find(|r| r.name() == rule_name)
            .map(|r| r.as_mut() as &mut dyn Rule)
    }

    fn get_rules(&self) -> Vec<&dyn Rule> {
        self.rules.iter()
            .map(|r| r.as_ref())
            .collect()
    }

    fn get_rules_by_type(&self, rule_type: RuleType) -> Vec<&dyn Rule> {
        self.rules.iter()
            .filter(|r| r.rule_type() == rule_type)
            .map(|r| r.as_ref())
            .collect()
    }

    async fn evaluate_rules(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<Vec<RuleResult>> {
        // Sort rules by priority (higher priority first)
        let mut sorted_rules: Vec<&Box<dyn Rule>> = self.rules.iter()
            .filter(|r| r.is_enabled())
            .collect();

        sorted_rules.sort_by(|a, b| b.priority().cmp(&a.priority()));

        // Evaluate each rule
        let mut results = Vec::new();

        for rule in sorted_rules {
            match rule.evaluate(task_category, task_score, system_score, task_priority).await {
                Ok(result) => {
                    debug!("Rule {} evaluated: triggered={}", rule.name(), result.triggered);
                    results.push(result);
                },
                Err(e) => {
                    warn!("Error evaluating rule {}: {:?}", rule.name(), e);
                }
            }
        }

        Ok(results)
    }

    async fn evaluate_rules_by_type(
        &self,
        rule_type: RuleType,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<Vec<RuleResult>> {
        // Sort rules by priority (higher priority first)
        let mut sorted_rules: Vec<&Box<dyn Rule>> = self.rules.iter()
            .filter(|r| r.is_enabled() && r.rule_type() == rule_type)
            .collect();

        sorted_rules.sort_by(|a, b| b.priority().cmp(&a.priority()));

        // Evaluate each rule
        let mut results = Vec::new();

        for rule in sorted_rules {
            match rule.evaluate(task_category, task_score, system_score, task_priority).await {
                Ok(result) => {
                    debug!("Rule {} evaluated: triggered={}", rule.name(), result.triggered);
                    results.push(result);
                },
                Err(e) => {
                    warn!("Error evaluating rule {}: {:?}", rule.name(), e);
                }
            }
        }

        Ok(results)
    }
}

/// Create a new RuleSet with default rules
pub fn create_rule_set() -> Box<dyn RuleSet> {
    Box::new(RuleSetImpl::with_default_rules())
}

/// Create a new empty RuleSet
pub fn create_empty_rule_set() -> Box<dyn RuleSet> {
    Box::new(RuleSetImpl::new())
}

/// Select an execution strategy based on rule results
pub fn select_strategy_from_results(
    results: &[RuleResult],
    default_strategy: ExecutionStrategyType,
) -> ExecutionStrategyType {
    // Find the first triggered rule with a recommended strategy
    for result in results {
        if result.triggered {
            if let Some(strategy) = result.recommended_strategy {
                return strategy;
            }
        }
    }

    // If no rule was triggered, use the default strategy
    default_strategy
}