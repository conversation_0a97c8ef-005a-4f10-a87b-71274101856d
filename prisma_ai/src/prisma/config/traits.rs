use crate::UI::project_mgmt::metadata::chat::types::ChatSessionMetadata;
use crate::UI::project_mgmt::pm::types::{Project, ProjectParams, ProjectResponse, ProjectStatus, ProjectSummary};
use std::path::Path;

/// Trait for project management operations
pub trait ProjectManager {
    /// Create a new project with the given parameters
    fn create_project(&mut self, params: ProjectParams) -> ProjectResponse;
    
    /// Load a project from a file path
    fn load_project(&mut self, path: &Path) -> ProjectResponse;
    
    /// Save the current project to its file path or the specified path
    fn save_project(&self, project_id: &str, path: Option<&Path>) -> ProjectResponse;
    
    /// Get a project by its ID
    fn get_project(&self, project_id: &str) -> Option<Project>;
    
    /// List all projects
    fn list_projects(&self) -> Vec<ProjectSummary>;
    
    /// Update a project's basic information
    fn update_project(&mut self, project_id: &str, params: ProjectParams) -> ProjectResponse;
    
    /// Set a project's status
    fn set_project_status(&mut self, project_id: &str, status: ProjectStatus) -> ProjectResponse;
    
    /// Add a session to a project
    fn add_session(&mut self, project_id: &str, session: ChatSessionMetadata) -> ProjectResponse;
    
    /// Get a session from a project
    fn get_session(&self, project_id: &str, session_id: &str) -> Option<ChatSessionMetadata>;
    
    /// Export a project to JSON
    fn export_to_json(&self, project_id: &str) -> Result<String, String>;
}

/// Trait for project persistence operations
pub trait ProjectPersistence {
    /// Save a project to storage
    fn save_project(&self, project: &Project, path: Option<&Path>) -> Result<(), String>;
    
    /// Load a project from storage
    fn load_project(&self, path: &Path) -> Result<Project, String>;
    
    /// Check if a project exists at the given path
    fn project_exists(&self, path: &Path) -> bool;
}

/// Trait for managing project configuration
pub trait ConfigManager {
    /// Load the default configuration from TOML
    fn load_default_config(&self) -> Result<crate::UI::project_mgmt::pm::types::ProjectConfig, String>;
    
    /// Save configuration to TOML
    fn save_config(&self, config: &crate::UI::project_mgmt::pm::types::ProjectConfig, path: &Path) -> Result<(), String>;
}

pub trait ProjectConfigManager: ConfigManager {
    /// Get new project configuration
    fn get_new_project_config(&self) -> Result<NewProjectConfig, String>;
    
    /// Get embedding model configuration
    fn get_embedding_model_config(&self) -> Result<EmbeddingModelConfig, String>;
    
    /// Validate form field configuration
    fn validate_form_fields(&self, fields: &[FormField]) -> Result<(), String>;
}
