use crate::UI::project_mgmt::metadata::chat::types::ChatSessionMetadata;
use crate::UI::project_mgmt::pm::traits::{ConfigManager, ProjectManager, ProjectPersistence};
use crate::UI::project_mgmt::pm::types::{Project, ProjectParams, ProjectResponse, ProjectStatus, ProjectSummary};
use chrono::Utc;
use serde_json;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use uuid::Uuid;
use std::fmt::Debug;
use serde::{Serialize, Deserialize};

pub trait ConfigSection: Debug + Clone + Serialize + for<'de> Deserialize<'de> {}

pub trait ConfigLoader<T: ConfigSection> {
    fn load(&self, path: &Path) -> Result<T, String>;
    fn save(&self, config: &T, path: &Path) -> Result<(), String>;
}

pub struct GenericConfigLoader<T: ConfigSection> {
    phantom: std::marker::PhantomData<T>,
}

impl<T: ConfigSection> GenericConfigLoader<T> {
    pub fn new() -> Self {
        Self {
            phantom: std::marker::PhantomData,
        }
    }
}

impl<T: ConfigSection> ConfigLoader<T> for GenericConfigLoader<T> {
    fn load(&self, path: &Path) -> Result<T, String> {
        let contents = std::fs::read_to_string(path)
            .map_err(|e| format!("Failed to read config: {}", e))?;
            
        toml::from_str(&contents)
            .map_err(|e| format!("Failed to parse config: {}", e))
    }

    fn save(&self, config: &T, path: &Path) -> Result<(), String> {
        let contents = toml::to_string_pretty(config)
            .map_err(|e| format!("Failed to serialize config: {}", e))?;
            
        std::fs::write(path, contents)
            .map_err(|e| format!("Failed to write config: {}", e))
    }
}

/// Generic implementation of the ProjectManager trait
pub struct GenericProjectManager<C: ConfigManager, P: ProjectPersistence> {
    config_manager: C,
    persistence: P,
    projects: HashMap<String, Project>,
    current_project_id: Option<String>,
}

impl<C: ConfigManager, P: ProjectPersistence> GenericProjectManager<C, P> {
    /// Create a new generic project manager
    pub fn new(config_manager: C, persistence: P) -> Self {
        Self {
            config_manager,
            persistence,
            projects: HashMap::new(),
            current_project_id: None,
        }
    }
    
    /// Get the current active project
    pub fn current_project(&self) -> Option<&Project> {
        self.current_project_id.as_ref().and_then(|id| self.projects.get(id))
    }
    
    /// Set the current active project
    pub fn set_current_project(&mut self, project_id: &str) -> Result<(), String> {
        if self.projects.contains_key(project_id) {
            self.current_project_id = Some(project_id.to_string());
            Ok(())
        } else {
            Err(format!("Project with ID {} not found", project_id))
        }
    }
    
    /// Generate a project summary from a project
    fn generate_summary(&self, project: &Project) -> ProjectSummary {
        let total_messages = project.sessions.values()
            .map(|session| session.total_messages)
            .sum();
            
        ProjectSummary {
            id: project.id.clone(),
            name: project.name.clone(),
            description: project.description.clone(),
            created_at: project.created_at,
            updated_at: project.updated_at,
            session_count: project.sessions.len(),
            total_messages,
            status: ProjectStatus::Active, // Assuming active by default
        }
    }
}

impl<C: ConfigManager, P: ProjectPersistence> ProjectManager for GenericProjectManager<C, P> {
    fn create_project(&mut self, params: ProjectParams) -> ProjectResponse {
        let project_id = Uuid::new_v4().to_string();
        let now = Utc::now();
        
        let config = match self.config_manager.load_default_config() {
            Ok(config) => config,
            Err(e) => return ProjectResponse {
                success: false,
                message: format!("Failed to load default config: {}", e),
                project: None,
            },
        };
        
        let project = Project {
            id: project_id.clone(),
            name: params.name,
            description: params.description,
            created_at: now,
            updated_at: now,
            config,
            sessions: HashMap::new(),
            file_path: None,
        };
        
        self.projects.insert(project_id.clone(), project.clone());
        self.current_project_id = Some(project_id);
        
        ProjectResponse {
            success: true,
            message: "Project created successfully".to_string(),
            project: Some(project),
        }
    }
    
    fn load_project(&mut self, path: &Path) -> ProjectResponse {
        if (!self.persistence.project_exists(path)) {
            return ProjectResponse {
                success: false,
                message: format!("Project file does not exist at path: {}", path.display()),
                project: None,
            };
        }
        
        match self.persistence.load_project(path) {
            Ok(mut project) => {
                // Update the file path
                project.file_path = Some(path.to_path_buf());
                
                // Add to projects and set as current
                let project_id = project.id.clone();
                self.projects.insert(project_id.clone(), project.clone());
                self.current_project_id = Some(project_id);
                
                ProjectResponse {
                    success: true,
                    message: "Project loaded successfully".to_string(),
                    project: Some(project),
                }
            },
            Err(e) => ProjectResponse {
                success: false,
                message: format!("Failed to load project: {}", e),
                project: None,
            },
        }
    }
    
    fn save_project(&self, project_id: &str, path: Option<&Path>) -> ProjectResponse {
        if let Some(project) = self.projects.get(project_id) {
            let save_path = if let Some(p) = path {
                p
            } else if let Some(p) = &project.file_path {
                p.as_path()
            } else {
                return ProjectResponse {
                    success: false,
                    message: "No file path specified and project has no saved path".to_string(),
                    project: Some(project.clone()),
                };
            };
            
            match self.persistence.save_project(project, Some(save_path)) {
                Ok(_) => {
                    ProjectResponse {
                        success: true,
                        message: "Project saved successfully".to_string(),
                        project: Some(project.clone()),
                    }
                },
                Err(e) => ProjectResponse {
                    success: false,
                    message: format!("Failed to save project: {}", e),
                    project: Some(project.clone()),
                },
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn get_project(&self, project_id: &str) -> Option<Project> {
        self.projects.get(project_id).cloned()
    }
    
    fn list_projects(&self) -> Vec<ProjectSummary> {
        self.projects.values()
            .map(|p| self.generate_summary(p))
            .collect()
    }
    
    fn update_project(&mut self, project_id: &str, params: ProjectParams) -> ProjectResponse {
        if let Some(project) = self.projects.get_mut(project_id) {
            project.name = params.name;
            project.description = params.description;
            project.updated_at = Utc::now();
            
            ProjectResponse {
                success: true,
                message: "Project updated successfully".to_string(),
                project: Some(project.clone()),
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn set_project_status(&mut self, project_id: &str, status: ProjectStatus) -> ProjectResponse {
        // In a real implementation, you would update the project status
        // For now, we'll just check if the project exists
        if let Some(project) = self.projects.get(project_id) {
            ProjectResponse {
                success: true,
                message: format!("Project status updated to {:?}", status),
                project: Some(project.clone()),
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn add_session(&mut self, project_id: &str, session: ChatSessionMetadata) -> ProjectResponse {
        if let Some(project) = self.projects.get_mut(project_id) {
            project.sessions.insert(session.session_id.clone(), session);
            project.updated_at = Utc::now();
            
            ProjectResponse {
                success: true,
                message: "Session added to project successfully".to_string(),
                project: Some(project.clone()),
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn get_session(&self, project_id: &str, session_id: &str) -> Option<ChatSessionMetadata> {
        self.projects.get(project_id)
            .and_then(|p| p.sessions.get(session_id).cloned())
    }
    
    fn export_to_json(&self, project_id: &str) -> Result<String, String> {
        if let Some(project) = self.projects.get(project_id) {
            match serde_json::to_string_pretty(project) {
                Ok(json) => Ok(json),
                Err(e) => Err(format!("Failed to serialize project to JSON: {}", e)),
            }
        } else {
            Err(format!("Project with ID {} not found", project_id))
        }
    }
}
