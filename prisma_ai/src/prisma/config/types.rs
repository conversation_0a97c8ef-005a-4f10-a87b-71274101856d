use crate::UI::project_mgmt::metadata::chat::types::{ChatSessionMetadata, MetadataTrackingConfig};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// Main configuration structure loaded from the TOML file
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProjectConfig {
    #[serde(default)]
    pub prisma_engine_initialization: PrismaEngineConfig,
    #[serde(default)]
    pub project_metadata: ProjectMetadata,
    #[serde(default)]
    pub session_tracking: MetadataTrackingConfig,
    #[serde(default)]
    pub new_project: NewProjectConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct PrismaEngineConfig {
    pub enable_prisma_engine: bool,
}

/// Project metadata configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct ProjectMetadata {
    pub version: String,
    pub export_format: String,
}

/// Represents a complete project with all its data
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub config: ProjectConfig,
    pub sessions: HashMap<String, ChatSessionMetadata>,
    pub file_path: Option<PathBuf>,
}

/// Project status enum
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProjectStatus {
    Active,
    Archived,
    Deleted,
}

/// Project creation parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectParams {
    pub name: String,
    pub description: String,
}

/// Project summary for display in UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectSummary {
    pub id: String,
    pub name: String,
    pub description: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub session_count: usize,
    pub total_messages: u32,
    pub status: ProjectStatus,
}

/// Response type for project operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectResponse {
    pub success: bool,
    pub message: String,
    pub project: Option<Project>,
}

/// Form field configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormField {
    pub name: String,
    pub field_type: String,
    pub required: bool,
    pub label: String,
    pub multiline: Option<bool>,
}

/// New project configuration settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewProjectConfig {
    pub fields: Vec<FormField>,
    pub embedding_model: EmbeddingModelConfig,
}

/// Embedding model configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingModelConfig {
    pub required: bool,
    pub default_model: String,
    pub model_list_source: String,
}


