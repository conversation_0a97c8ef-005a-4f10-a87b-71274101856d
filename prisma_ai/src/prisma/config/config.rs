use std::path::{Path, PathBuf};
use anyhow::{Result, Context};
use serde::{Serialize, Deserialize};
use tracing::{info, error};
use tokio::fs;

use super::types::*;

/// Configuration manager for Prisma
pub struct ConfigManager {
    /// Path to the configuration file
    config_path: PathBuf,
    /// Loaded configuration
    config: ProjectConfig,
}

impl ConfigManager {
    /// Create a new configuration manager using the default config path
    pub async fn new() -> Result<Self> {
        let default_path = PathBuf::from("src/config/pm.toml");
        Self::from_path(&default_path).await
    }
    
    /// Create a new configuration manager from a specific file path
    pub async fn from_path<P: AsRef<Path>>(path: P) -> Result<Self> {
        let config_path = path.as_ref().to_path_buf();
        
        // Load the configuration file
        let content = fs::read_to_string(&config_path)
            .await
            .with_context(|| format!("Failed to read config file: {:?}", config_path))?;
            
        let config = toml::from_str::<ProjectConfig>(&content)
            .with_context(|| format!("Failed to parse TOML configuration"))?;
            
        info!("Loaded configuration from {:?}", config_path);
        
        Ok(Self {
            config_path,
            config,
        })
    }
    
    /// Check if the Prisma engine is enabled
    pub fn is_prisma_engine_enabled(&self) -> bool {
        self.config.prisma_engine_initialization.enable_prisma_engine
    }
    
    /// Get a reference to the entire config
    pub fn get_config(&self) -> &ProjectConfig {
        &self.config
    }
    
    /// Save the configuration to file
    pub async fn save(&self) -> Result<()> {
        let content = toml::to_string_pretty(&self.config)
            .context("Failed to serialize configuration")?;
            
        fs::write(&self.config_path, content)
            .await
            .with_context(|| format!("Failed to write configuration to {:?}", self.config_path))?;
            
        info!("Saved configuration to {:?}", self.config_path);
        
        Ok(())
    }
    
    /// Update the configuration
    pub fn update_config(&mut self, new_config: ProjectConfig) {
        self.config = new_config;
    }
}

pub struct ConfigLoader;

impl ConfigLoader {
    pub fn load_config(path: &Path) -> Result<ProjectConfig, String> {
        let contents = fs::read_to_string(path)
            .map_err(|e| format!("Failed to read config file: {}", e))?;
            
        toml::from_str(&contents)
            .map_err(|e| format!("Failed to parse TOML: {}", e))
    }

    pub fn load_new_project_config(path: &Path) -> Result<NewProjectConfig, String> {
        Self::load_config(path)
            .map(|config| config.new_project)
    }

    pub fn load_embedding_model_config(path: &Path) -> Result<EmbeddingModelConfig, String> {
        Self::load_new_project_config(path)
            .map(|config| config.embedding_model)
    }
}
