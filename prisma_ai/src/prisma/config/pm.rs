use crate::UI::project_mgmt::metadata::chat::types::ChatSessionMetadata;
use crate::UI::project_mgmt::pm::config::TomlConfigManager;
use crate::UI::project_mgmt::pm::traits::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ProjectManager, ProjectPersistence};
use crate::UI::project_mgmt::pm::types::{Project, ProjectParams, ProjectResponse, ProjectStatus, ProjectSummary};
use chrono::Utc;
use serde_json;
use std::collections::HashMap;
use std::fs;
use std::io::{Read, Write};
use std::path::{Path, PathBuf};

/// Main project management implementation
pub struct ProjectManagement {
    config_manager: TomlConfigManager,
    projects: HashMap<String, Project>,
    current_project_id: Option<String>,
    default_projects_dir: PathBuf,
    engine_enabled: bool,
}

/// File-based project persistence implementation
pub struct FileProjectPersistence {
    default_extension: String,
}

impl FileProjectPersistence {
    /// Create a new FileProjectPersistence with the specified default extension
    pub fn new(default_extension: String) -> Self {
        Self {
            default_extension,
        }
    }
    
    /// Create a default instance with .json extension
    pub fn default() -> Self {
        Self {
            default_extension: "json".to_string(),
        }
    }
}

impl ProjectPersistence for FileProjectPersistence {
    fn save_project(&self, project: &Project, path: Option<&Path>) -> Result<(), String> {
        let save_path = if let Some(p) = path {
            p.to_path_buf()
        } else if let Some(p) = &project.file_path {
            p.clone()
        } else {
            // Create a default path based on the project ID
            let mut path = std::env::temp_dir();
            path.push(format!("project_{}.{}", project.id, self.default_extension));
            path
        };
        
        // Ensure the directory exists
        if let Some(parent) = save_path.parent() {
            if !parent.exists() {
                if let Err(e) = fs::create_dir_all(parent) {
                    return Err(format!("Failed to create directory: {}", e));
                }
            }
        }
        
        // Serialize and write to file
        let json = match serde_json::to_string_pretty(project) {
            Ok(j) => j,
            Err(e) => return Err(format!("Failed to serialize project: {}", e)),
        };
        
        let mut file = match fs::File::create(&save_path) {
            Ok(f) => f,
            Err(e) => return Err(format!("Failed to create file: {}", e)),
        };
        
        match file.write_all(json.as_bytes()) {
            Ok(_) => Ok(()),
            Err(e) => Err(format!("Failed to write to file: {}", e)),
        }
    }
    
    fn load_project(&self, path: &Path) -> Result<Project, String> {
        let mut file = match fs::File::open(path) {
            Ok(f) => f,
            Err(e) => return Err(format!("Failed to open file: {}", e)),
        };
        
        let mut contents = String::new();
        if let Err(e) = file.read_to_string(&mut contents) {
            return Err(format!("Failed to read file: {}", e));
        }
        
        let mut project: Project = match serde_json::from_str(&contents) {
            Ok(p) => p,
            Err(e) => return Err(format!("Failed to parse project data: {}", e)),
        };
        
        // Update the file path
        project.file_path = Some(path.to_path_buf());
        
        Ok(project)
    }
    
    fn project_exists(&self, path: &Path) -> bool {
        path.exists() && path.is_file()
    }
}

impl ProjectManagement {
    /// Create a new ProjectManagement instance
    pub fn new() -> Self {
        let config_manager = TomlConfigManager::new(TomlConfigManager::default_config_path());
        let config = config_manager.load_config().unwrap_or_default();
        let engine_enabled = config.prisma_engine_initialization.enable_prisma_engine;
        
        let default_projects_dir = PathBuf::from(env!("CARGO_MANIFEST_DIR"))
            .join("projects");
        
        if !default_projects_dir.exists() {
            if let Err(e) = fs::create_dir_all(&default_projects_dir) {
                eprintln!("Warning: Failed to create projects directory: {}", e);
            }
        }
        
        Self {
            config_manager,
            projects: HashMap::new(),
            current_project_id: None,
            default_projects_dir,
            engine_enabled,
        }
    }
    
    /// Get the current active project
    pub fn current_project(&self) -> Option<&Project> {
        self.current_project_id.as_ref().and_then(|id| self.projects.get(id))
    }
    
    /// Set the current active project
    pub fn set_current_project(&mut self, project_id: &str) -> Result<(), String> {
        if self.projects.contains_key(project_id) {
            self.current_project_id = Some(project_id.to_string());
            Ok(())
        } else {
            Err(format!("Project with ID {} not found", project_id))
        }
    }
    
    /// Generate a project summary from a project
    fn generate_summary(&self, project: &Project) -> ProjectSummary {
        let total_messages = project.sessions.values()
            .map(|session| session.total_messages)
            .sum();
            
        ProjectSummary {
            id: project.id.clone(),
            name: project.name.clone(),
            description: project.description.clone(),
            created_at: project.created_at,
            updated_at: project.updated_at,
            session_count: project.sessions.len(),
            total_messages,
            status: ProjectStatus::Active, // Assuming active by default
        }
    }
    
    /// Load all projects from the default directory
    pub fn load_all_projects(&mut self) -> Result<Vec<ProjectSummary>, String> {
        let persistence = FileProjectPersistence::default();
        
        if !self.default_projects_dir.exists() {
            return Ok(vec![]);
        }
        
        let entries = match fs::read_dir(&self.default_projects_dir) {
            Ok(entries) => entries,
            Err(e) => return Err(format!("Failed to read projects directory: {}", e)),
        };
        
        let mut summaries = Vec::new();
        
        for entry in entries {
            let entry = match entry {
                Ok(e) => e,
                Err(_) => continue, // Skip entries with errors
            };
            
            let path = entry.path();
            if path.is_file() && path.extension().map_or(false, |ext| ext == "json") {
                match persistence.load_project(&path) {
                    Ok(project) => {
                        let project_id = project.id.clone();
                        let summary = self.generate_summary(&project);
                        summaries.push(summary);
                        self.projects.insert(project_id, project);
                    },
                    Err(e) => {
                        eprintln!("Failed to load project from {}: {}", path.display(), e);
                        // Continue loading other projects
                    }
                }
            }
        }
        
        Ok(summaries)
    }
    
    pub fn is_engine_enabled(&self) -> bool {
        self.engine_enabled
    }
}

impl ProjectManager for ProjectManagement {
    fn create_project(&mut self, params: ProjectParams) -> ProjectResponse {
        let persistence = FileProjectPersistence::default();
        
        // Generate a unique ID using current timestamp and a random number
        let timestamp = Utc::now().timestamp();
        let random = rand::random::<u32>();
        let project_id = format!("project_{}_{}", timestamp, random);
        
        let now = Utc::now();
        
        let config = match self.config_manager.load_default_config() {
            Ok(config) => config,
            Err(e) => return ProjectResponse {
                success: false,
                message: format!("Failed to load default config: {}", e),
                project: None,
            },
        };
        
        // Create a file path for the new project
        let file_name = format!("{}.json", project_id);
        let file_path = self.default_projects_dir.join(file_name);
        
        let project = Project {
            id: project_id.clone(),
            name: params.name,
            description: params.description,
            created_at: now,
            updated_at: now,
            config,
            sessions: HashMap::new(),
            file_path: Some(file_path.clone()),
        };
        
        // Save the project
        match persistence.save_project(&project, Some(&file_path)) {
            Ok(_) => {
                self.projects.insert(project_id.clone(), project.clone());
                self.current_project_id = Some(project_id);
                
                ProjectResponse {
                    success: true,
                    message: "Project created and saved successfully".to_string(),
                    project: Some(project),
                }
            },
            Err(e) => ProjectResponse {
                success: false,
                message: format!("Project created but failed to save: {}", e),
                project: Some(project),
            },
        }
    }
    
    fn load_project(&mut self, path: &Path) -> ProjectResponse {
        let persistence = FileProjectPersistence::default();
        
        if !persistence.project_exists(path) {
            return ProjectResponse {
                success: false,
                message: format!("Project file does not exist at path: {}", path.display()),
                project: None,
            };
        }
        
        match persistence.load_project(path) {
            Ok(project) => {
                // Add to projects and set as current
                let project_id = project.id.clone();
                self.projects.insert(project_id.clone(), project.clone());
                self.current_project_id = Some(project_id);
                
                ProjectResponse {
                    success: true,
                    message: "Project loaded successfully".to_string(),
                    project: Some(project),
                }
            },
            Err(e) => ProjectResponse {
                success: false,
                message: format!("Failed to load project: {}", e),
                project: None,
            },
        }
    }
    
    fn save_project(&self, project_id: &str, path: Option<&Path>) -> ProjectResponse {
        let persistence = FileProjectPersistence::default();
        
        if let Some(project) = self.projects.get(project_id) {
            let save_path = if let Some(p) = path {
                p
            } else if let Some(p) = &project.file_path {
                p.as_path()
            } else {
                // Create a default path
                let file_name = format!("{}.json", project_id);
                let default_path = self.default_projects_dir.join(file_name);
                default_path.as_path()
            };
            
            match persistence.save_project(project, Some(save_path)) {
                Ok(_) => {
                    ProjectResponse {
                        success: true,
                        message: "Project saved successfully".to_string(),
                        project: Some(project.clone()),
                    }
                },
                Err(e) => ProjectResponse {
                    success: false,
                    message: format!("Failed to save project: {}", e),
                    project: Some(project.clone()),
                },
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn get_project(&self, project_id: &str) -> Option<Project> {
        self.projects.get(project_id).cloned()
    }
    
    fn list_projects(&self) -> Vec<ProjectSummary> {
        self.projects.values()
            .map(|p| self.generate_summary(p))
            .collect()
    }
    
    fn update_project(&mut self, project_id: &str, params: ProjectParams) -> ProjectResponse {
        if let Some(project) = self.projects.get_mut(project_id) {
            project.name = params.name;
            project.description = params.description;
            project.updated_at = Utc::now();
            
            // Save the updated project
            let response = self.save_project(project_id, None);
            if response.success {
                ProjectResponse {
                    success: true,
                    message: "Project updated and saved successfully".to_string(),
                    project: Some(project.clone()),
                }
            } else {
                response
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn set_project_status(&mut self, project_id: &str, status: ProjectStatus) -> ProjectResponse {
        // In a real implementation, you would update the project status in the project object
        // For now, we'll just check if the project exists and return a response
        if let Some(project) = self.projects.get(project_id) {
            ProjectResponse {
                success: true,
                message: format!("Project status updated to {:?}", status),
                project: Some(project.clone()),
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn add_session(&mut self, project_id: &str, session: ChatSessionMetadata) -> ProjectResponse {
        if let Some(project) = self.projects.get_mut(project_id) {
            project.sessions.insert(session.session_id.clone(), session);
            project.updated_at = Utc::now();
            
            // Save the updated project
            let response = self.save_project(project_id, None);
            if response.success {
                ProjectResponse {
                    success: true,
                    message: "Session added to project and saved successfully".to_string(),
                    project: Some(project.clone()),
                }
            } else {
                response
            }
        } else {
            ProjectResponse {
                success: false,
                message: format!("Project with ID {} not found", project_id),
                project: None,
            }
        }
    }
    
    fn get_session(&self, project_id: &str, session_id: &str) -> Option<ChatSessionMetadata> {
        self.projects.get(project_id)
            .and_then(|p| p.sessions.get(session_id).cloned())
    }
    
    fn export_to_json(&self, project_id: &str) -> Result<String, String> {
        if let Some(project) = self.projects.get(project_id) {
            match serde_json::to_string_pretty(project) {
                Ok(json) => Ok(json),
                Err(e) => Err(format!("Failed to serialize project to JSON: {}", e)),
            }
        } else {
            Err(format!("Project with ID {} not found", project_id))
        }
    }
}

impl ProjectConfigManager for ProjectManagement {
    fn get_new_project_config(&self) -> Result<NewProjectConfig, String> {
        match self.config_manager.load_default_config() {
            Ok(config) => Ok(config.new_project),
            Err(e) => Err(format!("Failed to load new project config: {}", e))
        }
    }

    fn get_embedding_model_config(&self) -> Result<EmbeddingModelConfig, String> {
        self.get_new_project_config()
            .map(|config| config.embedding_model)
    }

    fn validate_form_fields(&self, fields: &[FormField]) -> Result<(), String> {
        // Implement validation logic
        Ok(())
    }
}
