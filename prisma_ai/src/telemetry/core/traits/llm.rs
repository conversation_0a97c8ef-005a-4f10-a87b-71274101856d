use crate::telemetry::core::types::llm::{LLMConfig, LLMResponse, LLMProviderType, LLMMetrics, LoRAMetrics, EmbeddingMetrics};
use async_trait::async_trait;
use std::time::Duration;
use log::{Level, Record};
use serde_json::Value;

/// Trait defining the interface for LLM providers
#[async_trait]
pub trait LLMProvider {
    /// Initialize a new LLM provider with the given configuration
    fn new(config: LLMConfig) -> Self where Self: Sized;
    
    /// Get the type of this LLM provider
    fn get_provider_type(&self) -> LLMProviderType;
    
    /// Get the configuration of this LLM provider
    fn get_config(&self) -> &LLMConfig;
    
    /// Send a prompt to the LLM and get a response
    async fn send_prompt(&self, prompt: &str) -> Result<LLMResponse, String>;
    
    /// Check if the provider is properly configured
    fn is_configured(&self) -> bool;
}

/// Trait for collecting LLM metrics
#[async_trait]
pub trait LLMMetricCollector {
    /// Collect metrics for an LLM inference operation
    async fn collect_inference_metrics(&self, 
        model_name: &str, 
        inference_time: Duration,
        total_tokens: usize,
        prompt_tokens: usize,
        completion_tokens: usize
    ) -> LLMMetrics;
    
    /// Record LLM usage statistics
    async fn record_llm_usage(&self, metrics: &LLMMetrics) -> Result<(), String>;
    
    /// Get aggregated metrics for a specific period
    async fn get_metrics_for_period(&self, period: Duration) -> Result<Vec<LLMMetrics>, String>;
}

/// Trait for LoRA adapter metrics
#[async_trait]
pub trait LoRAMetricCollector {
    /// Collect metrics for a LoRA adapter operation
    async fn collect_lora_metrics(&self, 
        adapter_name: &str,
        loading_time: Duration,
        memory_usage: usize
    ) -> LoRAMetrics;
    
    /// Record LoRA adapter usage
    async fn record_lora_usage(&self, metrics: &LoRAMetrics) -> Result<(), String>;
}

/// Trait for embedding model metrics
#[async_trait]
pub trait EmbeddingMetricCollector {
    /// Collect metrics for an embedding generation operation
    async fn collect_embedding_metrics(&self,
        model_name: &str,
        generation_time: Duration,
        dimensions: usize
    ) -> EmbeddingMetrics;
    
    /// Record embedding model usage
    async fn record_embedding_usage(&self, metrics: &EmbeddingMetrics) -> Result<(), String>;
}

/// Trait for LLM logging functionality
#[async_trait]
pub trait LLMLogger {
    /// Log an LLM request
    async fn log_request(&self, provider_type: &LLMProviderType, prompt: &str, config: &LLMConfig) -> Result<(), String>;
    
    /// Log an LLM response
    async fn log_response(&self, provider_type: &LLMProviderType, response: &LLMResponse, latency: Duration) -> Result<(), String>;
    
    /// Log an LLM error
    async fn log_error(&self, provider_type: &LLMProviderType, error: &str, prompt: &str) -> Result<(), String>;
    
    /// Log usage metrics
    async fn log_usage(&self, metrics: &LLMMetrics) -> Result<(), String>;
}

/// Trait for formatted LLM logging
pub trait LLMLogFormatter {
    /// Create a new formatter instance
    fn new() -> Self where Self: Sized;
    
    /// Format an LLM request for logging
    fn format_request(&self, provider_type: &LLMProviderType, prompt: &str, config: &LLMConfig) -> String;
    
    /// Format an LLM response for logging
    fn format_response(&self, provider_type: &LLMProviderType, response: &LLMResponse, latency: Duration) -> String;
    
    /// Format an LLM error for logging
    fn format_error(&self, provider_type: &LLMProviderType, error: &str, prompt: &str) -> String;
    
    /// Format usage metrics for logging
    fn format_usage(&self, metrics: &LLMMetrics) -> String;
}

/// Trait for LLM log filtering
pub trait LLMLogFilter {
    /// Determine if this log record should be processed
    fn should_log(&self, record: &Record) -> bool;
    
    /// Get the minimum log level
    fn min_level(&self) -> Level;
    
    /// Filter prompt content for sensitive information
    fn filter_prompt(&self, prompt: &str) -> String;
    
    /// Filter response content for sensitive information
    fn filter_response(&self, response: &str) -> String;
}

/// Trait for LLM dashboard data generation
pub trait LLMDashboardProvider {
    /// Get dashboard name
    fn get_name(&self) -> String;
    
    /// Get dashboard description
    fn get_description(&self) -> String;
    
    /// Generate dashboard JSON configuration
    fn generate_dashboard_config(&self) -> Result<Value, String>;
    
    /// Get metric queries for this dashboard
    fn get_metric_queries(&self) -> Vec<String>;
    
    /// Get panel configurations
    fn get_panels(&self) -> Vec<DashboardPanel>;
    
    /// Get refresh interval in seconds
    fn get_refresh_interval(&self) -> u64;
}

/// Dashboard panel information
#[derive(Clone, Debug)]
pub struct DashboardPanel {
    pub title: String,
    pub panel_type: PanelType,
    pub query: String,
    pub width: u32,
    pub height: u32,
    pub position: (u32, u32), // (x, y) coordinates
}

/// Type of dashboard panel
#[derive(Clone, Debug)]
pub enum PanelType {
    TimeSeries,
    Gauge,
    Stat,
    Table,
    Heatmap,
    BarChart,
    PieChart,
    Custom(String),
}

/// Trait for dashboard export functionality
pub trait DashboardExporter {
    /// Export dashboard to a file
    fn export_to_file(&self, path: &str, dashboard: &dyn LLMDashboardProvider) -> Result<(), String>;
    
    /// Export dashboard to a monitoring system
    fn export_to_system(&self, dashboard: &dyn LLMDashboardProvider) -> Result<(), String>;
    
    /// Get supported export formats
    fn get_supported_formats(&self) -> Vec<String>;
}

/// Trait for dashboard customization
pub trait DashboardCustomization {
    /// Apply custom theme to dashboard
    fn apply_theme(&self, dashboard_config: &mut Value, theme: &str) -> Result<(), String>;
    
    /// Add annotations to dashboard
    fn add_annotations(&self, dashboard_config: &mut Value, annotations: Vec<String>) -> Result<(), String>;
    
    /// Set time range for dashboard
    fn set_time_range(&self, dashboard_config: &mut Value, from: &str, to: &str) -> Result<(), String>;
    
    /// Add variables to dashboard
    fn add_variables(&self, dashboard_config: &mut Value, variables: Vec<(String, String)>) -> Result<(), String>;
}

/// Alert severity levels
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum AlertSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// Alert notification channels
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AlertChannel {
    Email(String),
    Slack(String),
    Webhook(String),
    PagerDuty(String),
    Custom(String, String), // (channel_type, channel_details)
}

/// Alert condition type
#[derive(Debug, Clone)]
pub enum AlertCondition {
    Threshold {
        metric: String,
        operator: AlertOperator,
        value: f64,
        duration: Duration,
    },
    ErrorRate {
        threshold_percent: f64,
        window: Duration,
    },
    LatencySpike {
        threshold_ms: u64,
        percentile: u8, // 95 for p95, etc.
    },
    TokenUsage {
        limit: usize,
        window: Duration,
    },
    Custom(String), // Custom condition as a string expression
}

/// Alert operators for threshold conditions
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AlertOperator {
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    Equal,
    NotEqual,
}

/// Alert definition
#[derive(Debug, Clone)]
pub struct AlertDefinition {
    pub name: String,
    pub description: String,
    pub condition: AlertCondition,
    pub severity: AlertSeverity,
    pub channels: Vec<AlertChannel>,
    pub cooldown: Duration,
    pub enabled: bool,
    pub tags: Vec<String>,
}

/// Trait for LLM alert management
#[async_trait]
pub trait LLMAlertManager {
    /// Add a new alert definition
    async fn add_alert(&mut self, alert: AlertDefinition) -> Result<(), String>;
    
    /// Remove an alert by name
    async fn remove_alert(&mut self, name: &str) -> Result<(), String>;
    
    /// Update an existing alert
    async fn update_alert(&mut self, alert: AlertDefinition) -> Result<(), String>;
    
    /// Get all alert definitions
    async fn get_alerts(&self) -> Result<Vec<AlertDefinition>, String>;
    
    /// Enable or disable an alert
    async fn set_alert_enabled(&mut self, name: &str, enabled: bool) -> Result<(), String>;
    
    /// Check if any alerts are triggered based on current metrics
    async fn check_alerts(&self, metrics: &LLMMetrics) -> Result<Vec<(AlertDefinition, String)>, String>;
}

/// Trait for alert notifications
#[async_trait]
pub trait AlertNotifier {
    /// Send an alert notification
    async fn send_notification(&self, alert: &AlertDefinition, message: &str) -> Result<(), String>;
    
    /// Get notification history
    async fn get_notification_history(&self, limit: usize) -> Result<Vec<AlertNotification>, String>;
    
    /// Check if a notification is in cooldown
    fn is_in_cooldown(&self, alert_name: &str) -> bool;
}

/// A record of an alert notification
#[derive(Debug, Clone)]
pub struct AlertNotification {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub alert_name: String,
    pub severity: AlertSeverity,
    pub message: String,
    pub channels: Vec<AlertChannel>,
    pub acknowledged: bool,
    pub acknowledged_by: Option<String>,
    pub acknowledged_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// Trait for alert state storage
#[async_trait]
pub trait AlertStateStore {
    /// Save alert state
    async fn save_alert_state(&self, state: &AlertState) -> Result<(), String>;
    
    /// Load alert state
    async fn load_alert_state(&self, alert_name: &str) -> Result<Option<AlertState>, String>;
    
    /// Get all alert states
    async fn get_all_alert_states(&self) -> Result<Vec<AlertState>, String>;
    
    /// Delete alert state
    async fn delete_alert_state(&self, alert_name: &str) -> Result<(), String>;
}

/// State of an alert
#[derive(Debug, Clone)]
pub struct AlertState {
    pub alert_name: String,
    pub last_triggered: Option<chrono::DateTime<chrono::Utc>>,
    pub last_notification: Option<chrono::DateTime<chrono::Utc>>,
    pub current_status: AlertStatus,
    pub triggered_count: usize,
    pub acknowledged: bool,
}

/// Status of an alert
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AlertStatus {
    Normal,
    Triggered,
    Acknowledged,
    Resolved,
}
