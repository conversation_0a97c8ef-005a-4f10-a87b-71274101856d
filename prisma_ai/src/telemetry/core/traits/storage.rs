use std::sync::Arc;
use crate::telemetry::core::{
    config::Settings,
    types::storage::{SurrealDBEvent, SurrealDBMetrics},
    error::TelemetryResult,
};
use crate::telemetry::alerts::types::Alert;
use crate::telemetry::dashboards::Dashboard;

/// Trait defining the interface for sending alerts from database operations
pub trait StorageAlerts {
    /// Create a new instance of the alerts handler
    fn new(settings: Arc<Settings>) -> Self where Self: Sized;
    
    /// Process a database event and determine if an alert should be generated
    fn handle_event(&self, event: &SurrealDBEvent) -> Option<Alert>;
}

/// Trait defining the interface for logging database operations
pub trait StorageLogger {
    /// Create a new instance of the logger
    fn new(settings: Arc<Settings>) -> Self where Self: Sized;
    
    /// Log a query event with timing information
    fn log_query_event(&self, query_type: &str, table: &str, duration: f64, metadata: Option<std::collections::HashMap<String, String>>);
    
    /// Log a query error
    fn log_query_error(&self, query_type: &str, table: &str, error: &str, metadata: Option<std::collections::HashMap<String, String>>);
    
    /// Log a transaction event
    fn log_transaction_event(&self, operation_count: usize, duration: f64, metadata: Option<std::collections::HashMap<String, String>>);
    
    /// Log a transaction error
    fn log_transaction_error(&self, error: &str, metadata: Option<std::collections::HashMap<String, String>>);
}

/// Trait defining the interface for metrics collection from database operations
pub trait StorageMetrics {
    /// Create a new metrics collector
    fn new(settings: Arc<Settings>) -> TelemetryResult<Self> where Self: Sized;
    
    /// Record a new operation
    fn record_operation(&self);
    
    /// Record operation duration
    fn record_operation_duration(&self, duration: f64);
    
    /// Record query duration
    fn record_query_duration(&self, duration: f64);
    
    /// Set current metrics
    fn update_metrics(&self, metrics: &SurrealDBMetrics);
}

/// Trait defining the interface for tracing database operations
pub trait StorageTracing {
    /// Create a new tracer
    fn new(settings: Arc<Settings>) -> Self where Self: Sized;
    
    /// Trace a database query execution
    fn trace_query<T>(&self, query: &str, f: impl FnOnce() -> Result<T, surrealdb::Error>) -> Result<T, surrealdb::Error>;
    
    /// Trace a database transaction
    fn trace_transaction<T>(&self, f: impl FnOnce() -> Result<T, surrealdb::Error>) -> Result<T, surrealdb::Error>;
    
    /// Trace document operations (create, update, delete)
    fn trace_document_operation<T>(&self, operation: &str, table: &str, f: impl FnOnce() -> Result<T, surrealdb::Error>) -> Result<T, surrealdb::Error>;
}

/// Trait defining the interface for creating dashboards for storage telemetry
pub trait StorageDashboards {
    /// Create an overview dashboard
    fn overview_dashboard(&self) -> Dashboard;
    
    /// Create a performance dashboard
    fn performance_dashboard(&self) -> Dashboard;
    
    /// Create a query-focused dashboard 
    fn query_dashboard(&self) -> Dashboard;
    
    /// Create an error tracking dashboard
    fn error_dashboard(&self) -> Dashboard;
}
