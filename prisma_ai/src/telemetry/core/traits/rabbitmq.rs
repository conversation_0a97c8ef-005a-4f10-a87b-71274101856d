use std::collections::HashMap;
use std::sync::Arc;
use async_trait::async_trait;
use crate::telemetry::core::{TelemetryResult, types::{RabbitMQEvent, RabbitMQMetrics}};
// Change import to use the re-exported AlertLevel from the main telemetry module
use crate::telemetry::AlertLevel;

/// Common trait for all RabbitMQ telemetry components
#[async_trait]
pub trait RabbitMQTelemetryComponent {
    async fn connect(&self) -> TelemetryResult<()>;
    async fn disconnect(&self) -> TelemetryResult<()>;
    fn is_connected(&self) -> bool;
}

/// Trait for RabbitMQ telemetry components that handle events
#[async_trait]
pub trait RabbitMQEventHandler: RabbitMQTelemetryComponent {
    async fn handle_event(&self, event: RabbitMQEvent) -> TelemetryResult<()>;
    async fn subscribe_to_events(&self, event_types: Vec<String>) -> TelemetryResult<()>;
}

/// Trait for RabbitMQ telemetry components that collect metrics
#[async_trait]
pub trait RabbitMQMetricsCollector: RabbitMQTelemetryComponent {
    async fn collect_metrics(&self) -> TelemetryResult<RabbitMQMetrics>;
    async fn record_connection_metrics(&self, connection_id: &str, metrics: HashMap<String, f64>) -> TelemetryResult<()>;
    async fn record_channel_metrics(&self, connection_id: &str, channel_id: u16, metrics: HashMap<String, f64>) -> TelemetryResult<()>;
    async fn record_queue_metrics(&self, queue_name: &str, metrics: HashMap<String, f64>) -> TelemetryResult<()>;
    async fn record_exchange_metrics(&self, exchange_name: &str, metrics: HashMap<String, f64>) -> TelemetryResult<()>;
    async fn record_message_metrics(&self, metrics: HashMap<String, f64>) -> TelemetryResult<()>;
}

/// Trait for RabbitMQ telemetry components that handle logging
#[async_trait]
pub trait RabbitMQLogger: RabbitMQTelemetryComponent {
    async fn log_connection_event(&self, event: &str, connection_id: &str, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
    async fn log_channel_event(&self, event: &str, connection_id: &str, channel_id: u16, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
    async fn log_queue_event(&self, event: &str, queue_name: &str, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
    async fn log_exchange_event(&self, event: &str, exchange_name: &str, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
    async fn log_message_event(&self, event: &str, exchange: &str, routing_key: &str, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
    async fn log_error(&self, component: &str, error: &str, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
}

/// Trait for RabbitMQ telemetry components that handle alerts
#[async_trait]
pub trait RabbitMQAlertHandler: RabbitMQTelemetryComponent {
    async fn send_alert(&self, level: AlertLevel, message: &str, metadata: Option<HashMap<String, String>>) -> TelemetryResult<()>;
    async fn setup_alert_channels(&self) -> TelemetryResult<()>;
    async fn check_thresholds(&self) -> TelemetryResult<()>;
}

/// Trait for RabbitMQ telemetry components that provide health checks
#[async_trait]
pub trait RabbitMQHealthCheck: RabbitMQTelemetryComponent {
    async fn check_connection(&self) -> TelemetryResult<bool>;
    async fn check_queues(&self, queue_names: &[String]) -> TelemetryResult<HashMap<String, bool>>;
    async fn get_health_status(&self) -> TelemetryResult<HashMap<String, String>>;
}
