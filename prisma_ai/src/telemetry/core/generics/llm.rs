//! Generic trait implementations for LLM telemetry
use async_trait::async_trait;
use chrono::Utc;
use log::{Level, Record};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;

use crate::telemetry::core::traits::llm::{
    AlertChannel, AlertCondition, AlertDefinition, AlertNotification, AlertOperator, AlertSeverity, AlertState, AlertStatus,
    AlertNotifier, AlertStateStore, DashboardPanel, LLMAlertManager, LLMDashboardProvider,
    LLMLogFilter, LLMLogFormatter, LLMLogger, LLMMetricCollector, LLMProvider, LoRAMetricCollector, 
    EmbeddingMetricCollector, PanelType
};
use crate::telemetry::core::types::llm::{
    EmbeddingMetrics, LLMConfig, LLMMetrics, LLMProviderType, LLMResponse, LoRAMetrics
};

/// Generic implementation of LLMMetricCollector
pub struct GenericLLMMetricCollector<S> {
    storage: S,
    metrics_cache: Arc<Mutex<Vec<LLMMetrics>>>,
}

impl<S> GenericLLMMetricCollector<S> {
    pub fn new(storage: S) -> Self {
        Self {
            storage,
            metrics_cache: Arc::new(Mutex::new(Vec::new())),
        }
    }
}

#[async_trait]
impl<S: Send + Sync> LLMMetricCollector for GenericLLMMetricCollector<S> 
where 
    S: Send + Sync
{
    async fn collect_inference_metrics(
        &self, 
        model_name: &str, 
        inference_time: Duration,
        total_tokens: usize,
        prompt_tokens: usize,
        completion_tokens: usize
    ) -> LLMMetrics {
        let tokens_per_second = if inference_time.as_secs_f64() > 0.0 {
            total_tokens as f64 / inference_time.as_secs_f64()
        } else {
            0.0
        };
        
        LLMMetrics {
            model_name: model_name.to_string(),
            inference_time,
            tokens_per_second,
            total_tokens,
            prompt_tokens,
            completion_tokens,
            batch_size: 1, // Default value
            memory_used: 0, // Default value
        }
    }
    
    async fn record_llm_usage(&self, metrics: &LLMMetrics) -> Result<(), String> {
        let mut cache = self.metrics_cache.lock().map_err(|e| e.to_string())?;
        cache.push(metrics.clone());
        Ok(())
    }
    
    async fn get_metrics_for_period(&self, _period: Duration) -> Result<Vec<LLMMetrics>, String> {
        let cache = self.metrics_cache.lock().map_err(|e| e.to_string())?;
        Ok(cache.clone())
    }
}

/// Generic implementation of LoRAMetricCollector
pub struct GenericLoRAMetricCollector<S> {
    storage: S,
    metrics_cache: Arc<Mutex<Vec<LoRAMetrics>>>,
}

impl<S> GenericLoRAMetricCollector<S> {
    pub fn new(storage: S) -> Self {
        Self {
            storage,
            metrics_cache: Arc::new(Mutex::new(Vec::new())),
        }
    }
}

#[async_trait]
impl<S: Send + Sync> LoRAMetricCollector for GenericLoRAMetricCollector<S> {
    async fn collect_lora_metrics(
        &self, 
        adapter_name: &str,
        loading_time: Duration,
        memory_usage: usize
    ) -> LoRAMetrics {
        LoRAMetrics {
            adapter_name: adapter_name.to_string(),
            loading_time,
            memory_usage,
            switch_time: Duration::from_secs(0), // Default value
        }
    }
    
    async fn record_lora_usage(&self, metrics: &LoRAMetrics) -> Result<(), String> {
        let mut cache = self.metrics_cache.lock().map_err(|e| e.to_string())?;
        cache.push(metrics.clone());
        Ok(())
    }
}

/// Generic implementation of EmbeddingMetricCollector
pub struct GenericEmbeddingMetricCollector<S> {
    storage: S,
    metrics_cache: Arc<Mutex<Vec<EmbeddingMetrics>>>,
}

impl<S> GenericEmbeddingMetricCollector<S> {
    pub fn new(storage: S) -> Self {
        Self {
            storage,
            metrics_cache: Arc::new(Mutex::new(Vec::new())),
        }
    }
}

#[async_trait]
impl<S: Send + Sync> EmbeddingMetricCollector for GenericEmbeddingMetricCollector<S> {
    async fn collect_embedding_metrics(
        &self,
        model_name: &str,
        generation_time: Duration,
        dimensions: usize
    ) -> EmbeddingMetrics {
        EmbeddingMetrics {
            model_name: model_name.to_string(),
            generation_time,
            batch_size: 1, // Default value
            total_dimensions: dimensions,
            memory_used: 0, // Default value
        }
    }
    
    async fn record_embedding_usage(&self, metrics: &EmbeddingMetrics) -> Result<(), String> {
        let mut cache = self.metrics_cache.lock().map_err(|e| e.to_string())?;
        cache.push(metrics.clone());
        Ok(())
    }
}

/// Generic implementation of LLMLogger
pub struct GenericLLMLogger<F: LLMLogFormatter, L: LLMLogFilter> {
    formatter: F,
    filter: L,
}

impl<F: LLMLogFormatter, L: LLMLogFilter> GenericLLMLogger<F, L> {
    pub fn new(formatter: F, filter: L) -> Self {
        Self {
            formatter,
            filter,
        }
    }
}

#[async_trait]
impl<F: LLMLogFormatter + Send + Sync, L: LLMLogFilter + Send + Sync> LLMLogger for GenericLLMLogger<F, L> {
    async fn log_request(&self, provider_type: &LLMProviderType, prompt: &str, config: &LLMConfig) -> Result<(), String> {
        let formatted = self.formatter.format_request(provider_type, prompt, config);
        log::info!("{}", formatted);
        Ok(())
    }
    
    async fn log_response(&self, provider_type: &LLMProviderType, response: &LLMResponse, latency: Duration) -> Result<(), String> {
        let formatted = self.formatter.format_response(provider_type, response, latency);
        log::info!("{}", formatted);
        Ok(())
    }
    
    async fn log_error(&self, provider_type: &LLMProviderType, error: &str, prompt: &str) -> Result<(), String> {
        let filtered_prompt = self.filter.filter_prompt(prompt);
        let formatted = self.formatter.format_error(provider_type, error, &filtered_prompt);
        log::error!("{}", formatted);
        Ok(())
    }
    
    async fn log_usage(&self, metrics: &LLMMetrics) -> Result<(), String> {
        let formatted = self.formatter.format_usage(metrics);
        log::info!("{}", formatted);
        Ok(())
    }
}

/// Generic implementation of LLMLogFormatter
pub struct GenericLLMLogFormatter;

impl LLMLogFormatter for GenericLLMLogFormatter {
    fn new() -> Self {
        Self {}
    }
    
    fn format_request(&self, provider_type: &LLMProviderType, prompt: &str, config: &LLMConfig) -> String {
        format!("[LLM:{}] Request with prompt (length: {}) and config: {:?}", 
                format!("{:?}", provider_type), prompt.len(), config)
    }
    
    fn format_response(&self, provider_type: &LLMProviderType, response: &LLMResponse, latency: Duration) -> String {
        format!("[LLM:{}] Response received in {}ms: {} tokens generated", 
                format!("{:?}", provider_type), latency.as_millis(), response.token_count)
    }
    
    fn format_error(&self, provider_type: &LLMProviderType, error: &str, prompt: &str) -> String {
        format!("[LLM:{}] Error: {} for prompt: {}", 
                format!("{:?}", provider_type), error, prompt)
    }
    
    fn format_usage(&self, metrics: &LLMMetrics) -> String {
        format!("[LLM:{}] Usage: {}ms, {} tokens ({} prompt, {} completion), {} tokens/s", 
                metrics.model_name, metrics.inference_time.as_millis(), metrics.total_tokens, 
                metrics.prompt_tokens, metrics.completion_tokens, metrics.tokens_per_second)
    }
}

/// Generic implementation of LLMLogFilter
pub struct GenericLLMLogFilter {
    min_level: Level,
    sensitive_patterns: Vec<String>,
}

impl GenericLLMLogFilter {
    pub fn new(min_level: Level, sensitive_patterns: Vec<String>) -> Self {
        Self {
            min_level,
            sensitive_patterns,
        }
    }
}

impl LLMLogFilter for GenericLLMLogFilter {
    fn should_log(&self, record: &Record) -> bool {
        record.level() >= self.min_level()
    }
    
    fn min_level(&self) -> Level {
        self.min_level
    }
    
    fn filter_prompt(&self, prompt: &str) -> String {
        let mut filtered = prompt.to_string();
        for pattern in &self.sensitive_patterns {
            filtered = filtered.replace(pattern, "[REDACTED]");
        }
        filtered
    }
    
    fn filter_response(&self, response: &str) -> String {
        let mut filtered = response.to_string();
        for pattern in &self.sensitive_patterns {
            filtered = filtered.replace(pattern, "[REDACTED]");
        }
        filtered
    }
}

/// Generic implementation of LLMAlertManager
pub struct GenericLLMAlertManager<N: AlertNotifier, S: AlertStateStore> {
    alerts: Arc<Mutex<HashMap<String, AlertDefinition>>>,
    notifier: N,
    state_store: S,
}

impl<N: AlertNotifier, S: AlertStateStore> GenericLLMAlertManager<N, S> {
    pub fn new(notifier: N, state_store: S) -> Self {
        Self {
            alerts: Arc::new(Mutex::new(HashMap::new())),
            notifier,
            state_store,
        }
    }
}

#[async_trait]
impl<N: AlertNotifier + Send + Sync, S: AlertStateStore + Send + Sync> LLMAlertManager for GenericLLMAlertManager<N, S> {
    async fn add_alert(&mut self, alert: AlertDefinition) -> Result<(), String> {
        let mut alerts = self.alerts.lock().map_err(|e| e.to_string())?;
        alerts.insert(alert.name.clone(), alert);
        Ok(())
    }
    
    async fn remove_alert(&mut self, name: &str) -> Result<(), String> {
        let mut alerts = self.alerts.lock().map_err(|e| e.to_string())?;
        alerts.remove(name);
        self.state_store.delete_alert_state(name).await?;
        Ok(())
    }
    
    async fn update_alert(&mut self, alert: AlertDefinition) -> Result<(), String> {
        let mut alerts = self.alerts.lock().map_err(|e| e.to_string())?;
        alerts.insert(alert.name.clone(), alert);
        Ok(())
    }
    
    async fn get_alerts(&self) -> Result<Vec<AlertDefinition>, String> {
        let alerts = self.alerts.lock().map_err(|e| e.to_string())?;
        Ok(alerts.values().cloned().collect())
    }
    
    async fn set_alert_enabled(&mut self, name: &str, enabled: bool) -> Result<(), String> {
        let mut alerts = self.alerts.lock().map_err(|e| e.to_string())?;
        if let Some(alert) = alerts.get_mut(name) {
            alert.enabled = enabled;
            Ok(())
        } else {
            Err(format!("Alert '{}' not found", name))
        }
    }
    
    async fn check_alerts(&self, metrics: &LLMMetrics) -> Result<Vec<(AlertDefinition, String)>, String> {
        let alerts = self.alerts.lock().map_err(|e| e.to_string())?;
        let mut triggered = Vec::new();
        
        for alert in alerts.values() {
            if !alert.enabled {
                continue;
            }
            
            // Check if the alert condition is triggered
            let is_triggered = match &alert.condition {
                AlertCondition::Threshold { metric, operator, value, .. } => {
                    let metric_value = match metric.as_str() {
                        "inference_time" => metrics.inference_time.as_secs_f64(),
                        "tokens_per_second" => metrics.tokens_per_second,
                        "total_tokens" => metrics.total_tokens as f64,
                        "memory_used" => metrics.memory_used as f64,
                        _ => continue,
                    };
                    
                    match operator {
                        AlertOperator::GreaterThan => metric_value > *value,
                        AlertOperator::LessThan => metric_value < *value,
                        AlertOperator::GreaterThanOrEqual => metric_value >= *value,
                        AlertOperator::LessThanOrEqual => metric_value <= *value,
                        AlertOperator::Equal => (metric_value - value).abs() < f64::EPSILON,
                        AlertOperator::NotEqual => (metric_value - value).abs() >= f64::EPSILON,
                    }
                },
                // Other condition types would be handled here
                _ => false,
            };
            
            if is_triggered {
                let message = format!("Alert '{}' triggered: {}", alert.name, alert.description);
                
                // Check if the alert is in cooldown
                if !self.notifier.is_in_cooldown(&alert.name) {
                    // Update alert state
                    let state = AlertState {
                        alert_name: alert.name.clone(),
                        last_triggered: Some(Utc::now()),
                        last_notification: Some(Utc::now()),
                        current_status: AlertStatus::Triggered,
                        triggered_count: 1,
                        acknowledged: false,
                    };
                    self.state_store.save_alert_state(&state).await?;
                    
                    // Send notification
                    self.notifier.send_notification(alert, &message).await?;
                    
                    // Add to triggered alerts
                    triggered.push((alert.clone(), message));
                }
            }
        }
        
        Ok(triggered)
    }
}

/// Generic implementation of LLMDashboardProvider
pub struct GenericLLMDashboardProvider {
    name: String,
    description: String,
    panels: Vec<DashboardPanel>,
    refresh_interval: u64,
}

impl GenericLLMDashboardProvider {
    pub fn new(name: String, description: String, refresh_interval: u64) -> Self {
        Self {
            name,
            description,
            panels: Vec::new(),
            refresh_interval,
        }
    }
    
    pub fn add_panel(&mut self, panel: DashboardPanel) {
        self.panels.push(panel);
    }
}

impl LLMDashboardProvider for GenericLLMDashboardProvider {
    fn get_name(&self) -> String {
        self.name.clone()
    }
    
    fn get_description(&self) -> String {
        self.description.clone()
    }
    
    fn generate_dashboard_config(&self) -> Result<Value, String> {
        // Generate a simple dashboard configuration
        let mut panels_json = Vec::new();
        
        for panel in &self.panels {
            let panel_json = serde_json::json!({
                "title": panel.title,
                "type": format!("{:?}", panel.panel_type),
                "query": panel.query,
                "gridPos": {
                    "x": panel.position.0,
                    "y": panel.position.1,
                    "w": panel.width,
                    "h": panel.height
                }
            });
            panels_json.push(panel_json);
        }
        
        let dashboard_json = serde_json::json!({
            "dashboard": {
                "id": null,
                "title": self.name,
                "description": self.description,
                "panels": panels_json,
                "refresh": self.refresh_interval,
                "schemaVersion": 30,
                "time": {
                    "from": "now-6h",
                    "to": "now"
                }
            },
            "overwrite": true
        });
        
        Ok(dashboard_json)
    }
    
    fn get_metric_queries(&self) -> Vec<String> {
        self.panels
            .iter()
            .map(|panel| panel.query.clone())
            .collect()
    }
    
    fn get_panels(&self) -> Vec<DashboardPanel> {
        self.panels.clone()
    }
    
    fn get_refresh_interval(&self) -> u64 {
        self.refresh_interval
    }
}
