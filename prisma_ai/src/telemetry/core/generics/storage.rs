use std::sync::Arc;
use std::time::Instant;
use std::collections::HashMap;
use crate::telemetry::core::{
    config::Settings,
    types::storage::{SurrealDBEvent, SurrealDBEventType, SurrealDBMetrics},
    error::TelemetryResult,
    traits::storage::{StorageAlerts, StorageLogger, StorageMetrics, StorageTracing},
};
use crate::telemetry::alerts::types::{Alert, AlertLevel};
use tracing::{info, warn, error};

/// Generic implementation for handling database events across different storage backends
pub struct GenericStorageEventHandler<A, L, M, T>
where
    A: StorageAlerts,
    L: StorageLogger,
    M: StorageMetrics,
    T: StorageTracing,
{
    /// Alert handler
    alerts: A,
    /// Logger for events
    logger: L,
    /// Metrics collector
    metrics: M,
    /// Tracer for distributed tracing
    tracer: T,
    /// Shared settings
    settings: Arc<Settings>,
}

impl<A, L, M, T> GenericStorageEventHandler<A, L, M, T>
where
    A: StorageAlerts,
    L: StorageLogger,
    M: StorageMetrics,
    T: StorageTracing,
{
    pub fn new(settings: Arc<Settings>) -> TelemetryResult<Self> {
        Ok(Self {
            alerts: A::new(Arc::clone(&settings)),
            logger: L::new(Arc::clone(&settings)),
            metrics: M::new(Arc::clone(&settings))?,
            tracer: T::new(Arc::clone(&settings)),
            settings,
        })
    }

    /// Process a database event
    pub fn process_event(&self, event: &SurrealDBEvent) {
        // Log the event
        match &event.event_type {
            SurrealDBEventType::QueryExecuted => {
                let duration = event.duration_ms.unwrap_or(0) as f64 / 1000.0;
                let table = event.metadata.as_ref()
                    .and_then(|m| m.get("table").and_then(|t| t.as_str()).map(|s| s.to_string()))
                    .unwrap_or_else(|| "unknown".to_string());
                
                let mut metadata = HashMap::new();
                if let Some(db) = &event.database {
                    metadata.insert("database".to_string(), db.clone());
                }
                if let Some(ns) = &event.namespace {
                    metadata.insert("namespace".to_string(), ns.clone());
                }
                
                self.logger.log_query_event(
                    "query", 
                    &table, 
                    duration, 
                    Some(metadata)
                );
                self.metrics.record_query_duration(duration);
            }
            SurrealDBEventType::TransactionStarted | SurrealDBEventType::TransactionCommitted => {
                if let Some(duration) = event.duration_ms {
                    let duration_sec = duration as f64 / 1000.0;
                    let mut metadata = HashMap::new();
                    
                    if let Some(transaction_id) = &event.transaction_id {
                        metadata.insert("transaction_id".to_string(), transaction_id.clone());
                    }
                    
                    self.logger.log_transaction_event(
                        1, // Single operation count (simplified)
                        duration_sec,
                        Some(metadata)
                    );
                }
            }
            SurrealDBEventType::TransactionRollback => {
                let mut metadata = HashMap::new();
                
                if let Some(transaction_id) = &event.transaction_id {
                    metadata.insert("transaction_id".to_string(), transaction_id.clone());
                }
                if let Some(err) = &event.error {
                    metadata.insert("error".to_string(), err.clone());
                    self.logger.log_transaction_error(err, Some(metadata.clone()));
                }
            }
            _ => {
                // Handle other event types as needed
            }
        }
        
        // Check for alerts
        if let Some(alert) = self.alerts.handle_event(event) {
            // Log the alert
            match alert.level {
                AlertLevel::INFO => {
                    info!(
                        message = %alert.message,
                        module = %alert.module,
                        level = %alert.level.to_string(),
                        metadata = ?alert.metadata,
                        "Alert generated"
                    );
                }
                AlertLevel::WARN => {
                    warn!(
                        message = %alert.message,
                        module = %alert.module,
                        level = %alert.level.to_string(),
                        metadata = ?alert.metadata,
                        "Alert generated"
                    );
                }
                AlertLevel::ERROR => {
                    error!(
                        message = %alert.message,
                        module = %alert.module,
                        level = %alert.level.to_string(),
                        metadata = ?alert.metadata,
                        "Alert generated"
                    );
                }
            }
        }
        
        // Record operation for metrics
        self.metrics.record_operation();
    }
    
    /// Execute a database query with telemetry
    pub fn execute_query<F, R>(&self, query: &str, table: &str, f: F) -> Result<R, surrealdb::Error>
    where
        F: FnOnce() -> Result<R, surrealdb::Error>,
    {
        let start = Instant::now();
        
        let result = self.tracer.trace_query(query, f);
        
        let duration = start.elapsed().as_secs_f64();
        self.metrics.record_operation_duration(duration);
        self.metrics.record_query_duration(duration);
        
        let mut metadata = HashMap::new();
        metadata.insert("table".to_string(), table.to_string());
        metadata.insert("duration_ms".to_string(), (duration * 1000.0).to_string());
        
        match &result {
            Ok(_) => {
                self.logger.log_query_event("query", table, duration, Some(metadata));
            }
            Err(e) => {
                self.logger.log_query_error("query", table, &e.to_string(), Some(metadata));
            }
        }
        
        result
    }
    
    /// Execute a database transaction with telemetry
    pub fn execute_transaction<F, R>(&self, f: F) -> Result<R, surrealdb::Error>
    where
        F: FnOnce() -> Result<R, surrealdb::Error>,
    {
        let start = Instant::now();
        
        let result = self.tracer.trace_transaction(f);
        
        let duration = start.elapsed().as_secs_f64();
        self.metrics.record_operation_duration(duration);
        
        let mut metadata = HashMap::new();
        metadata.insert("duration_ms".to_string(), (duration * 1000.0).to_string());
        
        match &result {
            Ok(_) => {
                self.logger.log_transaction_event(1, duration, Some(metadata));
            }
            Err(e) => {
                self.logger.log_transaction_error(&e.to_string(), Some(metadata));
            }
        }
        
        result
    }
}

/// A simple no-operation implementation of StorageMetrics for environments
/// where metrics collection is not needed or is disabled
pub struct NoOpStorageMetrics {}

impl StorageMetrics for NoOpStorageMetrics {
    fn new(_settings: Arc<Settings>) -> TelemetryResult<Self> {
        Ok(Self {})
    }
    
    fn record_operation(&self) {
        // No-op
    }
    
    fn record_operation_duration(&self, _duration: f64) {
        // No-op
    }
    
    fn record_query_duration(&self, _duration: f64) {
        // No-op
    }
    
    fn update_metrics(&self, _metrics: &SurrealDBMetrics) {
        // No-op
    }
}
