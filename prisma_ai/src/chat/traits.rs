// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/chat/traits.rs
// =================================================================================================
// Purpose: Defines the core interfaces for chat metadata tracking, session management, and
// persistence. These traits establish the contract that implementations must follow to provide
// chat metadata functionality.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses ChatMetadataSummary, ChatSessionMetadata, and Participant types
// - err: Uses PrismaResult for error handling
// =================================================================================================
// External Dependencies:
// - std::collections: For HashMap to store session summaries
// =================================================================================================
// Module Interactions:
// - Implemented by chat.rs for the main ChatMetadata functionality
// - Implemented by generics.rs for generic implementations
// - Implemented by surrealdb_persistence.rs for database persistence
// - Used by UI components to interact with chat metadata
// - Provides a consistent interface for different persistence mechanisms
// =================================================================================================

use crate::chat::types::{ChatMetadataSummary, ChatSessionMetadata};
use crate::err::PrismaResult;
use std::collections::HashMap;

/// Trait for objects that can track chat session metadata
pub trait ChatMetadataTracker {
    /// Initialize a new chat session and start tracking metadata
    fn init_session(&mut self, project_id: &str, session_id: &str) -> PrismaResult<()>;

    /// Record a participant joining the chat
    fn record_participant_join(&mut self, participant_id: &str, name: &str, role: &str) -> PrismaResult<()>;

    /// Record a participant leaving the chat
    fn record_participant_leave(&mut self, participant_id: &str) -> PrismaResult<()>;

    /// Update the active status of a participant
    fn update_participant_activity(&mut self, participant_id: &str) -> PrismaResult<()>;

    /// Record a new message in the chat
    fn record_message(&mut self, participant_id: &str) -> PrismaResult<()>;

    /// End the current chat session
    fn end_session(&mut self) -> PrismaResult<()>;

    /// Get the current session metadata
    fn get_current_metadata(&self) -> PrismaResult<ChatSessionMetadata>;

    /// Generate a summary of the current session for UI display
    fn generate_summary(&self, project_name: &str) -> PrismaResult<ChatMetadataSummary>;

    /// Export the metadata to JSON
    fn export_to_json(&self) -> PrismaResult<String>;
}

/// Trait for objects that can manage multiple chat sessions
pub trait ChatSessionManager {
    /// Get a list of all session IDs
    fn list_sessions(&self) -> Vec<String>;

    /// Get metadata for a specific session
    fn get_session_metadata(&self, session_id: &str) -> Option<ChatSessionMetadata>;

    /// Get a summary of all sessions
    fn get_all_session_summaries(&self) -> HashMap<String, ChatMetadataSummary>;

    /// Export all session metadata to JSON
    fn export_all_to_json(&self) -> PrismaResult<String>;
}

/// Trait for handling metadata persistence
pub trait MetadataPersistence: std::any::Any {
    /// Save current metadata to storage
    fn save_metadata(&self, metadata: &ChatSessionMetadata) -> PrismaResult<()>;

    /// Load metadata from storage
    fn load_metadata(&self, session_id: &str) -> PrismaResult<ChatSessionMetadata>;

    /// Check if metadata exists for a session
    fn metadata_exists(&self, session_id: &str) -> bool;

    /// Get the TypeId of this implementation
    fn type_id(&self) -> std::any::TypeId {
        std::any::TypeId::of::<Self>()
    }
}
