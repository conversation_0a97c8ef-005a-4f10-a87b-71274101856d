// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/chat/mod.rs
// =================================================================================================
// Purpose: Main module file for the chat system, which provides functionality for tracking and
// managing chat session metadata such as participant information, message counts, and session
// duration. This module serves as the entry point for all chat-related functionality.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Defines core data structures for chat metadata
// - traits.rs: Defines interfaces for chat metadata tracking and persistence
// - chat.rs: Implements the main ChatMetadata functionality
// - generics.rs: Provides generic implementations of chat metadata tracking
// - surrealdb_persistence.rs: Implements SurrealDB-based persistence for chat metadata
// =================================================================================================
// External Dependencies:
// - serde_json: For serialization and deserialization of chat metadata
// - chrono: For timestamp handling and duration calculations
// - surrealdb: For database persistence of chat metadata
// =================================================================================================
// Module Interactions:
// - Used by UI components to track and display chat sessions
// - Interacts with storage systems for persistence of chat metadata
// - Provides metadata for analytics and reporting features
// - Supports both in-memory and database-backed persistence options
// ================================================================================================

pub mod types;
pub mod traits;
pub mod generics;
pub mod chat;
pub mod surrealdb_persistence;

pub use types::{
    ChatMetadataSummary,
    ChatSessionMetadata,
    Participant,
    ParticipantRole,
    MetadataTrackingConfig
};

pub use traits::{
    ChatMetadataTracker,
    ChatSessionManager,
    MetadataPersistence
};

pub use generics::GenericChatMetadataTracker;
pub use chat::ChatMetadata;
pub use surrealdb_persistence::SurrealDbMetadataPersistence;
