// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/chat/types.rs
// =================================================================================================
// Purpose: Defines the core data structures for chat metadata, including participants, sessions,
// and summary information. These types form the foundation of the chat metadata tracking system.
// =================================================================================================
// Internal Dependencies:
// - None (this is a foundational module that other modules depend on)
// =================================================================================================
// External Dependencies:
// - chrono: For timestamp handling and date/time operations
// - serde: For serialization and deserialization of chat metadata
// - std::collections: For HashMap to store participant information
// =================================================================================================
// Module Interactions:
// - Used by chat.rs to implement the main ChatMetadata functionality
// - Used by traits.rs to define interfaces that operate on these types
// - Used by generics.rs for generic implementations
// - Used by surrealdb_persistence.rs for database persistence
// - Used by UI components for displaying chat metadata
// =================================================================================================

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Represents a participant in a chat session
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Participant {
    pub id: String,
    pub name: String,
    pub role: ParticipantRole,
    pub join_time: DateTime<Utc>,
    pub last_active: DateTime<Utc>,
    pub message_count: u32,
    pub is_active: bool,
}

/// Defines the role of a participant in the chat
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ParticipantRole {
    User,
    Agent,
    System,
}

/// Tracks metadata for a single chat session
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatSessionMetadata {
    pub session_id: String,
    pub project_id: String,
    pub start_time: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub participants: HashMap<String, Participant>,
    pub total_messages: u32,
    pub active_duration_seconds: u64,
}

/// Represents a summary of chat metadata suitable for UI display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMetadataSummary {
    pub session_id: String,
    pub project_name: String,
    pub start_time: DateTime<Utc>,
    pub duration_minutes: u32,
    pub participant_count: u32,
    pub message_count: u32,
    pub user_message_count: u32,
    pub agent_message_count: u32,
}

/// Configuration for metadata tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetadataTrackingConfig {
    pub enable_participant_tracking: bool,
    pub enable_message_tracking: bool,
    pub enable_time_tracking: bool,
    pub tracking_interval_seconds: u32,
}
