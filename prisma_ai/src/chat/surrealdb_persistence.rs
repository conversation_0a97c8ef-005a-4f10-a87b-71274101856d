// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/chat/surrealdb_persistence.rs
// =================================================================================================
// Purpose: Implements the MetadataPersistence trait using SurrealDB as the backend storage system.
// This implementation provides a scalable, database-backed persistence mechanism for chat session
// metadata, supporting both synchronous and asynchronous operations.
// =================================================================================================
// Internal Dependencies:
// - traits.rs: Implements the MetadataPersistence trait
// - types.rs: Uses the ChatSessionMetadata type
// - err: Uses PrismaError, PrismaResult, and GenericError for error handling
// - storage/surrealdb: Uses SurrealDbConnection for database access
// =================================================================================================
// External Dependencies:
// - std::sync: For Arc (thread-safe reference counting)
// - async_trait: For async trait implementation
// - serde_json: For serialization and deserialization of metadata
// - std::collections: For HashMap to process query results
// - chrono: For timestamp handling
// - uuid: For generating unique identifiers
// - tokio: For runtime to execute async operations in sync contexts
// =================================================================================================
// Module Interactions:
// - Used by chat.rs to persist chat session metadata
// - Interacts with SurrealDB for database operations
// - Provides both synchronous and asynchronous interfaces
// - Supports listing all available sessions for metadata loading
// - Handles serialization and deserialization of metadata
// =================================================================================================

use std::sync::Arc;
use std::collections::HashMap;
use serde_json;

use crate::chat::traits::MetadataPersistence;
use crate::chat::types::ChatSessionMetadata;
use crate::err::{GenericError, PrismaResult};
use crate::err::types::errors_mod::PrismaError as DomainPrismaError;
use crate::storage::surrealdb::SurrealDbConnection;
use crate::storage::traits::DataStore;

/// SurrealDB implementation of MetadataPersistence
pub struct SurrealDbMetadataPersistence {
    /// Database connection
    db: Arc<SurrealDbConnection>,
    /// Table name for chat session metadata
    table_name: String,
}

impl SurrealDbMetadataPersistence {
    /// Create a new SurrealDbMetadataPersistence with the specified database connection
    pub fn new(db: Arc<SurrealDbConnection>) -> Self {
        Self {
            db,
            table_name: "chat_session_metadata".to_string(),
        }
    }

    /// Create a new SurrealDbMetadataPersistence with the specified database connection and table name
    pub fn with_table(db: Arc<SurrealDbConnection>, table_name: String) -> Self {
        Self {
            db,
            table_name,
        }
    }
}

impl MetadataPersistence for SurrealDbMetadataPersistence {
    /// Save current metadata to SurrealDB
    fn save_metadata(&self, metadata: &ChatSessionMetadata) -> PrismaResult<()> {
        // Convert to a future and block on it
        // This is necessary because the trait method is not async
        // In a real implementation, the trait would be updated to use async_trait
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(self.save_metadata_async(metadata))
    }

    /// Load metadata from SurrealDB
    fn load_metadata(&self, session_id: &str) -> PrismaResult<ChatSessionMetadata> {
        // Convert to a future and block on it
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(self.load_metadata_async(session_id))
    }

    /// Check if metadata exists for a session in SurrealDB
    fn metadata_exists(&self, session_id: &str) -> bool {
        // Convert to a future and block on it
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(self.metadata_exists_async(session_id))
            .unwrap_or(false)
    }
}

impl SurrealDbMetadataPersistence {
    /// Save metadata to SurrealDB asynchronously
    async fn save_metadata_async(&self, metadata: &ChatSessionMetadata) -> PrismaResult<()> {
        // Convert the metadata to a JSON value
        let data_value = serde_json::to_value(metadata)
            .map_err(|e| {
                let domain_error = DomainPrismaError::SerializationError(
                    format!("Failed to serialize chat session metadata: {}", e)
                );
                GenericError::from(domain_error)
            })?;

        // Check if the record already exists
        let exists = self.metadata_exists_async(&metadata.session_id).await?;

        if exists {
            // Update the existing record
            let _updated = self.db.update(
                self.table_name.as_str(),
                &metadata.session_id,
                &data_value
            ).await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(
                    format!("Failed to update chat session metadata: {}", e)
                );
                GenericError::from(domain_error)
            })?;
        } else {
            // Create a new record
            let _created = self.db.create(
                self.table_name.as_str(),
                &data_value
            ).await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(
                    format!("Failed to create chat session metadata: {}", e)
                );
                GenericError::from(domain_error)
            })?;
        }

        Ok(())
    }

    /// Load metadata from SurrealDB asynchronously
    async fn load_metadata_async(&self, session_id: &str) -> PrismaResult<ChatSessionMetadata> {
        // Query the database for the session metadata
        let metadata: Option<ChatSessionMetadata> = self.db.get(
            self.table_name.as_str(),
            session_id
        ).await
        .map_err(|e| {
            let domain_error = DomainPrismaError::DatabaseError(
                format!("Failed to load chat session metadata: {}", e)
            );
            GenericError::from(domain_error)
        })?;

        // Return the metadata or an error if not found
        metadata.ok_or_else(|| {
            let domain_error = DomainPrismaError::NotFoundError(
                format!("Chat session metadata not found for session ID: {}", session_id)
            );
            GenericError::from(domain_error)
        })
    }

    /// Check if metadata exists for a session in SurrealDB asynchronously
    async fn metadata_exists_async(&self, session_id: &str) -> PrismaResult<bool> {
        // Query the database to check if the session metadata exists
        let metadata: Option<ChatSessionMetadata> = self.db.get(
            self.table_name.as_str(),
            session_id
        ).await
        .map_err(|e| {
            let domain_error = DomainPrismaError::DatabaseError(
                format!("Failed to check if chat session metadata exists: {}", e)
            );
            GenericError::from(domain_error)
        })?;

        Ok(metadata.is_some())
    }

    /// List all available session IDs from SurrealDB asynchronously
    pub async fn list_session_ids_async(&self) -> PrismaResult<Vec<String>> {
        // Query the database for all session IDs
        let query = format!("SELECT session_id FROM {}", self.table_name);

        // Use the query method from the DataStore trait
        let results: Vec<HashMap<String, String>> = self.db.query(
            &query,
            &[] // No parameters needed for this query
        ).await
        .map_err(|e| {
            let domain_error = DomainPrismaError::DatabaseError(
                format!("Failed to query chat session IDs: {}", e)
            );
            GenericError::from(domain_error)
        })?;

        // Extract the session_id field from each result
        let session_ids = results
            .into_iter()
            .filter_map(|map| map.get("session_id").cloned())
            .collect();

        Ok(session_ids)
    }

    /// List all available session IDs from SurrealDB
    pub fn list_session_ids(&self) -> PrismaResult<Vec<String>> {
        // Convert to a future and block on it
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(self.list_session_ids_async())
    }
}
