// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/chat/chat.rs
// =================================================================================================
// Purpose: Implements the main ChatMetadata functionality, which provides a complete solution for
// tracking and managing chat session metadata. This implementation supports multiple persistence
// mechanisms, including SurrealDB, and provides comprehensive session management capabilities.
// =================================================================================================
// Internal Dependencies:
// - traits.rs: Implements ChatMetadataTracker, ChatSessionManager, and MetadataPersistence traits
// - types.rs: Uses ChatMetadataSummary, ChatSessionMetadata, Participant, and ParticipantRole types
// - surrealdb_persistence.rs: Integrates with SurrealDB for database persistence
// - err: Uses PrismaError and PrismaResult for error handling
// =================================================================================================
// External Dependencies:
// - chrono: For timestamp handling and duration calculations
// - serde_json: For serialization of metadata to JSON
// - std::collections: For HashMap to store sessions and participants
// - std::any: For type identification of persistence implementations
// =================================================================================================
// Module Interactions:
// - Used by UI components to track and display chat sessions
// - Interacts with persistence implementations for storage
// - Provides comprehensive session management capabilities
// - Supports both in-memory and database-backed persistence
// - Integrates with SurrealDB for scalable database persistence
// =================================================================================================

use crate::chat::traits::{ChatMetadataTracker, ChatSessionManager, MetadataPersistence};
use crate::chat::types::{ChatMetadataSummary, ChatSessionMetadata, Participant, ParticipantRole};
use crate::err::{PrismaResult, GenericError};
use crate::err::types::errors_mod::PrismaError;
use chrono::Utc;
use serde_json;
use std::collections::HashMap;
use std::any::Any;

/// Main implementation of chat metadata functionality
pub struct ChatMetadata {
    current_session: Option<ChatSessionMetadata>,
    persistence: Box<dyn MetadataPersistence>,
    sessions: HashMap<String, ChatSessionMetadata>,
}

impl ChatMetadata {
    /// Create a new ChatMetadata instance with the specified persistence mechanism
    pub fn new(persistence: Box<dyn MetadataPersistence>) -> Self {
        Self {
            current_session: None,
            persistence,
            sessions: HashMap::new(),
        }
    }

    /// Calculate the duration between two timestamps in seconds
    fn calculate_duration_seconds(&self, start: chrono::DateTime<Utc>, end: chrono::DateTime<Utc>) -> u64 {
        let duration = end.signed_duration_since(start);
        duration.num_seconds() as u64
    }

    /// Parse a role string into a ParticipantRole enum
    fn parse_role(&self, role: &str) -> ParticipantRole {
        match role.to_lowercase().as_str() {
            "agent" => ParticipantRole::Agent,
            "system" => ParticipantRole::System,
            _ => ParticipantRole::User,
        }
    }

    /// Load all available sessions from persistence
    pub fn load_all_sessions(&mut self) -> PrismaResult<()> {
        // Get a list of all available session IDs from the persistence layer
        // This would typically be implemented by the persistence provider
        // For example, listing all files in a directory or querying a database

        // Clear existing sessions to avoid duplicates
        self.sessions.clear();

        // Use the SurrealDB connection to query for all chat sessions
        // For each session ID, load the metadata and add it to the sessions map
        let session_ids = self.list_available_session_ids()?;

        for session_id in session_ids {
            // Skip if the session ID is empty
            if session_id.is_empty() {
                continue;
            }

            // Load the session metadata from persistence
            match self.persistence.load_metadata(&session_id) {
                Ok(metadata) => {
                    // Add the loaded metadata to the sessions map
                    self.sessions.insert(session_id, metadata);
                },
                Err(err) => {
                    // Log the error but continue loading other sessions
                    eprintln!("Failed to load session {}: {}", session_id, err);
                    // We don't return the error to allow partial loading of sessions
                }
            }
        }

        Ok(())
    }

    /// List all available session IDs from the persistence layer
    fn list_available_session_ids(&self) -> PrismaResult<Vec<String>> {
        // Check if the persistence implementation is SurrealDbMetadataPersistence
        // We can use dynamic dispatch to check the type at runtime
        if let Some(surrealdb_persistence) = self.get_surrealdb_persistence() {
            // Use the SurrealDB implementation to list session IDs
            surrealdb_persistence.list_session_ids()
        } else {
            // For other persistence implementations, we'll return an empty list
            // In a real implementation, this would be replaced with appropriate logic
            // based on the type of persistence being used

            // Example implementation for a file-based persistence:
            // let session_dir = std::path::Path::new("sessions");
            // if !session_dir.exists() {
            //     return Ok(vec![]);
            // }
            //
            // let mut session_ids = Vec::new();
            // for entry in std::fs::read_dir(session_dir)? {
            //     let entry = entry?;
            //     let path = entry.path();
            //     if path.is_file() && path.extension().map_or(false, |ext| ext == "json") {
            //         if let Some(file_stem) = path.file_stem() {
            //             if let Some(session_id) = file_stem.to_str() {
            //                 session_ids.push(session_id.to_string());
            //             }
            //         }
            //     }
            // }
            //
            // Ok(session_ids)

            // For now, return an empty list for non-SurrealDB persistence
            Ok(vec![])
        }
    }

    /// Get the SurrealDB persistence implementation if available
    fn get_surrealdb_persistence(&self) -> Option<&super::SurrealDbMetadataPersistence> {
        // Get a reference to the persistence object
        let persistence_ref = &*self.persistence;

        // Use the Any trait to downcast to SurrealDbMetadataPersistence
        let type_id = std::any::TypeId::of::<super::SurrealDbMetadataPersistence>();

        if MetadataPersistence::type_id(persistence_ref) == type_id {
            // Use the Any trait to downcast
            let any_ref = persistence_ref as &dyn std::any::Any;
            any_ref.downcast_ref::<super::SurrealDbMetadataPersistence>()
        } else {
            None
        }
    }
}

impl ChatMetadataTracker for ChatMetadata {
    fn init_session(&mut self, project_id: &str, session_id: &str) -> PrismaResult<()> {
        // Check if we already have an active session
        if self.current_session.is_some() {
            return Err(PrismaError::SessionAlreadyExists(
                "A session is already active. End the current session first.".to_string()
            ).into());
        }

        // Initialize a new session
        let now = Utc::now();
        let new_session = ChatSessionMetadata {
            session_id: session_id.to_string(),
            project_id: project_id.to_string(),
            start_time: now,
            last_activity: now,
            end_time: None,
            participants: HashMap::new(),
            total_messages: 0,
            active_duration_seconds: 0,
        };

        self.current_session = Some(new_session);
        Ok(())
    }

    fn record_participant_join(&mut self, participant_id: &str, name: &str, role: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            // Check if participant already exists
            if session.participants.contains_key(participant_id) {
                return Err(PrismaError::ParticipantAlreadyExists(participant_id.to_string()).into());
            }

            let now = Utc::now();
            session.last_activity = now;

            // Parse role without borrowing self
            let parsed_role = match role.to_lowercase().as_str() {
                "agent" => ParticipantRole::Agent,
                "system" => ParticipantRole::System,
                _ => ParticipantRole::User,
            };

            // Create a new participant
            let participant = Participant {
                id: participant_id.to_string(),
                name: name.to_string(),
                role: parsed_role,
                join_time: now,
                last_active: now,
                message_count: 0,
                is_active: true,
            };

            // Add the participant to the session
            session.participants.insert(participant_id.to_string(), participant);

            // Save the updated metadata
            self.persistence.save_metadata(session)?;

            Ok(())
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn record_participant_leave(&mut self, participant_id: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.last_activity = now;

            // Update participant status
            if let Some(participant) = session.participants.get_mut(participant_id) {
                participant.is_active = false;
                participant.last_active = now;

                // Save the updated metadata
                self.persistence.save_metadata(session)?;

                Ok(())
            } else {
                Err(PrismaError::ParticipantNotFound(format!("Participant {} not found in the session", participant_id)).into())
            }
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn update_participant_activity(&mut self, participant_id: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.last_activity = now;

            // Update participant's last activity time
            if let Some(participant) = session.participants.get_mut(participant_id) {
                participant.last_active = now;

                // Save the updated metadata
                self.persistence.save_metadata(session)?;

                Ok(())
            } else {
                Err(PrismaError::ParticipantNotFound(format!("Participant {} not found in the session", participant_id)).into())
            }
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn record_message(&mut self, participant_id: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.last_activity = now;
            session.total_messages += 1;

            // Update participant's message count
            if let Some(participant) = session.participants.get_mut(participant_id) {
                participant.message_count += 1;
                participant.last_active = now;

                // Save the updated metadata
                self.persistence.save_metadata(session)?;

                Ok(())
            } else {
                Err(PrismaError::ParticipantNotFound(format!("Participant {} not found in the session", participant_id)).into())
            }
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn end_session(&mut self) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.end_time = Some(now);
            session.last_activity = now;

            // Calculate the total active duration
            // Calculate duration without borrowing self
            let duration = {
                let start = session.start_time;
                let duration = now.signed_duration_since(start);
                duration.num_seconds() as u64
            };
            session.active_duration_seconds = duration;

            // Save the final metadata
            self.persistence.save_metadata(session)?;

            // Add to sessions collection before clearing current session
            let session_id = session.session_id.clone();
            let session_data = session.clone();
            self.sessions.insert(session_id, session_data);

            // Clear the current session
            self.current_session = None;

            Ok(())
        } else {
            Err(PrismaError::SessionNotFound("No active session to end.".to_string()).into())
        }
    }

    fn get_current_metadata(&self) -> PrismaResult<ChatSessionMetadata> {
        if let Some(session) = &self.current_session {
            Ok(session.clone())
        } else {
            Err(PrismaError::SessionNotFound("No active session.".to_string()).into())
        }
    }

    fn generate_summary(&self, project_name: &str) -> PrismaResult<ChatMetadataSummary> {
        if let Some(session) = &self.current_session {
            let now = Utc::now();
            // Calculate duration without borrowing self
            let duration = if let Some(end_time) = session.end_time {
                let start = session.start_time;
                let duration = end_time.signed_duration_since(start);
                duration.num_seconds() as u64
            } else {
                let start = session.start_time;
                let duration = now.signed_duration_since(start);
                duration.num_seconds() as u64
            };

            // Count messages by role
            let mut user_messages = 0;
            let mut agent_messages = 0;

            for participant in session.participants.values() {
                match participant.role {
                    ParticipantRole::User => user_messages += participant.message_count,
                    ParticipantRole::Agent => agent_messages += participant.message_count,
                    _ => {}
                }
            }

            Ok(ChatMetadataSummary {
                session_id: session.session_id.clone(),
                project_name: project_name.to_string(),
                start_time: session.start_time,
                duration_minutes: (duration / 60) as u32,
                participant_count: session.participants.len() as u32,
                message_count: session.total_messages,
                user_message_count: user_messages,
                agent_message_count: agent_messages,
            })
        } else {
            Err(PrismaError::SessionNotFound("No active session.".to_string()).into())
        }
    }

    fn export_to_json(&self) -> PrismaResult<String> {
        if let Some(session) = &self.current_session {
            serde_json::to_string_pretty(session).map_err(|e|
                PrismaError::Serialization(format!("Failed to serialize metadata to JSON: {}", e)).into()
            )
        } else {
            Err(PrismaError::SessionNotFound("No active session to export.".to_string()).into())
        }
    }
}

impl ChatSessionManager for ChatMetadata {
    fn list_sessions(&self) -> Vec<String> {
        self.sessions.keys().cloned().collect()
    }

    fn get_session_metadata(&self, session_id: &str) -> Option<ChatSessionMetadata> {
        self.sessions.get(session_id).cloned()
    }

    fn get_all_session_summaries(&self) -> HashMap<String, ChatMetadataSummary> {
        let mut summaries = HashMap::new();

        for (id, session) in &self.sessions {
            // Calculate duration without borrowing self
            let duration = if let Some(end_time) = session.end_time {
                let start = session.start_time;
                let duration = end_time.signed_duration_since(start);
                duration.num_seconds() as u64
            } else {
                let now = Utc::now();
                let start = session.start_time;
                let duration = now.signed_duration_since(start);
                duration.num_seconds() as u64
            };

            // Count messages by role
            let mut user_messages = 0;
            let mut agent_messages = 0;

            for participant in session.participants.values() {
                match participant.role {
                    ParticipantRole::User => user_messages += participant.message_count,
                    ParticipantRole::Agent => agent_messages += participant.message_count,
                    _ => {}
                }
            }

            let summary = ChatMetadataSummary {
                session_id: id.clone(),
                project_name: session.project_id.clone(), // Using project_id as name for now
                start_time: session.start_time,
                duration_minutes: (duration / 60) as u32,
                participant_count: session.participants.len() as u32,
                message_count: session.total_messages,
                user_message_count: user_messages,
                agent_message_count: agent_messages,
            };

            summaries.insert(id.clone(), summary);
        }

        summaries
    }

    fn export_all_to_json(&self) -> PrismaResult<String> {
        serde_json::to_string_pretty(&self.sessions).map_err(|e|
            PrismaError::Serialization(format!("Failed to serialize all sessions to JSON: {}", e)).into()
        )
    }
}
