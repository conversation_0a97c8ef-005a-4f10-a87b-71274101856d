// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/chat/generics.rs
// =================================================================================================
// Purpose: Provides a generic implementation of the ChatMetadataTracker trait that can work with
// any persistence mechanism. This implementation serves as a reusable foundation for chat metadata
// tracking that can be extended or used directly.
// =================================================================================================
// Internal Dependencies:
// - traits.rs: Uses ChatMetadataTracker and MetadataPersistence traits
// - types.rs: Uses ChatMetadataSummary, ChatSessionMetadata, Participant, and ParticipantRole types
// - err: Uses PrismaError and PrismaResult for error handling
// =================================================================================================
// External Dependencies:
// - chrono: For timestamp handling and duration calculations
// - serde_json: For serialization of metadata to JSON
// - std::collections: For HashMap to store participant information
// =================================================================================================
// Module Interactions:
// - Used by chat.rs as a foundation for the main ChatMetadata implementation
// - Interacts with any implementation of MetadataPersistence for storage
// - Provides a complete implementation that can be used directly by applications
// - Serves as a reference implementation for other ChatMetadataTracker implementations
// =================================================================================================

use crate::chat::traits::{ChatMetadataTracker, MetadataPersistence};
use crate::chat::types::{ChatMetadataSummary, ChatSessionMetadata, Participant, ParticipantRole};
use crate::err::{PrismaResult, GenericError};
use crate::err::types::errors_mod::PrismaError;
use chrono::{DateTime, Utc};
use serde_json;
use std::collections::HashMap;

/// A generic implementation of the ChatMetadataTracker trait
pub struct GenericChatMetadataTracker {
    current_session: Option<ChatSessionMetadata>,
    persistence: Box<dyn MetadataPersistence>,
}

impl GenericChatMetadataTracker {
    /// Create a new metadata tracker with the specified persistence mechanism
    pub fn new(persistence: Box<dyn MetadataPersistence>) -> Self {
        Self {
            current_session: None,
            persistence,
        }
    }

    /// Calculate the duration between two timestamps in seconds
    fn calculate_duration_seconds(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> u64 {
        let duration = end.signed_duration_since(start);
        duration.num_seconds() as u64
    }

    /// Parse a role string into a ParticipantRole enum
    fn parse_role(&self, role: &str) -> ParticipantRole {
        match role.to_lowercase().as_str() {
            "agent" => ParticipantRole::Agent,
            "system" => ParticipantRole::System,
            _ => ParticipantRole::User,
        }
    }
}

impl ChatMetadataTracker for GenericChatMetadataTracker {
    fn init_session(&mut self, project_id: &str, session_id: &str) -> PrismaResult<()> {
        // Check if we already have an active session
        if self.current_session.is_some() {
            return Err(PrismaError::SessionAlreadyExists(
                "A session is already active. End the current session first.".to_string()
            ).into());
        }

        // Initialize a new session
        let now = Utc::now();
        let new_session = ChatSessionMetadata {
            session_id: session_id.to_string(),
            project_id: project_id.to_string(),
            start_time: now,
            last_activity: now,
            end_time: None,
            participants: HashMap::new(),
            total_messages: 0,
            active_duration_seconds: 0,
        };

        self.current_session = Some(new_session);
        Ok(())
    }

    fn record_participant_join(&mut self, participant_id: &str, name: &str, role: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            // Check if participant already exists
            if session.participants.contains_key(participant_id) {
                return Err(PrismaError::ParticipantAlreadyExists(participant_id.to_string()).into());
            }

            let now = Utc::now();
            session.last_activity = now;

            // Parse role without borrowing self
            let parsed_role = match role.to_lowercase().as_str() {
                "agent" => ParticipantRole::Agent,
                "system" => ParticipantRole::System,
                _ => ParticipantRole::User,
            };

            // Create a new participant
            let participant = Participant {
                id: participant_id.to_string(),
                name: name.to_string(),
                role: parsed_role,
                join_time: now,
                last_active: now,
                message_count: 0,
                is_active: true,
            };

            // Add the participant to the session
            session.participants.insert(participant_id.to_string(), participant);

            // Save the updated metadata
            self.persistence.save_metadata(session)?;

            Ok(())
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn record_participant_leave(&mut self, participant_id: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.last_activity = now;

            // Update participant status
            if let Some(participant) = session.participants.get_mut(participant_id) {
                participant.is_active = false;
                participant.last_active = now;

                // Save the updated metadata
                self.persistence.save_metadata(session)?;

                Ok(())
            } else {
                Err(PrismaError::ParticipantNotFound(format!("Participant {} not found in the session", participant_id)).into())
            }
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn update_participant_activity(&mut self, participant_id: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.last_activity = now;

            // Update participant's last activity time
            if let Some(participant) = session.participants.get_mut(participant_id) {
                participant.last_active = now;

                // Save the updated metadata
                self.persistence.save_metadata(session)?;

                Ok(())
            } else {
                Err(PrismaError::ParticipantNotFound(format!("Participant {} not found in the session", participant_id)).into())
            }
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn record_message(&mut self, participant_id: &str) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.last_activity = now;
            session.total_messages += 1;

            // Update participant's message count
            if let Some(participant) = session.participants.get_mut(participant_id) {
                participant.message_count += 1;
                participant.last_active = now;

                // Save the updated metadata
                self.persistence.save_metadata(session)?;

                Ok(())
            } else {
                Err(PrismaError::ParticipantNotFound(format!("Participant {} not found in the session", participant_id)).into())
            }
        } else {
            Err(PrismaError::SessionNotFound("No active session. Initialize a session first.".to_string()).into())
        }
    }

    fn end_session(&mut self) -> PrismaResult<()> {
        if let Some(session) = &mut self.current_session {
            let now = Utc::now();
            session.end_time = Some(now);
            session.last_activity = now;

            // Calculate the total active duration
            // Calculate duration without borrowing self
            let duration = {
                let start = session.start_time;
                let duration = now.signed_duration_since(start);
                duration.num_seconds() as u64
            };
            session.active_duration_seconds = duration;

            // Save the final metadata
            self.persistence.save_metadata(session)?;

            // Clear the current session
            self.current_session = None;

            Ok(())
        } else {
            Err(PrismaError::SessionNotFound("No active session to end.".to_string()).into())
        }
    }

    fn get_current_metadata(&self) -> PrismaResult<ChatSessionMetadata> {
        if let Some(session) = &self.current_session {
            Ok(session.clone())
        } else {
            Err(PrismaError::SessionNotFound("No active session.".to_string()).into())
        }
    }

    fn generate_summary(&self, project_name: &str) -> PrismaResult<ChatMetadataSummary> {
        if let Some(session) = &self.current_session {
            let now = Utc::now();
            // Calculate duration without borrowing self
            let duration = if let Some(end_time) = session.end_time {
                let start = session.start_time;
                let duration = end_time.signed_duration_since(start);
                duration.num_seconds() as u64
            } else {
                let start = session.start_time;
                let duration = now.signed_duration_since(start);
                duration.num_seconds() as u64
            };

            // Count messages by role
            let mut user_messages = 0;
            let mut agent_messages = 0;

            for participant in session.participants.values() {
                match participant.role {
                    ParticipantRole::User => user_messages += participant.message_count,
                    ParticipantRole::Agent => agent_messages += participant.message_count,
                    _ => {}
                }
            }

            Ok(ChatMetadataSummary {
                session_id: session.session_id.clone(),
                project_name: project_name.to_string(),
                start_time: session.start_time,
                duration_minutes: (duration / 60) as u32,
                participant_count: session.participants.len() as u32,
                message_count: session.total_messages,
                user_message_count: user_messages,
                agent_message_count: agent_messages,
            })
        } else {
            Err(PrismaError::SessionNotFound("No active session.".to_string()).into())
        }
    }

    fn export_to_json(&self) -> PrismaResult<String> {
        if let Some(session) = &self.current_session {
            serde_json::to_string_pretty(session).map_err(|e|
                PrismaError::Serialization(format!("Failed to serialize metadata to JSON: {}", e)).into()
            )
        } else {
            Err(PrismaError::SessionNotFound("No active session to export.".to_string()).into())
        }
    }
}
