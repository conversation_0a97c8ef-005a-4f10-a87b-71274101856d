/* linenoise.h -- VERSION 1.0
 *
 * Guerrilla line editing library against the idea that a line editing lib
 * needs to be 20,000 lines of C++ code.
 *
 * See linenoise.cpp for more information.
 *
 * ------------------------------------------------------------------------
 *
 * Copyright (c) 2010-2023, <PERSON> <antirez at gmail dot com>
 * Copyright (c) 2010-2013, <PERSON> <pcnoordhuis at gmail dot com>
 * Copyright (c) 2025, <PERSON> <eric<PERSON>in17 at gmail dot com>
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 *  *  Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *
 *  *  Redistributions in binary form must reproduce the above copyright
 *     notice, this list of conditions and the following disclaimer in the
 *     documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef __LINENOISE_H
#define __LINENOISE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stddef.h> /* For size_t. */
#include <stdlib.h>

extern const char * linenoiseEditMore;

/* The linenoiseState structure represents the state during line editing.
 * We pass this state to functions implementing specific editing
 * functionalities. */
struct linenoiseState {
    int          in_completion;  /* The user pressed TAB and we are now in completion
                         * mode, so input is handled by completeLine(). */
    size_t       completion_idx; /* Index of next completion to propose. */
    int          ifd;            /* Terminal stdin file descriptor. */
    int          ofd;            /* Terminal stdout file descriptor. */
    char *       buf;            /* Edited line buffer. */
    size_t       buflen;         /* Edited line buffer size. */
    const char * prompt;         /* Prompt to display. */
    size_t       plen;           /* Prompt length. */
    size_t       pos;            /* Current cursor position. */
    size_t       oldcolpos;      /* Previous refresh cursor column position. */
    size_t       len;            /* Current edited line length. */
    size_t       cols;           /* Number of columns in terminal. */
    size_t       oldrows;        /* Rows used by last refreshed line (multiline mode) */
    int          history_index;  /* The history index we are currently editing. */
};

struct linenoiseCompletions {
    size_t  len     = 0;
    char ** cvec    = nullptr;
    bool    to_free = true;

    ~linenoiseCompletions() {
        if (!to_free) {
            return;
        }

        for (size_t i = 0; i < len; ++i) {
            free(cvec[i]);
        }

        free(cvec);
    }
};

/* Non blocking API. */
int          linenoiseEditStart(struct linenoiseState * l, int stdin_fd, int stdout_fd, char * buf, size_t buflen,
                                const char * prompt);
const char * linenoiseEditFeed(struct linenoiseState * l);
void         linenoiseEditStop(struct linenoiseState * l);
void         linenoiseHide(struct linenoiseState * l);
void         linenoiseShow(struct linenoiseState * l);

/* Blocking API. */
const char * linenoise(const char * prompt);
void         linenoiseFree(void * ptr);

/* Completion API. */
typedef void(linenoiseCompletionCallback)(const char *, linenoiseCompletions *);
typedef const char *(linenoiseHintsCallback) (const char *, int * color, int * bold);
typedef void(linenoiseFreeHintsCallback)(const char *);
void linenoiseSetCompletionCallback(linenoiseCompletionCallback *);
void linenoiseSetHintsCallback(linenoiseHintsCallback *);
void linenoiseSetFreeHintsCallback(linenoiseFreeHintsCallback *);
void linenoiseAddCompletion(linenoiseCompletions *, const char *);

/* History API. */
int linenoiseHistoryAdd(const char * line);
int linenoiseHistorySetMaxLen(int len);
int linenoiseHistorySave(const char * filename);
int linenoiseHistoryLoad(const char * filename);

/* Other utilities. */
void linenoiseClearScreen(void);
void linenoiseSetMultiLine(int ml);
void linenoisePrintKeyCodes(void);
void linenoiseMaskModeEnable(void);
void linenoiseMaskModeDisable(void);

/* Encoding functions. */
typedef size_t(linenoisePrevCharLen)(const char * buf, size_t buf_len, size_t pos, size_t * col_len);
typedef size_t(linenoiseNextCharLen)(const char * buf, size_t buf_len, size_t pos, size_t * col_len);
typedef size_t(linenoiseReadCode)(int fd, char * buf, size_t buf_len, int * c);

void linenoiseSetEncodingFunctions(linenoisePrevCharLen * prevCharLenFunc, linenoiseNextCharLen * nextCharLenFunc,
                                   linenoiseReadCode * readCodeFunc);

#ifdef __cplusplus
}
#endif

#endif /* __LINENOISE_H */
