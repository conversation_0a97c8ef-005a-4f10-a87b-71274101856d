# PrismaAI Configuration

# Service Credentials
services:
  # RabbitMQ Configuration
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    vhost: /
    management_port: 15672
    version: 3.13.7

  # SurrealDB Configuration
  surrealdb:
    host: localhost
    port: 8000
    username: root
    password: root
    namespace: prisma
    database: prisma
    use_http: true  # Use HTTP protocol instead of WebSocket
    version: 2.2.1

  # Loki Configuration
  loki:
    host: localhost
    port: 3100
    version: 3.4.2

# Loki Server Configuration
auth_enabled: false

server:
  http_listen_port: 3100

ingester:
  lifecycler:
    address: 127.0.0.1
    ring:
      kvstore:
        store: inmemory
      replication_factor: 1
    final_sleep: 0s
  chunk_idle_period: 5m
  chunk_retain_period: 30s

schema_config:
  configs:
    - from: 2020-05-15
      store: boltdb
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

storage_config:
  boltdb:
    directory: /loki/index

  filesystem:
    directory: /loki/chunks

limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h