┌────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                          Configuration                                          │
│                                                                                                │
│                       ┌───────────────────────────────────────────────────┐                    │
│                       │                   pm.toml                          │ ← [CONFIGURATION]  │
│                       └─────────────────────────┬─────────────────────────┘                    │
└───────────────────────────────────────────────┬─┘                                             │
                                               │
                                               ▼
┌────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                          Main Entry Point                                       │
│                       ┌───────────────────────────────────────────────────┐                    │
│                       │                    pm.rs                          │                    │
│                       └─┬───────────────────┬───────────────────────────┬─┘                    │
└─────────────────────────┼───────────────────┼───────────────────────────┼──────────────────────┘
                         │                    │                           │
                         ▼                    │                           │
┌──────────────────────────────┐             │                           │
│     Prisma_UI         │             │                           │
│                             │             │                           │
│ ┌─────────────────────────┐ │             │                           │
│ │       handlers/         │ │             │                           │
│ │ - llm_handler.rs       │ │             │                           │
│ │ - embedding_handler.rs  │ │             │                           │
│ │ - chat_handler.rs      │ │             │                           │
│ └─────────────────────────┘ │             │                           │
│                             │             │                           │
│ ┌─────────────────────────┐ │             │                           │
│ │      publishers/        │ │             │                           │
│ │ - llm_publisher.rs     │ │             │                           │
│ │ - embedding_publisher.rs│ │             │                           │
│ │ - chat_publisher.rs    │ │             │                           │
│ └─────────────────────────┘ │             │                           │
│                             │             │                           │
│ ┌─────────────────────────┐ │             │                           │
│ │       services/         │ │             │                           │
│ └─────────────────────────┘ │             │                           │
└──────────────┬───────────────┘             │                           │
               │                             ▼                           │
               │                  ┌──────────────────────────────┐       │
               │                  │     Resource Monitor         │       │
               │                  │                             │       │
               │                  │ - Hardware detection        │       │
               │                  │ - Resource tracking         │       │
               │                  │ - Performance metrics       │       │
               │                  └──────────────┬───────────────┘       │
               │                                 │                       │
               ▼                                ▼                       ▼
┌──────────────────────────────┐    ┌──────────────────────────────┐
│      Task Creation Layer     │    │         Decision Maker       │
│          [TCL]              │───▶│    [Resource Rules Check]     │
└──────────────────────────────┘    └──────────────┬───────────────┘
                                                   │
                                                   ▼
                    ┌──────────────────────────────────────────────────┐
                    │                  Executor                        │
                    │          [Execution Strategies]                  │
                    └────────────────────┬─────────────────────────────┘
                                                        │
                                                        ▼
┌────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                     Execution Strategies                                        │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────┐                    │
│  │     rayon.rs        │  │      tokio.rs       │  │ Direct Execution    │                    │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────┘                    │
└────────────────────────────────────────────────────────────────────────────────────────────────┘

## Workflow Explanation ##

### LLM Interface and Implementation Mapping to PrismaEngine Modules

The following table shows which PrismaEngine module is most appropriate for each LLM interface and implementation file:

#### Task Creation Layer (TCL)

- **embedding.rs/embedding_impl.rs**: Embedding generation task definition and implementation
- **chat.rs/chat_impl.rs**: Chat task definition and implementation
- **inference.rs/inference_impl.rs**: Core inference task definition and implementation
- **batch.rs/batch_impl.rs**: Batch processing operations for token handling
- **sampling.rs/sampling_impl.rs**: Sampling parameters and strategies for text generation
- **vocab.rs/vocab_impl.rs**: Vocabulary handling for tokenization and detokenization
- **lora.rs/lora_impl.rs**: LoRA adapter management for fine-tuned models

#### Monitor

- **system.rs/system_impl.rs**: System-level information about LLM resources
- **threading.rs/threading_impl.rs**: Threading configuration and utilization
- **performance.rs/performance_impl.rs**: Performance metrics collection and analysis

#### Decision Maker

- **state.rs/state_impl.rs**: LLM state information for decision-making
- **properties.rs/properties_impl.rs**: Model properties affecting resource allocation

#### Executor

- **context.rs/context_impl.rs**: Context management during task execution
- **cache.rs/cache_impl.rs**: KV cache management for efficient inference
- **memory.rs/memory_impl.rs**: Memory management for model execution

#### Agent Manager

- **metadata.rs/metadata_impl.rs**: Model metadata for agent configuration

#### Execution Strategies

- No direct mapping, though threading_impl.rs has some relevance

#### Cross-Cutting Concerns

- **streaming.rs/streaming_impl.rs**: Streaming output implementation (used by TCL, Executor)
- **streaming_perf.rs**: Performance metrics for streaming (used by Monitor)
- **streaming_recovery.rs**: Recovery mechanisms for streaming (used by Executor)
- **service.rs**: Main LLM service implementation (used by PrismaEngine)
- **mod.rs**: Module definitions and exports (used by all modules)

## Workflow Explanation (Updated)

1. **Configuration** - System loads settings from `pm.toml`:
   - Initializes system-wide configurations
   - Sets resource limits and operational parameters
   - Configures execution strategies and priorities

2. **Main Entry Point** - `pm.rs` initializes and coordinates all components:
   - Bootstraps the system components
   - Manages component lifecycle
   - Coordinates inter-component communication

3. **Prisma_UI** - Handles all communication:
   - Processes incoming UI requests
   - Manages message handlers (LLM, Embedding, Chat..etc)
   - Coordinates publishers for result distribution
   - Provides service interfaces for component interaction

4. **Resource Monitor** - Tracks available system resources:
   - Detects hardware capabilities
   - Monitors system performance metrics
   - Provides resource information to the decision maker
   - Tracks CPU, Memory, GPU, Network, and Disk usage

5. **Task Creation Layer (TCL)** - Defines and creates task objects:
   - Implements the `Task` trait for different types of work (LLM, DB, Embedding, etc.)
   - Standardizes how tasks declare their resource requirements
   - Creates task objects that can be submitted for execution
   - Manages task priorities and categories

6. **Decision Maker** - Determines optimal execution strategy:
   - Analyzes resource availability using system scores
   - Evaluates task requirements using prisma scores
   - Selects appropriate execution approach
   - Manages execution priorities and queuing

7. **Executor** - Dispatches tasks based on decisions:
   - Routes tasks to appropriate execution strategy
   - Manages execution lifecycle
   - Handles task queuing and prioritization
   - Monitors execution progress

8. **Execution Strategies** - Different concurrency models:
   - `rayon.rs` - CPU-intensive parallel work
   - `tokio.rs` - I/O bound and async operations
   - Direct execution - Simple, lightweight tasks


-------------------------------------------------------------------------

## PRISMA Agentic System: Module Architecture and Integration

The PRISMA agentic system is built on a modular architecture that enables robust agent management, intelligent task execution, and efficient resource utilization. Below is a comprehensive description of the key modules and how they integrate to form a cohesive agentic system.

### 1. Agent Manager (`agent_manager`)

The Agent Manager module serves as the central registry and lifecycle manager for all agents in the system. It provides a comprehensive framework for creating, configuring, and managing agents with different roles and capabilities.

**Key Components:**
- **AgentManager**: The main class that orchestrates all agent-related functionality, managing agent registration, state, capabilities, and communication.
- **EnhancedAgentRegistry**: Maintains a registry of all agents with their sequence IDs, supporting persistence to a database and in-memory caching.
- **AgentStateManagerImpl**: Tracks and manages the runtime state of agents, including their activity status, conversation history, and context.
- **AgentCapabilityManager**: Manages the capabilities that agents possess, allowing for dynamic capability assignment and verification.
- **AgentCommunicationManager**: Facilitates agent-to-agent and agent-to-human communication through various channels, including RabbitMQ.
- **ConfigIntegrationManager**: Loads and manages agent configurations from configuration files like `pm.toml`.
- **UiIntegrationManager**: Bridges the gap between the UI and agent system, enabling agent creation and configuration through the user interface.
- **ModelMetadataManager**: Manages metadata about LLM models that agents can use, including resource requirements and capabilities.

**Integration Points:**
- Interfaces with the **Decision Maker** to provide agent state information for task routing decisions
- Works with the **Executor** to assign tasks to appropriate agents
- Connects with the **Storage** module for persisting agent data
- Integrates with the **LLM Interface** for model selection and configuration
- Communicates with the **Prisma UI** for user interaction and configuration

### 2. Decision Maker (`decision_maker`)

The Decision Maker module is responsible for determining the optimal execution strategy for tasks based on their resource requirements and current system resource availability. It implements sophisticated decision logic that balances performance, resource utilization, and priority.

**Key Components:**
- **RuleBasedDecisionMaker**: The core component that implements the DecisionLogic trait, making strategy decisions based on rules, resource constraints, and priorities.
- **PrismaScoreEvaluator**: Evaluates task resource requirements to generate a PrismaScore that quantifies needed resources.
- **SystemScoreEvaluator**: Evaluates system resource availability to generate a SystemScore that quantifies available resources.
- **RuleSet**: A collection of rules that determine execution strategies based on task categories, resource requirements, and system state.
- **StateTracker**: Tracks the state of LLM models, including loading status, memory usage, and performance metrics.

**Integration Points:**
- Receives task information from the **Task Creation Layer (TCL)** for evaluation
- Gets system resource information from the **Monitor** module
- Provides execution strategy decisions to the **Executor**
- Coordinates with the **Agent Manager** for agent-specific resource allocation

### 3. Execution Strategies (`execution_strategies`)

The Execution Strategies module provides different strategies for executing tasks based on their characteristics and system state. It includes direct execution for lightweight tasks, Rayon-based parallel execution for CPU-intensive tasks, and Tokio-based asynchronous execution for I/O-bound tasks.

**Key Components:**
- **ExecutionStrategies**: The main manager that orchestrates all execution strategies, providing a unified interface for strategy selection and task execution.
- **DirectStrategy**: Implements direct execution in the current async context for lightweight, non-blocking tasks.
- **RayonStrategy**: Implements parallel execution using Rayon for CPU-intensive tasks that benefit from parallelism.
- **TokioStrategy**: Implements asynchronous execution using Tokio for I/O-bound tasks that benefit from async concurrency.

**Integration Points:**
- Used by the **Executor** to execute tasks with the appropriate strategy
- Configured by the **Decision Maker** based on task characteristics and system state
- Optimized based on feedback from the **Monitor** module

### 4. Executor (`executor`)

The Executor module is responsible for managing task execution, dispatching tasks to appropriate queues based on priority, and coordinating worker tasks that process the queued tasks. It serves as the execution engine for the PRISMA system.

**Key Components:**
- **TaskExecutor**: The main class that implements the Executor trait, managing task submission, queuing, and execution.
- **PriorityQueueManager**: Manages priority queues for different task priorities, ensuring high-priority tasks are processed first.
- **DirectQueue, RayonQueue, TokioQueue**: Queue implementations for different execution strategies.
- **CacheManager**: Provides cache management services for efficient task execution.
- **ContextManager**: Manages execution contexts for tasks.
- **Dispatcher**: Routes tasks to appropriate queues based on priority and strategy.
- **MemoryManager**: Manages memory allocation and deallocation for task execution.
- **ResultProcessor**: Processes and formats task results.
- **StrategyWorker**: Worker implementations for different execution strategies.

**Integration Points:**
- Receives tasks from the **PrismaEngine** via the submit_task method
- Uses execution strategies from the **Execution Strategies** module
- Coordinates with the **Decision Maker** for strategy selection
- Reports execution metrics to the **Monitor** module
- Interfaces with the **LLM Interface** for model execution

### 5. Monitor (`monitor`)

The Monitor module is responsible for tracking system resources and internal PRISMA components, providing metrics and alerts for resource utilization and performance. It serves as the observability layer for the PRISMA system.

**Key Components:**
- **Monitor**: The main class that aggregates metrics from different monitoring sources and provides a unified interface.
- **SystemInfoMonitor**: Monitors system resources like CPU, memory, disk, and network.
- **QueueMonitor**: Monitors task queue lengths and processing rates.
- **TaskMonitor**: Monitors task execution status and performance.

**Integration Points:**
- Provides system resource information to the **Decision Maker**
- Reports execution metrics to the **PrismaEngine**
- Alerts the system about resource constraints
- Tracks performance of the **Executor** and **Execution Strategies**

### 6. Task Creation Layer (`tcl`)

The Task Creation Layer (TCL) module is responsible for defining and creating task objects that can be submitted for execution. It standardizes how tasks declare their resource requirements and creates task objects that can be executed by the system.

**Key Components:**
- **TaskFactory**: The main factory for creating different types of tasks.
- **LlmTask**: Implements the Task trait for LLM inference operations.
- **EmbeddingTask**: Implements the Task trait for embedding generation operations.
- **StorageTask**: Implements the Task trait for database storage operations.

**Integration Points:**
- Creates tasks that are submitted to the **Executor** via the PrismaEngine
- Uses the **LLM Interface** for model operations
- Interfaces with the **Storage** module for database operations
- Coordinates with the **Agent Manager** for agent-specific tasks

### 7. LLM Interface (`llm/interface`)

The LLM Interface module provides a unified interface for interacting with language models, abstracting away the details of different model implementations and providing a consistent API for the rest of the system.

**Key Components:**
- **VocabManager**: Manages vocabulary and tokenization for language models.
- **Inference**: Provides methods for running inference with language models.
- **BatchOperations**: Handles batch processing of tokens.
- **Sampling**: Implements sampling strategies for text generation.
- **EmbeddingGenerator**: Generates embeddings from text.
- **StreamingOutput**: Handles streaming output from language models.
- **ContextManager**: Manages context for language model operations.

**Integration Points:**
- Used by the **TCL** for creating LLM-related tasks
- Interfaces with the **Executor** for task execution
- Provides model information to the **Agent Manager**
- Reports performance metrics to the **Monitor**

### 8. Storage (`storage`)

The Storage module provides a unified interface for database operations, abstracting away the details of different database implementations and providing a consistent API for the rest of the system.

**Key Components:**
- **SurrealDbConnection**: Implements the DatabaseConnection and DataStore traits for SurrealDB.
- **LongTermMemoryStore**: Provides methods for storing and retrieving agent memories.
- **DataStore**: Defines the interface for CRUD operations on database records.

**Integration Points:**
- Used by the **TCL** for creating storage-related tasks
- Stores agent data for the **Agent Manager**
- Persists conversation history for agents
- Stores embeddings for semantic search

### 9. Prisma UI (`prisma_ui`)

The Prisma UI module provides the user interface for interacting with the PRISMA system, including chat interfaces, agent configuration, and system monitoring.

**Key Components:**
- **ChatPublisher**: Publishes chat messages to the UI.
- **ChatService**: Handles chat interactions between users and agents.
- **RabbitMQPublisher**: Publishes events to RabbitMQ for UI consumption.
- **StreamingAdapter**: Adapts streaming output from LLM models for UI consumption.

**Integration Points:**
- Interfaces with the **Agent Manager** for agent creation and configuration
- Submits tasks to the **PrismaEngine** for execution
- Receives results from the **Executor** via publishers
- Displays information from the **Monitor** for system status

### System Integration and Workflow

The PRISMA agentic system integrates these modules to provide a comprehensive framework for agent-based AI applications. The workflow typically follows these steps:

1. The **Prisma UI** receives a user request (e.g., a chat message to an agent).
2. The request is routed to the appropriate service (e.g., ChatService).
3. The service gets the agent information from the **Agent Manager**.
4. The service creates a task using the **TCL** (e.g., an LlmTask for generating a response).
5. The task is submitted to the **PrismaEngine**, which uses the **Decision Maker** to determine the optimal execution strategy.
6. The **Executor** dispatches the task to the appropriate execution strategy based on the decision.
7. The task is executed using the **LLM Interface** for model operations.
8. The result is returned to the service and published to the UI using the appropriate publisher.
9. Throughout this process, the **Monitor** tracks system resources and performance metrics.

This modular architecture enables the PRISMA system to efficiently manage agents, make intelligent decisions about task execution, and provide a responsive user experience while optimizing resource utilization.

**Core Interaction Flow:**

The following Mermaid diagram illustrates the sequence of interactions for a typical user request (e.g., sending a chat message to an agent):

```mermaid
sequenceDiagram
    participant UI (Flutter)
    participant WS Handler (prisma_ui/handlers)
    participant Service (prisma_ui/services e.g., ChatService)
    participant PrismaEngine (Arc<RwLock>)
    participant AgentManager (via Engine)
    participant StorageService (via Engine)
    participant ConfigLoader (or Service)
    participant Executor (via Engine)
    participant LlmService (via Task)
    participant Publisher (prisma_ui/publishers)

    UI->>+WS Handler: Send Message (e.g., chat, agent_id, user_msg)
    WS Handler->>+Service: Route Request(data)
    Note over Service: Service holds Arc<RwLock<PrismaEngine>>
    Service->>+PrismaEngine: read() access
    Service->>PrismaEngine: Get AgentManager handle
    PrismaEngine-->>-Service: AgentManager handle (e.g., Arc clone)
    Service->>+AgentManager: register_or_get_agent(agent_id)
    AgentManager-->>-Service: seq_id
    Service->>+PrismaEngine: read() access
    Service->>PrismaEngine: Get StorageService handle
    PrismaEngine-->>-Service: StorageService handle (e.g., Arc clone)
    Service->>+StorageService: get_conversation_history(agent_id, limit=5)
    StorageService-->>-Service: history
    Service->>+ConfigLoader: Load pm.toml agent(agent_id)
    ConfigLoader-->>-Service: agent_config {role, goal, template_key}
    Service->>+ConfigLoader: Load prompt_template.toml template(template_key)
    ConfigLoader-->>-Service: template_string
    Service->>Service: Construct Prompt(template, agent_config.role, agent_config.goal, history, user_msg)
    Service->>Service: Create LlmTask(prompt, seq_id, params)
    Service->>+PrismaEngine: write() access for submit
    Service->>PrismaEngine: submit_task(LlmTask)
    Note over PrismaEngine,Executor: Engine uses DecisionMaker, routes to Executor
    PrismaEngine->>+Executor: submit_task(LlmTask, strategy)
    Executor->>Executor: Execute LlmTask (calls LlmService.decode)
    Executor-->>-PrismaEngine: Task Result (e.g., generated text)
    PrismaEngine-->>-Service: Task Result (via await on submit_task)
    Service->>+Publisher: Publish Result(result)
    Publisher->>-UI: Send Result via WebSocket/RabbitMQ
    UI->>UI: Display Result
```


