You can access:
RabbitMQ management interface at http://localhost:15672
SurrealDB is accessible at http://localhost:8000
Loki is ready to receive logs at http://localhost:3100


grafna service token - glsa_Go16KlrwTtqtNmeymEg56hIysMVEHyVg_8be567e3
Name :
prisma-pipeline	
ID :
sa-1-prisma-pipeline

#pull
git fetch origin
git reset --hard origin/main
git clean -fd

# push at the push command
git push --set-upstream origin main:main

# surrealdb url test
curl -v http://localhost:8000/status

#############################################################################################################

flutter run -d macos
flutter run -d "iPhone 12" ( open -a Simulator )
flutter run -d "iPhone 12" --release
flutter run -d macos --release

flutter devices

#############################################################################################################

1 - ok for clarification and hope that we are on the same page - the prisma_ai/src/prisma/docss.md has the prisma diagram.
2 - i would like to also know when calling the telemetry from prisma_ai/src/telemetry, should it be used in the prisma engine or prisma_ai/src/prisma_ui
3 - prisma_ai/src/config/prompt_template.toml is empty but it must map the values from prisma_ai/src/config/pm.toml making sure the agents are are aware of thier environment and avoid hallucinations
4 - not sure if to use the stm and ltm in the prisma_ai/src/config/prompt_template.toml or directly from prisma_ai/src/storage/mod.rs
prisma_ai/src/storage/surrealdb.rs
prisma_ai/src/storage/traits.rs
prisma_ai/src/storage/types.rs
 or have a hybrd approach?
 5 - participants can either be human or llm as shown from the flutter ui - prisma_ui/lib/screens/model_selection_screen.dart
 6 prisma_ai/src/prisma_ui/services/new_project/embeddings.rs
prisma_ai/src/prisma_ui/services/new_project/load_model.rs
prisma_ai/src/prisma_ui/services/new_project/new_project.rs
prisma_ai/src/prisma_ui/services/new_project/project_details.rs is supposed to be mapped to prisma_ui/lib/screens/new_project_screen.dart
prisma_ui/lib/screens/model_selection_screen.dart
7 - embeddings will be stored in surreal db 
8 - for one project, i plan to have like seperate chat rooms labeled "collaboration", so basically, i want some agents to communicate with specfic agents or all the agents - the user in the frontend will be able to choose - prisma_ui/lib/screens/collaboration_screen.dart / prisma_ai/src/prisma_ui/services/participants/collab.rs
9 - i have prisma_ai/src/prisma_ui/services/participants/prompt_template.rs to as a place holder for promt prompt_template
10 - prisma_ai/src/prisma_ui/services/participants/embeddings.rs is for individual agents and prisma_ai/src/prisma_ui/services/new_project/embeddings.rs is project based
11 - prisma_ai/src/prisma_ui/services/participants/human.rs
prisma_ai/src/prisma_ui/services/participants/llm.rs for participants
12 - prisma_ai/src/prisma_ui/services/participants/notifications.rs - leave for last 

one more question i was wondering if rabbitmq (lapin) can be used for logic for routing messages between agents, using rabbitmq exchanges?, i forgot to mention tool calling using mcp ( Instructions /mcp/1.md
Instructions /mcp/2.md
Instructions /mcp/3.md
Instructions /mcp/4.md
Instructions /mcp/5.md
Instructions /mcp/6.md
Instructions /mcp/7.md
Instructions /mcp/8.md
Instructions /mcp/9.md
Instructions /mcp/10.md
Instructions /mcp/11.md
Instructions /mcp/12.md
Instructions /mcp/13.md
Instructions /mcp/14.md
Instructions /mcp/15.md
Instructions /mcp/16.md)
---the software must be desingned to be an mcp client. Now, im not sure mcp works but it is to my knowledge that the agent must generate a json structure, so im not sure if we should use like a middleware between agent and mcp or if we can utilize the rabbitmq exchange options?
