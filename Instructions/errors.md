running 1 test
test integration::executor_tests::test_priority_queue_execution_order ... FAILED

failures:

---- integration::executor_tests::test_priority_queue_execution_order stdout ----
Starting priority queue execution order test
Starting Rayon queue with current status: Stopped
Starting Rayon queue
Creating new channel with capacity 10000
New channel created
Updating queue status to Running
Queue status updated to Running
Creating worker loop with dedicated pool: false
Worker loop created
Storing worker handle
Worker handle stored
Waiting for worker loop to initialize
Rayon worker loop started
Creating semaphore with 16 max concurrent tasks
Rayon worker loop waiting for tasks
Initial queue status in worker loop: Running
Starting to receive tasks from channel
Waiting for task with 1 second timeout
Rayon worker loop ping - still alive
Worker loop initialization wait complete
Rayon queue started
Starting Tokio queue
Updating queue status to Running
Queue status updated to Running
Creating new channel with capacity 10000
Creating worker loop
Tokio queue started
Starting Background queue with current status: Stopped
Starting Background queue
Creating custom worker loop that transfers tasks to the RayonQueue
Creating new channel with capacity 100
New channel created
Custom worker loop created
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Tokio worker loop started
Initial queue status in worker loop: Running
Starting to receive tasks from channel
Background queue worker loop started
Tokio worker loop ping - still alive
Background queue worker loop ping
Worker loop initialization wait complete
Background queue started
Starting Standard queue with current status: Stopped
Starting Standard queue
Creating new channel with capacity 100
New channel created
Creating worker loop
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Priority queue worker loop started for strategy: Tokio
Priority queue worker loop ping - strategy: Tokio
Worker loop initialization wait complete
Standard queue started
Starting RealTime queue with current status: Stopped
Starting RealTime queue
Creating new channel with capacity 100
New channel created
Creating worker loop
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Priority queue worker loop started for strategy: Tokio
Priority queue worker loop ping - strategy: Tokio
Worker loop initialization wait complete
RealTime queue started
Submitting task Low-1 with priority Low
Background queue enqueue called with status: Running
Enqueuing task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 in Background queue
Created oneshot channel for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Created PrioritizedTask for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Sending task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 to Background queue channel
Successfully sent task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 to Background queue channel
Updating Background queue length from 0 to 1
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 enqueued successfully in Background queue
Submitting task Low-2 with priority Low
Background queue enqueue called with status: Running
Enqueuing task c245383c-d284-4aa5-abc0-eaa48d19bb79 in Background queue
Created oneshot channel for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Created PrioritizedTask for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Sending task c245383c-d284-4aa5-abc0-eaa48d19bb79 to Background queue channel
Successfully sent task c245383c-d284-4aa5-abc0-eaa48d19bb79 to Background queue channel
Updating Background queue length from 1 to 2
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 enqueued successfully in Background queue
Submitting task Normal-1 with priority Normal
Submitting task Normal-2 with priority Normal
Submitting task High-1 with priority High
Submitting task High-2 with priority High
Submitting task Realtime-1 with priority Realtime
Submitting task Realtime-2 with priority Realtime
All tasks submitted, waiting for completion
Waiting for task Low-1 (priority: Low)
Background queue worker received task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Updating queue length from 2 to 1
Transferring task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 to RayonQueue
RayonQueue status before enqueuing task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7: Running
Enqueue called with queue status: Running
Enqueuing task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 in Rayon queue
Created oneshot channel for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Created RayonTask for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Sending task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 to channel
Successfully sent task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 to channel
Updating queue length from 0 to 1
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 enqueued successfully in Rayon queue
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 successfully transferred to RayonQueue
Background queue worker received task c245383c-d284-4aa5-abc0-eaa48d19bb79
Updating queue length from 1 to 0
Transferring task c245383c-d284-4aa5-abc0-eaa48d19bb79 to RayonQueue
RayonQueue status before enqueuing task c245383c-d284-4aa5-abc0-eaa48d19bb79: Running
Enqueue called with queue status: Running
Enqueuing task c245383c-d284-4aa5-abc0-eaa48d19bb79 in Rayon queue
Created oneshot channel for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Created RayonTask for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Sending task c245383c-d284-4aa5-abc0-eaa48d19bb79 to channel
Successfully sent task c245383c-d284-4aa5-abc0-eaa48d19bb79 to channel
Updating queue length from 1 to 2
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 enqueued successfully in Rayon queue
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 successfully transferred to RayonQueue
Priority queue worker received task 1accd7e3-8c2b-43d0-a382-7c3cfa61ea37 with priority Normal for strategy Tokio
Updating queue length from 2 to 1
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 1accd7e3-8c2b-43d0-a382-7c3cfa61ea37
Executing task 1accd7e3-8c2b-43d0-a382-7c3cfa61ea37 with Tokio strategy
Task Normal-1 (priority: Normal) started execution at order: 0
Priority queue worker received task a7a1175c-c504-4c69-8888-97c7acd1b8b8 with priority High for strategy Tokio
Updating queue length from 4 to 3
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task a7a1175c-c504-4c69-8888-97c7acd1b8b8
Executing task a7a1175c-c504-4c69-8888-97c7acd1b8b8 with Tokio strategy
Task High-1 (priority: High) started execution at order: 1
Rayon worker received a task
Current queue status: Running
Rayon worker processing task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Updating active tasks count from 0 to 1
Updating queue length from 2 to 1
Acquiring semaphore permit for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Successfully acquired semaphore permit for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Preparing to execute task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Acquiring lock on task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Lock acquired for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Using global pool for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Using timeout of 30s for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Starting task execution with Rayon for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Using global Rayon pool for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Task cloned for global Rayon execution: 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Spawning task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 on global Rayon thread pool
Waiting for result from global Rayon thread pool for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Inside global Rayon thread pool for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Creating new Tokio runtime for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 in global pool
Executing task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 on new runtime in global pool
Task Low-1 (priority: Low) started execution at order: 2
Task Normal-1 (priority: Normal) completed at order: 0 after 101.671139ms
Task 1accd7e3-8c2b-43d0-a382-7c3cfa61ea37 execution completed in 101.736814ms with result: true
Priority queue worker received task 809ac76e-4fff-4e21-90af-3b23f2c66b9f with priority Normal for strategy Tokio
Updating queue length from 1 to 0
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 809ac76e-4fff-4e21-90af-3b23f2c66b9f
Executing task 809ac76e-4fff-4e21-90af-3b23f2c66b9f with Tokio strategy
Task Normal-2 (priority: Normal) started execution at order: 3
Task High-1 (priority: High) completed at order: 1 after 101.781022ms
Task a7a1175c-c504-4c69-8888-97c7acd1b8b8 execution completed in 101.789618ms with result: true
Priority queue worker received task 44523488-5ed7-4af2-b4bb-36e004269125 with priority High for strategy Tokio
Updating queue length from 3 to 2
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 44523488-5ed7-4af2-b4bb-36e004269125
Executing task 44523488-5ed7-4af2-b4bb-36e004269125 with Tokio strategy
Task High-2 (priority: High) started execution at order: 4
Task Low-1 (priority: Low) completed at order: 2 after 102.628312ms
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 execution completed with result: true
Sending result for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 back to main thread from global pool
Successfully sent result for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 from global pool
Successfully received result for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 from global pool
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 execution succeeded with Rayon in 103 ms
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 completed within timeout
Updating statistics for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Updating active tasks count from 1 to 0
Sending result for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Successfully sent result for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7 processing completed
Waiting for task with 1 second timeout
Rayon worker received a task
Current queue status: Running
Rayon worker processing task c245383c-d284-4aa5-abc0-eaa48d19bb79
Updating active tasks count from 0 to 1
Updating queue length from 1 to 0
Acquiring semaphore permit for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Successfully acquired semaphore permit for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Preparing to execute task c245383c-d284-4aa5-abc0-eaa48d19bb79
Acquiring lock on task c245383c-d284-4aa5-abc0-eaa48d19bb79
Lock acquired for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Using global pool for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Using timeout of 30s for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Starting task execution with Rayon for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Using global Rayon pool for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Task cloned for global Rayon execution: c245383c-d284-4aa5-abc0-eaa48d19bb79
Spawning task c245383c-d284-4aa5-abc0-eaa48d19bb79 on global Rayon thread pool
Waiting for result from global Rayon thread pool for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Inside global Rayon thread pool for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Creating new Tokio runtime for task c245383c-d284-4aa5-abc0-eaa48d19bb79 in global pool
Received result from RayonQueue for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Successfully forwarded result for task 7ceb8da2-f96f-414e-9418-3aa0a24af7a7
Executing task c245383c-d284-4aa5-abc0-eaa48d19bb79 on new runtime in global pool
Task Low-2 (priority: Low) started execution at order: 5
Task Low-1 completed with execution order: 2, completion order: 2
Waiting for task Low-2 (priority: Low)
Task Normal-2 (priority: Normal) completed at order: 3 after 100.807448ms
Task 809ac76e-4fff-4e21-90af-3b23f2c66b9f execution completed in 100.831772ms with result: true
Task High-2 (priority: High) completed at order: 4 after 102.030862ms
Task 44523488-5ed7-4af2-b4bb-36e004269125 execution completed in 102.042562ms with result: true
Priority queue worker received task 0d1533e9-887f-4ea0-9c58-ac0b86c73417 with priority Realtime for strategy Tokio
Updating queue length from 2 to 1
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 0d1533e9-887f-4ea0-9c58-ac0b86c73417
Executing task 0d1533e9-887f-4ea0-9c58-ac0b86c73417 with Tokio strategy
Task Realtime-1 (priority: Realtime) started execution at order: 6
Task Low-2 (priority: Low) completed at order: 5 after 101.737624ms
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 execution completed with result: true
Sending result for task c245383c-d284-4aa5-abc0-eaa48d19bb79 back to main thread from global pool
Successfully sent result for task c245383c-d284-4aa5-abc0-eaa48d19bb79 from global pool
Successfully received result for task c245383c-d284-4aa5-abc0-eaa48d19bb79 from global pool
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 execution succeeded with Rayon in 101 ms
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 completed within timeout
Updating statistics for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Updating active tasks count from 1 to 0
Sending result for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Successfully sent result for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Task c245383c-d284-4aa5-abc0-eaa48d19bb79 processing completed
Waiting for task with 1 second timeout
Received result from RayonQueue for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Successfully forwarded result for task c245383c-d284-4aa5-abc0-eaa48d19bb79
Task Low-2 completed with execution order: 5, completion order: 5
Waiting for task Normal-1 (priority: Normal)
Task Normal-1 completed with execution order: 0, completion order: 0
Waiting for task Normal-2 (priority: Normal)
Task Normal-2 completed with execution order: 3, completion order: 3
Waiting for task High-1 (priority: High)
Task High-1 completed with execution order: 1, completion order: 1
Waiting for task High-2 (priority: High)
Task High-2 completed with execution order: 4, completion order: 4
Waiting for task Realtime-1 (priority: Realtime)
Task Realtime-1 (priority: Realtime) completed at order: 6 after 102.253711ms
Task 0d1533e9-887f-4ea0-9c58-ac0b86c73417 execution completed in 102.286965ms with result: true
Priority queue worker received task c0a65171-67a4-4951-83b1-242948f73e26 with priority Realtime for strategy Tokio
Updating queue length from 1 to 0
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task c0a65171-67a4-4951-83b1-242948f73e26
Executing task c0a65171-67a4-4951-83b1-242948f73e26 with Tokio strategy
Task Realtime-2 (priority: Realtime) started execution at order: 7
Task Realtime-1 completed with execution order: 6, completion order: 6
Waiting for task Realtime-2 (priority: Realtime)
Task Realtime-2 (priority: Realtime) completed at order: 7 after 102.224499ms
Task c0a65171-67a4-4951-83b1-242948f73e26 execution completed in 102.254346ms with result: true
Task Realtime-2 completed with execution order: 7, completion order: 7
Verifying execution order:
  0: Normal-1 (priority: Normal, execution_order: 0, completion_order: 0)
  1: High-1 (priority: High, execution_order: 1, completion_order: 1)
  2: Low-1 (priority: Low, execution_order: 2, completion_order: 2)
  3: Normal-2 (priority: Normal, execution_order: 3, completion_order: 3)
  4: High-2 (priority: High, execution_order: 4, completion_order: 4)
  5: Low-2 (priority: Low, execution_order: 5, completion_order: 5)
  6: Realtime-1 (priority: Realtime, execution_order: 6, completion_order: 6)
  7: Realtime-2 (priority: Realtime, execution_order: 7, completion_order: 7)

thread 'integration::executor_tests::test_priority_queue_execution_order' panicked at prisma_ai/tests/integration/executor_tests.rs:3523:5:
assertion `left == right` failed: First task should be Realtime priority
  left: Normal
 right: Realtime
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace


failures:
    integration::executor_tests::test_priority_queue_execution_order

test result: FAILED. 0 passed; 1 failed; 0 ignored; 0 measured; 158 filtered out; finished in 0.82s

error: test failed, to rerun pass `--test integration_tests`
(base) daleneil@dales-MBP prisma_workspace % 