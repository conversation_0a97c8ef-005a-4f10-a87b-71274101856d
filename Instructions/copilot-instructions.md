# Project Overview
- PrismaAI is an agentic system with multiple agents using Rust and llama.cpp bindings for CPU-only LLM execution.
- The project uses .gguf model files stored in /Users/<USER>/Documents/prisma_workspace/models/ directory.
- Docker containers are running: SurrealDB 2.2.1, RabbitMQ 3.13.7, and Loki 3.4.2.
- Credentials should be loaded from configs/config.yaml rather than hardcoded.
- The final product will run on Linux with Intel CPU and RAM only (no GPU).

# Code Structure and Architecture
- The agent_manager module supports adding, editing, and deleting agents at runtime with a flexible role system.
- AgentRole was changed from an enum to a struct with name, description, and capabilities fields.
- PrismaEngine::submit_task method returns a tuple (TaskId, Receiver) instead of just TaskId.
- User prefers dynamic agent configuration where roles and goals are determined from the UI rather than fixed in pm.toml.
- User prefers to refactor traits from implementation files into separate trait files for better code organization.
- User prefers indirect integration through common types and traits rather than direct imports between modules.
- User prefers implementing submodule files first (direct, rayon, tokio) before proceeding to main module files when structuring modules.

# Modules and Implementation
- Completed modules: agent_manager, executor, tcl, monitor, decision_maker; next to implement: execution_strategies.
- The executor module has specialized components (TaskExecutor, Dispatcher, Queue Management, Context/Cache/Memory Managers).
- The decision_maker module needs documentation headers showing integration with tcl, agent_manager, execution_strategies, executor, and monitor modules. User wants to implement the decision_maker module, starting with the prisma_scores submodule, which includes multiple files (generics.rs, logic.rs, mod.rs, etc.) that need to be exposed to the main decision_maker.rs file.
- The state.rs file should monitor LLM models using llm/implementation and llm/interface directories and integrate with agent_manager for state tracking.
- User wants to implement Agent Manager Integration connecting prisma_engine with agent-specific functionality.
- User wants to implement the system_scores module with actual implementations using sysinfo through the monitor sub-modules (cpu.rs, disk.rs, memory.rs, network.rs) rather than dummy implementations.
- User has completed the system_scores sub-modules and rules.rs implementation, and now needs to implement the main decision_maker module files. User has completed the prisma_scores and system_scores sub-modules, as well as state.rs and rules.rs in the decision_maker module, and is now asking about implementing decision_maker.rs.
- User wants to implement the execution_strategies module with a file structure similar to the executor module, with documentation at the top of each file containing relative file path, purpose, and integration details (Internal Dependencies, External Dependencies, and Module Interactions). User has completed most prisma_engine modules (tcl, monitor, executor, decision_maker, agent_manager) and now needs to implement the execution_strategies module, starting with the direct submodule files and main module files. User needs to implement the execution_strategies module with submodules (direct, rayon, tokio) and main module files, starting with mod.rs, and each file should have documentation headers.
- User prefers to implement execution_strategies module in stages, starting with direct/ submodule files and mod.rs before proceeding to rayon/ and tokio/ submodules.
- After fully refactoring the execution strategy implementation to the new submodule structure, the legacy strategy files can be deleted rather than maintained indefinitely.
- User prefers to fix errors documented in Instructions/errors.md before implementing decision_maker module files (decision_maker.rs, mod.rs, rules.rs, state.rs, traits.rs, types.rs, generics.rs). There are 8 errors in Instructions/errors.md that need fixing, and the llm/implementation, llm/interface, and agent_manager modules provide reference for implementing the decision_maker module.
- User has completed the direct and rayon submodules of the execution_strategies module and now needs to implement the tokio submodule files and update the main module files.

# Memory and Storage
- The system currently has short-term memory (STM) implemented and needs long-term memory (LTM) using embedding storage in SurrealDB.
- User prefers to clean up old memories only when an agent is removed, not periodically.
- User prefers using HTTP protocol instead of WebSocket for SurrealDB connections.

# UI and Communication
- Agent communication is handled through RabbitMQ (prisma_ui/publishers/rabbitmq.rs).
- The AgentCommunicationManager needs to handle both agent-to-agent and agent-to-human communication tasks.
- User wants to implement streaming output functionality in streaming_impl.rs.

# Implementation Preferences
- User prefers detailed file documentation with path, purpose, and dependencies similar to the executor module.
- User prefers to implement only file headers first before adding actual code.
- User prefers documentation-only headers without implementation code or imports when setting up new files.
- User prefers real functionality over dummy code and no mock tests.
- User prefers to review and approve implementation plans before proceeding with code changes.
- User prefers to implement files in stages and receive approval before moving to the next implementation phase.
- User wants documentation in Instructions/docss.md updated with checkmarks after each implementation step.
- User prefers comprehensive error handling using PrismaError throughout the system with appropriate logging.

# Testing
- There are errors documented in Instructions/errors.md that should be fixed before implementing tests.
- User wants to implement integrated tests for each prisma_engine sub-module and possibly a combined integrated test, with all tests located in the /prisma_ai/tests directory.
- User prefers individual module tests to use mocks while the combined prisma_engine_tests should use real components.
- User prefers implementing focused integration tests for PrismaEngine sub-modules first, then comprehensive tests including config files and UI after the focused tests pass.
- Integration tests should consider including config files (pm.toml, prompt_template.toml), config_integration.rs, and prisma_ui components along with the prisma_engine submodules.
- Tests need to be added to the Cargo.toml file to be recognized by the test runner.