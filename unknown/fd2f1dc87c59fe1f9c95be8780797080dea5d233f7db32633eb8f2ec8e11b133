// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/learning/performance_tracker.rs
// =================================================================================================
// Purpose: Implementation of performance tracking for task execution metrics.
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    TaskCategory, TaskPriority, ExecutionStrategyType, TaskId, ResourceType, ResourceUsage
};

use super::traits::PerformanceTracker;
use super::types::{
    TaskPerformanceMetrics, StrategyPerformanceStats, SystemLoadSnapshot,
    PerformanceTrend, LearningConfig
};

/// Implementation of performance tracking
pub struct PerformanceTrackerImpl {
    /// Active task tracking (tasks that have started but not completed)
    active_tasks: Arc<RwLock<HashMap<TaskId, TaskPerformanceMetrics>>>,

    /// Completed task metrics (sliding window)
    completed_tasks: Arc<RwLock<Vec<TaskPerformanceMetrics>>>,

    /// Aggregated performance statistics
    strategy_stats: Arc<RwLock<HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>>>,

    /// Configuration for learning behavior
    config: LearningConfig,
}

impl PerformanceTrackerImpl {
    /// Create a new performance tracker
    pub fn new(config: LearningConfig) -> Self {
        Self {
            active_tasks: Arc::new(RwLock::new(HashMap::new())),
            completed_tasks: Arc::new(RwLock::new(Vec::new())),
            strategy_stats: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// Calculate performance statistics for a strategy-category combination
    async fn calculate_stats(
        &self,
        strategy: ExecutionStrategyType,
        category: TaskCategory,
        metrics: &[TaskPerformanceMetrics],
    ) -> StrategyPerformanceStats {
        let relevant_metrics: Vec<&TaskPerformanceMetrics> = metrics
            .iter()
            .filter(|m| m.execution_strategy == strategy && m.task_category == category)
            .collect();

        if relevant_metrics.is_empty() {
            return StrategyPerformanceStats {
                strategy,
                category,
                task_count: 0,
                success_count: 0,
                success_rate: 0.0,
                avg_execution_duration: Duration::from_millis(0),
                avg_queue_duration: Duration::from_millis(0),
                p95_execution_duration: Duration::from_millis(0),
                avg_resource_usage: HashMap::new(),
                trend: PerformanceTrend::Unknown,
                last_updated: SystemTime::now(),
            };
        }

        let task_count = relevant_metrics.len();
        let success_count = relevant_metrics.iter().filter(|m| m.success).count();
        let success_rate = success_count as f64 / task_count as f64;

        // Calculate average execution duration
        let total_execution_time: Duration = relevant_metrics
            .iter()
            .map(|m| m.execution_duration)
            .sum();
        let avg_execution_duration = total_execution_time / task_count as u32;

        // Calculate average queue duration
        let total_queue_time: Duration = relevant_metrics
            .iter()
            .map(|m| m.queue_duration)
            .sum();
        let avg_queue_duration = total_queue_time / task_count as u32;

        // Calculate 95th percentile execution duration
        let mut execution_times: Vec<Duration> = relevant_metrics
            .iter()
            .map(|m| m.execution_duration)
            .collect();
        execution_times.sort();
        let p95_index = (task_count as f64 * 0.95) as usize;
        let p95_execution_duration = execution_times
            .get(p95_index.min(task_count - 1))
            .copied()
            .unwrap_or(Duration::from_millis(0));

        // Calculate average resource usage
        let mut avg_resource_usage = HashMap::new();
        for resource_type in [ResourceType::CPU, ResourceType::Memory, ResourceType::DiskIO, ResourceType::GPU] {
            let total_usage: f64 = relevant_metrics
                .iter()
                .filter_map(|m| m.resource_usage.get(&resource_type))
                .map(|usage| usage.0)
                .sum();

            if task_count > 0 {
                avg_resource_usage.insert(resource_type, ResourceUsage(total_usage / task_count as f64));
            }
        }

        // Calculate trend (simplified - compare recent vs older performance)
        let trend = self.calculate_trend_for_metrics(&relevant_metrics).await;

        StrategyPerformanceStats {
            strategy,
            category,
            task_count,
            success_count,
            success_rate,
            avg_execution_duration,
            avg_queue_duration,
            p95_execution_duration,
            avg_resource_usage,
            trend,
            last_updated: SystemTime::now(),
        }
    }

    /// Calculate performance trend from metrics
    async fn calculate_trend_for_metrics(&self, metrics: &[&TaskPerformanceMetrics]) -> PerformanceTrend {
        if metrics.len() < 4 {
            return PerformanceTrend::Unknown;
        }

        // Split metrics into recent and older halves
        let mid_point = metrics.len() / 2;
        let older_metrics = &metrics[..mid_point];
        let recent_metrics = &metrics[mid_point..];

        // Calculate average execution time for each half
        let older_avg = older_metrics
            .iter()
            .map(|m| m.execution_duration.as_millis() as f64)
            .sum::<f64>() / older_metrics.len() as f64;

        let recent_avg = recent_metrics
            .iter()
            .map(|m| m.execution_duration.as_millis() as f64)
            .sum::<f64>() / recent_metrics.len() as f64;

        // Determine trend based on performance change
        let change_ratio = (recent_avg - older_avg) / older_avg;

        if change_ratio < -self.config.significance_threshold {
            PerformanceTrend::Improving // Lower execution time is better
        } else if change_ratio > self.config.significance_threshold {
            PerformanceTrend::Degrading // Higher execution time is worse
        } else {
            PerformanceTrend::Stable
        }
    }
}

#[async_trait]
impl PerformanceTracker for PerformanceTrackerImpl {
    async fn record_task_start(
        &mut self,
        task_id: TaskId,
        category: TaskCategory,
        priority: TaskPriority,
        strategy: ExecutionStrategyType,
        system_load: SystemLoadSnapshot,
    ) -> PrismaResult<()> {
        let now = SystemTime::now();

        let metrics = TaskPerformanceMetrics {
            task_id,
            task_category: category,
            task_priority: priority,
            execution_strategy: strategy,
            execution_duration: Duration::from_millis(0), // Will be updated on completion
            queue_duration: Duration::from_millis(0), // Will be calculated on completion
            success: false, // Will be updated on completion
            error_message: None,
            resource_usage: HashMap::new(), // Will be updated on completion
            system_load,
            submitted_at: now, // Approximation - ideally this would be set when task is submitted
            started_at: now,
            completed_at: now, // Will be updated on completion
        };

        let mut active_tasks = self.active_tasks.write().await;
        active_tasks.insert(task_id, metrics);

        debug!("Recorded task start for task {} with strategy {:?}", task_id, strategy);
        Ok(())
    }

    async fn record_task_completion(
        &mut self,
        task_id: TaskId,
        success: bool,
        error_message: Option<String>,
        resource_usage: HashMap<ResourceType, ResourceUsage>,
    ) -> PrismaResult<()> {
        let now = SystemTime::now();

        // Remove from active tasks and update with completion data
        let mut active_tasks = self.active_tasks.write().await;
        if let Some(mut metrics) = active_tasks.remove(&task_id) {
            metrics.success = success;
            metrics.error_message = error_message;
            metrics.resource_usage = resource_usage;
            metrics.completed_at = now;

            // Calculate actual durations
            metrics.execution_duration = now.duration_since(metrics.started_at)
                .unwrap_or(Duration::from_millis(0));
            metrics.queue_duration = metrics.started_at.duration_since(metrics.submitted_at)
                .unwrap_or(Duration::from_millis(0));

            // Add to completed tasks
            let mut completed_tasks = self.completed_tasks.write().await;
            completed_tasks.push(metrics);

            // Keep only recent tasks (sliding window)
            let cutoff_time = now - self.config.max_data_age;
            completed_tasks.retain(|m| m.completed_at > cutoff_time);

            debug!("Recorded task completion for task {} (success: {})", task_id, success);
        } else {
            warn!("Attempted to record completion for unknown task: {}", task_id);
        }

        Ok(())
    }

    async fn get_task_metrics(&self, task_id: TaskId) -> PrismaResult<Option<TaskPerformanceMetrics>> {
        // Check active tasks first
        let active_tasks = self.active_tasks.read().await;
        if let Some(metrics) = active_tasks.get(&task_id) {
            return Ok(Some(metrics.clone()));
        }

        // Check completed tasks
        let completed_tasks = self.completed_tasks.read().await;
        let metrics = completed_tasks
            .iter()
            .find(|m| m.task_id == task_id)
            .cloned();

        Ok(metrics)
    }

    async fn get_strategy_stats(
        &self,
        strategy: ExecutionStrategyType,
        category: TaskCategory,
    ) -> PrismaResult<Option<StrategyPerformanceStats>> {
        let strategy_stats = self.strategy_stats.read().await;
        Ok(strategy_stats.get(&(strategy, category)).cloned())
    }

    async fn get_all_stats(&self) -> PrismaResult<HashMap<(ExecutionStrategyType, TaskCategory), StrategyPerformanceStats>> {
        let strategy_stats = self.strategy_stats.read().await;
        Ok(strategy_stats.clone())
    }

    async fn update_statistics(&mut self) -> PrismaResult<()> {
        let completed_tasks = self.completed_tasks.read().await;
        let metrics: Vec<TaskPerformanceMetrics> = completed_tasks.clone();
        drop(completed_tasks);

        // Group metrics by strategy and category
        let mut strategy_category_combinations = std::collections::HashSet::new();
        for metrics in &metrics {
            strategy_category_combinations.insert((metrics.execution_strategy, metrics.task_category.clone()));
        }

        // Calculate stats for each combination
        let mut new_stats = HashMap::new();
        for (strategy, category) in strategy_category_combinations {
            let stats = self.calculate_stats(strategy, category.clone(), &metrics).await;
            new_stats.insert((strategy, category), stats);
        }

        // Update stored statistics
        let mut strategy_stats = self.strategy_stats.write().await;
        *strategy_stats = new_stats;

        info!("Updated performance statistics for {} strategy-category combinations", strategy_stats.len());
        Ok(())
    }

    async fn cleanup_old_data(&mut self) -> PrismaResult<()> {
        let cutoff_time = SystemTime::now() - self.config.max_data_age;

        // Clean up completed tasks
        let mut completed_tasks = self.completed_tasks.write().await;
        let initial_count = completed_tasks.len();
        completed_tasks.retain(|m| m.completed_at > cutoff_time);
        let removed_count = initial_count - completed_tasks.len();

        if removed_count > 0 {
            info!("Cleaned up {} old task metrics", removed_count);
        }

        Ok(())
    }
}

/// Create a new performance tracker with default configuration
pub fn create_performance_tracker() -> PerformanceTrackerImpl {
    PerformanceTrackerImpl::new(LearningConfig::default())
}

/// Create a new performance tracker with custom configuration
pub fn create_performance_tracker_with_config(config: LearningConfig) -> PerformanceTrackerImpl {
    PerformanceTrackerImpl::new(config)
}
