use std::collections::HashMap;

use prisma_ai::prisma::prisma_engine::decision_maker::{
    RuleBasedDecisionMaker,
    DecisionMakerConfig,
};
use prisma_ai::prisma::prisma_engine::decision_maker::traits::StrategySelector;
use prisma_ai::prisma::prisma_engine::decision_maker::prisma_scores::{
    create_task_score_analyzer,
    create_queue_score_analyzer,
    create_usage_score_analyzer,
    logic::combine_all_scores,
};
use prisma_ai::prisma::prisma_engine::types::{
    ExecutionStrategyType,
    TaskCategory,
    TaskPriority,
    ResourceType,
    ResourceUsage,
    PrismaScore,
    SystemScore,
};

/// Tests for creating a RuleBasedDecisionMaker with default configuration
#[tokio::test]
async fn test_decision_maker_default_config() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Verify default configuration values
    let config = decision_maker.get_config();
    assert_eq!(config.default_strategy, ExecutionStrategyType::Tokio,
               "Default strategy should be Tokio");
    assert!(config.high_cpu_threshold > 0.0,
            "High CPU threshold should be greater than 0");
    assert!(config.high_memory_threshold > 0.0,
            "High memory threshold should be greater than 0");
}

/// Tests for creating a RuleBasedDecisionMaker with custom configuration
#[tokio::test]
async fn test_decision_maker_custom_config() {
    // Create a custom configuration
    let mut custom_config = DecisionMakerConfig::default();
    custom_config.default_strategy = ExecutionStrategyType::Rayon;
    custom_config.high_cpu_threshold = 85.0;
    custom_config.high_memory_threshold = 90.0;
    custom_config.enable_rule_based_decisions = true;
    custom_config.enable_state_tracking = true;

    // Create a decision maker with custom configuration
    let decision_maker = RuleBasedDecisionMaker::new(custom_config.clone());

    // Verify custom configuration values
    let config = decision_maker.get_config();
    assert_eq!(config.default_strategy, ExecutionStrategyType::Rayon,
               "Custom default strategy should be Rayon");
    assert_eq!(config.high_cpu_threshold, 85.0,
               "Custom high CPU threshold should be 85.0");
    assert_eq!(config.high_memory_threshold, 90.0,
               "Custom high memory threshold should be 90.0");
    assert_eq!(config.enable_rule_based_decisions, true,
               "Rule-based decisions should be enabled");
    assert_eq!(config.enable_state_tracking, true,
               "State tracking should be enabled");
}

/// Tests for verifying default strategy settings for different task categories
#[tokio::test]
async fn test_default_strategy_settings() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Verify default strategies for different task categories
    // Note: get_recommended_strategy returns None for new decision makers (no learned strategies yet)
    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::LLMInference).await,
        None,
        "New decision maker should have no learned strategies for LLMInference"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::EmbeddingGeneration).await,
        None,
        "New decision maker should have no learned strategies for EmbeddingGeneration"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::DatabaseQuery).await,
        None,
        "New decision maker should have no learned strategies for DatabaseQuery"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::FileProcessing).await,
        None,
        "New decision maker should have no learned strategies for FileProcessing"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::NetworkRequest).await,
        None,
        "New decision maker should have no learned strategies for NetworkRequest"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::Internal).await,
        None,
        "New decision maker should have no learned strategies for Internal"
    );
}

/// Tests for learned strategy functionality
#[tokio::test]
async fn test_learned_strategy_functionality() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Initially, no learned strategies should exist
    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::LLMInference).await,
        None,
        "Initially, no learned strategies should exist"
    );

    // Set a learned strategy for LLMInference
    decision_maker.set_recommended_strategy(&TaskCategory::LLMInference, ExecutionStrategyType::Rayon).await;

    // Verify the learned strategy is returned
    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::LLMInference).await,
        Some(ExecutionStrategyType::Rayon),
        "Learned strategy should be returned"
    );

    // Verify other categories still return None
    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::EmbeddingGeneration).await,
        None,
        "Other categories should still return None"
    );

    // Set another learned strategy
    decision_maker.set_recommended_strategy(&TaskCategory::DatabaseQuery, ExecutionStrategyType::Direct).await;

    // Verify both learned strategies are preserved
    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::LLMInference).await,
        Some(ExecutionStrategyType::Rayon),
        "First learned strategy should be preserved"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::DatabaseQuery).await,
        Some(ExecutionStrategyType::Direct),
        "Second learned strategy should be returned"
    );

    // Clear all learned strategies
    decision_maker.clear_learned_strategies().await;

    // Verify all strategies are cleared
    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::LLMInference).await,
        None,
        "Learned strategies should be cleared"
    );

    assert_eq!(
        decision_maker.get_recommended_strategy(&TaskCategory::DatabaseQuery).await,
        None,
        "All learned strategies should be cleared"
    );
}

/// Tests for enabling/disabling rule-based decisions
#[tokio::test]
async fn test_rule_based_decisions_toggle() {
    // Create a decision maker with rule-based decisions disabled
    let mut config = DecisionMakerConfig::default();
    config.enable_rule_based_decisions = false;

    let decision_maker = RuleBasedDecisionMaker::new(config);

    // Verify rule-based decisions are disabled
    assert_eq!(
        decision_maker.get_config().enable_rule_based_decisions,
        false,
        "Rule-based decisions should be disabled"
    );

    // Create a decision maker with rule-based decisions enabled
    let mut config = DecisionMakerConfig::default();
    config.enable_rule_based_decisions = true;

    let decision_maker = RuleBasedDecisionMaker::new(config);

    // Verify rule-based decisions are enabled
    assert_eq!(
        decision_maker.get_config().enable_rule_based_decisions,
        true,
        "Rule-based decisions should be enabled"
    );
}

/// Tests for enabling/disabling state tracking
#[tokio::test]
async fn test_state_tracking_toggle() {
    // Create a decision maker with state tracking disabled
    let mut config = DecisionMakerConfig::default();
    config.enable_state_tracking = false;

    let decision_maker = RuleBasedDecisionMaker::new(config);

    // Verify state tracking is disabled
    assert_eq!(
        decision_maker.get_config().enable_state_tracking,
        false,
        "State tracking should be disabled"
    );

    // Create a decision maker with state tracking enabled
    let mut config = DecisionMakerConfig::default();
    config.enable_state_tracking = true;

    let decision_maker = RuleBasedDecisionMaker::new(config);

    // Verify state tracking is enabled
    assert_eq!(
        decision_maker.get_config().enable_state_tracking,
        true,
        "State tracking should be enabled"
    );
}

/// Tests for task score analysis for CPU-intensive tasks
#[tokio::test]
async fn test_task_score_analysis_cpu_intensive() {
    // Create a task score analyzer
    let task_analyzer = create_task_score_analyzer(None);

    // Define a CPU-intensive task category and parameters
    let category = TaskCategory::LLMInference;
    let priority = TaskPriority::Normal;

    // Create additional parameters to indicate CPU-intensive task
    let mut params = HashMap::new();
    params.insert("cpu_intensive".to_string(), "true".to_string());

    // Analyze the task score
    let score = task_analyzer.analyze_task_score(&category, priority, Some(params)).await.unwrap();

    // Verify the score has appropriate CPU resource allocation
    assert!(
        score.resources.get(&ResourceType::CPU).unwrap().0 >= 0.3,
        "CPU-intensive task should have high CPU resource allocation"
    );

    // Verify the score is normalized (all values between 0 and 1)
    for (_, usage) in &score.resources {
        assert!(
            usage.0 >= 0.0 && usage.0 <= 1.0,
            "Resource usage should be normalized between 0 and 1"
        );
    }
}

/// Tests for task score analysis for memory-intensive tasks
#[tokio::test]
async fn test_task_score_analysis_memory_intensive() {
    // Create a task score analyzer
    let task_analyzer = create_task_score_analyzer(None);

    // Define a memory-intensive task category and parameters
    let category = TaskCategory::EmbeddingGeneration;
    let priority = TaskPriority::High;

    // Create additional parameters to indicate memory-intensive task
    let mut params = HashMap::new();
    params.insert("memory_intensive".to_string(), "true".to_string());
    params.insert("model_size".to_string(), "large".to_string());

    // Analyze the task score
    let score = task_analyzer.analyze_task_score(&category, priority, Some(params.clone())).await.unwrap();

    // Verify the score has appropriate Memory resource allocation
    assert!(
        score.resources.get(&ResourceType::Memory).unwrap().0 >= 0.3,
        "Memory-intensive task should have high Memory resource allocation"
    );

    // Verify that high priority increases the resource allocation
    let normal_priority_score = task_analyzer.analyze_task_score(
        &category,
        TaskPriority::Normal,
        Some(params.clone())
    ).await.unwrap();

    assert!(
        score.resources.get(&ResourceType::Memory).unwrap().0 >
        normal_priority_score.resources.get(&ResourceType::Memory).unwrap().0,
        "Higher priority should increase resource allocation"
    );
}

/// Tests for task score analysis for I/O-intensive tasks
#[tokio::test]
async fn test_task_score_analysis_io_intensive() {
    // Create a task score analyzer
    let task_analyzer = create_task_score_analyzer(None);

    // Define an I/O-intensive task category and parameters
    let category = TaskCategory::FileProcessing;
    let priority = TaskPriority::Normal;

    // Create additional parameters to indicate I/O-intensive task
    let mut params = HashMap::new();
    params.insert("io_intensive".to_string(), "true".to_string());

    // Analyze the task score
    let score = task_analyzer.analyze_task_score(&category, priority, Some(params)).await.unwrap();

    // Verify the score has appropriate DiskIO resource allocation
    assert!(
        score.resources.get(&ResourceType::DiskIO).unwrap().0 >= 0.2,
        "I/O-intensive task should have high DiskIO resource allocation"
    );

    // Verify the score is normalized (all values between 0 and 1)
    for (_, usage) in &score.resources {
        assert!(
            usage.0 >= 0.0 && usage.0 <= 1.0,
            "Resource usage should be normalized between 0 and 1"
        );
    }
}

/// Tests for queue score analysis with different queue states
#[tokio::test]
async fn test_queue_score_analysis() {
    // Create a queue score analyzer
    let queue_analyzer = create_queue_score_analyzer(None);

    // Test with empty queue
    let empty_queue_score = queue_analyzer.analyze_queue_score(
        0, // queue length
        0.1, // avg wait time
        TaskPriority::Normal
    ).await.unwrap();

    // Test with normal queue
    let normal_queue_score = queue_analyzer.analyze_queue_score(
        10, // queue length
        1.0, // avg wait time
        TaskPriority::Normal
    ).await.unwrap();

    // Test with congested queue
    let congested_queue_score = queue_analyzer.analyze_queue_score(
        50, // queue length
        5.0, // avg wait time
        TaskPriority::Normal
    ).await.unwrap();

    // Verify that congested queue has higher resource allocation than normal queue
    for resource_type in [ResourceType::CPU, ResourceType::Memory] {
        assert!(
            congested_queue_score.resources.get(&resource_type).unwrap().0 >=
            normal_queue_score.resources.get(&resource_type).unwrap().0,
            "Congested queue should have higher resource allocation than normal queue"
        );
    }

    // Verify that normal queue has higher resource allocation than empty queue
    for resource_type in [ResourceType::CPU, ResourceType::Memory] {
        assert!(
            normal_queue_score.resources.get(&resource_type).unwrap().0 >=
            empty_queue_score.resources.get(&resource_type).unwrap().0,
            "Normal queue should have higher resource allocation than empty queue"
        );
    }
}

/// Tests for usage score analysis with different resource usage patterns
#[tokio::test]
async fn test_usage_score_analysis() {
    // Create a usage score analyzer
    let usage_analyzer = create_usage_score_analyzer(None);

    // Create resource usage data for low, medium, and high usage
    let mut low_usage = HashMap::new();
    low_usage.insert(ResourceType::CPU, vec![ResourceUsage(0.1), ResourceUsage(0.15), ResourceUsage(0.2)]);
    low_usage.insert(ResourceType::Memory, vec![ResourceUsage(0.2), ResourceUsage(0.25), ResourceUsage(0.3)]);

    let mut medium_usage = HashMap::new();
    medium_usage.insert(ResourceType::CPU, vec![ResourceUsage(0.4), ResourceUsage(0.45), ResourceUsage(0.5)]);
    medium_usage.insert(ResourceType::Memory, vec![ResourceUsage(0.5), ResourceUsage(0.55), ResourceUsage(0.6)]);

    let mut high_usage = HashMap::new();
    high_usage.insert(ResourceType::CPU, vec![ResourceUsage(0.8), ResourceUsage(0.85), ResourceUsage(0.9)]);
    high_usage.insert(ResourceType::Memory, vec![ResourceUsage(0.7), ResourceUsage(0.75), ResourceUsage(0.8)]);

    // Analyze usage scores
    let low_score = usage_analyzer.analyze_usage_score(&low_usage).await.unwrap();
    let medium_score = usage_analyzer.analyze_usage_score(&medium_usage).await.unwrap();
    let high_score = usage_analyzer.analyze_usage_score(&high_usage).await.unwrap();

    // Verify that high usage results in higher resource allocation
    for resource_type in [ResourceType::CPU, ResourceType::Memory] {
        assert!(
            high_score.resources.get(&resource_type).unwrap().0 >=
            medium_score.resources.get(&resource_type).unwrap().0,
            "High usage should result in higher resource allocation than medium usage"
        );

        assert!(
            medium_score.resources.get(&resource_type).unwrap().0 >=
            low_score.resources.get(&resource_type).unwrap().0,
            "Medium usage should result in higher resource allocation than low usage"
        );
    }
}

/// Tests for combined score calculation with different weight configurations
#[tokio::test]
async fn test_combined_score_calculation() {
    // Create resource maps for task, queue, and usage scores
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.3));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.4));
    let task_score = PrismaScore { resources: task_resources };

    let mut queue_resources = HashMap::new();
    queue_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    queue_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let queue_score = PrismaScore { resources: queue_resources };

    let mut usage_resources = HashMap::new();
    usage_resources.insert(ResourceType::CPU, ResourceUsage(0.8));
    usage_resources.insert(ResourceType::Memory, ResourceUsage(0.9));
    let usage_score = PrismaScore { resources: usage_resources };

    // Test with equal weights
    let equal_weights = [0.33, 0.33, 0.34]; // Approximately equal weights
    let _equal_combined = combine_all_scores(
        &task_score,
        Some(&queue_score),
        Some(&usage_score),
        &equal_weights
    ).unwrap();

    // Test with task-biased weights
    let task_biased_weights = [0.7, 0.15, 0.15];
    let task_biased_combined = combine_all_scores(
        &task_score,
        Some(&queue_score),
        Some(&usage_score),
        &task_biased_weights
    ).unwrap();

    // Test with usage-biased weights
    let usage_biased_weights = [0.15, 0.15, 0.7];
    let usage_biased_combined = combine_all_scores(
        &task_score,
        Some(&queue_score),
        Some(&usage_score),
        &usage_biased_weights
    ).unwrap();

    // Print the values for debugging
    println!("Task CPU: {}", task_score.resources.get(&ResourceType::CPU).unwrap().0);
    println!("Usage CPU: {}", usage_score.resources.get(&ResourceType::CPU).unwrap().0);
    println!("Task-biased CPU: {}", task_biased_combined.resources.get(&ResourceType::CPU).unwrap().0);
    println!("Usage-biased CPU: {}", usage_biased_combined.resources.get(&ResourceType::CPU).unwrap().0);

    // Verify that task-biased combined score is closer to task score
    let task_cpu_diff = (task_score.resources.get(&ResourceType::CPU).unwrap().0 -
                         task_biased_combined.resources.get(&ResourceType::CPU).unwrap().0).abs();
    let usage_cpu_diff = (usage_score.resources.get(&ResourceType::CPU).unwrap().0 -
                          task_biased_combined.resources.get(&ResourceType::CPU).unwrap().0).abs();

    assert!(
        task_cpu_diff < usage_cpu_diff,
        "Task-biased combined score should be closer to task score than usage score. Task diff: {}, Usage diff: {}",
        task_cpu_diff, usage_cpu_diff
    );

    // Verify that usage-biased combined score is closer to usage score
    let task_cpu_diff = (task_score.resources.get(&ResourceType::CPU).unwrap().0 -
                         usage_biased_combined.resources.get(&ResourceType::CPU).unwrap().0).abs();
    let usage_cpu_diff = (usage_score.resources.get(&ResourceType::CPU).unwrap().0 -
                          usage_biased_combined.resources.get(&ResourceType::CPU).unwrap().0).abs();

    assert!(
        usage_cpu_diff < task_cpu_diff,
        "Usage-biased combined score should be closer to usage score than task score. Usage diff: {}, Task diff: {}",
        usage_cpu_diff, task_cpu_diff
    );
}

// ===== Decision Making Tests =====

/// Test strategy selection for LLM inference tasks
#[tokio::test]
async fn test_strategy_selection_llm_inference() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score for LLM inference
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.7));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.7));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal priority
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Verify that LLM inference tasks use Tokio by default
    assert_eq!(
        strategy,
        ExecutionStrategyType::Tokio,
        "LLM inference tasks should use Tokio by default"
    );
    println!("LLM inference strategy selection reason: {}", reason.description);

    // Test with realtime priority
    let (realtime_strategy, realtime_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Realtime
    ).await.unwrap();

    // Verify that realtime tasks always use Tokio
    assert_eq!(
        realtime_strategy,
        ExecutionStrategyType::Tokio,
        "Realtime LLM inference tasks should use Tokio"
    );
    println!("Realtime LLM inference strategy selection reason: {}", realtime_reason.description);
}

/// Test strategy selection for embedding generation tasks
#[tokio::test]
async fn test_strategy_selection_embedding_generation() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score for embedding generation
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.8));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.7));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.8));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal priority
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::EmbeddingGeneration,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Verify that embedding generation tasks use Rayon by default
    assert_eq!(
        strategy,
        ExecutionStrategyType::Rayon,
        "Embedding generation tasks should use Rayon by default"
    );
    println!("Embedding generation strategy selection reason: {}", reason.description);

    // Create a system score with low CPU availability
    let mut low_cpu_availability = HashMap::new();
    low_cpu_availability.insert(ResourceType::CPU, ResourceUsage(0.2));
    low_cpu_availability.insert(ResourceType::Memory, ResourceUsage(0.8));
    let low_cpu_system_score = SystemScore { availability: low_cpu_availability };

    // Test with low CPU availability
    let (low_cpu_strategy, low_cpu_reason) = decision_maker.select_strategy(
        &TaskCategory::EmbeddingGeneration,
        &task_score,
        &low_cpu_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Low CPU embedding generation strategy selection reason: {}", low_cpu_reason.description);

    // Note: The actual strategy might vary based on the implementation
    // In some implementations, low CPU might cause a switch to Tokio
    // In others, it might still use Rayon
    // We're just verifying that a valid strategy is returned
    assert!(
        matches!(low_cpu_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio),
        "With low CPU, embedding generation should use either Rayon or Tokio"
    );
}

/// Test strategy selection for database query tasks
#[tokio::test]
async fn test_strategy_selection_database_query() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score for database query
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.3));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.4));
    task_resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.6));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.7));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.8));
    system_availability.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.7));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal priority
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::DatabaseQuery,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Verify that database query tasks use Tokio by default
    assert_eq!(
        strategy,
        ExecutionStrategyType::Tokio,
        "Database query tasks should use Tokio by default"
    );
    println!("Database query strategy selection reason: {}", reason.description);

    // Test with high priority
    let (high_priority_strategy, high_priority_reason) = decision_maker.select_strategy(
        &TaskCategory::DatabaseQuery,
        &task_score,
        &system_score,
        TaskPriority::High
    ).await.unwrap();

    // Verify that high priority database query tasks still use Tokio
    assert_eq!(
        high_priority_strategy,
        ExecutionStrategyType::Tokio,
        "High priority database query tasks should use Tokio"
    );
    println!("High priority database query strategy selection reason: {}", high_priority_reason.description);
}

/// Test strategy selection for file processing tasks
#[tokio::test]
async fn test_strategy_selection_file_processing() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score for file processing
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.6));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.3));
    task_resources.insert(ResourceType::DiskIO, ResourceUsage(0.7));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.7));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.8));
    system_availability.insert(ResourceType::DiskIO, ResourceUsage(0.6));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal priority
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::FileProcessing,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Verify that file processing tasks use Rayon by default
    assert_eq!(
        strategy,
        ExecutionStrategyType::Rayon,
        "File processing tasks should use Rayon by default"
    );
    println!("File processing strategy selection reason: {}", reason.description);

    // Create a system score with low disk IO availability
    let mut low_disk_availability = HashMap::new();
    low_disk_availability.insert(ResourceType::CPU, ResourceUsage(0.7));
    low_disk_availability.insert(ResourceType::Memory, ResourceUsage(0.8));
    low_disk_availability.insert(ResourceType::DiskIO, ResourceUsage(0.2));
    let low_disk_system_score = SystemScore { availability: low_disk_availability };

    // Test with low disk IO availability
    let (low_disk_strategy, low_disk_reason) = decision_maker.select_strategy(
        &TaskCategory::FileProcessing,
        &task_score,
        &low_disk_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Low disk IO file processing strategy selection reason: {}", low_disk_reason.description);

    // Note: The actual strategy might vary based on the implementation
    // In some implementations, low disk IO might cause a switch to a different strategy
    // We're just verifying that a valid strategy is returned
    assert!(
        matches!(low_disk_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "With low disk IO, file processing should use a valid strategy"
    );
}

/// Test resource constraint checking with sufficient resources
#[tokio::test]
async fn test_resource_constraint_sufficient() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score with moderate resource requirements
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.4));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    task_resources.insert(ResourceType::DiskIO, ResourceUsage(0.3));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with sufficient resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.7));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.8));
    system_availability.insert(ResourceType::DiskIO, ResourceUsage(0.9));
    let system_score = SystemScore { availability: system_availability };

    // Check if the task can be executed
    let can_execute = decision_maker.can_execute(&task_score, &system_score).await.unwrap();

    // Verify that the task can be executed with sufficient resources
    assert!(
        can_execute,
        "Task should be executable with sufficient resources"
    );
}

/// Test resource constraint checking with insufficient resources
#[tokio::test]
async fn test_resource_constraint_insufficient() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score with high resource requirements
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.9));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.8));
    task_resources.insert(ResourceType::DiskIO, ResourceUsage(0.7));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with insufficient resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.2));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.3));
    system_availability.insert(ResourceType::DiskIO, ResourceUsage(0.4));
    let system_score = SystemScore { availability: system_availability };

    // Check if the task can be executed
    let can_execute = decision_maker.can_execute(&task_score, &system_score).await.unwrap();

    // The result depends on the implementation:
    // - If the decision maker strictly enforces resource constraints, can_execute should be false
    // - If it's more lenient, it might still return true but adjust the strategy

    // For now, we'll just print the result for debugging
    println!("Can execute with insufficient resources: {}", can_execute);

    // Test strategy selection with insufficient resources
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::FileProcessing,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the strategy and reason for debugging
    println!("Strategy with insufficient resources: {:?}", strategy);
    println!("Reason: {}", reason.description);

    // Verify that a valid strategy is returned
    assert!(
        matches!(strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "With insufficient resources, a valid strategy should still be selected"
    );
}

/// Test priority-based decisions with different task priorities
#[tokio::test]
async fn test_priority_based_decisions() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.6));
    let system_score = SystemScore { availability: system_availability };

    // Test with low priority
    let (low_strategy, low_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Low
    ).await.unwrap();

    // Test with normal priority
    let (normal_strategy, normal_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Test with high priority
    let (high_strategy, high_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::High
    ).await.unwrap();

    // Test with realtime priority
    let (realtime_strategy, realtime_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Realtime
    ).await.unwrap();

    // Print the strategies and reasons for debugging
    println!("Low priority strategy: {:?}", low_strategy);
    println!("Low priority reason: {}", low_reason.description);
    println!("Normal priority strategy: {:?}", normal_strategy);
    println!("Normal priority reason: {}", normal_reason.description);
    println!("High priority strategy: {:?}", high_strategy);
    println!("High priority reason: {}", high_reason.description);
    println!("Realtime priority strategy: {:?}", realtime_strategy);
    println!("Realtime priority reason: {}", realtime_reason.description);

    // Verify that realtime tasks always use Tokio
    assert_eq!(
        realtime_strategy,
        ExecutionStrategyType::Tokio,
        "Realtime tasks should always use Tokio"
    );

    // Verify that all strategies are valid
    assert!(
        matches!(low_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Low priority tasks should use a valid strategy"
    );
    assert!(
        matches!(normal_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Normal priority tasks should use a valid strategy"
    );
    assert!(
        matches!(high_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "High priority tasks should use a valid strategy"
    );
}

// ===== Rule Evaluation Tests =====

/// Test priority rules with different task priorities
#[tokio::test]
async fn test_priority_rules() {
    // Create a decision maker with default configuration (includes priority rules)
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with high CPU usage to trigger high priority rule
    let mut high_cpu_availability = HashMap::new();
    high_cpu_availability.insert(ResourceType::CPU, ResourceUsage(0.8)); // 80% CPU usage (20% available)
    high_cpu_availability.insert(ResourceType::Memory, ResourceUsage(0.6));
    let high_cpu_system_score = SystemScore { availability: high_cpu_availability };

    // Test with realtime priority
    let (realtime_strategy, realtime_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &high_cpu_system_score,
        TaskPriority::Realtime
    ).await.unwrap();

    // Print the reason for debugging
    println!("Realtime priority rule reason: {}", realtime_reason.description);

    // Verify that realtime tasks use Tokio (as defined in the realtime priority rule)
    assert_eq!(
        realtime_strategy,
        ExecutionStrategyType::Tokio,
        "Realtime priority tasks should use Tokio"
    );

    // Test with high priority
    let (high_strategy, high_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &high_cpu_system_score,
        TaskPriority::High
    ).await.unwrap();

    // Print the reason for debugging
    println!("High priority rule reason: {}", high_reason.description);

    // Test with normal priority
    let (normal_strategy, normal_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &high_cpu_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Normal priority rule reason: {}", normal_reason.description);

    // Test with low priority
    let (low_strategy, low_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &high_cpu_system_score,
        TaskPriority::Low
    ).await.unwrap();

    // Print the reason for debugging
    println!("Low priority rule reason: {}", low_reason.description);

    // Verify that all strategies are valid
    assert!(
        matches!(high_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "High priority tasks should use a valid strategy"
    );
    assert!(
        matches!(normal_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Normal priority tasks should use a valid strategy"
    );
    assert!(
        matches!(low_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Low priority tasks should use a valid strategy"
    );
}

/// Test resource rules with different resource constraints
#[tokio::test]
async fn test_resource_rules() {
    // Create a decision maker with default configuration (includes resource rules)
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score for CPU-intensive task
    let mut cpu_task_resources = HashMap::new();
    cpu_task_resources.insert(ResourceType::CPU, ResourceUsage(0.7));
    cpu_task_resources.insert(ResourceType::Memory, ResourceUsage(0.3));
    let cpu_task_score = PrismaScore { resources: cpu_task_resources };

    // Create a task score for memory-intensive task
    let mut memory_task_resources = HashMap::new();
    memory_task_resources.insert(ResourceType::CPU, ResourceUsage(0.3));
    memory_task_resources.insert(ResourceType::Memory, ResourceUsage(0.7));
    let memory_task_score = PrismaScore { resources: memory_task_resources };

    // Create a system score with high CPU usage to trigger CPU constraint rule
    let mut high_cpu_availability = HashMap::new();
    high_cpu_availability.insert(ResourceType::CPU, ResourceUsage(0.9)); // 90% CPU usage (10% available)
    high_cpu_availability.insert(ResourceType::Memory, ResourceUsage(0.5));
    let high_cpu_system_score = SystemScore { availability: high_cpu_availability };

    // Create a system score with high memory usage to trigger memory constraint rule
    let mut high_memory_availability = HashMap::new();
    high_memory_availability.insert(ResourceType::CPU, ResourceUsage(0.5));
    high_memory_availability.insert(ResourceType::Memory, ResourceUsage(0.9)); // 90% memory usage (10% available)
    let high_memory_system_score = SystemScore { availability: high_memory_availability };

    // Test CPU constraint rule with CPU-intensive task
    let (cpu_strategy, cpu_reason) = decision_maker.select_strategy(
        &TaskCategory::EmbeddingGeneration, // CPU-intensive task category
        &cpu_task_score,
        &high_cpu_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("CPU constraint rule reason: {}", cpu_reason.description);

    // Test memory constraint rule with memory-intensive task
    let (memory_strategy, memory_reason) = decision_maker.select_strategy(
        &TaskCategory::Internal, // Simple task that could use Direct execution
        &memory_task_score,
        &high_memory_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Memory constraint rule reason: {}", memory_reason.description);

    // Verify that strategies are appropriate for the constraints
    // Note: The actual strategies may vary based on implementation details
    assert!(
        matches!(cpu_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio),
        "CPU-constrained tasks should use an appropriate strategy"
    );
    assert!(
        matches!(memory_strategy, ExecutionStrategyType::Direct | ExecutionStrategyType::Tokio | ExecutionStrategyType::Rayon),
        "Memory-constrained tasks should use an appropriate strategy"
    );
}

/// Test category rules with different task categories
#[tokio::test]
async fn test_category_rules() {
    // Create a decision maker with default configuration (includes category rules)
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.5));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.5));
    let system_score = SystemScore { availability: system_availability };

    // Test with different task categories
    let categories = [
        TaskCategory::LLMInference,
        TaskCategory::EmbeddingGeneration,
        TaskCategory::DatabaseQuery,
        TaskCategory::FileProcessing,
        TaskCategory::NetworkRequest,
        TaskCategory::Internal,
    ];

    for category in &categories {
        let (strategy, reason) = decision_maker.select_strategy(
            category,
            &task_score,
            &system_score,
            TaskPriority::Normal
        ).await.unwrap();

        // Print the reason for debugging
        println!("Category rule for {:?} reason: {}", category, reason.description);

        // Verify that the strategy is appropriate for the category
        match category {
            TaskCategory::LLMInference => {
                assert_eq!(
                    strategy,
                    ExecutionStrategyType::Tokio,
                    "LLMInference tasks should use Tokio"
                );
            },
            TaskCategory::EmbeddingGeneration => {
                assert_eq!(
                    strategy,
                    ExecutionStrategyType::Rayon,
                    "EmbeddingGeneration tasks should use Rayon"
                );
            },
            TaskCategory::DatabaseQuery => {
                assert_eq!(
                    strategy,
                    ExecutionStrategyType::Tokio,
                    "DatabaseQuery tasks should use Tokio"
                );
            },
            TaskCategory::FileProcessing => {
                assert_eq!(
                    strategy,
                    ExecutionStrategyType::Rayon,
                    "FileProcessing tasks should use Rayon"
                );
            },
            TaskCategory::NetworkRequest => {
                assert_eq!(
                    strategy,
                    ExecutionStrategyType::Tokio,
                    "NetworkRequest tasks should use Tokio"
                );
            },
            TaskCategory::Internal => {
                assert_eq!(
                    strategy,
                    ExecutionStrategyType::Direct,
                    "Internal tasks should use Direct"
                );
            },
            _ => {
                assert!(
                    matches!(strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
                    "Custom tasks should use a valid strategy"
                );
            }
        }
    }
}

/// Test system rules with different system states
#[tokio::test]
async fn test_system_rules() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut normal_availability = HashMap::new();
    normal_availability.insert(ResourceType::CPU, ResourceUsage(0.5));
    normal_availability.insert(ResourceType::Memory, ResourceUsage(0.5));
    let normal_system_score = SystemScore { availability: normal_availability };

    // Create a system score with critical resource availability
    let mut critical_availability = HashMap::new();
    critical_availability.insert(ResourceType::CPU, ResourceUsage(0.95)); // 95% CPU usage (5% available)
    critical_availability.insert(ResourceType::Memory, ResourceUsage(0.95)); // 95% memory usage (5% available)
    let critical_system_score = SystemScore { availability: critical_availability };

    // Test with normal system state
    let (normal_strategy, normal_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &normal_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Normal system state reason: {}", normal_reason.description);

    // Test with critical system state
    let (critical_strategy, critical_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &critical_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Critical system state reason: {}", critical_reason.description);

    // Verify that strategies are appropriate for the system state
    assert!(
        matches!(normal_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Normal system state should use a valid strategy"
    );
    assert!(
        matches!(critical_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Critical system state should use a valid strategy"
    );

    // Check if the task can be executed in normal system state
    let can_execute_normal = decision_maker.can_execute(&task_score, &normal_system_score).await.unwrap();
    println!("Can execute in normal system state: {}", can_execute_normal);

    // Check if the task can be executed in critical system state
    let can_execute_critical = decision_maker.can_execute(&task_score, &critical_system_score).await.unwrap();
    println!("Can execute in critical system state: {}", can_execute_critical);

    // Verify that tasks can be executed in normal system state
    assert!(
        can_execute_normal,
        "Tasks should be executable in normal system state"
    );
}

/// Test rule priority handling with conflicting rules
#[tokio::test]
async fn test_rule_priority_handling() {
    // Create a decision maker with default configuration
    let mut decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.7));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.7));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with high resource usage
    let mut high_usage_availability = HashMap::new();
    high_usage_availability.insert(ResourceType::CPU, ResourceUsage(0.9)); // 90% CPU usage (10% available)
    high_usage_availability.insert(ResourceType::Memory, ResourceUsage(0.9)); // 90% memory usage (10% available)
    let high_usage_system_score = SystemScore { availability: high_usage_availability };

    // Test with realtime priority (highest priority rule)
    let (realtime_strategy, realtime_reason) = decision_maker.select_strategy(
        &TaskCategory::EmbeddingGeneration, // Would normally use Rayon
        &task_score,
        &high_usage_system_score,
        TaskPriority::Realtime // Should trigger realtime priority rule
    ).await.unwrap();

    // Print the reason for debugging
    println!("Realtime priority (highest priority) reason: {}", realtime_reason.description);

    // Verify that realtime priority rule takes precedence
    assert_eq!(
        realtime_strategy,
        ExecutionStrategyType::Tokio,
        "Realtime priority rule should take precedence"
    );

    // Test with high priority and high CPU usage (conflicting rules)
    let (high_priority_strategy, high_priority_reason) = decision_maker.select_strategy(
        &TaskCategory::EmbeddingGeneration, // Would normally use Rayon
        &task_score,
        &high_usage_system_score,
        TaskPriority::High // Should trigger high priority rule
    ).await.unwrap();

    // Print the reason for debugging
    println!("High priority with high CPU usage reason: {}", high_priority_reason.description);

    // Verify that a valid strategy is selected
    assert!(
        matches!(high_priority_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Conflicting rules should result in a valid strategy"
    );
}

/// Test rule set evaluation with multiple rules
#[tokio::test]
async fn test_rule_set_evaluation() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.6));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.6));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with high resource usage
    let mut high_usage_availability = HashMap::new();
    high_usage_availability.insert(ResourceType::CPU, ResourceUsage(0.85)); // 85% CPU usage (15% available)
    high_usage_availability.insert(ResourceType::Memory, ResourceUsage(0.85)); // 85% memory usage (15% available)
    let high_usage_system_score = SystemScore { availability: high_usage_availability };

    // Test with different combinations of task categories and priorities
    let test_cases = [
        (TaskCategory::LLMInference, TaskPriority::Realtime),
        (TaskCategory::EmbeddingGeneration, TaskPriority::High),
        (TaskCategory::DatabaseQuery, TaskPriority::Normal),
        (TaskCategory::FileProcessing, TaskPriority::Low),
        (TaskCategory::Internal, TaskPriority::Normal),
    ];

    for (category, priority) in &test_cases {
        let (strategy, reason) = decision_maker.select_strategy(
            category,
            &task_score,
            &high_usage_system_score,
            *priority
        ).await.unwrap();

        // Print the reason for debugging
        println!("Rule set evaluation for {:?} with {:?} priority reason: {}", category, priority, reason.description);

        // Verify that a valid strategy is selected
        assert!(
            matches!(strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
            "Rule set evaluation should result in a valid strategy"
        );
    }

    // Test with rule-based decisions disabled
    let mut config = DecisionMakerConfig::default();
    config.enable_rule_based_decisions = false;
    let decision_maker_no_rules = RuleBasedDecisionMaker::new(config);

    // Test with rule-based decisions disabled
    let (no_rules_strategy, no_rules_reason) = decision_maker_no_rules.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &high_usage_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("No rules evaluation reason: {}", no_rules_reason.description);

    // Verify that a valid strategy is selected even without rules
    assert!(
        matches!(no_rules_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "No rules evaluation should result in a valid strategy"
    );
}

// ===== Integration with Other Components Tests =====

/// Test integration with the monitor module for system metrics
#[tokio::test]
async fn test_integration_with_monitor() {
    use std::sync::Arc;
    use tokio::sync::RwLock;
    use prisma_ai::prisma::prisma_engine::monitor::system::system_info::SystemInfoMonitor;
    use prisma_ai::prisma::prisma_engine::monitor::MonitorConfig;
    use prisma_ai::prisma::prisma_engine::monitor::system::traits::SystemMonitoring;
    use prisma_ai::prisma::prisma_engine::decision_maker::system_scores::create_system_score_evaluator;
    use prisma_ai::prisma::prisma_engine::decision_maker::system_scores::types::SystemScoreConfig;

    // Create a system monitor with default configuration
    let monitor_config = MonitorConfig::default();
    let system_monitor = SystemInfoMonitor::new(monitor_config);
    let system_monitor_arc = Arc::new(RwLock::new(system_monitor));

    // Create a decision maker with default configuration
    let mut config = DecisionMakerConfig::default();
    config.enable_rule_based_decisions = true;
    config.enable_state_tracking = true;

    // Create a decision maker that uses the system monitor for system metrics
    let decision_maker = RuleBasedDecisionMaker::with_system_monitor(config, system_monitor_arc.clone());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    // This would normally come from the monitor, but we'll create it manually for testing
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.7));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal priority
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Monitor integration strategy selection reason: {}", reason.description);

    // Verify that a valid strategy is selected
    assert!(
        matches!(strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Monitor integration should result in a valid strategy"
    );

    // Test can_execute with the system score
    let can_execute = decision_maker.can_execute(&task_score, &system_score).await.unwrap();
    println!("Can execute with monitor integration: {}", can_execute);

    // Verify that the task can be executed
    assert!(
        can_execute,
        "Task should be executable with monitor integration"
    );
}

/// Test integration with the state tracker for model state
#[tokio::test]
async fn test_integration_with_state_tracker() {
    use std::time::{Duration, SystemTime};
    use prisma_ai::prisma::prisma_engine::decision_maker::state::create_state_tracker_with_config;
    use prisma_ai::prisma::prisma_engine::decision_maker::types::{StateTrackerConfig, ModelState};

    // Create a state tracker with custom configuration
    let state_config = StateTrackerConfig {
        update_interval_ms: 1000,
        model_ttl: Duration::from_secs(60),
        max_loaded_models: 2,
        preload_models: false,
        preload_model_ids: Vec::new(),
    };
    let mut state_tracker = create_state_tracker_with_config(state_config);

    // Add a model state to the state tracker
    let model_state = ModelState {
        model_id: "test-model".to_string(),
        loaded: true,
        in_use: false,
        memory_usage: 1024 * 1024 * 1024, // 1 GB
        last_used: SystemTime::now(),
        load_time: 5.0, // 5 seconds
        tokens_processed: 1000,
        requests_processed: 10,
        avg_tokens_per_second: 200.0,
        kv_cache_usage: 0.5, // 50%
    };
    state_tracker.update_model_state("test-model", model_state.clone()).await.unwrap();

    // Create a decision maker with default configuration
    let mut config = DecisionMakerConfig::default();
    config.enable_rule_based_decisions = true;
    config.enable_state_tracking = true;

    // Create a decision maker
    let mut decision_maker = RuleBasedDecisionMaker::new(config);

    // Set the state tracker using the async method
    decision_maker.set_state_tracker(state_tracker).await.unwrap();

    // Create a task score for LLM inference
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    task_resources.insert(ResourceType::GPU, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.7));
    system_availability.insert(ResourceType::GPU, ResourceUsage(0.8));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal priority
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("State tracker integration strategy selection reason: {}", reason.description);

    // Verify that a valid strategy is selected
    assert!(
        matches!(strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "State tracker integration should result in a valid strategy"
    );

    // Test getting model state from the decision maker
    let model_state_result = decision_maker.get_model_state("test-model").await.unwrap();

    // Verify that the model state is retrieved correctly
    assert!(
        model_state_result.is_some(),
        "Model state should be retrievable from the decision maker"
    );

    if let Some(retrieved_state) = model_state_result {
        println!("Retrieved model state: model_id={}, loaded={}, memory_usage={}MB",
                 retrieved_state.model_id, retrieved_state.loaded, retrieved_state.memory_usage / (1024 * 1024));

        // Verify that the model state matches what we set
        assert_eq!(
            retrieved_state.model_id,
            "test-model",
            "Model ID should match"
        );
        assert_eq!(
            retrieved_state.loaded,
            true,
            "Model should be loaded"
        );
        assert_eq!(
            retrieved_state.memory_usage,
            1024 * 1024 * 1024,
            "Memory usage should match"
        );
    }
}

// ===== Edge Cases and Error Handling Tests =====

/// Test behavior with invalid task categories
#[tokio::test]
async fn test_invalid_task_categories() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with normal resource availability
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.7));
    let system_score = SystemScore { availability: system_availability };

    // Create a custom task category that's not in the default configuration
    // We'll use a custom variant of TaskCategory
    let custom_category = TaskCategory::Custom("TestCustomCategory".to_string());

    // Test with the custom category
    let (strategy, reason) = decision_maker.select_strategy(
        &custom_category,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Custom category strategy selection reason: {}", reason.description);

    // Verify that the decision_maker falls back to the default strategy
    assert_eq!(
        strategy,
        ExecutionStrategyType::Tokio, // Default strategy is Tokio
        "Custom category should use the default strategy"
    );

    // Test with an empty custom category
    let empty_category = TaskCategory::Custom("".to_string());
    let (empty_strategy, empty_reason) = decision_maker.select_strategy(
        &empty_category,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Empty category strategy selection reason: {}", empty_reason.description);

    // Verify that the decision_maker still returns a valid strategy
    assert!(
        matches!(empty_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Empty category should still return a valid strategy"
    );
}

/// Test behavior with missing resource metrics
#[tokio::test]
async fn test_missing_resource_metrics() {
    // Create a decision maker with default configuration
    let decision_maker = RuleBasedDecisionMaker::new(DecisionMakerConfig::default());

    // Create a task score that requires CPU and Memory
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    task_resources.insert(ResourceType::DiskIO, ResourceUsage(0.3));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score with missing resource metrics (only CPU, no Memory or DiskIO)
    let mut partial_availability = HashMap::new();
    partial_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    // Intentionally omit Memory and DiskIO
    let partial_system_score = SystemScore { availability: partial_availability };

    // Test with the partial system score
    let (partial_strategy, partial_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &partial_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Partial system score strategy selection reason: {}", partial_reason.description);

    // Verify that the decision_maker still returns a valid strategy
    assert!(
        matches!(partial_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Partial system score should still return a valid strategy"
    );

    // Test can_execute with the partial system score
    let can_execute_partial = decision_maker.can_execute(&task_score, &partial_system_score).await.unwrap();
    println!("Can execute with partial system score: {}", can_execute_partial);

    // Create a completely empty system score
    let empty_system_score = SystemScore { availability: HashMap::new() };

    // Test with the empty system score
    let (empty_strategy, empty_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &empty_system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Empty system score strategy selection reason: {}", empty_reason.description);

    // Verify that the decision_maker still returns a valid strategy
    assert!(
        matches!(empty_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Empty system score should still return a valid strategy"
    );

    // Test can_execute with the empty system score
    let can_execute_empty = decision_maker.can_execute(&task_score, &empty_system_score).await.unwrap();
    println!("Can execute with empty system score: {}", can_execute_empty);
}

/// Test recovery from evaluation errors
#[tokio::test]
async fn test_recovery_from_evaluation_errors() {
    // Create a decision maker with default configuration
    let mut config = DecisionMakerConfig::default();
    config.enable_rule_based_decisions = true;
    let decision_maker = RuleBasedDecisionMaker::new(config);

    // Create a task score
    let mut task_resources = HashMap::new();
    task_resources.insert(ResourceType::CPU, ResourceUsage(0.5));
    task_resources.insert(ResourceType::Memory, ResourceUsage(0.5));
    let task_score = PrismaScore { resources: task_resources };

    // Create a system score
    let mut system_availability = HashMap::new();
    system_availability.insert(ResourceType::CPU, ResourceUsage(0.6));
    system_availability.insert(ResourceType::Memory, ResourceUsage(0.7));
    let system_score = SystemScore { availability: system_availability };

    // Test with normal conditions
    let (strategy, reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("Normal conditions strategy selection reason: {}", reason.description);

    // Verify that the decision_maker returns a valid strategy
    assert!(
        matches!(strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "Normal conditions should return a valid strategy"
    );

    // Now create a scenario where rule evaluation would fail
    // We'll do this by disabling rule-based decisions
    let mut config_no_rules = DecisionMakerConfig::default();
    config_no_rules.enable_rule_based_decisions = false;
    let decision_maker_no_rules = RuleBasedDecisionMaker::new(config_no_rules);

    // Test with rule-based decisions disabled
    let (no_rules_strategy, no_rules_reason) = decision_maker_no_rules.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Normal
    ).await.unwrap();

    // Print the reason for debugging
    println!("No rules strategy selection reason: {}", no_rules_reason.description);

    // Verify that the decision_maker falls back to direct decision making
    assert!(
        matches!(no_rules_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "No rules should fall back to direct decision making"
    );

    // Test with a very high task priority to ensure it still works
    let (high_priority_strategy, high_priority_reason) = decision_maker.select_strategy(
        &TaskCategory::LLMInference,
        &task_score,
        &system_score,
        TaskPriority::Realtime
    ).await.unwrap();

    // Print the reason for debugging
    println!("High priority strategy selection reason: {}", high_priority_reason.description);

    // Verify that the decision_maker still returns a valid strategy
    assert!(
        matches!(high_priority_strategy, ExecutionStrategyType::Rayon | ExecutionStrategyType::Tokio | ExecutionStrategyType::Direct),
        "High priority should still return a valid strategy"
    );
}

// ===== SystemScore Evaluation Tests =====

/// Import necessary types for SystemScore tests
use std::time::SystemTime;

use prisma_ai::prisma::prisma_engine::decision_maker::{
    ResourceThreshold,
    ResourceTrend,
    CpuScoreCalculator,
    MemoryScoreCalculator,
    DiskScoreCalculator,
    NetworkScoreCalculator,
    ResourceScoreCalculator,
};
use prisma_ai::prisma::prisma_engine::monitor::system::types::{
    SystemMetrics,
    CpuMetrics,
    MemoryMetrics,
    DiskMetrics,
    NetworkMetrics,
    DiskInfo,
    NetworkInterfaceInfo,
};

/// Tests for CPU score calculation with different utilization levels
#[tokio::test]
async fn test_cpu_score_calculation() {
    // Create a CPU score calculator with default configuration
    let cpu_calculator = CpuScoreCalculator::new();

    // Create system metrics with low CPU utilization
    let low_cpu_metrics = create_system_metrics_with_cpu(20.0);

    // Calculate score for low CPU utilization
    let low_score = cpu_calculator.calculate_score(&low_cpu_metrics).await.unwrap();

    // Create system metrics with medium CPU utilization
    let medium_cpu_metrics = create_system_metrics_with_cpu(50.0);

    // Calculate score for medium CPU utilization
    let medium_score = cpu_calculator.calculate_score(&medium_cpu_metrics).await.unwrap();

    // Create system metrics with high CPU utilization
    let high_cpu_metrics = create_system_metrics_with_cpu(80.0);

    // Calculate score for high CPU utilization
    let high_score = cpu_calculator.calculate_score(&high_cpu_metrics).await.unwrap();

    // Verify that higher CPU utilization results in lower availability
    assert!(
        low_score.availability.0 > medium_score.availability.0,
        "Low CPU utilization should result in higher availability than medium utilization"
    );

    assert!(
        medium_score.availability.0 > high_score.availability.0,
        "Medium CPU utilization should result in higher availability than high utilization"
    );

    // Print the actual availability levels for debugging
    println!("Low CPU utilization level: {:?}", low_score.level);
    println!("Medium CPU utilization level: {:?}", medium_score.level);
    println!("High CPU utilization level: {:?}", high_score.level);

    // Verify that higher CPU utilization results in lower availability levels
    // Note: The actual levels may vary based on implementation details
    assert!(
        low_score.level as u8 >= medium_score.level as u8,
        "Low CPU utilization should result in higher or equal availability level than medium"
    );

    assert!(
        medium_score.level as u8 >= high_score.level as u8,
        "Medium CPU utilization should result in higher or equal availability level than high"
    );
}

/// Tests for memory score calculation with different utilization levels
#[tokio::test]
async fn test_memory_score_calculation() {
    // Create a memory score calculator with default configuration
    let memory_calculator = MemoryScoreCalculator::new();

    // Create system metrics with low memory utilization
    let low_memory_metrics = create_system_metrics_with_memory(30.0, 10.0);

    // Calculate score for low memory utilization
    let low_score = memory_calculator.calculate_score(&low_memory_metrics).await.unwrap();

    // Create system metrics with medium memory utilization
    let medium_memory_metrics = create_system_metrics_with_memory(60.0, 30.0);

    // Calculate score for medium memory utilization
    let medium_score = memory_calculator.calculate_score(&medium_memory_metrics).await.unwrap();

    // Create system metrics with high memory utilization
    let high_memory_metrics = create_system_metrics_with_memory(85.0, 50.0);

    // Calculate score for high memory utilization
    let high_score = memory_calculator.calculate_score(&high_memory_metrics).await.unwrap();

    // Verify that higher memory utilization results in lower availability
    assert!(
        low_score.availability.0 > medium_score.availability.0,
        "Low memory utilization should result in higher availability than medium utilization"
    );

    assert!(
        medium_score.availability.0 > high_score.availability.0,
        "Medium memory utilization should result in higher availability than high utilization"
    );

    // Print the actual availability levels for debugging
    println!("Low memory utilization level: {:?}", low_score.level);
    println!("Medium memory utilization level: {:?}", medium_score.level);
    println!("High memory utilization level: {:?}", high_score.level);

    // Verify that higher memory utilization results in lower availability levels
    // Note: The actual levels may vary based on implementation details
    assert!(
        low_score.level as u8 >= medium_score.level as u8,
        "Low memory utilization should result in higher or equal availability level than medium"
    );

    assert!(
        medium_score.level as u8 >= high_score.level as u8,
        "Medium memory utilization should result in higher or equal availability level than high"
    );
}

/// Tests for disk score calculation with different I/O rates
#[tokio::test]
async fn test_disk_score_calculation() {
    // Create a disk score calculator with default configuration
    let disk_calculator = DiskScoreCalculator::new();

    // Create system metrics with low disk I/O
    let low_disk_metrics = create_system_metrics_with_disk(20.0, 10.0, 5.0);

    // Calculate score for low disk I/O
    let low_score = disk_calculator.calculate_score(&low_disk_metrics).await.unwrap();

    // Create system metrics with medium disk I/O
    let medium_disk_metrics = create_system_metrics_with_disk(50.0, 30.0, 20.0);

    // Calculate score for medium disk I/O
    let medium_score = disk_calculator.calculate_score(&medium_disk_metrics).await.unwrap();

    // Create system metrics with high disk I/O
    let high_disk_metrics = create_system_metrics_with_disk(80.0, 70.0, 60.0);

    // Calculate score for high disk I/O
    let high_score = disk_calculator.calculate_score(&high_disk_metrics).await.unwrap();

    // Verify that higher disk I/O results in lower availability
    assert!(
        low_score.availability.0 > medium_score.availability.0,
        "Low disk I/O should result in higher availability than medium I/O"
    );

    assert!(
        medium_score.availability.0 > high_score.availability.0,
        "Medium disk I/O should result in higher availability than high I/O"
    );

    // Print the actual availability levels for debugging
    println!("Low disk I/O level: {:?}", low_score.level);
    println!("Medium disk I/O level: {:?}", medium_score.level);
    println!("High disk I/O level: {:?}", high_score.level);

    // Verify that higher disk I/O results in lower availability levels
    // Note: The actual levels may vary based on implementation details
    assert!(
        low_score.level as u8 >= medium_score.level as u8,
        "Low disk I/O should result in higher or equal availability level than medium"
    );

    assert!(
        medium_score.level as u8 >= high_score.level as u8,
        "Medium disk I/O should result in higher or equal availability level than high"
    );
}

/// Tests for network score calculation with different bandwidth usage
#[tokio::test]
async fn test_network_score_calculation() {
    // Create a network score calculator with default configuration
    let network_calculator = NetworkScoreCalculator::new();

    // Create system metrics with low network bandwidth usage
    let low_network_metrics = create_system_metrics_with_network(10.0, 5.0);

    // Calculate score for low network bandwidth usage
    let low_score = network_calculator.calculate_score(&low_network_metrics).await.unwrap();

    // Create system metrics with medium network bandwidth usage
    let medium_network_metrics = create_system_metrics_with_network(40.0, 30.0);

    // Calculate score for medium network bandwidth usage
    let medium_score = network_calculator.calculate_score(&medium_network_metrics).await.unwrap();

    // Create system metrics with high network bandwidth usage
    let high_network_metrics = create_system_metrics_with_network(80.0, 70.0);

    // Calculate score for high network bandwidth usage
    let high_score = network_calculator.calculate_score(&high_network_metrics).await.unwrap();

    // Verify that higher network bandwidth usage results in lower availability
    assert!(
        low_score.availability.0 > medium_score.availability.0,
        "Low network bandwidth usage should result in higher availability than medium usage"
    );

    assert!(
        medium_score.availability.0 > high_score.availability.0,
        "Medium network bandwidth usage should result in higher availability than high usage"
    );

    // Print the actual availability levels for debugging
    println!("Low network bandwidth usage level: {:?}", low_score.level);
    println!("Medium network bandwidth usage level: {:?}", medium_score.level);
    println!("High network bandwidth usage level: {:?}", high_score.level);

    // Verify that higher network bandwidth usage results in lower availability levels
    // Note: The actual levels may vary based on implementation details
    assert!(
        low_score.level as u8 >= medium_score.level as u8,
        "Low network bandwidth usage should result in higher or equal availability level than medium"
    );

    assert!(
        medium_score.level as u8 >= high_score.level as u8,
        "Medium network bandwidth usage should result in higher or equal availability level than high"
    );
}

/// Tests for trend analysis with increasing, decreasing, and stable patterns
#[tokio::test]
async fn test_trend_analysis() {
    // Create a CPU score calculator with default configuration
    let cpu_calculator = CpuScoreCalculator::new();

    // Create system metrics with different CPU utilization levels
    let low_cpu_metrics = create_system_metrics_with_cpu(20.0);
    let medium_cpu_metrics = create_system_metrics_with_cpu(50.0);
    let high_cpu_metrics = create_system_metrics_with_cpu(80.0);

    // Calculate scores for different CPU utilization levels
    let low_score = cpu_calculator.calculate_score(&low_cpu_metrics).await.unwrap();
    let medium_score = cpu_calculator.calculate_score(&medium_cpu_metrics).await.unwrap();
    let high_score = cpu_calculator.calculate_score(&high_cpu_metrics).await.unwrap();

    // Test increasing trend (availability decreasing)
    let increasing_historical_scores = vec![high_score.clone(), medium_score.clone()];
    let increasing_trend = cpu_calculator.calculate_trend(&low_score, &increasing_historical_scores).await.unwrap();

    // Test decreasing trend (availability increasing)
    let decreasing_historical_scores = vec![low_score.clone(), medium_score.clone()];
    let decreasing_trend = cpu_calculator.calculate_trend(&high_score, &decreasing_historical_scores).await.unwrap();

    // Test stable trend
    let stable_historical_scores = vec![medium_score.clone(), medium_score.clone()];
    let stable_trend = cpu_calculator.calculate_trend(&medium_score, &stable_historical_scores).await.unwrap();

    // Verify that trends are correctly identified
    assert_eq!(
        increasing_trend, ResourceTrend::Increasing,
        "Trend should be increasing when availability is increasing"
    );

    assert_eq!(
        decreasing_trend, ResourceTrend::Decreasing,
        "Trend should be decreasing when availability is decreasing"
    );

    assert_eq!(
        stable_trend, ResourceTrend::Stable,
        "Trend should be stable when availability is stable"
    );
}

/// Tests for system critical state detection with different thresholds
#[tokio::test]
async fn test_system_critical_state_detection() {
    // Create a CPU score calculator with custom thresholds
    let cpu_threshold = ResourceThreshold {
        resource_type: ResourceType::CPU,
        high_threshold: 75.0,
        medium_threshold: 50.0,
        low_threshold: 25.0,
        enabled: true,
    };
    let cpu_calculator = CpuScoreCalculator::with_config(cpu_threshold, 1.0);

    // Create system metrics with different CPU utilization levels
    let normal_cpu_metrics = create_system_metrics_with_cpu(60.0);
    let critical_cpu_metrics = create_system_metrics_with_cpu(90.0);

    // Calculate scores for different CPU utilization levels
    let normal_score = cpu_calculator.calculate_score(&normal_cpu_metrics).await.unwrap();
    let critical_score = cpu_calculator.calculate_score(&critical_cpu_metrics).await.unwrap();

    // Print the actual availability levels for debugging
    println!("Normal CPU utilization level: {:?}", normal_score.level);
    println!("Critical CPU utilization level: {:?}", critical_score.level);
    println!("Normal CPU availability: {}", normal_score.availability.0);
    println!("Critical CPU availability: {}", critical_score.availability.0);

    // Verify that critical state has lower availability than normal state
    assert!(
        normal_score.level as u8 >= critical_score.level as u8,
        "Normal CPU utilization should result in higher or equal availability level than critical"
    );

    // Verify that availability values reflect the critical state
    assert!(
        normal_score.availability.0 > critical_score.availability.0,
        "Normal CPU utilization should result in higher availability than critical utilization"
    );

    // Verify that critical availability is low
    assert!(
        critical_score.availability.0 < 50.0,
        "Critical CPU utilization should result in low availability"
    );
}

// Helper functions to create test metrics

/// Create system metrics with specified CPU utilization
fn create_system_metrics_with_cpu(usage_percent: f64) -> SystemMetrics {
    let cpu_metrics = CpuMetrics {
        usage_percent,
        core_usage_percent: vec![usage_percent; 4],
        physical_cores: 2,
        logical_cores: 4,
        frequency_mhz: Some(2500.0),
        load_average: Some((usage_percent / 100.0 * 4.0, 0.0, 0.0)),
        temperature_celsius: None,
        timestamp: SystemTime::now(),
    };

    SystemMetrics {
        cpu: Some(cpu_metrics),
        memory: None,
        disk: None,
        network: None,
        timestamp: SystemTime::now(),
    }
}

/// Create system metrics with specified memory utilization
fn create_system_metrics_with_memory(usage_percent: f64, swap_usage_percent: f64) -> SystemMetrics {
    let total_bytes = 16 * 1024 * 1024 * 1024; // 16 GB
    let used_bytes = (total_bytes as f64 * usage_percent / 100.0) as u64;
    let free_bytes = total_bytes - used_bytes;
    let available_bytes = free_bytes;

    let swap_total_bytes = 8 * 1024 * 1024 * 1024; // 8 GB
    let swap_used_bytes = (swap_total_bytes as f64 * swap_usage_percent / 100.0) as u64;

    let memory_metrics = MemoryMetrics {
        total_bytes,
        available_bytes,
        used_bytes,
        free_bytes,
        usage_percent,
        swap_total_bytes,
        swap_used_bytes,
        swap_usage_percent,
        timestamp: SystemTime::now(),
    };

    SystemMetrics {
        cpu: None,
        memory: Some(memory_metrics),
        disk: None,
        network: None,
        timestamp: SystemTime::now(),
    }
}

/// Create system metrics with specified disk utilization and I/O rates
fn create_system_metrics_with_disk(usage_percent: f64, read_mb_per_sec: f64, write_mb_per_sec: f64) -> SystemMetrics {
    let total_bytes = 1024 * 1024 * 1024 * 1024; // 1 TB
    let used_bytes = (total_bytes as f64 * usage_percent / 100.0) as u64;
    let available_bytes = total_bytes - used_bytes;

    let read_bytes_per_sec = read_mb_per_sec * 1024.0 * 1024.0;
    let write_bytes_per_sec = write_mb_per_sec * 1024.0 * 1024.0;

    let mut disks = HashMap::new();
    disks.insert("root".to_string(), DiskInfo {
        name: "root".to_string(),
        total_bytes,
        available_bytes,
        used_bytes,
        usage_percent,
        disk_type: Some("SSD".to_string()),
        read_iops: Some(read_bytes_per_sec / 4096.0),
        write_iops: Some(write_bytes_per_sec / 4096.0),
        read_bytes_per_sec: Some(read_bytes_per_sec),
        write_bytes_per_sec: Some(write_bytes_per_sec),
    });

    let disk_metrics = DiskMetrics {
        disks,
        total_iops: Some((read_bytes_per_sec + write_bytes_per_sec) / 4096.0),
        total_throughput_bytes_per_sec: Some(read_bytes_per_sec + write_bytes_per_sec),
        timestamp: SystemTime::now(),
    };

    SystemMetrics {
        cpu: None,
        memory: None,
        disk: Some(disk_metrics),
        network: None,
        timestamp: SystemTime::now(),
    }
}

/// Create system metrics with specified network bandwidth usage
fn create_system_metrics_with_network(rx_mb_per_sec: f64, tx_mb_per_sec: f64) -> SystemMetrics {
    let rx_bytes_per_sec = rx_mb_per_sec * 1024.0 * 1024.0;
    let tx_bytes_per_sec = tx_mb_per_sec * 1024.0 * 1024.0;

    let mut interfaces = HashMap::new();
    interfaces.insert("eth0".to_string(), NetworkInterfaceInfo {
        name: "eth0".to_string(),
        is_up: true,
        mac_address: Some("00:11:22:33:44:55".to_string()),
        ip_addresses: vec!["*************".to_string()],
        rx_bytes_per_sec,
        tx_bytes_per_sec,
        rx_packets_per_sec: rx_bytes_per_sec / 1500.0,
        tx_packets_per_sec: tx_bytes_per_sec / 1500.0,
        rx_errors: 0,
        tx_errors: 0,
    });

    let network_metrics = NetworkMetrics {
        interfaces,
        total_rx_bytes_per_sec: rx_bytes_per_sec,
        total_tx_bytes_per_sec: tx_bytes_per_sec,
        timestamp: SystemTime::now(),
    };

    SystemMetrics {
        cpu: None,
        memory: None,
        disk: None,
        network: Some(network_metrics),
        timestamp: SystemTime::now(),
    }
}