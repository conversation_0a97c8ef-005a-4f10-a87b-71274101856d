// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/agent_communication.rs
// =================================================================================================
// Purpose: Implements the AgentCommunicator trait for agent-to-agent and agent-to-human communication.
// This file handles message passing between agents and humans, with support for direct messages,
// broadcasts, and conversation history. It integrates with RabbitMQ for real-time message delivery.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses AgentId, HumanId, AgentMessage, MessagePriority, and RecipientType
// - traits.rs: Implements the AgentCommunicator trait
// =================================================================================================
// External Dependencies:
// - std::collections: For HashMap and HashSet
// - tokio::sync::RwLock: For thread-safe access to shared data
// - tracing: For logging
// - chrono: For timestamp handling
// - uuid: For generating unique message IDs
// - serde_json: For message serialization
// - crate::err: For error handling
// - crate::storage::SurrealDbConnection: For database persistence
// - crate::config::YamlConfigManager: For loading RabbitMQ configuration
// =================================================================================================
// Module Interactions:
// - Used by AgentManager to facilitate communication between agents
// - Integrates with RabbitMQ for real-time message delivery
// - Interacts with the database for persistent storage of messages
// - Provides message history and conversation tracking
// - Enforces communication permissions (e.g., which agents can talk to humans)
// =================================================================================================

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use chrono::Utc;
use uuid::Uuid;
use serde_json::json;
use serde::{Serialize, Deserialize};

use crate::err::{PrismaResult, GenericError};
use crate::storage::{SurrealDbConnection, DataStore};
use crate::config::YamlConfigManager;

use super::types::{AgentId, HumanId, AgentMessage, MessagePriority, RecipientType};
use super::traits::AgentCommunicator;

/// Database representation of agent messages
#[derive(Debug, Serialize, Deserialize)]
struct AgentMessageRecord {
    /// Unique identifier for the message
    id: String,

    /// Agent that sent the message
    from_agent: String,

    /// Type of recipient (agent, human, broadcast)
    recipient_type: String,

    /// ID of the recipient (agent ID, human ID, or "broadcast")
    recipient_id: String,

    /// Optional list of recipient IDs for broadcast messages
    broadcast_recipients: Option<Vec<String>>,

    /// Content of the message
    content: String,

    /// Priority of the message
    priority: String,

    /// When the message was created
    #[serde(with = "chrono::serde::ts_seconds")]
    timestamp: chrono::DateTime<chrono::Utc>,

    /// Optional metadata for the message
    metadata: Option<serde_json::Value>,
}

/// RabbitMQ publisher for agent communication
#[derive(Debug, Clone)]
pub struct RabbitMQPublisher {
    /// Connection string for RabbitMQ
    connection_string: String,

    /// Exchange name for agent messages
    exchange_name: String,

    /// Queue name prefix for agent messages
    queue_prefix: String,

    /// Whether the publisher is connected
    is_connected: Arc<RwLock<bool>>,
}

impl RabbitMQPublisher {
    /// Creates a new RabbitMQPublisher
    pub async fn new(config_manager: Arc<YamlConfigManager>) -> PrismaResult<Self> {
        let connection_string = config_manager.get_rabbitmq_connection_string().await;
        info!("Initializing RabbitMQPublisher with connection: {}", connection_string);

        Ok(Self {
            connection_string,
            exchange_name: "agent.messages".to_string(),
            queue_prefix: "agent.queue.".to_string(),
            is_connected: Arc::new(RwLock::new(false)),
        })
    }

    /// Connects to RabbitMQ
    pub async fn connect(&self) -> PrismaResult<()> {
        // This would use the lapin-lws crate to connect to RabbitMQ
        // For now, we'll just set is_connected to true
        info!("Connecting to RabbitMQ: {}", self.connection_string);
        let mut is_connected = self.is_connected.write().await;
        *is_connected = true;
        Ok(())
    }

    /// Publishes a message to RabbitMQ
    pub async fn publish_message(&self, message: &AgentMessage) -> PrismaResult<()> {
        let is_connected = *self.is_connected.read().await;
        if !is_connected {
            return Err(GenericError::from("RabbitMQPublisher not connected"));
        }

        // This would use the lapin-lws crate to publish a message to RabbitMQ
        // For now, we'll just log the message
        match &message.to {
            RecipientType::Agent(agent_id) => {
                debug!("Publishing message to agent {}: {}", agent_id, message.content);
                // Routing key would be agent.{agent_id}
            }
            RecipientType::Human(human_id) => {
                debug!("Publishing message to human {}: {}", human_id, message.content);
                // Routing key would be human.{human_id}
            }
            RecipientType::Broadcast(recipients) => {
                debug!("Broadcasting message to {} recipients", recipients.len());
                // Would publish to multiple routing keys
            }
        }

        Ok(())
    }
}

/// Manager for agent-to-agent and agent-to-human communication
#[derive(Debug)]
pub struct AgentCommunicationManager {
    /// Optional database connection for message persistence
    db_connection: Option<Arc<SurrealDbConnection>>,

    /// In-memory message cache for quick access
    message_cache: Arc<RwLock<HashMap<String, Vec<AgentMessage>>>>,

    /// Agents that can communicate with humans
    human_communication_enabled: Arc<RwLock<HashSet<AgentId>>>,

    /// RabbitMQ publisher for sending messages
    rabbitmq_publisher: Arc<RwLock<Option<Arc<RabbitMQPublisher>>>>,

    /// Configuration manager for loading RabbitMQ credentials
    config_manager: Option<Arc<YamlConfigManager>>,

    /// Flag indicating if initialization has been attempted
    init_attempted: Arc<RwLock<bool>>,
}

impl AgentCommunicationManager {
    /// Creates a new AgentCommunicationManager
    pub fn new(
        db_connection: Option<Arc<SurrealDbConnection>>,
        config_manager: Option<Arc<YamlConfigManager>>,
    ) -> Self {
        info!("Initializing AgentCommunicationManager");
        Self {
            db_connection,
            message_cache: Arc::new(RwLock::new(HashMap::new())),
            human_communication_enabled: Arc::new(RwLock::new(HashSet::new())),
            rabbitmq_publisher: Arc::new(RwLock::new(None)),
            config_manager,
            init_attempted: Arc::new(RwLock::new(false)),
        }
    }

    /// Initializes the RabbitMQ publisher
    pub async fn init_rabbitmq(&self) -> PrismaResult<()> {
        // Check if initialization has already been attempted
        let init_attempted = *self.init_attempted.read().await;
        if init_attempted {
            // Check if already initialized
            let publisher_opt = self.rabbitmq_publisher.read().await;
            if publisher_opt.is_some() {
                debug!("RabbitMQ publisher already initialized");
                return Ok(());
            }
            debug!("RabbitMQ publisher initialization was attempted but failed previously");
            return Ok(());
        }

        // Mark initialization as attempted
        {
            let mut init_flag = self.init_attempted.write().await;
            *init_flag = true;
        }

        // Initialize if not already done
        if let Some(config_manager) = &self.config_manager {
            let publisher = RabbitMQPublisher::new(config_manager.clone()).await?;
            publisher.connect().await?;

            // Update the publisher in the RwLock
            let mut publisher_lock = self.rabbitmq_publisher.write().await;
            *publisher_lock = Some(Arc::new(publisher));

            info!("RabbitMQ publisher initialized");
            Ok(())
        } else {
            warn!("No config manager provided, RabbitMQ publisher not initialized");
            Ok(())
        }
    }

    /// Saves a message to the database if available
    async fn save_message_to_db(&self, message: &AgentMessage) -> PrismaResult<()> {
        if let Some(db) = &self.db_connection {
            // Extract recipient information
            let (recipient_type, recipient_id, broadcast_recipients) = match &message.to {
                RecipientType::Agent(id) => ("agent".to_string(), id.clone(), None),
                RecipientType::Human(id) => ("human".to_string(), id.clone(), None),
                RecipientType::Broadcast(ids) => {
                    ("broadcast".to_string(), "broadcast".to_string(), Some(ids.clone()))
                }
            };

            debug!("Saving message {} from {} to {} in database", message.id, message.from, recipient_id);

            // Convert priority to string
            let priority_str = match message.priority {
                MessagePriority::Low => "low",
                MessagePriority::Normal => "normal",
                MessagePriority::High => "high",
                MessagePriority::Urgent => "urgent",
            };

            // Create the message record
            let record = AgentMessageRecord {
                id: message.id.clone(),
                from_agent: message.from.clone(),
                recipient_type,
                recipient_id,
                broadcast_recipients,
                content: message.content.clone(),
                priority: priority_str.to_string(),
                timestamp: message.timestamp,
                metadata: message.metadata.clone(),
            };

            // Create the record in the database
            let create_query = "CREATE agent_messages CONTENT $1";
            let record_json = serde_json::to_string(&record).map_err(|e| GenericError::from(format!("Failed to serialize record: {}", e)))?;
            db.query::<serde_json::Value>(create_query, &[("1", &record_json)]).await?;

            debug!("Message {} saved to database", message.id);
        }
        Ok(())
    }

    /// Loads messages for a recipient from the database if available
    async fn load_messages_from_db(&self, recipient_id: &str) -> PrismaResult<Vec<AgentMessage>> {
        if let Some(db) = &self.db_connection {
            debug!("Loading messages for recipient {} from database", recipient_id);

            // Query for direct messages to this recipient
            let query = "SELECT * FROM agent_messages WHERE recipient_id = $1 ORDER BY timestamp DESC LIMIT 100";
            let records: Vec<AgentMessageRecord> = db.query(query, &[("1", recipient_id)]).await?;

            // Also query for broadcast messages that include this recipient
            let broadcast_query = "SELECT * FROM agent_messages WHERE recipient_type = 'broadcast' AND broadcast_recipients CONTAINS $1 ORDER BY timestamp DESC LIMIT 100";
            let broadcast_records: Vec<AgentMessageRecord> = db.query(broadcast_query, &[("1", recipient_id)]).await?;

            // Combine the results
            let mut all_records = records;
            all_records.extend(broadcast_records);

            // Sort by timestamp (newest first)
            all_records.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

            // Limit to the most recent 100 messages
            if all_records.len() > 100 {
                all_records.truncate(100);
            }

            // Convert records to AgentMessage objects
            let mut messages = Vec::new();
            for record in all_records {
                // Convert priority string to MessagePriority enum
                let priority = match record.priority.as_str() {
                    "low" => MessagePriority::Low,
                    "high" => MessagePriority::High,
                    "urgent" => MessagePriority::Urgent,
                    _ => MessagePriority::Normal, // Default to normal
                };

                // Create recipient type
                let to = match record.recipient_type.as_str() {
                    "agent" => RecipientType::Agent(record.recipient_id),
                    "human" => RecipientType::Human(record.recipient_id),
                    "broadcast" => {
                        if let Some(recipients) = record.broadcast_recipients {
                            RecipientType::Broadcast(recipients)
                        } else {
                            // Fallback if broadcast_recipients is missing
                            RecipientType::Broadcast(vec![recipient_id.to_string()])
                        }
                    },
                    _ => {
                        // Unknown recipient type, default to the recipient_id
                        RecipientType::Agent(record.recipient_id)
                    }
                };

                // Create the message
                let message = AgentMessage {
                    id: record.id,
                    from: record.from_agent,
                    to,
                    content: record.content,
                    priority,
                    timestamp: record.timestamp,
                    metadata: record.metadata,
                };

                messages.push(message);
            }

            debug!("Loaded {} messages for recipient {}", messages.len(), recipient_id);
            return Ok(messages);
        }

        // No database connection or no messages found
        Ok(Vec::new())
    }

    /// Adds a message to the in-memory cache
    async fn add_to_cache(&self, message: AgentMessage) -> PrismaResult<()> {
        let mut cache = self.message_cache.write().await;

        // Get the recipient ID or IDs
        match &message.to {
            RecipientType::Agent(id) | RecipientType::Human(id) => {
                // Add to recipient's message list
                cache.entry(id.clone())
                    .or_insert_with(Vec::new)
                    .push(message.clone());
            }
            RecipientType::Broadcast(ids) => {
                // Add to each recipient's message list
                for id in ids {
                    cache.entry(id.clone())
                        .or_insert_with(Vec::new)
                        .push(message.clone());
                }
            }
        }

        Ok(())
    }

    /// Gets messages for a recipient from the cache
    async fn get_from_cache(&self, recipient_id: &str) -> PrismaResult<Vec<AgentMessage>> {
        let cache = self.message_cache.read().await;

        if let Some(messages) = cache.get(recipient_id) {
            Ok(messages.clone())
        } else {
            Ok(Vec::new())
        }
    }

    /// Publishes a message to RabbitMQ
    async fn publish_to_rabbitmq(&self, message: &AgentMessage) -> PrismaResult<()> {
        let publisher_opt = self.rabbitmq_publisher.read().await;
        if let Some(publisher) = &*publisher_opt {
            publisher.publish_message(message).await?
        } else {
            debug!("RabbitMQ publisher not initialized, message not published");
        }

        Ok(())
    }

    /// Checks if an agent can communicate with humans
    async fn check_human_communication(&self, agent_id: &AgentId) -> PrismaResult<bool> {
        let enabled = self.human_communication_enabled.read().await;
        Ok(enabled.contains(agent_id))
    }
}

#[async_trait::async_trait]
impl AgentCommunicator for AgentCommunicationManager {
    async fn send_message(&self, message: AgentMessage) -> PrismaResult<()> {
        // Save to database for persistence
        self.save_message_to_db(&message).await?;

        // Add to in-memory cache for quick access
        self.add_to_cache(message.clone()).await?;

        // Publish to RabbitMQ for real-time delivery
        self.publish_to_rabbitmq(&message).await?;

        Ok(())
    }

    async fn send_agent_to_agent(&self, from: &AgentId, to: &AgentId, content: String, priority: MessagePriority) -> PrismaResult<()> {
        let message = AgentMessage {
            id: Uuid::new_v4().to_string(),
            from: from.clone(),
            to: RecipientType::Agent(to.clone()),
            content,
            priority,
            timestamp: Utc::now(),
            metadata: None,
        };

        self.send_message(message).await
    }

    async fn send_agent_to_human(&self, from: &AgentId, to: &HumanId, content: String, priority: MessagePriority) -> PrismaResult<()> {
        // Check if the agent is allowed to communicate with humans
        if !self.check_human_communication(from).await? {
            return Err(GenericError::from(format!("Agent {} is not allowed to communicate with humans", from)));
        }

        let message = AgentMessage {
            id: Uuid::new_v4().to_string(),
            from: from.clone(),
            to: RecipientType::Human(to.clone()),
            content,
            priority,
            timestamp: Utc::now(),
            metadata: None,
        };

        self.send_message(message).await
    }

    async fn broadcast_to_agents(&self, from: &AgentId, to: &[AgentId], content: String, priority: MessagePriority) -> PrismaResult<()> {
        if to.is_empty() {
            return Ok(());
        }

        let message = AgentMessage {
            id: Uuid::new_v4().to_string(),
            from: from.clone(),
            to: RecipientType::Broadcast(to.iter().map(|id| id.clone()).collect()),
            content,
            priority,
            timestamp: Utc::now(),
            metadata: None,
        };

        self.send_message(message).await
    }

    async fn broadcast_to_humans(&self, from: &AgentId, to: &[HumanId], content: String, priority: MessagePriority) -> PrismaResult<()> {
        // Check if the agent is allowed to communicate with humans
        if !self.check_human_communication(from).await? {
            return Err(GenericError::from(format!("Agent {} is not allowed to communicate with humans", from)));
        }

        if to.is_empty() {
            return Ok(());
        }

        let message = AgentMessage {
            id: Uuid::new_v4().to_string(),
            from: from.clone(),
            to: RecipientType::Broadcast(to.iter().map(|id| id.clone()).collect()),
            content,
            priority,
            timestamp: Utc::now(),
            metadata: None,
        };

        self.send_message(message).await
    }

    async fn get_messages(&self, agent_id: &AgentId) -> PrismaResult<Vec<AgentMessage>> {
        // Try to get from cache first
        let cached_messages = self.get_from_cache(agent_id).await?;

        if !cached_messages.is_empty() {
            return Ok(cached_messages);
        }

        // If not in cache, try to load from database
        let db_messages = self.load_messages_from_db(agent_id).await?;

        // Add loaded messages to cache
        if !db_messages.is_empty() {
            let mut cache = self.message_cache.write().await;
            cache.insert(agent_id.clone(), db_messages.clone());
        }

        Ok(db_messages)
    }

    async fn get_human_messages(&self, human_id: &HumanId) -> PrismaResult<Vec<AgentMessage>> {
        // Try to get from cache first
        let cached_messages = self.get_from_cache(human_id).await?;

        if !cached_messages.is_empty() {
            return Ok(cached_messages);
        }

        // If not in cache, try to load from database
        let db_messages = self.load_messages_from_db(human_id).await?;

        // Add loaded messages to cache
        if !db_messages.is_empty() {
            let mut cache = self.message_cache.write().await;
            cache.insert(human_id.clone(), db_messages.clone());
        }

        Ok(db_messages)
    }

    async fn get_agent_conversation(&self, agent1: &AgentId, agent2: &AgentId) -> PrismaResult<Vec<AgentMessage>> {
        // Get all messages for both agents
        let messages1 = self.get_messages(agent1).await?;
        let messages2 = self.get_messages(agent2).await?;

        // Filter for messages between these two agents
        let mut conversation = Vec::new();

        for message in messages1 {
            match &message.to {
                RecipientType::Agent(id) if id == agent2 => conversation.push(message),
                RecipientType::Broadcast(ids) if ids.contains(agent2) => conversation.push(message),
                _ => {}
            }
        }

        for message in messages2 {
            match &message.to {
                RecipientType::Agent(id) if id == agent1 => {
                    // Avoid duplicates
                    if !conversation.iter().any(|m| m.id == message.id) {
                        conversation.push(message);
                    }
                },
                RecipientType::Broadcast(ids) if ids.contains(agent1) => {
                    // Avoid duplicates
                    if !conversation.iter().any(|m| m.id == message.id) {
                        conversation.push(message);
                    }
                },
                _ => {}
            }
        }

        // Sort by timestamp
        conversation.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        Ok(conversation)
    }

    async fn get_human_conversation(&self, agent_id: &AgentId, human_id: &HumanId) -> PrismaResult<Vec<AgentMessage>> {
        // Get all messages for both the agent and the human
        let agent_messages = self.get_messages(agent_id).await?;
        let human_messages = self.get_human_messages(human_id).await?;

        // Filter for messages between the agent and the human
        let mut conversation = Vec::new();

        for message in agent_messages {
            match &message.to {
                RecipientType::Human(id) if id == human_id => conversation.push(message),
                RecipientType::Broadcast(ids) if ids.contains(human_id) => conversation.push(message),
                _ => {}
            }
        }

        for message in human_messages {
            if &message.from == agent_id {
                // Avoid duplicates
                if !conversation.iter().any(|m| m.id == message.id) {
                    conversation.push(message);
                }
            }
        }

        // Sort by timestamp
        conversation.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        Ok(conversation)
    }

    async fn set_human_communication_enabled(&self, agent_id: &AgentId, enabled: bool) -> PrismaResult<()> {
        let mut human_comm = self.human_communication_enabled.write().await;

        if enabled {
            human_comm.insert(agent_id.clone());
            debug!("Enabled human communication for agent {}", agent_id);
        } else {
            human_comm.remove(agent_id);
            debug!("Disabled human communication for agent {}", agent_id);
        }

        Ok(())
    }

    async fn can_communicate_with_humans(&self, agent_id: &AgentId) -> PrismaResult<bool> {
        self.check_human_communication(agent_id).await
    }
}
