// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/learning/adaptive_decision_maker.rs
// =================================================================================================
// Purpose: Adaptive decision maker that combines rule-based decisions with learning.
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    TaskCategory, TaskPriority, ExecutionStrategyType, PrismaScore, SystemScore, TaskId
};
use crate::prisma::prisma_engine::traits::DecisionLogic;
use crate::prisma::prisma_engine::decision_maker::decision_maker::RuleBasedDecisionMaker;

use super::traits::{LearningDecisionMaker, PerformanceTracker, AdaptationEngine};
use super::types::{
    TaskPerformanceMetrics, AdaptationRecommendation, LearningState, LearningConfig,
    SystemLoadSnapshot
};
use super::performance_tracker::PerformanceTrackerImpl;
use super::learning_engine::LearningEngineImpl;

/// Adaptive decision maker that learns from performance data
pub struct AdaptiveLearningDecisionMaker {
    /// Base rule-based decision maker
    base_decision_maker: Arc<RwLock<RuleBasedDecisionMaker>>,

    /// Performance tracker for collecting metrics
    performance_tracker: Arc<RwLock<PerformanceTrackerImpl>>,

    /// Learning engine for generating adaptations
    learning_engine: Arc<LearningEngineImpl>,

    /// Current learning state
    learning_state: Arc<RwLock<LearningState>>,

    /// Configuration for learning behavior
    config: LearningConfig,
}

impl AdaptiveLearningDecisionMaker {
    /// Create a new adaptive learning decision maker
    pub fn new(
        base_decision_maker: RuleBasedDecisionMaker,
        config: LearningConfig,
    ) -> Self {
        let performance_tracker = PerformanceTrackerImpl::new(config.clone());
        let learning_engine = LearningEngineImpl::new(config.clone());

        Self {
            base_decision_maker: Arc::new(RwLock::new(base_decision_maker)),
            performance_tracker: Arc::new(RwLock::new(performance_tracker)),
            learning_engine: Arc::new(learning_engine),
            learning_state: Arc::new(RwLock::new(LearningState::default())),
            config,
        }
    }

    /// Get current system load snapshot (simplified implementation)
    async fn get_system_load_snapshot(&self) -> SystemLoadSnapshot {
        // In a real implementation, this would collect actual system metrics
        // For now, we'll create a basic snapshot
        SystemLoadSnapshot {
            cpu_usage: 0.5, // 50% - placeholder
            memory_usage: 0.6, // 60% - placeholder
            active_tasks: 10, // placeholder
            queue_lengths: HashMap::new(), // placeholder
            timestamp: SystemTime::now(),
        }
    }

    /// Check if enough time has passed since last adaptation
    async fn should_adapt_now(&self) -> bool {
        let learning_state = self.learning_state.read().await;
        let time_since_last_adaptation = SystemTime::now()
            .duration_since(learning_state.last_adaptation)
            .unwrap_or(Duration::from_secs(0));

        time_since_last_adaptation >= self.config.adaptation_interval
    }

    /// Update performance statistics
    async fn update_performance_stats(&self) -> PrismaResult<()> {
        let mut performance_tracker = self.performance_tracker.write().await;
        performance_tracker.update_statistics().await?;
        performance_tracker.cleanup_old_data().await?;

        let mut learning_state = self.learning_state.write().await;
        learning_state.last_stats_update = SystemTime::now();

        Ok(())
    }
}

#[async_trait]
impl LearningDecisionMaker for AdaptiveLearningDecisionMaker {
    async fn decide_with_learning(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<ExecutionStrategyType> {
        // First, try to get a learned recommendation
        if self.config.enable_learning {
            let performance_tracker = self.performance_tracker.read().await;
            let all_stats = performance_tracker.get_all_stats().await?;
            drop(performance_tracker);

            // Check if we have learned a better strategy for this category
            if let Ok(Some(learned_strategy)) = self.learning_engine
                .get_best_strategy(task_category.clone(), &all_stats).await
            {
                debug!("Using learned strategy {:?} for category {:?}", learned_strategy, task_category);
                return Ok(learned_strategy);
            }
        }

        // Fall back to rule-based decision making
        let base_decision_maker = self.base_decision_maker.read().await;
        let strategy = base_decision_maker
            .decide_strategy(task_category, task_score, system_score, task_priority)
            .await?;

        debug!("Using rule-based strategy {:?} for category {:?}", strategy, task_category);
        Ok(strategy)
    }

    async fn update_with_performance(
        &mut self,
        metrics: TaskPerformanceMetrics,
    ) -> PrismaResult<()> {
        // Add metrics to learning state
        let mut learning_state = self.learning_state.write().await;
        learning_state.recent_metrics.push(metrics.clone());

        // Keep only recent metrics (sliding window)
        let cutoff_time = SystemTime::now() - self.config.max_data_age;
        learning_state.recent_metrics.retain(|m| m.completed_at > cutoff_time);

        debug!("Updated learning state with performance metrics for task {}", metrics.task_id);
        Ok(())
    }

    async fn apply_adaptations(
        &mut self,
        recommendations: Vec<AdaptationRecommendation>,
    ) -> PrismaResult<()> {
        if recommendations.is_empty() {
            return Ok(());
        }

        let mut base_decision_maker = self.base_decision_maker.write().await;
        let mut applied_count = 0;

        for recommendation in &recommendations {
            // Only apply high-confidence recommendations
            if recommendation.confidence >= 0.7 {
                base_decision_maker.set_recommended_strategy(
                    &recommendation.category,
                    recommendation.recommended_strategy,
                ).await;
                applied_count += 1;

                info!(
                    "Applied adaptation: {:?} -> {:?} (confidence: {:.2})",
                    recommendation.category,
                    recommendation.recommended_strategy,
                    recommendation.confidence
                );
            } else {
                debug!(
                    "Skipped low-confidence adaptation: {:?} -> {:?} (confidence: {:.2})",
                    recommendation.category,
                    recommendation.recommended_strategy,
                    recommendation.confidence
                );
            }
        }

        // Update learning state
        let mut learning_state = self.learning_state.write().await;
        learning_state.adaptation_history.extend(recommendations);
        learning_state.last_adaptation = SystemTime::now();

        info!("Applied {} out of {} adaptation recommendations", applied_count, learning_state.adaptation_history.len());
        Ok(())
    }

    async fn get_learning_state(&self) -> PrismaResult<LearningState> {
        let learning_state = self.learning_state.read().await;
        Ok(learning_state.clone())
    }

    async fn should_adapt(&self) -> PrismaResult<bool> {
        if !self.config.enable_real_time_adaptation {
            return Ok(false);
        }

        // Check if enough time has passed
        if !self.should_adapt_now().await {
            return Ok(false);
        }

        // Check if we have enough data
        let learning_state = self.learning_state.read().await;
        let has_enough_data = learning_state.recent_metrics.len() >= self.config.min_samples_for_adaptation;

        Ok(has_enough_data)
    }

    async fn perform_adaptation(&mut self) -> PrismaResult<Vec<AdaptationRecommendation>> {
        if !self.should_adapt().await? {
            return Ok(Vec::new());
        }

        // Update performance statistics
        self.update_performance_stats().await?;

        // Get current performance data
        let performance_tracker = self.performance_tracker.read().await;
        let all_stats = performance_tracker.get_all_stats().await?;
        drop(performance_tracker);

        // Generate adaptation recommendations
        let recommendations = self.learning_engine.analyze_performance(&all_stats).await?;

        if !recommendations.is_empty() {
            info!("Generated {} adaptation recommendations", recommendations.len());

            // Apply the recommendations
            self.apply_adaptations(recommendations.clone()).await?;
        }

        Ok(recommendations)
    }
}

/// Record task start for performance tracking
pub async fn record_task_start(
    decision_maker: &AdaptiveLearningDecisionMaker,
    task_id: TaskId,
    category: TaskCategory,
    priority: TaskPriority,
    strategy: ExecutionStrategyType,
) -> PrismaResult<()> {
    let system_load = decision_maker.get_system_load_snapshot().await;
    let mut performance_tracker = decision_maker.performance_tracker.write().await;
    performance_tracker.record_task_start(task_id, category, priority, strategy, system_load).await
}

/// Record task completion for performance tracking
pub async fn record_task_completion(
    decision_maker: &AdaptiveLearningDecisionMaker,
    task_id: TaskId,
    success: bool,
    error_message: Option<String>,
    resource_usage: HashMap<crate::prisma::prisma_engine::types::ResourceType, crate::prisma::prisma_engine::types::ResourceUsage>,
) -> PrismaResult<()> {
    let mut performance_tracker = decision_maker.performance_tracker.write().await;
    performance_tracker.record_task_completion(task_id, success, error_message, resource_usage).await
}

/// Create a new adaptive learning decision maker with default configuration
pub fn create_adaptive_decision_maker(base_decision_maker: RuleBasedDecisionMaker) -> AdaptiveLearningDecisionMaker {
    AdaptiveLearningDecisionMaker::new(base_decision_maker, LearningConfig::default())
}

/// Create a new adaptive learning decision maker with custom configuration
pub fn create_adaptive_decision_maker_with_config(
    base_decision_maker: RuleBasedDecisionMaker,
    config: LearningConfig,
) -> AdaptiveLearningDecisionMaker {
    AdaptiveLearningDecisionMaker::new(base_decision_maker, config)
}
