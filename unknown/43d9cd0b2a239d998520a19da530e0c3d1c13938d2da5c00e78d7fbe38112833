// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies/execution_strategies.rs
// =================================================================================================
// Purpose: Implements the main execution strategies manager. This component provides a unified
// interface for selecting and using different execution strategies based on task characteristics
// and system state. It manages the lifecycle of strategy instances and provides factory methods
// for creating them.
//
// Integration:
// - Internal Dependencies:
//   - `traits.rs`: Uses ExecutionStrategy trait for strategy interface
//   - `types.rs`: Uses ExecutionStrategyConfig for strategy configuration
//   - `direct/`: Uses DirectStrategy for direct execution
//   - `rayon/`: Uses RayonStrategy for Rayon-based parallel execution
//   - `tokio/`: Uses TokioStrategy for Tokio-based asynchronous execution
//
// - External Dependencies:
//   - `crate::prisma::prisma_engine::traits::Task`: The Task trait implemented by all tasks
//   - `crate::err::PrismaResult`: Result type for error handling
//   - `crate::prisma::prisma_engine::types::ExecutionStrategyType`: Enum for strategy types
//
// - Module Interactions:
//   - `executor`: Uses ExecutionStrategies for task execution
//   - `decision_maker`: Provides input for strategy selection
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use std::any::Any;
// No tracing or async_trait needed here

use crate::prisma::prisma_engine::traits::Task;
use crate::err::{PrismaResult, GenericError};
use crate::prisma::prisma_engine::types::ExecutionStrategyType;

use super::traits::ExecutionStrategy;
use super::types::ExecutionStrategyConfig;
use super::direct::{DirectStrategy, DirectStrategyConfig};
use super::rayon::{RayonStrategy, RayonStrategyConfig};
use super::tokio::{TokioStrategy, TokioStrategyConfig};

/// ExecutionStrategies manages the lifecycle of execution strategy instances and provides
/// factory methods for creating them. It serves as a unified interface for selecting and
/// using different execution strategies based on task characteristics and system state.
pub struct ExecutionStrategies {
    /// Map of strategy instances by type
    strategies: HashMap<ExecutionStrategyType, Arc<RwLock<Box<dyn ExecutionStrategy>>>>,
    /// Default strategy type to use when none is specified
    default_strategy: ExecutionStrategyType,
}

impl ExecutionStrategies {
    /// Creates a new ExecutionStrategies instance with default strategies
    pub fn new() -> Self {
        let mut strategies = HashMap::new();

        // Create and add the direct strategy
        let direct_strategy: Box<dyn ExecutionStrategy> = Box::new(DirectStrategy::default());
        strategies.insert(
            ExecutionStrategyType::Direct,
            Arc::new(RwLock::new(direct_strategy)),
        );

        // Create and add the rayon strategy
        let rayon_strategy: Box<dyn ExecutionStrategy> = Box::new(RayonStrategy::default());
        strategies.insert(
            ExecutionStrategyType::Rayon,
            Arc::new(RwLock::new(rayon_strategy)),
        );

        // Create and add the tokio strategy
        let tokio_strategy: Box<dyn ExecutionStrategy> = Box::new(TokioStrategy::default());
        strategies.insert(
            ExecutionStrategyType::Tokio,
            Arc::new(RwLock::new(tokio_strategy)),
        );

        ExecutionStrategies {
            strategies,
            default_strategy: ExecutionStrategyType::Direct,
        }
    }

    /// Creates a new ExecutionStrategies instance with custom configurations
    pub fn with_configs(configs: HashMap<ExecutionStrategyType, ExecutionStrategyConfig>) -> Self {
        let mut strategies = HashMap::new();

        // Create and add strategies based on configs
        for (strategy_type, config) in configs.iter() {
            match strategy_type {
                ExecutionStrategyType::Direct => {
                    // Convert the generic config to a DirectStrategyConfig
                    let direct_config = DirectStrategyConfig {
                        max_concurrent_tasks: config.max_concurrent_tasks,
                        task_timeout: config.task_timeout,
                        detailed_logging: config.detailed_logging,
                    };

                    let direct_strategy: Box<dyn ExecutionStrategy> = Box::new(DirectStrategy::new(direct_config));
                    strategies.insert(
                        ExecutionStrategyType::Direct,
                        Arc::new(RwLock::new(direct_strategy)),
                    );
                },
                ExecutionStrategyType::Rayon => {
                    // Convert the generic config to a RayonStrategyConfig
                    let rayon_config = RayonStrategyConfig {
                        num_threads: config.max_concurrent_tasks,
                        max_concurrent_tasks: config.max_concurrent_tasks,
                        task_timeout: config.task_timeout,
                        detailed_logging: config.detailed_logging,
                        use_work_stealing: true,
                        thread_stack_size: None,
                        thread_name: Some("prisma-rayon-worker".to_string()),
                    };

                    let rayon_strategy: Box<dyn ExecutionStrategy> = Box::new(RayonStrategy::new(rayon_config));
                    strategies.insert(
                        ExecutionStrategyType::Rayon,
                        Arc::new(RwLock::new(rayon_strategy)),
                    );
                },
                ExecutionStrategyType::Tokio => {
                    // Convert the generic config to a TokioStrategyConfig
                    let tokio_config = TokioStrategyConfig {
                        max_concurrent_tasks: config.max_concurrent_tasks,
                        task_timeout: config.task_timeout,
                        detailed_logging: config.detailed_logging,
                        use_dedicated_runtime: false,
                        worker_threads: None,
                        thread_name: Some("prisma-tokio-worker".to_string()),
                    };

                    let tokio_strategy: Box<dyn ExecutionStrategy> = Box::new(TokioStrategy::new(tokio_config));
                    strategies.insert(
                        ExecutionStrategyType::Tokio,
                        Arc::new(RwLock::new(tokio_strategy)),
                    );
                },
            }
        }

        // Add default strategies for any missing types
        if !strategies.contains_key(&ExecutionStrategyType::Direct) {
            let direct_strategy: Box<dyn ExecutionStrategy> = Box::new(DirectStrategy::default());
            strategies.insert(
                ExecutionStrategyType::Direct,
                Arc::new(RwLock::new(direct_strategy)),
            );
        }

        // Add default Rayon strategy if missing
        if !strategies.contains_key(&ExecutionStrategyType::Rayon) {
            let rayon_strategy: Box<dyn ExecutionStrategy> = Box::new(RayonStrategy::default());
            strategies.insert(
                ExecutionStrategyType::Rayon,
                Arc::new(RwLock::new(rayon_strategy)),
            );
        }

        // Add default Tokio strategy if missing
        if !strategies.contains_key(&ExecutionStrategyType::Tokio) {
            let tokio_strategy: Box<dyn ExecutionStrategy> = Box::new(TokioStrategy::default());
            strategies.insert(
                ExecutionStrategyType::Tokio,
                Arc::new(RwLock::new(tokio_strategy)),
            );
        }

        ExecutionStrategies {
            strategies,
            default_strategy: ExecutionStrategyType::Direct,
        }
    }

    /// Sets the default strategy type
    pub fn set_default_strategy(&mut self, strategy_type: ExecutionStrategyType) {
        self.default_strategy = strategy_type;
    }

    /// Gets a reference to a strategy by type
    pub fn get_strategy(&self, strategy_type: ExecutionStrategyType) -> Option<Arc<RwLock<Box<dyn ExecutionStrategy>>>> {
        self.strategies.get(&strategy_type).cloned()
    }

    /// Gets a reference to the default strategy
    pub fn get_default_strategy(&self) -> Arc<RwLock<Box<dyn ExecutionStrategy>>> {
        self.strategies.get(&self.default_strategy).cloned().unwrap_or_else(|| {
            // Fallback to Direct if default is not available
            self.strategies.get(&ExecutionStrategyType::Direct).cloned().unwrap_or_else(|| {
                // This should never happen as we always initialize with Direct
                panic!("No default strategy available")
            })
        })
    }

    /// Executes a task using the specified strategy
    pub async fn execute_task(
        &self,
        task: &mut (dyn Task + Send + Sync),
        strategy_type: ExecutionStrategyType,
    ) -> PrismaResult<Box<dyn Any + Send>> {
        let strategy = self.get_strategy(strategy_type).ok_or_else(|| {
            GenericError::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Strategy {:?} not found", strategy_type),
            ))
        })?;

        let mut strategy_guard = strategy.write().await;
        strategy_guard.execute_task(task).await
    }

    /// Executes a task using the default strategy
    pub async fn execute_task_default(
        &self,
        task: &mut (dyn Task + Send + Sync),
    ) -> PrismaResult<Box<dyn Any + Send>> {
        let strategy = self.get_default_strategy();
        let mut strategy_guard = strategy.write().await;
        strategy_guard.execute_task(task).await
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> using the specified strategy
    pub async fn execute_task_arc(
        &self,
        task_arc: Arc<Mutex<Box<dyn Task>>>,
        strategy_type: ExecutionStrategyType,
    ) -> PrismaResult<Box<dyn Any + Send>> {
        let strategy = self.get_strategy(strategy_type).ok_or_else(|| {
            GenericError::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Strategy {:?} not found", strategy_type),
            ))
        })?;

        let mut strategy_guard = strategy.write().await;
        strategy_guard.execute_task_arc(task_arc).await
    }

    /// Executes a task wrapped in Arc<Mutex<Box<dyn Task>>> using the default strategy
    pub async fn execute_task_arc_default(
        &self,
        task_arc: Arc<Mutex<Box<dyn Task>>>,
    ) -> PrismaResult<Box<dyn Any + Send>> {
        let strategy = self.get_default_strategy();
        let mut strategy_guard = strategy.write().await;
        strategy_guard.execute_task_arc(task_arc).await
    }

    /// Gets statistics for all strategies
    pub async fn get_all_stats(&self) -> HashMap<ExecutionStrategyType, Box<dyn Any + Send>> {
        let mut stats = HashMap::new();

        for (strategy_type, strategy) in &self.strategies {
            let strategy_guard = strategy.read().await;
            stats.insert(*strategy_type, strategy_guard.get_stats());
        }

        stats
    }

    /// Pauses all execution strategies
    pub async fn pause_all(&mut self) -> PrismaResult<()> {
        for (strategy_type, strategy) in &self.strategies {
            // For now, we'll just log that we're pausing the strategy
            // In the future, we can add pause functionality to the ExecutionStrategy trait
            tracing::info!("Pausing execution strategy: {:?}", strategy_type);
        }

        Ok(())
    }

    /// Resumes all execution strategies
    pub async fn resume_all(&mut self) -> PrismaResult<()> {
        for (strategy_type, strategy) in &self.strategies {
            // For now, we'll just log that we're resuming the strategy
            // In the future, we can add resume functionality to the ExecutionStrategy trait
            tracing::info!("Resuming execution strategy: {:?}", strategy_type);
        }

        Ok(())
    }

    /// Resets statistics for all strategies
    pub async fn reset_all_stats(&self) {
        for strategy in self.strategies.values() {
            let mut strategy_guard = strategy.write().await;
            strategy_guard.reset_stats();
        }
    }
}