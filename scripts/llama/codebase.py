import os
from pathlib import Path
import filecmp
from typing import Dict, Callable, Optional
import scan

def run(old_repo: Path, new_repo: Path, output_dir: Path, 
        progress_callback: Optional[Callable] = None) -> Dict:
    """
    Compare the file structure between old and new repos.
    Generates codebase.md with details of added/removed files.
    """
    result = {"status": "success", "message": "Completed analysis of codebase structure"}
    
    try:
        # Compare directories to get new, removed, and common files
        new_files, removed_files, common_files, old_relative, new_relative = scan.compare_directories(
            old_repo, new_repo, 
            progress_callback=lambda p: progress_callback(p * 0.2) if progress_callback else None
        )
        
        # Process remaining progress steps
        if progress_callback:
            progress_callback(30)
        
        # Generate markdown report
        output_file = output_dir / "codebase.md"
        
        with open(output_file, 'w') as f:
            f.write("###################################\n")
            f.write("CODEBASE CHANGES\n")
            f.write("###################################\n\n")
            
            # Report new files
            f.write("## NEW FILES\n\n")
            for i, rel_path in enumerate(sorted(new_files)):
                f.write("+++++++++++++++++++++++++++++++++\n")
                f.write(f"{i+1} - {rel_path}\n")
                f.write("+++++++++++++++++++++++++++++++++\n\n")
                f.write(f"New: {new_relative[rel_path]}\n\n")
            
            if progress_callback:
                progress_callback(20)
            
            # Report removed files
            f.write("\n## REMOVED FILES\n\n")
            for i, rel_path in enumerate(sorted(removed_files)):
                f.write("+++++++++++++++++++++++++++++++++\n")
                f.write(f"{i+1} - {rel_path}\n")
                f.write("+++++++++++++++++++++++++++++++++\n\n")
                f.write(f"Removed: {old_relative[rel_path]}\n\n")
            
            if progress_callback:
                progress_callback(20)
            
            # Summary section
            f.write("\n###################################\n")
            f.write("SUMMARY\n")
            f.write("###################################\n\n")
            
            f.write(f"Codebase changes: {len(removed_files)} files removed, {len(new_files)} files added.\n")
            f.write(f"Common files: {len(common_files)} files are present in both repositories.\n")
        
        result["new_files_count"] = len(new_files)
        result["removed_files_count"] = len(removed_files)
        result["common_files_count"] = len(common_files)
        
        if progress_callback:
            progress_callback(30)
        
    except Exception as e:
        result = {"status": "error", "message": f"Error analyzing codebase structure: {str(e)}"}
        raise
        
    return result
