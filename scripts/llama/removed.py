import os
from pathlib import Path
from typing import Dict, Callable, Optional
import scan

def run(old_repo: Path, new_repo: Path, output_dir: Path, 
        progress_callback: Optional[Callable] = None) -> Dict:
    """
    Compare files between old and new repos to find removed code.
    Generates removed.md with details of removed code blocks.
    """
    result = {"status": "success", "message": "Completed analysis of removed code"}
    
    try:
        # Compare directories to get common files and removed files
        _, removed_files, common_files, old_relative, new_relative = scan.compare_directories(
            old_repo, new_repo, 
            progress_callback=lambda p: progress_callback(p * 0.2) if progress_callback else None
        )
        
        removed_blocks_all = {}
        
        # First handle completely removed files
        for rel_path in removed_files:
            if rel_path.endswith(('.h', '.hpp')):
                old_file = old_relative[rel_path]
                old_blocks = scan.extract_code_blocks(old_file)
                if old_blocks:
                    removed_blocks_all[rel_path] = {"*REMOVED FILE*": old_blocks}
        
        # Then check common files for removed blocks
        header_files = [f for f in common_files if f.endswith(('.h', '.hpp'))]
        total_files = len(header_files)
        
        for i, rel_path in enumerate(header_files):
            if progress_callback and total_files > 0:
                progress_callback(20 + (i / total_files) * 60)
                
            old_file = old_relative[rel_path]
            new_file = new_relative[rel_path]
            
            # Extract code blocks from both files
            old_blocks = scan.extract_code_blocks(old_file)
            new_blocks = scan.extract_code_blocks(new_file)
            
            # Group changes
            changes = scan.group_changes(old_blocks, new_blocks)
            
            # Add removed blocks to our collection
            if changes["removed"]:
                removed_blocks_all[rel_path] = changes["removed"]
        
        # Generate markdown report
        if progress_callback:
            progress_callback(80)
            
        output_file = output_dir / "removed.md"
        
        with open(output_file, 'w') as f:
            f.write("###################################\n")
            f.write("REMOVED CODE\n")
            f.write("###################################\n\n")
            
            count = 1
            for file_path, blocks in removed_blocks_all.items():
                if isinstance(next(iter(blocks.values())), dict) and "*REMOVED FILE*" in blocks:
                    # This is a completely removed file
                    f.write(f"+++++++++++++++++++++++++++++++++\n")
                    f.write(f"{count} - REMOVED FILE: {file_path}\n")
                    f.write("+++++++++++++++++++++++++++++++++\n\n")
                    count += 1
                    
                    # List all blocks that were in this removed file
                    for name, content in blocks["*REMOVED FILE*"].items():
                        f.write(f"Removed Block: {name}\n\n")
                        f.write("```c++\n")
                        f.write(content)
                        f.write("\n```\n\n")
                else:
                    # These are removed blocks from existing files
                    for name, content in blocks.items():
                        f.write("+++++++++++++++++++++++++++++++++\n")
                        f.write(f"{count} - {name} (from {file_path})\n")
                        f.write("+++++++++++++++++++++++++++++++++\n\n")
                        
                        f.write("Removed Code:\n")
                        f.write("```c++\n")
                        f.write(content)
                        f.write("\n```\n\n")
                        
                        count += 1
            
            f.write("###################################\n")
            f.write("SUMMARY\n")
            f.write("###################################\n\n")
            
            total_removed_files = sum(1 for blocks in removed_blocks_all.values() if "*REMOVED FILE*" in blocks)
            total_removed_blocks = sum(len(blocks) for blocks in removed_blocks_all.values() 
                                    if "*REMOVED FILE*" not in blocks) + sum(len(blocks["*REMOVED FILE*"]) 
                                    for blocks in removed_blocks_all.values() if "*REMOVED FILE*" in blocks)
            
            f.write(f"Removed Files: {total_removed_files} files\n")
            f.write(f"Removed Code Blocks: {total_removed_blocks} blocks\n")
        
        result["removed_files_count"] = total_removed_files
        result["removed_blocks_count"] = total_removed_blocks
        
        if progress_callback:
            progress_callback(20)
        
    except Exception as e:
        result = {"status": "error", "message": f"Error analyzing removed code: {str(e)}"}
        raise
        
    return result
