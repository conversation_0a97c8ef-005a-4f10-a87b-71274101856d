import os
from pathlib import Path
from typing import Dict, Callable, Optional
import scan

def run(old_repo: Path, new_repo: Path, output_dir: Path, 
        progress_callback: Optional[Callable] = None) -> Dict:
    """
    Compare files between old and new repos to find edited code.
    Generates edit.md with details of changes.
    """
    result = {"status": "success", "message": "Completed analysis of edited code"}
    
    try:
        # Compare directories to get common files
        _, _, common_files, old_relative, new_relative = scan.compare_directories(
            old_repo, new_repo, 
            progress_callback=lambda p: progress_callback(p * 0.2) if progress_callback else None
        )
        
        # Focus on header files for code comparison
        header_files = [f for f in common_files if f.endswith(('.h', '.hpp'))]
        
        edited_blocks_all = {}
        total_files = len(header_files)
        
        # Process each file to find edited code blocks
        for i, rel_path in enumerate(header_files):
            if progress_callback and total_files > 0:
                progress_callback(20 + (i / total_files) * 60)
                
            old_file = old_relative[rel_path]
            new_file = new_relative[rel_path]
            
            # Extract code blocks from both files
            old_blocks = scan.extract_code_blocks(old_file)
            new_blocks = scan.extract_code_blocks(new_file)
            
            # Group changes
            changes = scan.group_changes(old_blocks, new_blocks)
            
            # Add edited blocks to our collection
            if changes["edited"]:
                edited_blocks_all[rel_path] = changes["edited"]
        
        # Generate markdown report
        if progress_callback:
            progress_callback(80)
            
        output_file = output_dir / "edit.md"
        
        with open(output_file, 'w') as f:
            f.write("###################################\n")
            f.write("CODE CHANGED\n")
            f.write("###################################\n\n")
            
            count = 1
            for file_path, blocks in edited_blocks_all.items():
                for name, content in blocks.items():
                    f.write("+++++++++++++++++++++++++++++++++\n")
                    f.write(f"{count} - {name}\n")
                    f.write("+++++++++++++++++++++++++++++++++\n\n")
                    
                    f.write("Old: \n")
                    f.write("```c++\n")
                    f.write(content["old"])
                    f.write("\n```\n\n")
                    
                    f.write("New:\n")
                    f.write("```c++\n")
                    f.write(content["new"])
                    f.write("\n```\n\n")
                    
                    count += 1
            
            f.write("###################################\n")
            f.write("SUMMARY\n")
            f.write("###################################\n\n")
            
            total_edits = sum(len(blocks) for blocks in edited_blocks_all.values())
            f.write(f"Files changed: {len(edited_blocks_all)} files with {total_edits} edits\n")
        
        result["edited_count"] = total_edits
        result["files_count"] = len(edited_blocks_all)
        
        if progress_callback:
            progress_callback(20)
        
    except Exception as e:
        result = {"status": "error", "message": f"Error analyzing edited code: {str(e)}"}
        raise
        
    return result
