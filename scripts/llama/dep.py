import os
from pathlib import Path
from typing import Dict, Callable, Optional
import scan
import re

def run(old_repo: Path, new_repo: Path, output_dir: Path, 
        progress_callback: Optional[Callable] = None) -> Dict:
    """
    Compare files between old and new repos to find deprecated code.
    Generates dep.md with details of deprecated code blocks.
    """
    result = {"status": "success", "message": "Completed analysis of deprecated code"}
    
    try:
        # Compare directories to get common files
        _, _, common_files, old_relative, new_relative = scan.compare_directories(
            old_repo, new_repo, 
            progress_callback=lambda p: progress_callback(p * 0.2) if progress_callback else None
        )
        
        # Focus on header files
        header_files = [f for f in common_files if f.endswith(('.h', '.hpp'))]
        total_files = len(header_files)
        
        deprecated_blocks_all = {}
        
        # Check each file for deprecated code
        for i, rel_path in enumerate(header_files):
            if progress_callback and total_files > 0:
                progress_callback(20 + (i / total_files) * 60)
                
            old_file = old_relative[rel_path]
            new_file = new_relative[rel_path]
            
            # Extract code blocks from both files
            old_blocks = scan.extract_code_blocks(old_file)
            new_blocks = scan.extract_code_blocks(new_file)
            
            # Look for newly deprecated blocks
            newly_deprecated = {}
            for name, content in new_blocks.items():
                if name in old_blocks:
                    # Check if this block is now deprecated but wasn't before
                    if scan.is_deprecated(content) and not scan.is_deprecated(old_blocks[name]):
                        newly_deprecated[name] = {
                            "old": old_blocks[name],
                            "new": content
                        }
            
            # Also find blocks marked as deprecated in new code (whether they existed before or not)
            for name, content in new_blocks.items():
                if scan.is_deprecated(content) and name not in newly_deprecated:
                    if name in old_blocks:
                        newly_deprecated[name] = {
                            "old": old_blocks[name],
                            "new": content
                        }
                    else:
                        newly_deprecated[name] = {
                            "old": None,
                            "new": content
                        }
            
            if newly_deprecated:
                deprecated_blocks_all[rel_path] = newly_deprecated
        
        # Generate markdown report
        if progress_callback:
            progress_callback(80)
            
        output_file = output_dir / "dep.md"
        
        with open(output_file, 'w') as f:
            f.write("###################################\n")
            f.write("DEPRECATED CODE\n")
            f.write("###################################\n\n")
            
            count = 1
            for file_path, blocks in deprecated_blocks_all.items():
                for name, content in blocks.items():
                    f.write("+++++++++++++++++++++++++++++++++\n")
                    f.write(f"{count} - {name}\n")
                    f.write("+++++++++++++++++++++++++++++++++\n\n")
                    
                    if content["old"] is not None:
                        f.write("Old Version (Before Deprecation):\n")
                        f.write("```c++\n")
                        f.write(content["old"])
                        f.write("\n```\n\n")
                    
                    f.write("New Deprecated Version:\n")
                    f.write("```c++\n")
                    f.write(content["new"])
                    f.write("\n```\n\n")
                    
                    # Try to extract the deprecation message
                    dep_match = re.search(r'DEPRECATED\([^,]*,\s*"([^"]*)"', content["new"])
                    if dep_match:
                        f.write(f"Deprecation Message: \"{dep_match.group(1)}\"\n\n")
                    
                    count += 1
            
            f.write("###################################\n")
            f.write("SUMMARY\n")
            f.write("###################################\n\n")
            
            total_deprecated = sum(len(blocks) for blocks in deprecated_blocks_all.values())
            f.write(f"Deprecated Code Blocks: {total_deprecated} blocks in {len(deprecated_blocks_all)} files\n")
        
        result["deprecated_count"] = total_deprecated
        result["files_count"] = len(deprecated_blocks_all)
        
        if progress_callback:
            progress_callback(20)
        
    except Exception as e:
        result = {"status": "error", "message": f"Error analyzing deprecated code: {str(e)}"}
        raise
        
    return result
