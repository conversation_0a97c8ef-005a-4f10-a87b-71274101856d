###################################
DEPRECATED CODE
###################################

+++++++++++++++++++++++++++++++++
1 - deprecated
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
namespace deprecated

        inline void dpct_free(void *ptr,
                              const sycl::queue &q)
        {
            if (ptr)
            {
                sycl::free(ptr, q.get_context());
            }
```

New Deprecated Version:
```c++
namespace deprecated

        inline void dpct_free(void *ptr,
                              const sycl::queue &q)
        {
            if (ptr)
            {
                sycl::free(ptr, q.get_context());
            }
```

+++++++++++++++++++++++++++++++++
2 - void_8
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
inline void
    has_capability_or_fail(const sycl::device &dev,
                           const std::initializer_list<sycl::aspect> &props)
    {
        for (const auto &it : props)
        {
            if (dev.has(it))
                continue;
            switch (it)
            {
            case sycl::aspect::fp64:
                throw std::runtime_error("'double' is not supported in '" +
                                         dev.get_info<sycl::info::device::name>() +
                                         "' device");
                break;
            case sycl::aspect::fp16:
                throw std::runtime_error("'half' is not supported in '" +
                                         dev.get_info<sycl::info::device::name>() +
                                         "' device");
                break;
            default:
#define __SYCL_ASPECT(ASPECT, ID) \
    case sycl::aspect::ASPECT:    \
        return #ASPECT;
#define __SYCL_ASPECT_DEPRECATED(ASPECT, ID, MESSAGE) __SYCL_ASPECT(ASPECT, ID)
#define __SYCL_ASPECT_DEPRECATED_ALIAS(ASPECT, ID, MESSAGE)
                auto getAspectNameStr = [](sycl::aspect AspectNum) -> std::string
                {
                    switch (AspectNum)
                    {
#include <sycl/info/aspects.def>
#include <sycl/info/aspects_deprecated.def>
                    default:
                        return "unknown aspect";
                    }
```

New Deprecated Version:
```c++
inline void
    has_capability_or_fail(const sycl::device &dev,
                           const std::initializer_list<sycl::aspect> &props)
    {
        for (const auto &it : props)
        {
            if (dev.has(it))
                continue;
            switch (it)
            {
            case sycl::aspect::fp64:
                throw std::runtime_error("'double' is not supported in '" +
                                         dev.get_info<sycl::info::device::name>() +
                                         "' device");
                break;
            case sycl::aspect::fp16:
                throw std::runtime_error("'half' is not supported in '" +
                                         dev.get_info<sycl::info::device::name>() +
                                         "' device");
                break;
            default:
#define __SYCL_ASPECT(ASPECT, ID) \
    case sycl::aspect::ASPECT:    \
        return #ASPECT;
#define __SYCL_ASPECT_DEPRECATED(ASPECT, ID, MESSAGE) __SYCL_ASPECT(ASPECT, ID)
#define __SYCL_ASPECT_DEPRECATED_ALIAS(ASPECT, ID, MESSAGE)
                auto getAspectNameStr = [](sycl::aspect AspectNum) -> std::string
                {
                    switch (AspectNum)
                    {
#include <sycl/info/aspects.def>
#include <sycl/info/aspects_deprecated.def>
                    default:
                        return "unknown aspect";
                    }
```

+++++++++++++++++++++++++++++++++
3 - ID
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(ASPECT, ID, MESSAGE)
```

New Deprecated Version:
```c++
DEPRECATED(ASPECT, ID, MESSAGE)
```

+++++++++++++++++++++++++++++++++
4 - basic_json
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
ref basic_json class
This class implements a both iterators (iterator and const_iterator) for the
@ref basic_json class.
@note An iterator is called *initialized* when a pointer to a JSON value has
      been set (e.g., by a constructor or a copy assignment). If the iterator is
      default-constructed, it is *uninitialized* and most methods are undefined.
      **The library uses assertions to detect calls on uninitialized iterators.**
@requirement The class satisfies the following concept requirements:
-
[BidirectionalIterator](https://en.cppreference.com/w/cpp/named_req/BidirectionalIterator):
  The iterator that can be moved can be moved in both directions (i.e.
  incremented and decremented).
@since version 1.0.0, simplified in version 2.0.9, change to bidirectional
       iterators in version 3.0.0 (see https://github.com/nlohmann/json/issues/593)
*/
template<typename BasicJsonType>
class iter_impl // NOLINT(cppcoreguidelines-special-member-functions,hicpp-special-member-functions)
{
    /// the iterator with BasicJsonType of different const-ness
    using other_iter_impl = iter_impl<typename std::conditional<std::is_const<BasicJsonType>::value, typename std::remove_const<BasicJsonType>::type, const BasicJsonType>::type>;
    /// allow basic_json to access private members
    friend other_iter_impl;
    friend BasicJsonType;
    friend iteration_proxy<iter_impl>;
    friend iteration_proxy_value<iter_impl>;

    using object_t = typename BasicJsonType::object_t;
    using array_t = typename BasicJsonType::array_t;
    // make sure BasicJsonType is basic_json or const basic_json
    static_assert(is_basic_json<typename std::remove_const<BasicJsonType>::type>::value,
                  "iter_impl only accepts (const) basic_json");
    // superficial check for the LegacyBidirectionalIterator named requirement
    static_assert(std::is_base_of<std::bidirectional_iterator_tag, std::bidirectional_iterator_tag>::value
                  &&  std::is_base_of<std::bidirectional_iterator_tag, typename std::iterator_traits<typename array_t::iterator>::iterator_category>::value,
                  "basic_json iterator assumes array and object type iterators satisfy the LegacyBidirectionalIterator named requirement.");

  public:
    /// The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17.
    /// The C++ Standard has never required user-defined iterators to derive from std::iterator.
    /// A user-defined iterator should provide publicly accessible typedefs named
    /// iterator_category, value_type, difference_type, pointer, and reference.
    /// Note that value_type is required to be non-const, even for constant iterators.
    using iterator_category = std::bidirectional_iterator_tag;

    /// the type of the values when the iterator is dereferenced
    using value_type = typename BasicJsonType::value_type;
    /// a type to represent differences between iterators
    using difference_type = typename BasicJsonType::difference_type;
    /// defines a pointer to the type iterated over (value_type)
    using pointer = typename std::conditional<std::is_const<BasicJsonType>::value,
          typename BasicJsonType::const_pointer,
          typename BasicJsonType::pointer>::type;
    /// defines a reference to the type iterated over (value_type)
    using reference =
        typename std::conditional<std::is_const<BasicJsonType>::value,
        typename BasicJsonType::const_reference,
        typename BasicJsonType::reference>::type;

    iter_impl() = default;
    ~iter_impl() = default;
    iter_impl(iter_impl&&) noexcept = default;
    iter_impl& operator=(iter_impl&&) noexcept = default;

    /*!
    @brief constructor for a given JSON instance
    @param[in] object  pointer to a JSON object for this iterator
    @pre object != nullptr
    @post The iterator is initialized; i.e. `m_object != nullptr`.
    */
    explicit iter_impl(pointer object) noexcept : m_object(object)
    {
        JSON_ASSERT(m_object != nullptr);

        switch (m_object->m_data.m_type)
        {
            case value_t::object:
            {
                m_it.object_iterator = typename object_t::iterator();
                break;
            }
```

New Deprecated Version:
```c++
ref basic_json class
This class implements a both iterators (iterator and const_iterator) for the
@ref basic_json class.
@note An iterator is called *initialized* when a pointer to a JSON value has
      been set (e.g., by a constructor or a copy assignment). If the iterator is
      default-constructed, it is *uninitialized* and most methods are undefined.
      **The library uses assertions to detect calls on uninitialized iterators.**
@requirement The class satisfies the following concept requirements:
-
[BidirectionalIterator](https://en.cppreference.com/w/cpp/named_req/BidirectionalIterator):
  The iterator that can be moved can be moved in both directions (i.e.
  incremented and decremented).
@since version 1.0.0, simplified in version 2.0.9, change to bidirectional
       iterators in version 3.0.0 (see https://github.com/nlohmann/json/issues/593)
*/
template<typename BasicJsonType>
class iter_impl // NOLINT(cppcoreguidelines-special-member-functions,hicpp-special-member-functions)
{
    /// the iterator with BasicJsonType of different const-ness
    using other_iter_impl = iter_impl<typename std::conditional<std::is_const<BasicJsonType>::value, typename std::remove_const<BasicJsonType>::type, const BasicJsonType>::type>;
    /// allow basic_json to access private members
    friend other_iter_impl;
    friend BasicJsonType;
    friend iteration_proxy<iter_impl>;
    friend iteration_proxy_value<iter_impl>;

    using object_t = typename BasicJsonType::object_t;
    using array_t = typename BasicJsonType::array_t;
    // make sure BasicJsonType is basic_json or const basic_json
    static_assert(is_basic_json<typename std::remove_const<BasicJsonType>::type>::value,
                  "iter_impl only accepts (const) basic_json");
    // superficial check for the LegacyBidirectionalIterator named requirement
    static_assert(std::is_base_of<std::bidirectional_iterator_tag, std::bidirectional_iterator_tag>::value
                  &&  std::is_base_of<std::bidirectional_iterator_tag, typename std::iterator_traits<typename array_t::iterator>::iterator_category>::value,
                  "basic_json iterator assumes array and object type iterators satisfy the LegacyBidirectionalIterator named requirement.");

  public:
    /// The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17.
    /// The C++ Standard has never required user-defined iterators to derive from std::iterator.
    /// A user-defined iterator should provide publicly accessible typedefs named
    /// iterator_category, value_type, difference_type, pointer, and reference.
    /// Note that value_type is required to be non-const, even for constant iterators.
    using iterator_category = std::bidirectional_iterator_tag;

    /// the type of the values when the iterator is dereferenced
    using value_type = typename BasicJsonType::value_type;
    /// a type to represent differences between iterators
    using difference_type = typename BasicJsonType::difference_type;
    /// defines a pointer to the type iterated over (value_type)
    using pointer = typename std::conditional<std::is_const<BasicJsonType>::value,
          typename BasicJsonType::const_pointer,
          typename BasicJsonType::pointer>::type;
    /// defines a reference to the type iterated over (value_type)
    using reference =
        typename std::conditional<std::is_const<BasicJsonType>::value,
        typename BasicJsonType::const_reference,
        typename BasicJsonType::reference>::type;

    iter_impl() = default;
    ~iter_impl() = default;
    iter_impl(iter_impl&&) noexcept = default;
    iter_impl& operator=(iter_impl&&) noexcept = default;

    /*!
    @brief constructor for a given JSON instance
    @param[in] object  pointer to a JSON object for this iterator
    @pre object != nullptr
    @post The iterator is initialized; i.e. `m_object != nullptr`.
    */
    explicit iter_impl(pointer object) noexcept : m_object(object)
    {
        JSON_ASSERT(m_object != nullptr);

        switch (m_object->m_data.m_type)
        {
            case value_t::object:
            {
                m_it.object_iterator = typename object_t::iterator();
                break;
            }
```

+++++++++++++++++++++++++++++++++
5 - JSON_HEDLEY_DEPRECATED_FOR
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, parse(ptr, ptr + len))
    static basic_json parse(detail::span_input_adapter&& i,
                            const parser_callback_t cb = nullptr,
                            const bool allow_exceptions = true,
                            const bool ignore_comments = false)
    {
        basic_json result;
        parser(i.get(), cb, allow_exceptions, ignore_comments).parse(true, result);
        return result;
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, parse(ptr, ptr + len))
    static basic_json parse(detail::span_input_adapter&& i,
                            const parser_callback_t cb = nullptr,
                            const bool allow_exceptions = true,
                            const bool ignore_comments = false)
    {
        basic_json result;
        parser(i.get(), cb, allow_exceptions, ignore_comments).parse(true, result);
        return result;
    }
```

+++++++++++++++++++++++++++++++++
6 - JSON_HEDLEY_DEPRECATED_FOR_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, accept(ptr, ptr + len))
    static bool accept(detail::span_input_adapter&& i,
                       const bool ignore_comments = false)
    {
        return parser(i.get(), nullptr, false, ignore_comments).accept(true);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, accept(ptr, ptr + len))
    static bool accept(detail::span_input_adapter&& i,
                       const bool ignore_comments = false)
    {
        return parser(i.get(), nullptr, false, ignore_comments).accept(true);
    }
```

+++++++++++++++++++++++++++++++++
7 - JSON_HEDLEY_DEPRECATED_FOR_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_cbor(ptr, ptr + len))
    static basic_json from_cbor(const T* ptr, std::size_t len,
                                const bool strict = true,
                                const bool allow_exceptions = true,
                                const cbor_tag_handler_t tag_handler = cbor_tag_handler_t::error)
    {
        return from_cbor(ptr, ptr + len, strict, allow_exceptions, tag_handler);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_cbor(ptr, ptr + len))
    static basic_json from_cbor(const T* ptr, std::size_t len,
                                const bool strict = true,
                                const bool allow_exceptions = true,
                                const cbor_tag_handler_t tag_handler = cbor_tag_handler_t::error)
    {
        return from_cbor(ptr, ptr + len, strict, allow_exceptions, tag_handler);
    }
```

+++++++++++++++++++++++++++++++++
8 - JSON_HEDLEY_DEPRECATED_FOR_3
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_cbor(ptr, ptr + len))
    static basic_json from_cbor(detail::span_input_adapter&& i,
                                const bool strict = true,
                                const bool allow_exceptions = true,
                                const cbor_tag_handler_t tag_handler = cbor_tag_handler_t::error)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::cbor).sax_parse(input_format_t::cbor, &sdp, strict, tag_handler);
        return res ? result : basic_json(value_t::discarded);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_cbor(ptr, ptr + len))
    static basic_json from_cbor(detail::span_input_adapter&& i,
                                const bool strict = true,
                                const bool allow_exceptions = true,
                                const cbor_tag_handler_t tag_handler = cbor_tag_handler_t::error)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::cbor).sax_parse(input_format_t::cbor, &sdp, strict, tag_handler);
        return res ? result : basic_json(value_t::discarded);
    }
```

+++++++++++++++++++++++++++++++++
9 - JSON_HEDLEY_DEPRECATED_FOR_4
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_msgpack(ptr, ptr + len))
    static basic_json from_msgpack(const T* ptr, std::size_t len,
                                   const bool strict = true,
                                   const bool allow_exceptions = true)
    {
        return from_msgpack(ptr, ptr + len, strict, allow_exceptions);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_msgpack(ptr, ptr + len))
    static basic_json from_msgpack(const T* ptr, std::size_t len,
                                   const bool strict = true,
                                   const bool allow_exceptions = true)
    {
        return from_msgpack(ptr, ptr + len, strict, allow_exceptions);
    }
```

+++++++++++++++++++++++++++++++++
10 - JSON_HEDLEY_DEPRECATED_FOR_5
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_msgpack(ptr, ptr + len))
    static basic_json from_msgpack(detail::span_input_adapter&& i,
                                   const bool strict = true,
                                   const bool allow_exceptions = true)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::msgpack).sax_parse(input_format_t::msgpack, &sdp, strict);
        return res ? result : basic_json(value_t::discarded);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_msgpack(ptr, ptr + len))
    static basic_json from_msgpack(detail::span_input_adapter&& i,
                                   const bool strict = true,
                                   const bool allow_exceptions = true)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::msgpack).sax_parse(input_format_t::msgpack, &sdp, strict);
        return res ? result : basic_json(value_t::discarded);
    }
```

+++++++++++++++++++++++++++++++++
11 - JSON_HEDLEY_DEPRECATED_FOR_6
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_ubjson(ptr, ptr + len))
    static basic_json from_ubjson(const T* ptr, std::size_t len,
                                  const bool strict = true,
                                  const bool allow_exceptions = true)
    {
        return from_ubjson(ptr, ptr + len, strict, allow_exceptions);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_ubjson(ptr, ptr + len))
    static basic_json from_ubjson(const T* ptr, std::size_t len,
                                  const bool strict = true,
                                  const bool allow_exceptions = true)
    {
        return from_ubjson(ptr, ptr + len, strict, allow_exceptions);
    }
```

+++++++++++++++++++++++++++++++++
12 - JSON_HEDLEY_DEPRECATED_FOR_7
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_ubjson(ptr, ptr + len))
    static basic_json from_ubjson(detail::span_input_adapter&& i,
                                  const bool strict = true,
                                  const bool allow_exceptions = true)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::ubjson).sax_parse(input_format_t::ubjson, &sdp, strict);
        return res ? result : basic_json(value_t::discarded);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_ubjson(ptr, ptr + len))
    static basic_json from_ubjson(detail::span_input_adapter&& i,
                                  const bool strict = true,
                                  const bool allow_exceptions = true)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::ubjson).sax_parse(input_format_t::ubjson, &sdp, strict);
        return res ? result : basic_json(value_t::discarded);
    }
```

+++++++++++++++++++++++++++++++++
13 - JSON_HEDLEY_DEPRECATED_FOR_8
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_bson(ptr, ptr + len))
    static basic_json from_bson(const T* ptr, std::size_t len,
                                const bool strict = true,
                                const bool allow_exceptions = true)
    {
        return from_bson(ptr, ptr + len, strict, allow_exceptions);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_bson(ptr, ptr + len))
    static basic_json from_bson(const T* ptr, std::size_t len,
                                const bool strict = true,
                                const bool allow_exceptions = true)
    {
        return from_bson(ptr, ptr + len, strict, allow_exceptions);
    }
```

+++++++++++++++++++++++++++++++++
14 - JSON_HEDLEY_DEPRECATED_FOR_9
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_bson(ptr, ptr + len))
    static basic_json from_bson(detail::span_input_adapter&& i,
                                const bool strict = true,
                                const bool allow_exceptions = true)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::bson).sax_parse(input_format_t::bson, &sdp, strict);
        return res ? result : basic_json(value_t::discarded);
    }
```

New Deprecated Version:
```c++
JSON_HEDLEY_WARN_UNUSED_RESULT
    JSON_HEDLEY_DEPRECATED_FOR(3.8.0, from_bson(ptr, ptr + len))
    static basic_json from_bson(detail::span_input_adapter&& i,
                                const bool strict = true,
                                const bool allow_exceptions = true)
    {
        basic_json result;
        detail::json_sax_dom_parser<basic_json> sdp(result, allow_exceptions);
        auto ia = i.get();
        // NOLINTNEXTLINE(hicpp-move-const-arg,performance-move-const-arg)
        const bool res = binary_reader<decltype(ia)>(std::move(ia), input_format_t::bson).sax_parse(input_format_t::bson, &sdp, strict);
        return res ? result : basic_json(value_t::discarded);
    }
```

+++++++++++++++++++++++++++++++++
15 - DEPRECATED(since)
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
16 - DEPRECATED(since)_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
17 - DEPRECATED(since)_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
18 - DEPRECATED(since)_3
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
19 - DEPRECATED(since)_4
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
20 - DEPRECATED(since)_5
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
21 - DEPRECATED(since)_6
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(since)
```

New Deprecated Version:
```c++
DEPRECATED(since)
```

+++++++++++++++++++++++++++++++++
22 - apply
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
string apply(
        const nlohmann::ordered_json & messages,
        const nlohmann::ordered_json & tools,
        bool add_generation_prompt,
        const nlohmann::ordered_json & extra_context = nlohmann::ordered_json(),
        bool apply_polyfills = true)
    {
        fprintf(stderr, "[%s] Deprecated!\n", __func__);
        chat_template_inputs inputs;
        inputs.messages = messages;
        inputs.tools = tools;
        inputs.add_generation_prompt = add_generation_prompt;
        inputs.extra_context = extra_context;
        inputs.now = std::chrono::system_clock::now();

        chat_template_options opts;
        opts.apply_polyfills = apply_polyfills;

        return apply(inputs, opts);
    }
```

New Deprecated Version:
```c++
string apply(
        const nlohmann::ordered_json & messages,
        const nlohmann::ordered_json & tools,
        bool add_generation_prompt,
        const nlohmann::ordered_json & extra_context = nlohmann::ordered_json(),
        bool apply_polyfills = true)
    {
        fprintf(stderr, "[%s] Deprecated!\n", __func__);
        chat_template_inputs inputs;
        inputs.messages = messages;
        inputs.tools = tools;
        inputs.add_generation_prompt = add_generation_prompt;
        inputs.extra_context = extra_context;
        inputs.now = std::chrono::system_clock::now();

        chat_template_options opts;
        opts.apply_polyfills = apply_polyfills;

        return apply(inputs, opts);
    }
```

+++++++++++++++++++++++++++++++++
23 - GGML_BACKEND_API_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(
        GGML_BACKEND_API ggml_backend_buffer_t ggml_backend_metal_buffer_from_ptr(void * data, size_t size, size_t max_size)
```

New Deprecated Version:
```c++
DEPRECATED(
        GGML_BACKEND_API ggml_backend_buffer_t ggml_backend_metal_buffer_from_ptr(void * data, size_t size, size_t max_size)
```

+++++++++++++++++++++++++++++++++
24 - DEPRECATED
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
LLAMA_API DEPRECATED(size_t llama_get_state_size(struct llama_context * ctx),
        "use llama_state_get_size instead");
```

New Deprecated Version:
```c++
LLAMA_API DEPRECATED(size_t llama_get_state_size(struct llama_context * ctx),
        "use llama_state_get_size instead");
```

Deprecation Message: "use llama_state_get_size instead"

+++++++++++++++++++++++++++++++++
25 - DEPRECATED_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
LLAMA_API DEPRECATED(size_t llama_copy_state_data(
            struct llama_context * ctx,
                         uint8_t * dst),
        "use llama_state_get_data instead");
```

New Deprecated Version:
```c++
LLAMA_API DEPRECATED(size_t llama_copy_state_data(
            struct llama_context * ctx,
                         uint8_t * dst),
        "use llama_state_get_data instead");
```

+++++++++++++++++++++++++++++++++
26 - DEPRECATED_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
LLAMA_API DEPRECATED(size_t llama_set_state_data(
            struct llama_context * ctx,
                   const uint8_t * src),
        "use llama_state_set_data instead");
```

New Deprecated Version:
```c++
LLAMA_API DEPRECATED(size_t llama_set_state_data(
            struct llama_context * ctx,
                   const uint8_t * src),
        "use llama_state_set_data instead");
```

+++++++++++++++++++++++++++++++++
27 - DEPRECATED_3
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
LLAMA_API DEPRECATED(bool llama_load_session_file(
            struct llama_context * ctx,
                      const char * path_session,
                     llama_token * tokens_out,
                          size_t   n_token_capacity,
                          size_t * n_token_count_out),
        "use llama_state_load_file instead");
```

New Deprecated Version:
```c++
LLAMA_API DEPRECATED(bool llama_load_session_file(
            struct llama_context * ctx,
                      const char * path_session,
                     llama_token * tokens_out,
                          size_t   n_token_capacity,
                          size_t * n_token_count_out),
        "use llama_state_load_file instead");
```

+++++++++++++++++++++++++++++++++
28 - DEPRECATED_4
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
LLAMA_API DEPRECATED(bool llama_save_session_file(
            struct llama_context * ctx,
                      const char * path_session,
               const llama_token * tokens,
                          size_t   n_token_count),
        "use llama_state_save_file instead");
```

New Deprecated Version:
```c++
LLAMA_API DEPRECATED(bool llama_save_session_file(
            struct llama_context * ctx,
                      const char * path_session,
               const llama_token * tokens,
                          size_t   n_token_count),
        "use llama_state_save_file instead");
```

+++++++++++++++++++++++++++++++++
29 - is_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
CLS is equivalent to BOS
    DEPRECATED(LLAMA_API llama_token llama_vocab_cls(const struct llama_vocab * vocab), // classification
            "use llama_vocab_bos instead");
```

New Deprecated Version:
```c++
CLS is equivalent to BOS
    DEPRECATED(LLAMA_API llama_token llama_vocab_cls(const struct llama_vocab * vocab), // classification
            "use llama_vocab_bos instead");
```

+++++++++++++++++++++++++++++++++
30 - llama_context_params
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
struct llama_context_params {
        uint32_t n_ctx;             // text context, 0 = from model
        uint32_t n_batch;           // logical maximum batch size that can be submitted to llama_decode
        uint32_t n_ubatch;          // physical maximum batch size
        uint32_t n_seq_max;         // max number of sequences (i.e. distinct states for recurrent models)
        int32_t  n_threads;         // number of threads to use for generation
        int32_t  n_threads_batch;   // number of threads to use for batch processing

        enum llama_rope_scaling_type rope_scaling_type; // RoPE scaling type, from `enum llama_rope_scaling_type`
        enum llama_pooling_type      pooling_type;      // whether to pool (sum) embedding results by sequence id
        enum llama_attention_type    attention_type;    // attention type to use for embeddings

        // ref: https://github.com/ggml-org/llama.cpp/pull/2054
        float    rope_freq_base;   // RoPE base frequency, 0 = from model
        float    rope_freq_scale;  // RoPE frequency scaling factor, 0 = from model
        float    yarn_ext_factor;  // YaRN extrapolation mix factor, negative = from model
        float    yarn_attn_factor; // YaRN magnitude scaling factor
        float    yarn_beta_fast;   // YaRN low correction dim
        float    yarn_beta_slow;   // YaRN high correction dim
        uint32_t yarn_orig_ctx;    // YaRN original context size
        float    defrag_thold;     // defragment the KV cache if holes/size > thold, < 0 disabled (default)

        ggml_backend_sched_eval_callback cb_eval;
        void * cb_eval_user_data;

        enum ggml_type type_k; // data type for K cache [EXPERIMENTAL]
        enum ggml_type type_v; // data type for V cache [EXPERIMENTAL]

        // Keep the booleans together and at the end of the struct to avoid misalignment during copy-by-value.
        // TODO: move at the end of the struct
        bool logits_all;  // the llama_decode() call computes all logits, not just the last one (DEPRECATED - set llama_batch.logits instead)
        bool embeddings;  // if true, extract embeddings (together with logits)
        bool offload_kqv; // whether to offload the KQV ops (including the KV cache) to GPU
        bool flash_attn;  // whether to use flash attention [EXPERIMENTAL]
        bool no_perf;     // whether to measure performance timings

        // Abort callback
        // if it returns true, execution of llama_decode() will be aborted
        // currently works only with CPU execution
        ggml_abort_callback abort_callback;
        void *              abort_callback_data;
    };
```

New Deprecated Version:
```c++
struct llama_context_params {
        uint32_t n_ctx;             // text context, 0 = from model
        uint32_t n_batch;           // logical maximum batch size that can be submitted to llama_decode
        uint32_t n_ubatch;          // physical maximum batch size
        uint32_t n_seq_max;         // max number of sequences (i.e. distinct states for recurrent models)
        int32_t  n_threads;         // number of threads to use for generation
        int32_t  n_threads_batch;   // number of threads to use for batch processing

        enum llama_rope_scaling_type rope_scaling_type; // RoPE scaling type, from `enum llama_rope_scaling_type`
        enum llama_pooling_type      pooling_type;      // whether to pool (sum) embedding results by sequence id
        enum llama_attention_type    attention_type;    // attention type to use for embeddings

        // ref: https://github.com/ggml-org/llama.cpp/pull/2054
        float    rope_freq_base;   // RoPE base frequency, 0 = from model
        float    rope_freq_scale;  // RoPE frequency scaling factor, 0 = from model
        float    yarn_ext_factor;  // YaRN extrapolation mix factor, negative = from model
        float    yarn_attn_factor; // YaRN magnitude scaling factor
        float    yarn_beta_fast;   // YaRN low correction dim
        float    yarn_beta_slow;   // YaRN high correction dim
        uint32_t yarn_orig_ctx;    // YaRN original context size
        float    defrag_thold;     // defragment the KV cache if holes/size > thold, < 0 disabled (default)

        ggml_backend_sched_eval_callback cb_eval;
        void * cb_eval_user_data;

        enum ggml_type type_k; // data type for K cache [EXPERIMENTAL]
        enum ggml_type type_v; // data type for V cache [EXPERIMENTAL]

        // Keep the booleans together and at the end of the struct to avoid misalignment during copy-by-value.
        // TODO: move at the end of the struct
        bool logits_all;  // the llama_decode() call computes all logits, not just the last one (DEPRECATED - set llama_batch.logits instead)
        bool embeddings;  // if true, extract embeddings (together with logits)
        bool offload_kqv; // whether to offload the KQV ops (including the KV cache) to GPU
        bool flash_attn;  // whether to use flash attention [EXPERIMENTAL]
        bool no_perf;     // whether to measure performance timings

        // Abort callback
        // if it returns true, execution of llama_decode() will be aborted
        // currently works only with CPU execution
        ggml_abort_callback abort_callback;
        void *              abort_callback_data;
    };
```

+++++++++++++++++++++++++++++++++
31 - hint
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(func, hint)
```

New Deprecated Version:
```c++
DEPRECATED(func, hint)
```

+++++++++++++++++++++++++++++++++
32 - hint_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(func, hint)
```

New Deprecated Version:
```c++
DEPRECATED(func, hint)
```

+++++++++++++++++++++++++++++++++
33 - hint_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(func, hint)
```

New Deprecated Version:
```c++
DEPRECATED(func, hint)
```

+++++++++++++++++++++++++++++++++
34 - struct_13
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API struct llama_model * llama_load_model_from_file(
                             const char * path_model,
              struct llama_model_params   params)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API struct llama_model * llama_load_model_from_file(
                             const char * path_model,
              struct llama_model_params   params)
```

+++++++++++++++++++++++++++++++++
35 - void_24
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_free_model(struct llama_model * model)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_free_model(struct llama_model * model)
```

+++++++++++++++++++++++++++++++++
36 - struct_14
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API struct llama_context * llama_new_context_with_model(
                     struct llama_model * model,
            struct llama_context_params   params)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API struct llama_context * llama_new_context_with_model(
                     struct llama_model * model,
            struct llama_context_params   params)
```

+++++++++++++++++++++++++++++++++
37 - int32_t_18
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_n_ctx_train(const struct llama_model * model)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_n_ctx_train(const struct llama_model * model)
```

+++++++++++++++++++++++++++++++++
38 - int32_t_19
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_n_embd     (const struct llama_model * model)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_n_embd     (const struct llama_model * model)
```

+++++++++++++++++++++++++++++++++
39 - int32_t_20
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_n_layer    (const struct llama_model * model)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_n_layer    (const struct llama_model * model)
```

+++++++++++++++++++++++++++++++++
40 - int32_t_21
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_n_head     (const struct llama_model * model)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_n_head     (const struct llama_model * model)
```

+++++++++++++++++++++++++++++++++
41 - int32_t_22
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_n_vocab    (const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_n_vocab    (const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
42 - int32_t_23
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_get_kv_cache_token_count(const struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_get_kv_cache_token_count(const struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
43 - int32_t_24
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API int32_t llama_get_kv_cache_used_cells(const struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API int32_t llama_get_kv_cache_used_cells(const struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
44 - void_25
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_clear(
            struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_clear(
            struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
45 - bool_14
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API bool llama_kv_cache_seq_rm(
            struct llama_context * ctx,
                    llama_seq_id   seq_id,
                       llama_pos   p0,
                       llama_pos   p1)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API bool llama_kv_cache_seq_rm(
            struct llama_context * ctx,
                    llama_seq_id   seq_id,
                       llama_pos   p0,
                       llama_pos   p1)
```

+++++++++++++++++++++++++++++++++
46 - void_26
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_cp(
            struct llama_context * ctx,
                    llama_seq_id   seq_id_src,
                    llama_seq_id   seq_id_dst,
                       llama_pos   p0,
                       llama_pos   p1)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_cp(
            struct llama_context * ctx,
                    llama_seq_id   seq_id_src,
                    llama_seq_id   seq_id_dst,
                       llama_pos   p0,
                       llama_pos   p1)
```

+++++++++++++++++++++++++++++++++
47 - void_27
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_keep(
            struct llama_context * ctx,
                    llama_seq_id   seq_id)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_keep(
            struct llama_context * ctx,
                    llama_seq_id   seq_id)
```

+++++++++++++++++++++++++++++++++
48 - void_28
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_add(
            struct llama_context * ctx,
                    llama_seq_id   seq_id,
                       llama_pos   p0,
                       llama_pos   p1,
                       llama_pos   delta)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_add(
            struct llama_context * ctx,
                    llama_seq_id   seq_id,
                       llama_pos   p0,
                       llama_pos   p1,
                       llama_pos   delta)
```

+++++++++++++++++++++++++++++++++
49 - void_29
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_div(
            struct llama_context * ctx,
                    llama_seq_id   seq_id,
                       llama_pos   p0,
                       llama_pos   p1,
                             int   d)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_seq_div(
            struct llama_context * ctx,
                    llama_seq_id   seq_id,
                       llama_pos   p0,
                       llama_pos   p1,
                             int   d)
```

+++++++++++++++++++++++++++++++++
50 - llama_pos_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_pos llama_kv_cache_seq_pos_max(
            struct llama_context * ctx,
                    llama_seq_id   seq_id)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_pos llama_kv_cache_seq_pos_max(
            struct llama_context * ctx,
                    llama_seq_id   seq_id)
```

+++++++++++++++++++++++++++++++++
51 - void_30
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_defrag(struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_defrag(struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
52 - bool_15
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API bool llama_kv_cache_can_shift(const struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API bool llama_kv_cache_can_shift(const struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
53 - void_31
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_update(struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API void llama_kv_cache_update(struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
54 - llama_get_state_size
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(size_t llama_get_state_size(struct llama_context * ctx)
```

New Deprecated Version:
```c++
DEPRECATED(size_t llama_get_state_size(struct llama_context * ctx)
```

+++++++++++++++++++++++++++++++++
55 - llama_copy_state_data
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(size_t llama_copy_state_data(
            struct llama_context * ctx,
                         uint8_t * dst)
```

New Deprecated Version:
```c++
DEPRECATED(size_t llama_copy_state_data(
            struct llama_context * ctx,
                         uint8_t * dst)
```

+++++++++++++++++++++++++++++++++
56 - llama_set_state_data
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(size_t llama_set_state_data(
            struct llama_context * ctx,
                   const uint8_t * src)
```

New Deprecated Version:
```c++
DEPRECATED(size_t llama_set_state_data(
            struct llama_context * ctx,
                   const uint8_t * src)
```

+++++++++++++++++++++++++++++++++
57 - llama_load_session_file
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(bool llama_load_session_file(
            struct llama_context * ctx,
                      const char * path_session,
                     llama_token * tokens_out,
                          size_t   n_token_capacity,
                          size_t * n_token_count_out)
```

New Deprecated Version:
```c++
DEPRECATED(bool llama_load_session_file(
            struct llama_context * ctx,
                      const char * path_session,
                     llama_token * tokens_out,
                          size_t   n_token_capacity,
                          size_t * n_token_count_out)
```

+++++++++++++++++++++++++++++++++
58 - llama_save_session_file
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(bool llama_save_session_file(
            struct llama_context * ctx,
                      const char * path_session,
               const llama_token * tokens,
                          size_t   n_token_count)
```

New Deprecated Version:
```c++
DEPRECATED(bool llama_save_session_file(
            struct llama_context * ctx,
                      const char * path_session,
               const llama_token * tokens,
                          size_t   n_token_count)
```

+++++++++++++++++++++++++++++++++
59 - const
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API const char * llama_token_get_text(const struct llama_vocab * vocab, llama_token token)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API const char * llama_token_get_text(const struct llama_vocab * vocab, llama_token token)
```

+++++++++++++++++++++++++++++++++
60 - float_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API float llama_token_get_score(const struct llama_vocab * vocab, llama_token token)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API float llama_token_get_score(const struct llama_vocab * vocab, llama_token token)
```

+++++++++++++++++++++++++++++++++
61 - enum_5
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API enum llama_token_attr llama_token_get_attr(const struct llama_vocab * vocab, llama_token token)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API enum llama_token_attr llama_token_get_attr(const struct llama_vocab * vocab, llama_token token)
```

+++++++++++++++++++++++++++++++++
62 - bool_16
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API bool llama_token_is_eog(const struct llama_vocab * vocab, llama_token token)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API bool llama_token_is_eog(const struct llama_vocab * vocab, llama_token token)
```

+++++++++++++++++++++++++++++++++
63 - bool_17
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API bool llama_token_is_control(const struct llama_vocab * vocab, llama_token token)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API bool llama_token_is_control(const struct llama_vocab * vocab, llama_token token)
```

+++++++++++++++++++++++++++++++++
64 - llama_token_20
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_bos(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_bos(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
65 - llama_token_21
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_eos(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_eos(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
66 - llama_token_22
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_eot(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_eot(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
67 - llama_token_23
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_cls(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_cls(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
68 - llama_token_24
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_sep(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_sep(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
69 - llama_token_25
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_nl (const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_nl (const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
70 - llama_token_26
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_pad(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_pad(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
71 - bool_18
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API bool llama_add_bos_token(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API bool llama_add_bos_token(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
72 - bool_19
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API bool llama_add_eos_token(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API bool llama_add_eos_token(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
73 - llama_token_27
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_pre(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_pre(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
74 - llama_token_28
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_suf(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_suf(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
75 - llama_token_29
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_mid(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_mid(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
76 - llama_token_30
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_pad(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_pad(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
77 - llama_token_31
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_rep(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_rep(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
78 - llama_token_32
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_sep(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_token_fim_sep(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
79 - llama_token_33
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API llama_token llama_vocab_cls(const struct llama_vocab * vocab)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API llama_token llama_vocab_cls(const struct llama_vocab * vocab)
```

+++++++++++++++++++++++++++++++++
80 - struct_15
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API struct llama_sampler * llama_sampler_init_softmax    (void)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API struct llama_sampler * llama_sampler_init_softmax    (void)
```

+++++++++++++++++++++++++++++++++
81 - struct_16
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(LLAMA_API struct llama_sampler * llama_sampler_init_grammar_lazy(
            const struct llama_vocab * vocab,
                          const char * grammar_str,
                          const char * grammar_root,
                         const char ** trigger_words,
                                size_t num_trigger_words,
                   const llama_token * trigger_tokens,
                                size_t num_trigger_tokens)
```

New Deprecated Version:
```c++
DEPRECATED(LLAMA_API struct llama_sampler * llama_sampler_init_grammar_lazy(
            const struct llama_vocab * vocab,
                          const char * grammar_str,
                          const char * grammar_root,
                         const char ** trigger_words,
                                size_t num_trigger_words,
                   const llama_token * trigger_tokens,
                                size_t num_trigger_tokens)
```

+++++++++++++++++++++++++++++++++
82 - in_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
size in bytes for all elements in a row

    GGML_DEPRECATED(
    GGML_API double ggml_type_sizef(enum ggml_type type), // ggml_type_size()/ggml_blck_size() as float
    "use ggml_row_size() instead");
```

New Deprecated Version:
```c++
size in bytes for all elements in a row

    GGML_DEPRECATED(
    GGML_API double ggml_type_sizef(enum ggml_type type), // ggml_type_size()/ggml_blck_size() as float
    "use ggml_row_size() instead");
```

+++++++++++++++++++++++++++++++++
83 - hint
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(func, hint)
```

New Deprecated Version:
```c++
DEPRECATED(func, hint)
```

+++++++++++++++++++++++++++++++++
84 - hint_1
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(func, hint)
```

New Deprecated Version:
```c++
DEPRECATED(func, hint)
```

+++++++++++++++++++++++++++++++++
85 - hint_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(func, hint)
```

New Deprecated Version:
```c++
DEPRECATED(func, hint)
```

+++++++++++++++++++++++++++++++++
86 - GGML_API_4
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(
    GGML_API double ggml_type_sizef(enum ggml_type type)
```

New Deprecated Version:
```c++
DEPRECATED(
    GGML_API double ggml_type_sizef(enum ggml_type type)
```

+++++++++++++++++++++++++++++++++
87 - struct_2
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_rope_custom(
            struct ggml_context * ctx,
            struct ggml_tensor  * a,
            struct ggml_tensor  * b,
            int                   n_dims,
            int                   mode,
            int                   n_ctx_orig,
            float                 freq_base,
            float                 freq_scale,
            float                 ext_factor,
            float                 attn_factor,
            float                 beta_fast,
            float                 beta_slow)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_rope_custom(
            struct ggml_context * ctx,
            struct ggml_tensor  * a,
            struct ggml_tensor  * b,
            int                   n_dims,
            int                   mode,
            int                   n_ctx_orig,
            float                 freq_base,
            float                 freq_scale,
            float                 ext_factor,
            float                 attn_factor,
            float                 beta_fast,
            float                 beta_slow)
```

+++++++++++++++++++++++++++++++++
88 - struct_3
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_rope_custom_inplace(
            struct ggml_context * ctx,
            struct ggml_tensor  * a,
            struct ggml_tensor  * b,
            int                   n_dims,
            int                   mode,
            int                   n_ctx_orig,
            float                 freq_base,
            float                 freq_scale,
            float                 ext_factor,
            float                 attn_factor,
            float                 beta_fast,
            float                 beta_slow)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_rope_custom_inplace(
            struct ggml_context * ctx,
            struct ggml_tensor  * a,
            struct ggml_tensor  * b,
            int                   n_dims,
            int                   mode,
            int                   n_ctx_orig,
            float                 freq_base,
            float                 freq_scale,
            float                 ext_factor,
            float                 attn_factor,
            float                 beta_fast,
            float                 beta_slow)
```

+++++++++++++++++++++++++++++++++
89 - struct_4
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_unary_f32(
            struct ggml_context        * ctx,
            struct ggml_tensor         * a,
                   ggml_unary_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_unary_f32(
            struct ggml_context        * ctx,
            struct ggml_tensor         * a,
                   ggml_unary_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
90 - struct_5
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_unary_inplace_f32(
            struct ggml_context        * ctx,
            struct ggml_tensor         * a,
                   ggml_unary_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_unary_inplace_f32(
            struct ggml_context        * ctx,
            struct ggml_tensor         * a,
                   ggml_unary_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
91 - struct_6
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_binary_f32(
            struct ggml_context         * ctx,
            struct ggml_tensor          * a,
            struct ggml_tensor          * b,
                   ggml_binary_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_binary_f32(
            struct ggml_context         * ctx,
            struct ggml_tensor          * a,
            struct ggml_tensor          * b,
                   ggml_binary_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
92 - struct_7
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_binary_inplace_f32(
            struct ggml_context         * ctx,
            struct ggml_tensor          * a,
            struct ggml_tensor          * b,
                   ggml_binary_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_binary_inplace_f32(
            struct ggml_context         * ctx,
            struct ggml_tensor          * a,
            struct ggml_tensor          * b,
                   ggml_binary_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
93 - struct_8
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom1_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
                   ggml_custom1_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom1_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
                   ggml_custom1_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
94 - struct_9
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom1_inplace_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
                   ggml_custom1_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom1_inplace_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
                   ggml_custom1_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
95 - struct_10
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom2_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
                   ggml_custom2_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom2_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
                   ggml_custom2_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
96 - struct_11
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom2_inplace_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
                   ggml_custom2_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom2_inplace_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
                   ggml_custom2_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
97 - struct_12
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom3_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
            struct ggml_tensor           * c,
                   ggml_custom3_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom3_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
            struct ggml_tensor           * c,
                   ggml_custom3_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
98 - struct_13
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom3_inplace_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
            struct ggml_tensor           * c,
                   ggml_custom3_op_f32_t   fun)
```

New Deprecated Version:
```c++
DEPRECATED(GGML_API struct ggml_tensor * ggml_map_custom3_inplace_f32(
            struct ggml_context          * ctx,
            struct ggml_tensor           * a,
            struct ggml_tensor           * b,
            struct ggml_tensor           * c,
                   ggml_custom3_op_f32_t   fun)
```

+++++++++++++++++++++++++++++++++
99 - llm_kv
+++++++++++++++++++++++++++++++++

Old Version (Before Deprecation):
```c++
enum llm_kv {
    LLM_KV_GENERAL_TYPE,
    LLM_KV_GENERAL_ARCHITECTURE,
    LLM_KV_GENERAL_QUANTIZATION_VERSION,
    LLM_KV_GENERAL_ALIGNMENT,
    LLM_KV_GENERAL_NAME,
    LLM_KV_GENERAL_AUTHOR,
    LLM_KV_GENERAL_VERSION,
    LLM_KV_GENERAL_URL,
    LLM_KV_GENERAL_DESCRIPTION,
    LLM_KV_GENERAL_LICENSE,
    LLM_KV_GENERAL_SOURCE_URL,
    LLM_KV_GENERAL_SOURCE_HF_REPO,

    LLM_KV_VOCAB_SIZE,
    LLM_KV_CONTEXT_LENGTH,
    LLM_KV_EMBEDDING_LENGTH,
    LLM_KV_FEATURES_LENGTH,
    LLM_KV_BLOCK_COUNT,
    LLM_KV_LEADING_DENSE_BLOCK_COUNT,
    LLM_KV_FEED_FORWARD_LENGTH,
    LLM_KV_EXPERT_FEED_FORWARD_LENGTH,
    LLM_KV_EXPERT_SHARED_FEED_FORWARD_LENGTH,
    LLM_KV_USE_PARALLEL_RESIDUAL,
    LLM_KV_TENSOR_DATA_LAYOUT,
    LLM_KV_EXPERT_COUNT,
    LLM_KV_EXPERT_USED_COUNT,
    LLM_KV_EXPERT_SHARED_COUNT,
    LLM_KV_EXPERT_WEIGHTS_SCALE,
    LLM_KV_EXPERT_WEIGHTS_NORM,
    LLM_KV_EXPERT_GATING_FUNC,
    LLM_KV_POOLING_TYPE,
    LLM_KV_LOGIT_SCALE,
    LLM_KV_DECODER_START_TOKEN_ID,
    LLM_KV_ATTN_LOGIT_SOFTCAPPING,
    LLM_KV_FINAL_LOGIT_SOFTCAPPING,
    LLM_KV_SWIN_NORM,
    LLM_KV_RESCALE_EVERY_N_LAYERS,
    LLM_KV_TIME_MIX_EXTRA_DIM,
    LLM_KV_TIME_DECAY_EXTRA_DIM,
    LLM_KV_RESIDUAL_SCALE,
    LLM_KV_EMBEDDING_SCALE,
    LLM_KV_TOKEN_SHIFT_COUNT,

    LLM_KV_ATTENTION_HEAD_COUNT,
    LLM_KV_ATTENTION_HEAD_COUNT_KV,
    LLM_KV_ATTENTION_MAX_ALIBI_BIAS,
    LLM_KV_ATTENTION_CLAMP_KQV,
    LLM_KV_ATTENTION_KEY_LENGTH,
    LLM_KV_ATTENTION_VALUE_LENGTH,
    LLM_KV_ATTENTION_LAYERNORM_EPS,
    LLM_KV_ATTENTION_LAYERNORM_RMS_EPS,
    LLM_KV_ATTENTION_GROUPNORM_EPS,
    LLM_KV_ATTENTION_GROUPNORM_GROUPS,
    LLM_KV_ATTENTION_CAUSAL,
    LLM_KV_ATTENTION_Q_LORA_RANK,
    LLM_KV_ATTENTION_KV_LORA_RANK,
    LLM_KV_ATTENTION_DECAY_LORA_RANK,
    LLM_KV_ATTENTION_ICLR_LORA_RANK,
    LLM_KV_ATTENTION_VALUE_RESIDUAL_MIX_LORA_RANK,
    LLM_KV_ATTENTION_GATE_LORA_RANK,
    LLM_KV_ATTENTION_RELATIVE_BUCKETS_COUNT,
    LLM_KV_ATTENTION_SLIDING_WINDOW,
    LLM_KV_ATTENTION_SCALE,

    LLM_KV_ROPE_DIMENSION_COUNT,
    LLM_KV_ROPE_DIMENSION_SECTIONS,
    LLM_KV_ROPE_FREQ_BASE,
    LLM_KV_ROPE_SCALE_LINEAR,
    LLM_KV_ROPE_SCALING_TYPE,
    LLM_KV_ROPE_SCALING_FACTOR,
    LLM_KV_ROPE_SCALING_ATTN_FACTOR,
    LLM_KV_ROPE_SCALING_ORIG_CTX_LEN,
    LLM_KV_ROPE_SCALING_FINETUNED,
    LLM_KV_ROPE_SCALING_YARN_LOG_MUL,

    LLM_KV_SPLIT_NO,
    LLM_KV_SPLIT_COUNT,
    LLM_KV_SPLIT_TENSORS_COUNT,

    LLM_KV_SSM_INNER_SIZE,
    LLM_KV_SSM_CONV_KERNEL,
    LLM_KV_SSM_STATE_SIZE,
    LLM_KV_SSM_TIME_STEP_RANK,
    LLM_KV_SSM_DT_B_C_RMS,

    LLM_KV_WKV_HEAD_SIZE,

    LLM_KV_TOKENIZER_MODEL,
    LLM_KV_TOKENIZER_PRE,
    LLM_KV_TOKENIZER_LIST,
    LLM_KV_TOKENIZER_TOKEN_TYPE,
    LLM_KV_TOKENIZER_TOKEN_TYPE_COUNT,
    LLM_KV_TOKENIZER_SCORES,
    LLM_KV_TOKENIZER_MERGES,
    LLM_KV_TOKENIZER_BOS_ID,
    LLM_KV_TOKENIZER_EOS_ID,
    LLM_KV_TOKENIZER_EOT_ID,
    LLM_KV_TOKENIZER_EOM_ID,
    LLM_KV_TOKENIZER_UNK_ID,
    LLM_KV_TOKENIZER_SEP_ID,
    LLM_KV_TOKENIZER_PAD_ID,
    LLM_KV_TOKENIZER_CLS_ID,
    LLM_KV_TOKENIZER_MASK_ID,
    LLM_KV_TOKENIZER_ADD_BOS,
    LLM_KV_TOKENIZER_ADD_EOS,
    LLM_KV_TOKENIZER_ADD_PREFIX,
    LLM_KV_TOKENIZER_REMOVE_EXTRA_WS,
    LLM_KV_TOKENIZER_PRECOMPILED_CHARSMAP,
    LLM_KV_TOKENIZER_HF_JSON,
    LLM_KV_TOKENIZER_RWKV,
    LLM_KV_TOKENIZER_CHAT_TEMPLATE,
    LLM_KV_TOKENIZER_CHAT_TEMPLATE_N,
    LLM_KV_TOKENIZER_FIM_PRE_ID,
    LLM_KV_TOKENIZER_FIM_SUF_ID,
    LLM_KV_TOKENIZER_FIM_MID_ID,
    LLM_KV_TOKENIZER_FIM_PAD_ID,
    LLM_KV_TOKENIZER_FIM_REP_ID,
    LLM_KV_TOKENIZER_FIM_SEP_ID,

    LLM_KV_ADAPTER_TYPE,
    LLM_KV_ADAPTER_LORA_ALPHA,

    LLM_KV_POSNET_EMBEDDING_LENGTH,
    LLM_KV_POSNET_BLOCK_COUNT,

    LLM_KV_CONVNEXT_EMBEDDING_LENGTH,
    LLM_KV_CONVNEXT_BLOCK_COUNT,

    // deprecated:
    LLM_KV_TOKENIZER_PREFIX_ID,
    LLM_KV_TOKENIZER_SUFFIX_ID,
    LLM_KV_TOKENIZER_MIDDLE_ID,
};
```

New Deprecated Version:
```c++
enum llm_kv {
    LLM_KV_GENERAL_TYPE,
    LLM_KV_GENERAL_ARCHITECTURE,
    LLM_KV_GENERAL_QUANTIZATION_VERSION,
    LLM_KV_GENERAL_ALIGNMENT,
    LLM_KV_GENERAL_NAME,
    LLM_KV_GENERAL_AUTHOR,
    LLM_KV_GENERAL_VERSION,
    LLM_KV_GENERAL_URL,
    LLM_KV_GENERAL_DESCRIPTION,
    LLM_KV_GENERAL_LICENSE,
    LLM_KV_GENERAL_SOURCE_URL,
    LLM_KV_GENERAL_SOURCE_HF_REPO,

    LLM_KV_VOCAB_SIZE,
    LLM_KV_CONTEXT_LENGTH,
    LLM_KV_EMBEDDING_LENGTH,
    LLM_KV_FEATURES_LENGTH,
    LLM_KV_BLOCK_COUNT,
    LLM_KV_LEADING_DENSE_BLOCK_COUNT,
    LLM_KV_FEED_FORWARD_LENGTH,
    LLM_KV_EXPERT_FEED_FORWARD_LENGTH,
    LLM_KV_EXPERT_SHARED_FEED_FORWARD_LENGTH,
    LLM_KV_USE_PARALLEL_RESIDUAL,
    LLM_KV_TENSOR_DATA_LAYOUT,
    LLM_KV_EXPERT_COUNT,
    LLM_KV_EXPERT_USED_COUNT,
    LLM_KV_EXPERT_SHARED_COUNT,
    LLM_KV_EXPERT_WEIGHTS_SCALE,
    LLM_KV_EXPERT_WEIGHTS_NORM,
    LLM_KV_EXPERT_GATING_FUNC,
    LLM_KV_POOLING_TYPE,
    LLM_KV_LOGIT_SCALE,
    LLM_KV_DECODER_START_TOKEN_ID,
    LLM_KV_ATTN_LOGIT_SOFTCAPPING,
    LLM_KV_FINAL_LOGIT_SOFTCAPPING,
    LLM_KV_SWIN_NORM,
    LLM_KV_RESCALE_EVERY_N_LAYERS,
    LLM_KV_TIME_MIX_EXTRA_DIM,
    LLM_KV_TIME_DECAY_EXTRA_DIM,
    LLM_KV_RESIDUAL_SCALE,
    LLM_KV_EMBEDDING_SCALE,
    LLM_KV_TOKEN_SHIFT_COUNT,

    LLM_KV_ATTENTION_HEAD_COUNT,
    LLM_KV_ATTENTION_HEAD_COUNT_KV,
    LLM_KV_ATTENTION_MAX_ALIBI_BIAS,
    LLM_KV_ATTENTION_CLAMP_KQV,
    LLM_KV_ATTENTION_KEY_LENGTH,
    LLM_KV_ATTENTION_VALUE_LENGTH,
    LLM_KV_ATTENTION_LAYERNORM_EPS,
    LLM_KV_ATTENTION_LAYERNORM_RMS_EPS,
    LLM_KV_ATTENTION_GROUPNORM_EPS,
    LLM_KV_ATTENTION_GROUPNORM_GROUPS,
    LLM_KV_ATTENTION_CAUSAL,
    LLM_KV_ATTENTION_Q_LORA_RANK,
    LLM_KV_ATTENTION_KV_LORA_RANK,
    LLM_KV_ATTENTION_DECAY_LORA_RANK,
    LLM_KV_ATTENTION_ICLR_LORA_RANK,
    LLM_KV_ATTENTION_VALUE_RESIDUAL_MIX_LORA_RANK,
    LLM_KV_ATTENTION_GATE_LORA_RANK,
    LLM_KV_ATTENTION_RELATIVE_BUCKETS_COUNT,
    LLM_KV_ATTENTION_SLIDING_WINDOW,
    LLM_KV_ATTENTION_SCALE,

    LLM_KV_ROPE_DIMENSION_COUNT,
    LLM_KV_ROPE_DIMENSION_SECTIONS,
    LLM_KV_ROPE_FREQ_BASE,
    LLM_KV_ROPE_SCALE_LINEAR,
    LLM_KV_ROPE_SCALING_TYPE,
    LLM_KV_ROPE_SCALING_FACTOR,
    LLM_KV_ROPE_SCALING_ATTN_FACTOR,
    LLM_KV_ROPE_SCALING_ORIG_CTX_LEN,
    LLM_KV_ROPE_SCALING_FINETUNED,
    LLM_KV_ROPE_SCALING_YARN_LOG_MUL,

    LLM_KV_SPLIT_NO,
    LLM_KV_SPLIT_COUNT,
    LLM_KV_SPLIT_TENSORS_COUNT,

    LLM_KV_SSM_INNER_SIZE,
    LLM_KV_SSM_CONV_KERNEL,
    LLM_KV_SSM_STATE_SIZE,
    LLM_KV_SSM_TIME_STEP_RANK,
    LLM_KV_SSM_DT_B_C_RMS,

    LLM_KV_WKV_HEAD_SIZE,

    LLM_KV_TOKENIZER_MODEL,
    LLM_KV_TOKENIZER_PRE,
    LLM_KV_TOKENIZER_LIST,
    LLM_KV_TOKENIZER_TOKEN_TYPE,
    LLM_KV_TOKENIZER_TOKEN_TYPE_COUNT,
    LLM_KV_TOKENIZER_SCORES,
    LLM_KV_TOKENIZER_MERGES,
    LLM_KV_TOKENIZER_BOS_ID,
    LLM_KV_TOKENIZER_EOS_ID,
    LLM_KV_TOKENIZER_EOT_ID,
    LLM_KV_TOKENIZER_EOM_ID,
    LLM_KV_TOKENIZER_UNK_ID,
    LLM_KV_TOKENIZER_SEP_ID,
    LLM_KV_TOKENIZER_PAD_ID,
    LLM_KV_TOKENIZER_CLS_ID,
    LLM_KV_TOKENIZER_MASK_ID,
    LLM_KV_TOKENIZER_ADD_BOS,
    LLM_KV_TOKENIZER_ADD_EOS,
    LLM_KV_TOKENIZER_ADD_PREFIX,
    LLM_KV_TOKENIZER_REMOVE_EXTRA_WS,
    LLM_KV_TOKENIZER_PRECOMPILED_CHARSMAP,
    LLM_KV_TOKENIZER_HF_JSON,
    LLM_KV_TOKENIZER_RWKV,
    LLM_KV_TOKENIZER_CHAT_TEMPLATE,
    LLM_KV_TOKENIZER_CHAT_TEMPLATE_N,
    LLM_KV_TOKENIZER_FIM_PRE_ID,
    LLM_KV_TOKENIZER_FIM_SUF_ID,
    LLM_KV_TOKENIZER_FIM_MID_ID,
    LLM_KV_TOKENIZER_FIM_PAD_ID,
    LLM_KV_TOKENIZER_FIM_REP_ID,
    LLM_KV_TOKENIZER_FIM_SEP_ID,

    LLM_KV_ADAPTER_TYPE,
    LLM_KV_ADAPTER_LORA_ALPHA,

    LLM_KV_POSNET_EMBEDDING_LENGTH,
    LLM_KV_POSNET_BLOCK_COUNT,

    LLM_KV_CONVNEXT_EMBEDDING_LENGTH,
    LLM_KV_CONVNEXT_BLOCK_COUNT,

    // deprecated:
    LLM_KV_TOKENIZER_PREFIX_ID,
    LLM_KV_TOKENIZER_SUFFIX_ID,
    LLM_KV_TOKENIZER_MIDDLE_ID,
};
```

###################################
SUMMARY
###################################

Deprecated Code Blocks: 99 blocks in 7 files
