###################################
NEW CODE
###################################

+++++++++++++++++++++++++++++++++
1 - get_scratchpad_mem (in ggml/src/ggml-sycl/common.hpp)
+++++++++++++++++++++++++++++++++

New:
```c++
memory get_scratchpad_mem(const dnnl::memory::desc & scratchpad_md,
                                    const dnnl::engine & eng, const queue_ptr q) {
        ggml_sycl_pool_alloc<uint8_t> * pool;
        auto it = scratchpad_map.find(q);
        if (it == scratchpad_map.end()) {
            scratchpad_map[q] = std::make_unique<ggml_sycl_pool_alloc<uint8_t>>(this->pool());
            pool = scratchpad_map[q].get();
        }
```

###################################
SUMMARY
###################################

New Files: 0 files
New Code Blocks: 1 blocks
