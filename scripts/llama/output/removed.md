###################################
REMOVED CODE
###################################

+++++++++++++++++++++++++++++++++
1 - REMOVED FILE: build/bin/ggml-common.h
+++++++++++++++++++++++++++++++++

Removed Block: uint16_t

```c++
typedef uint16_t ggml_half;
```

Removed Block: uint32_t

```c++
typedef uint32_t ggml_half2;
```

Removed Block: uint16_t_1

```c++
typedef uint16_t ggml_half;
```

Removed Block: uint32_t_1

```c++
typedef uint32_t ggml_half2;
```

Removed Block: half

```c++
typedef half  ggml_half;
```

Removed Block: half2

```c++
typedef half2 ggml_half2;
```

Removed Block: half_1

```c++
typedef half  ggml_half;
```

Removed Block: half2_1

```c++
typedef half2 ggml_half2;
```

Removed Block: half_2

```c++
typedef half  ggml_half;
```

Removed Block: half2_2

```c++
typedef half2 ggml_half2;
```

+++++++++++++++++++++++++++++++++
2 - inline_1 (from ggml/src/ggml-sycl/gemm.hpp)
+++++++++++++++++++++++++++++++++

Removed Code:
```c++
static inline void row_gemm(const dnnl::stream& stream, bool a_trans,
        bool b_trans, int m, int n, int k,
        const void* a, dt at, const void* b, dt bt, void* c, dt ct)
    {
        auto const eng = stream.get_engine();
        dnnl::memory::dims a_dims = { m, k }
```

###################################
SUMMARY
###################################

Removed Files: 1 files
Removed Code Blocks: 11 blocks
