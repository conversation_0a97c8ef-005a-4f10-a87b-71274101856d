###################################
CODE CHANGED
###################################

+++++++++++++++++++++++++++++++++
1 - inline
+++++++++++++++++++++++++++++++++

Old: 
```c++
static inline void row_gemm(sycl::queue& q, bool a_trans,
        bool b_trans, int m, int n, int k,
        const void* a, dt at, const void* b, dt bt, void* c, dt ct)
    {
        // Get the device associated with the queue
        sycl::device dev = q.get_device();
        // Get the context associated with the queue
        sycl::context ctx = q.get_context();
        const dnnl::engine eng = dnnl::sycl_interop::make_engine(dev, ctx);
        const dnnl::stream stream = dnnl::sycl_interop::make_stream(eng, q);
        dnnl::memory::dims a_dims = { m, k }
```

New:
```c++
static inline void row_gemm(ggml_backend_sycl_context & ctx, bool a_trans, bool b_trans, int m, int n, int k,
                                const void * a, dt at, const void * b, dt bt, void * c, dt ct, const queue_ptr & q) {
        auto stream = ctx.stream_dnnl(q);
        auto eng = ctx.engine_dnnl(q);
        dnnl::memory::dims a_dims = { m, k }
```

###################################
SUMMARY
###################################

Files changed: 1 files with 1 edits
