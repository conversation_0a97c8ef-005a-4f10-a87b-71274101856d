import os
import re
from pathlib import Path
import difflib
from typing import Dict, List, Set, Tuple, Callable, Optional

def get_all_files(directory: Path, extension: str = None) -> List[Path]:
    """Get all files in a directory recursively, optionally filtering by extension."""
    result = []
    for path in directory.rglob('*'):
        if path.is_file():
            if extension is None or path.suffix == extension:
                result.append(path)
    return result

def get_relative_paths(base_dir: Path, file_list: List[Path]) -> Dict[str, Path]:
    """Convert a list of absolute paths to relative paths from the base directory."""
    result = {}
    for file_path in file_list:
        try:
            relative_path = file_path.relative_to(base_dir)
            result[str(relative_path)] = file_path
        except ValueError:
            # Path is not relative to base_dir
            pass
    return result

def compare_directories(old_dir: Path, new_dir: Path, progress_callback: Optional[Callable] = None) -> Tuple[Set[str], Set[str], Set[str]]:
    """
    Compare two directories and find:
    - new files (in new_dir but not in old_dir)
    - removed files (in old_dir but not in new_dir)
    - common files (in both)
    """
    # Get all files in both directories
    old_files = get_all_files(old_dir)
    new_files = get_all_files(new_dir)
    
    if progress_callback:
        progress_callback(20)
    
    # Get relative paths
    old_relative = get_relative_paths(old_dir, old_files)
    new_relative = get_relative_paths(new_dir, new_files)
    
    if progress_callback:
        progress_callback(20)
    
    # Find sets of relative paths
    old_paths = set(old_relative.keys())
    new_paths = set(new_relative.keys())
    
    # Compute differences
    new_files_set = new_paths - old_paths
    removed_files_set = old_paths - new_paths
    common_files_set = old_paths.intersection(new_paths)
    
    if progress_callback:
        progress_callback(20)
    
    return new_files_set, removed_files_set, common_files_set, old_relative, new_relative

def extract_code_blocks(file_path: Path) -> Dict[str, str]:
    """Extract named code blocks (functions, structs, etc.) from a C/C++ header file."""
    if not file_path.exists() or file_path.suffix not in ['.h', '.hpp', '.c', '.cpp']:
        return {}
    
    content = file_path.read_text(errors='ignore')
    
    # Pattern to match functions, structs, enums, etc.
    patterns = [
        # Function declarations and definitions
        r'((?:LLAMA_API\s+)?(?:static\s+)?(?:inline\s+)?(?:const\s+)?(?:\w+\s+)+\w+\s*\([^{;]*\)\s*(?:{[^}]*}|;))',
        # Struct/enum definitions
        r'((?:typedef\s+)?struct\s+\w+\s*{[^}]*}(?:\s*\w+)?;)',
        r'((?:typedef\s+)?enum\s+\w+\s*{[^}]*}(?:\s*\w+)?;)',
        # Typedefs
        r'(typedef\s+(?:struct|enum)?\s*\w+(?:\s*\**)?\s+\w+;)',
        # Deprecated functions
        r'(DEPRECATED\([^)]*\))'
    ]
    
    blocks = {}
    for pattern in patterns:
        matches = re.finditer(pattern, content, re.DOTALL)
        for match in matches:
            # Try to extract name
            block_content = match.group(1).strip()
            
            # For functions and typedefs, try to get the name
            name_match = re.search(r'(?:struct|enum|)\s+(\w+)', block_content)
            if name_match:
                name = name_match.group(1)
            else:
                # Use a portion of the content as a key
                name = block_content[:40].strip().replace('\n', ' ')
            
            # Ensure unique keys
            i = 1
            base_name = name
            while name in blocks:
                name = f"{base_name}_{i}"
                i += 1
            
            blocks[name] = block_content
    
    return blocks

def compare_file_content(old_file: Path, new_file: Path) -> Dict:
    """Compare content of two files line by line."""
    if not old_file.exists() or not new_file.exists():
        return {"error": "One or both files don't exist"}
    
    old_content = old_file.read_text(errors='ignore').splitlines()
    new_content = new_file.read_text(errors='ignore').splitlines()
    
    diff = difflib.unified_diff(
        old_content, new_content, 
        fromfile=str(old_file), tofile=str(new_file),
        lineterm=''
    )
    
    return {"diff": list(diff)}

def is_deprecated(block: str) -> bool:
    """Check if a code block is marked as deprecated."""
    return "DEPRECATED" in block or "deprecated" in block.lower()

def group_changes(old_blocks: Dict[str, str], new_blocks: Dict[str, str]) -> Dict:
    """Group changes between old and new code blocks."""
    changes = {
        "edited": {},
        "new": {},
        "removed": {},
        "deprecated": {}
    }
    
    # Find edited and deprecated blocks
    for name, content in old_blocks.items():
        if name in new_blocks:
            if old_blocks[name] != new_blocks[name]:
                if is_deprecated(new_blocks[name]) and not is_deprecated(content):
                    changes["deprecated"][name] = {
                        "old": content,
                        "new": new_blocks[name]
                    }
                else:
                    changes["edited"][name] = {
                        "old": content,
                        "new": new_blocks[name]
                    }
        else:
            changes["removed"][name] = content
    
    # Find new blocks
    for name, content in new_blocks.items():
        if name not in old_blocks:
            changes["new"][name] = content
        elif is_deprecated(content) and not is_deprecated(old_blocks[name]):
            # This is already handled above, but we keep this check for clarity
            pass
    
    return changes
