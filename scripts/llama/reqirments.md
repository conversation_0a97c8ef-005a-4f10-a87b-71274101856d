conda activate llamacpp

in /Users/<USER>/Documents/prisma_workspace/scripts i need a script that compare old and new file/folders for changes?

1 - /Users/<USER>/Documents/prisma_workspace/scripts/main.py the main entrypoint
2 - /Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp - the directory with the new repo
3 - /Users/<USER>/Documents/prisma_workspace/prisma_ai/external - the directory with the current repo
4 - /Users/<USER>/Documents/prisma_workspace/scripts/scan.py - file to scan the files/folder
5 - /Users/<USER>/Documents/prisma_workspace/scripts/edit.py - checking for changes in /Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp/include/llama.h that are not present in /Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp/include/llama.h. --- compare with --- /Users/<USER>/Documents/prisma_workspace/prisma_ai/external
6 - /Users/<USER>/Documents/prisma_workspace/scripts/new.py - checking for any new functions, types...etc in /Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp/include/llama.h. - /Users/<USER>/Documents/prisma_workspace/prisma_ai/external
7 - /Users/<USER>/Documents/prisma_workspace/scripts/removed.py - checking for and functions, trpes, structs..etc that's been removed in - /Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp/include/llama.h -- /Users/<USER>/Documents/prisma_workspace/prisma_ai/external
8 - /Users/<USER>/Documents/prisma_workspace/scripts/dep.py - check for anr depricated code in - /Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp/include/llama.h compare with  - /Users/<USER>/Documents/prisma_workspace/prisma_ai/external
9 - /Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp - check if it matches /Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp for any added or deleted files -- compare with -- /Users/<USER>/Documents/prisma_workspace/prisma_ai/external

*******
outputs
*******

/Users/<USER>/Documents/prisma_workspace/scripts/ouput - this folder will be where the outputs will be

1 - /Users/<USER>/Documents/prisma_workspace/scripts/edit.py - will output "edit.md" this file will show all the new code that has been edited.

example: edit.md
###################################
CODE CHANGED
###################################

+++++++++++++++++++++++++++++++++
1 - llama_perf_sampler_data
+++++++++++++++++++++++++++++++++


Old: 
  struct llama_perf_sampler_data {
        double t_sample_ms;

        int32_t n_sample;
    };  


New:
  struct llama_perf_sampler_data_edit {
        double t_sample_ms;

        int32_t n_sample;
    };

...

###################################
SUMMARY
###################################

Files changed: N files changed


-----------------------------------
2 - /Users/<USER>/Documents/prisma_workspace/scripts/new.py - will output "new.md" this file will show all the new code that is not in - /Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp/include/llama.h

example : new.md
###################################
NEW CODE 
###################################

+++++++++++++++++++++++++++++++++
1 - llama_perf_sampler_data_new
+++++++++++++++++++++++++++++++++

New :

struct llama_perf_sampler_data_new {
        double t_sample_ms;

        int32_t n_sample;
    };

...

###################################
SUMMARY
###################################

Files New: N files new

3 - /Users/<USER>/Documents/prisma_workspace/scripts/removed.py - will output "removed.md" this will show all the code that has been removed

example : removed.md
###################################
REMOVED CODE 
###################################

+++++++++++++++++++++++++++++++++
1 - llama_perf_sampler_data_removed
+++++++++++++++++++++++++++++++++

New :

struct llama_perf_sampler_data_removed {
        double t_sample_ms;

        int32_t n_sample;
    };

...

###################################
SUMMARY
###################################

Files Removed N files removed

4 - /Users/<USER>/Documents/prisma_workspace/scripts/dep.py - will output "dep.md" this will show all the depricated code

example : dep.md
###################################
DEPRICATED CODE 
###################################

+++++++++++++++++++++++++++++++++
1 - llama_get_kv_cache_used_cells
+++++++++++++++++++++++++++++++++

New :

DEPRECATED(LLAMA_API int32_t llama_get_kv_cache_used_cells(const struct llama_context * ctx),
            "use llama_kv_self_used_cells instead");

...

###################################
SUMMARY
###################################

Files Removed: N files removed

5 - /Users/<USER>/Documents/prisma_workspace/scripts/codebase.py - this will output "codebase.md" - this will show all the compare both folder structure to check for any new or removed files

example : codebase.md
###################################
CODEBASE CODE 
###################################

+++++++++++++++++++++++++++++++++
1 - llama_get_kv_cache_used_cells
+++++++++++++++++++++++++++++++++

New :

/Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp/src/llama-adapter.cpp

old : 

/Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp/src/llama-adapter_old.cpp

...

###################################
SUMMARY
###################################

Codebase d: N files removed, N files added.

SPECS:

1 - have the code run concurenncy
2 - use tqdm to show 6 loading bars. 1 -new.py, edit.py, removed.py, dep.py, codebase.py and the 6th loading bar will be for all, it will show the overall completion.
3 robust error handling.









