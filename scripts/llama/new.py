import os
from pathlib import Path
from typing import Dict, Callable, Optional
import scan

def run(old_repo: Path, new_repo: Path, output_dir: Path, 
        progress_callback: Optional[Callable] = None) -> Dict:
    """
    Compare files between old and new repos to find new code.
    Generates new.md with details of new code blocks.
    """
    result = {"status": "success", "message": "Completed analysis of new code"}
    
    try:
        # Compare directories to get common files and new files
        new_files, _, common_files, old_relative, new_relative = scan.compare_directories(
            old_repo, new_repo, 
            progress_callback=lambda p: progress_callback(p * 0.2) if progress_callback else None
        )
        
        new_blocks_all = {}
        
        # First handle completely new files
        for rel_path in new_files:
            if rel_path.endswith(('.h', '.hpp')):
                new_file = new_relative[rel_path]
                new_blocks = scan.extract_code_blocks(new_file)
                if new_blocks:
                    new_blocks_all[rel_path] = {"*NEW FILE*": new_blocks}
        
        # Then check common files for new blocks
        header_files = [f for f in common_files if f.endswith(('.h', '.hpp'))]
        total_files = len(header_files)
        
        for i, rel_path in enumerate(header_files):
            if progress_callback and total_files > 0:
                progress_callback(20 + (i / total_files) * 60)
                
            old_file = old_relative[rel_path]
            new_file = new_relative[rel_path]
            
            # Extract code blocks from both files
            old_blocks = scan.extract_code_blocks(old_file)
            new_blocks = scan.extract_code_blocks(new_file)
            
            # Group changes
            changes = scan.group_changes(old_blocks, new_blocks)
            
            # Add new blocks to our collection
            if changes["new"]:
                new_blocks_all[rel_path] = changes["new"]
        
        # Generate markdown report
        if progress_callback:
            progress_callback(80)
            
        output_file = output_dir / "new.md"
        
        with open(output_file, 'w') as f:
            f.write("###################################\n")
            f.write("NEW CODE\n")
            f.write("###################################\n\n")
            
            count = 1
            for file_path, blocks in new_blocks_all.items():
                if isinstance(next(iter(blocks.values())), dict) and "*NEW FILE*" in blocks:
                    # This is a completely new file
                    f.write(f"+++++++++++++++++++++++++++++++++\n")
                    f.write(f"{count} - NEW FILE: {file_path}\n")
                    f.write("+++++++++++++++++++++++++++++++++\n\n")
                    count += 1
                    
                    # List all blocks in this new file
                    for name, content in blocks["*NEW FILE*"].items():
                        f.write(f"New Block: {name}\n\n")
                        f.write("```c++\n")
                        f.write(content)
                        f.write("\n```\n\n")
                else:
                    # These are new blocks in existing files
                    for name, content in blocks.items():
                        f.write("+++++++++++++++++++++++++++++++++\n")
                        f.write(f"{count} - {name} (in {file_path})\n")
                        f.write("+++++++++++++++++++++++++++++++++\n\n")
                        
                        f.write("New:\n")
                        f.write("```c++\n")
                        f.write(content)
                        f.write("\n```\n\n")
                        
                        count += 1
            
            f.write("###################################\n")
            f.write("SUMMARY\n")
            f.write("###################################\n\n")
            
            total_new_files = sum(1 for blocks in new_blocks_all.values() if "*NEW FILE*" in blocks)
            total_new_blocks = sum(len(blocks) for blocks in new_blocks_all.values() 
                                if "*NEW FILE*" not in blocks) + sum(len(blocks["*NEW FILE*"]) 
                                for blocks in new_blocks_all.values() if "*NEW FILE*" in blocks)
            
            f.write(f"New Files: {total_new_files} files\n")
            f.write(f"New Code Blocks: {total_new_blocks} blocks\n")
        
        result["new_files_count"] = total_new_files
        result["new_blocks_count"] = total_new_blocks
        
        if progress_callback:
            progress_callback(20)
        
    except Exception as e:
        result = {"status": "error", "message": f"Error analyzing new code: {str(e)}"}
        raise
        
    return result
