import os
import concurrent.futures
import tqdm
import sys
import traceback
import time
from pathlib import Path

# Import our modules
import scan
import edit
import new
import removed
import dep
import codebase

def ensure_output_dir():
    """Ensure the output directory exists."""
    output_dir = Path("/Users/<USER>/Documents/prisma_workspace/scripts/output")
    output_dir.mkdir(exist_ok=True)
    return output_dir

def run_with_progress(func, desc, **kwargs):
    """Run a function with a progress bar."""
    # Extract position from kwargs so it doesn't get passed to the actual function
    position = kwargs.pop('position', 0)
    progress_bar = tqdm.tqdm(total=100, desc=desc, position=position, leave=True)
    
    # Create a wrapper that updates the progress bar
    def progress_wrapper():
        try:
            result = func(progress_callback=progress_bar.update, **kwargs)
            progress_bar.update(100 - progress_bar.n)  # Ensure we reach 100%
            return result
        except Exception as e:
            progress_bar.colour = 'red'
            progress_bar.write(f"Error in {desc}: {str(e)}")
            traceback.print_exc()
            return None
        finally:
            progress_bar.close()
    
    return progress_wrapper

def main():
    """Main entry point for the comparison script."""
    try:
        print("Starting comparison of llama.cpp repositories...")
        
        # Ensure output directory exists
        output_dir = ensure_output_dir()
        
        # Define paths
        new_repo = Path("/Users/<USER>/Documents/prisma_workspace/scripts/new/llama.cpp")
        old_repo = Path("/Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp")
        
        # Check if directories exist
        if not new_repo.exists() or not old_repo.exists():
            print(f"Error: Required directories don't exist!")
            print(f"New repo exists: {new_repo.exists()}")
            print(f"Old repo exists: {old_repo.exists()}")
            return

        # Set up the tasks to run concurrently
        tasks = [
            ("Checking edited code", run_with_progress(edit.run, "Edited Code", position=0, 
                                                     old_repo=old_repo, new_repo=new_repo, output_dir=output_dir)),
            ("Checking new code", run_with_progress(new.run, "New Code", position=1,
                                                 old_repo=old_repo, new_repo=new_repo, output_dir=output_dir)),
            ("Checking removed code", run_with_progress(removed.run, "Removed Code", position=2,
                                                      old_repo=old_repo, new_repo=new_repo, output_dir=output_dir)),
            ("Checking deprecated code", run_with_progress(dep.run, "Deprecated Code", position=3,
                                                        old_repo=old_repo, new_repo=new_repo, output_dir=output_dir)),
            ("Checking codebase structure", run_with_progress(codebase.run, "Codebase Changes", position=4,
                                                           old_repo=old_repo, new_repo=new_repo, output_dir=output_dir))
        ]
        
        # Create the overall progress bar
        overall_progress = tqdm.tqdm(total=len(tasks), desc="Overall Progress", position=5, leave=True)
        
        # Run tasks concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(tasks)) as executor:
            futures = {executor.submit(task_func): task_name for task_name, task_func in tasks}
            
            for future in concurrent.futures.as_completed(futures):
                task_name = futures[future]
                try:
                    result = future.result()
                    if result is None:
                        print(f"⚠️ {task_name} completed with errors")
                    else:
                        print(f"✅ {task_name} completed successfully")
                except Exception as e:
                    print(f"❌ {task_name} failed: {str(e)}")
                    traceback.print_exc()
                
                overall_progress.update(1)
        
        overall_progress.close()
        print("\nComparison completed. Check the output directory for results.")
        
    except Exception as e:
        print(f"Error in main process: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
